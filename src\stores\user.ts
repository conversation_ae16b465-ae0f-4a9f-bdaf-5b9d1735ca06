import { defineStore } from 'pinia'
import { ref } from 'vue'

interface UserInfo {
    username: string
    role: '操作员' | '管理员' | '系统管理员'
    avatar?: string
}

export const useUserStore = defineStore('user', () => {
    const userInfo = ref<UserInfo | null>(null)

    // 设置用户信息
    const setUserInfo = (info: UserInfo) => {
        userInfo.value = info
    }

    // 清除用户信息
    const clearUserInfo = () => {
        userInfo.value = null
    }

    // 判断是否有权限
    const hasPermission = (requiredRole: UserInfo['role']) => {
        if (!userInfo.value) return false

        const roleLevel = {
            '操作员': 1,
            '管理员': 2,
            '系统管理员': 3
        }

        return roleLevel[userInfo.value.role] >= roleLevel[requiredRole]
    }

    return {
        userInfo,
        setUserInfo,
        clearUserInfo,
        hasPermission
    }
})
