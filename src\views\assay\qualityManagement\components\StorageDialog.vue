<template>
  <Dialog v-model="dialogVisible" title="数据入库">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="样品编号">{{ formData.sampleCode }}</el-descriptions-item>
      <el-descriptions-item label="检测项目">{{ formData.testItem }}</el-descriptions-item>
      <el-descriptions-item label="检测值">
        <span :class="{ 'text-danger': isExceeded, 'text-warning': isWarning }">
          {{ formData.testValue }} {{ formData.unit }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="标准限值">
        {{ standardValue }} {{ formData.unit }}
      </el-descriptions-item>
      <el-descriptions-item label="审核通过日期">{{ formData.auditDate }}</el-descriptions-item>
      <el-descriptions-item label="审核人">{{ formData.auditor || '-' }}</el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="center">入库信息</el-divider>
    
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="storageForm"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="数据库实例" prop="dbInstance">
        <el-select v-model="storageForm.dbInstance" placeholder="请选择数据库实例">
          <el-option label="主数据库" value="main" />
          <el-option label="历史数据库" value="history" />
          <el-option label="备份数据库" value="backup" />
        </el-select>
      </el-form-item>
      <el-form-item label="存储模式" prop="storageMode">
        <el-radio-group v-model="storageForm.storageMode">
          <el-radio label="normal">常规存储</el-radio>
          <el-radio label="compress">压缩存储</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="数据分类" prop="dataCategory">
        <el-select v-model="storageForm.dataCategory" placeholder="请选择数据分类">
          <el-option label="常规监测" value="routine" />
          <el-option label="特殊监测" value="special" />
          <el-option label="实验分析" value="experiment" />
          <el-option label="异常数据" value="exception" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据标签" prop="dataTags">
        <el-select v-model="storageForm.dataTags" multiple placeholder="请选择数据标签">
          <el-option label="进水" value="inflow" />
          <el-option label="出水" value="outflow" />
          <el-option label="工艺段" value="process" />
          <el-option label="超标" value="exceed" />
          <el-option label="异常" value="abnormal" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="storageForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
      </el-form-item>
      <el-form-item v-if="isExceeded" label="超标处理" prop="exceedNote">
        <el-input v-model="storageForm.exceedNote" type="textarea" :rows="2" placeholder="请输入超标处理说明" />
      </el-form-item>
      <el-form-item label="生成报告" prop="generateReport">
        <el-switch v-model="storageForm.generateReport" />
        <span class="ml-2 text-gray-500 text-sm">入库后自动生成检测报告</span>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

defineOptions({ name: 'StorageDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)

interface FormData {
  id?: number;
  sampleCode: string;
  testItem: string;
  testValue: string;
  unit: string;
  auditDate: string;
  auditor: string;
}

interface StorageForm {
  dbInstance: string;
  storageMode: 'normal' | 'compress';
  dataCategory: string;
  dataTags: string[];
  remark: string;
  exceedNote: string;
  generateReport: boolean;
}

// 表单数据
const formData = ref<FormData>({
  sampleCode: '',
  testItem: '',
  testValue: '',
  unit: '',
  auditDate: '',
  auditor: ''
})

// 入库表单
const storageForm = ref<StorageForm>({
  dbInstance: 'main',
  storageMode: 'normal',
  dataCategory: 'routine',
  dataTags: [],
  remark: '',
  exceedNote: '',
  generateReport: false
})

// 表单校验规则
const formRules = reactive<FormRules>({
  dbInstance: [{ required: true, message: '请选择数据库实例', trigger: 'change' }],
  storageMode: [{ required: true, message: '请选择存储模式', trigger: 'change' }],
  dataCategory: [{ required: true, message: '请选择数据分类', trigger: 'change' }],
  exceedNote: [{ required: true, message: '请输入超标处理说明', trigger: 'blur' }]
})

// 计算标准限值
const standardValue = computed((): string => {
  switch (formData.value.testItem) {
    case 'COD':
      return '50'
    case 'BOD5':
      return '10'
    case '氨氮':
      return '5'
    case '总磷':
      return '0.5'
    case '总氮':
      return '15'
    default:
      return '/'
  }
})

// 计算是否超标
const isExceeded = computed((): boolean => {
  if (!formData.value.testValue || !standardValue.value || standardValue.value === '/') {
    return false
  }
  
  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue.value)
  
  return testValue > stdValue
})

// 计算是否警告（接近超标）
const isWarning = computed((): boolean => {
  if (!formData.value.testValue || !standardValue.value || standardValue.value === '/' || isExceeded.value) {
    return false
  }
  
  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue.value)
  
  return testValue > stdValue * 0.9
})

const formRef = ref<FormInstance>()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (data: any): Promise<void> => {
  if (!data) return
  
  dialogVisible.value = true
  formData.value = {
    id: data.id,
    sampleCode: data.sampleCode,
    testItem: data.testItem,
    testValue: data.testValue || '',
    unit: data.unit || 'mg/L',
    auditDate: data.auditDate || '',
    auditor: data.auditor || ''
  }
  
  // 自动设置数据标签
  storageForm.value.dataTags = []
  if (formData.value.sampleCode.includes('进水') || formData.value.sampleCode.includes('入口')) {
    storageForm.value.dataTags.push('inflow')
  }
  if (formData.value.sampleCode.includes('出水') || formData.value.sampleCode.includes('出口')) {
    storageForm.value.dataTags.push('outflow')
  }
  if (isExceeded.value) {
    storageForm.value.dataTags.push('exceed')
    storageForm.value.exceedNote = `${formData.value.testItem}检测值为${formData.value.testValue}${formData.value.unit}，超过标准限值(${standardValue.value}${formData.value.unit})`
  }
  
  // 重置入库表单
  storageForm.value = {
    ...storageForm.value,
    dbInstance: 'main',
    storageMode: 'normal',
    dataCategory: 'routine',
    remark: '',
    generateReport: false
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async (): Promise<void> => {
  // 表单校验
  if (!formRef.value) return
  
  // 如果超标但未填写超标处理说明，则提示
  if (isExceeded.value && !storageForm.value.exceedNote) {
    ElMessage.warning('请填写超标处理说明')
    return
  }
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('数据入库成功')
    dialogVisible.value = false
    emit('success')
    
    // 如果选择生成报告，提示用户
    if (storageForm.value.generateReport) {
      ElMessage.info('已安排生成检测报告，请稍后查看')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script>

<style scoped>
.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}
</style> 