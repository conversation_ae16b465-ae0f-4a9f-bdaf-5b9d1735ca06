<template>
  <div class="accident-register">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      :disabled="formDisabled"
    >
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="事故编号" prop="accidentCode">
              <el-input v-model="formData.accidentCode" placeholder="自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="事故名称" prop="accidentName">
              <el-input v-model="formData.accidentName" placeholder="请输入事故名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="事故分类" prop="classificationType">
              <el-select v-model="formData.classificationType" placeholder="请选择事故分类" style="width: 100%">
                <el-option label="轻微事故" value="minor" />
                <el-option label="一般事故" value="normal" />
                <el-option label="重大事故" value="serious" />
                <el-option label="特大事故" value="critical" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="发生时间" prop="occurTime">
              <el-date-picker
                v-model="formData.occurTime"
                type="datetime"
                placeholder="选择发生时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发现时间" prop="findTime">
              <el-date-picker
                v-model="formData.findTime"
                type="datetime"
                placeholder="选择发现时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报告时间" prop="reportTime">
              <el-date-picker
                v-model="formData.reportTime"
                type="datetime"
                placeholder="选择报告时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发生地点" prop="location">
              <el-input v-model="formData.location" placeholder="请输入发生地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="涉事部门" prop="department">
              <el-select v-model="formData.department" placeholder="请选择涉事部门" style="width: 100%">
                <el-option label="生产部" value="production" />
                <el-option label="工程部" value="engineering" />
                <el-option label="安全部" value="safety" />
                <el-option label="行政部" value="administration" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="事故描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="4"
                placeholder="请详细描述事故经过"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>影响评估</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="伤亡人数" prop="casualties">
              <el-input-number v-model="formData.casualties" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="经济损失" prop="economicLoss">
              <el-input v-model="formData.economicLoss" placeholder="请输入经济损失金额">
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="影响程度" prop="impactLevel">
              <el-select v-model="formData.impactLevel" placeholder="请选择影响程度" style="width: 100%">
                <el-option label="轻微" value="slight" />
                <el-option label="一般" value="moderate" />
                <el-option label="严重" value="severe" />
                <el-option label="灾难性" value="catastrophic" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="影响说明" prop="impactDescription">
              <el-input
                v-model="formData.impactDescription"
                type="textarea"
                :rows="3"
                placeholder="请详细描述事故影响"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>原因分析与处理</span>
          </div>
        </template>
        <el-row>
          <el-col :span="24">
            <el-form-item label="原因分析" prop="causeAnalysis">
              <el-input
                v-model="formData.causeAnalysis"
                type="textarea"
                :rows="3"
                placeholder="请分析事故原因"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="处理措施" prop="measures">
              <el-input
                v-model="formData.measures"
                type="textarea"
                :rows="3"
                placeholder="请描述处理措施"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="预防建议" prop="preventionSuggestions">
              <el-input
                v-model="formData.preventionSuggestions"
                type="textarea"
                :rows="3"
                placeholder="请提出预防类似事故的建议"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <div class="form-actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

const formRef = ref<FormInstance>()
const formDisabled = ref(false)

// 表单数据
const formData = reactive({
  accidentCode: 'ACC-' + new Date().getTime().toString().slice(-8),
  accidentName: '',
  classificationType: '',
  occurTime: '',
  findTime: '',
  reportTime: '',
  location: '',
  department: '',
  description: '',
  casualties: 0,
  economicLoss: '',
  impactLevel: '',
  impactDescription: '',
  causeAnalysis: '',
  measures: '',
  preventionSuggestions: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  accidentName: [
    { required: true, message: '请输入事故名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  classificationType: [
    { required: true, message: '请选择事故分类', trigger: 'change' }
  ],
  occurTime: [
    { required: true, message: '请选择发生时间', trigger: 'change' }
  ],
  findTime: [
    { required: true, message: '请选择发现时间', trigger: 'change' }
  ],
  location: [
    { required: true, message: '请输入发生地点', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择涉事部门', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请描述事故经过', trigger: 'blur' },
    { min: 10, message: '描述不能少于10个字符', trigger: 'blur' }
  ],
  impactLevel: [
    { required: true, message: '请选择影响程度', trigger: 'change' }
  ]
})

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  
  ElMessageBox.confirm('确认重置表单？所有已填写内容将丢失', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    formRef.value!.resetFields()
    formData.accidentCode = 'ACC-' + new Date().getTime().toString().slice(-8)
    ElMessage.success('表单已重置')
  }).catch(() => {
    ElMessage.info('已取消重置')
  })
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      formDisabled.value = true
      // 这里提交表单数据到后端API
      setTimeout(() => {
        ElMessage.success('事故信息登记成功')
        formDisabled.value = false
        // 在实际项目中，可能会重置表单或跳转到其他页面
      }, 1000)
    } else {
      console.log('验证失败', fields)
      ElMessage.error('表单验证失败，请检查并填写正确信息')
    }
  })
}
</script>

<style lang="scss" scoped>
.accident-register {
  .form-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 20px;
  }
}
</style> 