<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">告警记录</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleExport">
              <Icon icon="ep:download" class="mr-1" />导出记录
            </el-button>
          </div>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex flex-col">
        <!-- 搜索表单 -->
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="告警类型" style="width: 200px;">
              <el-select v-model="searchForm.type" placeholder="请选择告警类型" clearable>
                <el-option label="CPU使用率" value="cpu" />
                <el-option label="磁盘空间" value="disk" />
                <el-option label="内存负载" value="memory" />
                <el-option label="终端离线" value="offline" />
              </el-select>
            </el-form-item>
            <el-form-item label="告警级别" style="width: 200px;">
              <el-select v-model="searchForm.level" placeholder="请选择告警级别" clearable>
                <el-option label="一般" value="normal" />
                <el-option label="重要" value="important" />
                <el-option label="紧急" value="urgent" />
              </el-select>
            </el-form-item>
            <el-form-item label="处理状态" style="width: 200px;">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="未处理" value="pending" />
                <el-option label="处理中" value="processing" />
                <el-option label="已处理" value="resolved" />
                <el-option label="已忽略" value="ignored" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围" style="width: 300px;">
              <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 告警记录列表 -->
        <div class="flex-1">
          <el-table :data="tableData" border style="width: 100%" height="100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="time" label="告警时间" width="180" align="center" />
            <el-table-column prop="name" label="告警名称" min-width="150" />
            <el-table-column prop="type" label="告警类型" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getTypeTag(row.type)">{{ getTypeName(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="level" label="告警级别" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getLevelTag(row.level)">{{ getLevelName(row.level) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="device" label="告警设备" min-width="150" />
            <el-table-column prop="value" label="告警值" width="120" align="center" />
            <el-table-column prop="status" label="处理状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusTag(row.status)">{{ getStatusName(row.status) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="notifyStatus" label="通知状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.notifyStatus === 'success' ? 'success' : 'danger'">
                  {{ row.notifyStatus === 'success' ? '已通知' : '通知失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleDetail(row)">详情</el-button>
                <el-button type="primary" link @click="handleProcess(row)">处理</el-button>
                <el-button type="primary" link @click="handleNotify(row)">重新通知</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>

    <!-- 告警详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="告警详情" width="800px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="告警名称">{{ currentDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="告警时间">{{ currentDetail.time }}</el-descriptions-item>
        <el-descriptions-item label="告警类型">{{ getTypeName(currentDetail.type) }}</el-descriptions-item>
        <el-descriptions-item label="告警级别">{{ getLevelName(currentDetail.level) }}</el-descriptions-item>
        <el-descriptions-item label="告警设备">{{ currentDetail.device }}</el-descriptions-item>
        <el-descriptions-item label="告警值">{{ currentDetail.value }}</el-descriptions-item>
        <el-descriptions-item label="处理状态">{{ getStatusName(currentDetail.status) }}</el-descriptions-item>
        <el-descriptions-item label="通知状态">
          {{ currentDetail.notifyStatus === 'success' ? '已通知' : '通知失败' }}
        </el-descriptions-item>
        <el-descriptions-item label="告警描述" :span="2">{{ currentDetail.description }}</el-descriptions-item>
        <el-descriptions-item label="处理记录" :span="2">
          <el-timeline>
            <el-timeline-item v-for="(record, index) in currentDetail.processRecords" :key="index"
              :timestamp="record.time" :type="record.type">
              {{ record.content }}
            </el-timeline-item>
          </el-timeline>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 处理告警对话框 -->
    <el-dialog v-model="processDialogVisible" title="处理告警" width="500px">
      <el-form ref="processFormRef" :model="processForm" :rules="processRules" label-width="100px">
        <el-form-item label="处理方式" prop="method">
          <el-select v-model="processForm.method" placeholder="请选择处理方式">
            <el-option label="已修复" value="fixed" />
            <el-option label="忽略" value="ignore" />
            <el-option label="转交他人" value="transfer" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理说明" prop="description">
          <el-input v-model="processForm.description" type="textarea" placeholder="请输入处理说明" />
        </el-form-item>
        <el-form-item label="转交人" prop="transferTo" v-if="processForm.method === 'transfer'">
          <el-select v-model="processForm.transferTo" placeholder="请选择转交人">
            <el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="processDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleProcessSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 定义告警记录类型
interface AlarmRecord {
  id: string
  time: string
  name: string
  type: string
  level: string
  device: string
  value: string
  status: string
  notifyStatus: string
  description: string
  processRecords: Array<{
    time: string
    type: 'success' | 'warning' | 'info' | 'primary' | 'danger'
    content: string
  }>
}

// 搜索表单
const searchForm = reactive({
  type: '',
  level: '',
  status: '',
  dateRange: []
})

// 表格数据
const tableData = ref<AlarmRecord[]>([
  {
    id: '1',
    time: '2024-04-21 10:00:00',
    name: 'CPU使用率过高',
    type: 'cpu',
    level: 'important',
    device: '服务器1',
    value: '95%',
    status: 'pending',
    notifyStatus: 'success',
    description: 'CPU使用率持续超过90%',
    processRecords: [
      {
        time: '2024-04-21 10:00:00',
        type: 'warning',
        content: '系统检测到CPU使用率异常'
      }
    ]
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框
const detailDialogVisible = ref(false)
const processDialogVisible = ref(false)
const currentDetail = ref<AlarmRecord>({} as AlarmRecord)
const processFormRef = ref<FormInstance>()

// 处理表单
const processForm = reactive({
  method: '',
  description: '',
  transferTo: ''
})

// 用户选项
const userOptions = [
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu' }
]

// 表单验证规则
const processRules = {
  method: [{ required: true, message: '请选择处理方式', trigger: 'change' }],
  description: [{ required: true, message: '请输入处理说明', trigger: 'blur' }],
  transferTo: [{ required: true, message: '请选择转交人', trigger: 'change' }]
}

// 方法
const getTypeTag = (type: string) => {
  switch (type) {
    case 'cpu':
      return 'danger'
    case 'disk':
      return 'warning'
    case 'memory':
      return 'info'
    case 'offline':
      return 'danger'
    default:
      return 'info'
  }
}

const getTypeName = (type: string) => {
  switch (type) {
    case 'cpu':
      return 'CPU使用率'
    case 'disk':
      return '磁盘空间'
    case 'memory':
      return '内存负载'
    case 'offline':
      return '终端离线'
    default:
      return type
  }
}

const getLevelTag = (level: string) => {
  switch (level) {
    case 'normal':
      return 'info'
    case 'important':
      return 'warning'
    case 'urgent':
      return 'danger'
    default:
      return 'info'
  }
}

const getLevelName = (level: string) => {
  switch (level) {
    case 'normal':
      return '一般'
    case 'important':
      return '重要'
    case 'urgent':
      return '紧急'
    default:
      return level
  }
}

const getStatusTag = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'processing':
      return 'primary'
    case 'resolved':
      return 'success'
    case 'ignored':
      return 'info'
    default:
      return 'info'
  }
}

const getStatusName = (status: string) => {
  switch (status) {
    case 'pending':
      return '未处理'
    case 'processing':
      return '处理中'
    case 'resolved':
      return '已处理'
    case 'ignored':
      return '已忽略'
    default:
      return status
  }
}

const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', searchForm)
}

const resetSearch = () => {
  searchForm.type = ''
  searchForm.level = ''
  searchForm.status = ''
  searchForm.dateRange = []
}

const handleDetail = (row: any) => {
  currentDetail.value = row
  detailDialogVisible.value = true
}

const handleProcess = (row: any) => {
  processForm.method = ''
  processForm.description = ''
  processForm.transferTo = ''
  processDialogVisible.value = true
}

const handleNotify = (row: any) => {
  // 实现重新通知逻辑
  ElMessage.success('重新通知成功')
}

const handleExport = () => {
  // 实现导出逻辑
  ElMessage.success('导出成功')
}

const handleProcessSubmit = async () => {
  if (!processFormRef.value) return
  await processFormRef.value.validate((valid) => {
    if (valid) {
      // 实现处理逻辑
      processDialogVisible.value = false
      ElMessage.success('处理成功')
    }
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
