<template>
  <Dialog v-model="dialogVisible" title="报告权限设置">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="报告编号">{{ formData.code }}</el-descriptions-item>
      <el-descriptions-item label="报告名称">{{ formData.name }}</el-descriptions-item>
      <el-descriptions-item label="报告类型">{{ getReportTypeName(formData.type) }}</el-descriptions-item>
      <el-descriptions-item label="创建日期">{{ formData.createDate }}</el-descriptions-item>
      <el-descriptions-item label="创建人">{{ formData.creator }}</el-descriptions-item>
      <el-descriptions-item label="当前权限">
        <el-tag :type="formData.accessPermission === 'public' ? 'success' : 'warning'">
          {{ formData.accessPermission === 'public' ? '公开' : '受限' }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="center">权限设置</el-divider>
    
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="permissionForm"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="访问权限类型" prop="accessType">
        <el-radio-group v-model="permissionForm.accessType">
          <el-radio label="public">
            <el-tag type="success">公开</el-tag>
            <span class="ml-2">所有用户可访问</span>
          </el-radio>
          <el-radio label="restricted">
            <el-tag type="warning">受限</el-tag>
            <span class="ml-2">仅授权用户可访问</span>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item v-if="permissionForm.accessType === 'restricted'" label="授权部门" prop="departments">
        <el-select v-model="permissionForm.departments" multiple placeholder="请选择授权部门">
          <el-option v-for="dept in departmentOptions" :key="dept.id" :label="dept.name" :value="dept.id" />
        </el-select>
      </el-form-item>
      
      <el-form-item v-if="permissionForm.accessType === 'restricted'" label="授权人员" prop="users">
        <el-select v-model="permissionForm.users" multiple placeholder="请选择授权人员">
          <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.id" />
        </el-select>
      </el-form-item>
      
      <el-form-item v-if="permissionForm.accessType === 'restricted'" label="授权角色" prop="roles">
        <el-select v-model="permissionForm.roles" multiple placeholder="请选择授权角色">
          <el-option v-for="role in roleOptions" :key="role.id" :label="role.name" :value="role.id" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="权限操作" prop="operations">
        <el-checkbox-group v-model="permissionForm.operations">
          <el-checkbox label="view">查看报告</el-checkbox>
          <el-checkbox label="download">下载报告</el-checkbox>
          <el-checkbox label="print">打印报告</el-checkbox>
          <el-checkbox label="share">分享报告</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="权限有效期" prop="validPeriod">
        <el-radio-group v-model="permissionForm.validPeriod">
          <el-radio label="permanent">永久有效</el-radio>
          <el-radio label="temporary">临时有效</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item v-if="permissionForm.validPeriod === 'temporary'" label="有效期限" prop="validDate">
        <el-date-picker
          v-model="permissionForm.validDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      
      <el-form-item label="访问记录" prop="recordAccess">
        <el-switch v-model="permissionForm.recordAccess" />
        <span class="ml-2 text-gray-500 text-sm">记录访问日志</span>
      </el-form-item>
      
      <el-form-item label="权限说明" prop="remark">
        <el-input v-model="permissionForm.remark" type="textarea" :rows="3" placeholder="请输入权限说明" />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

defineOptions({ name: 'PermissionDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)

// 部门选项
const departmentOptions = [
  { id: 1, name: '生产部' },
  { id: 2, name: '质量管理部' },
  { id: 3, name: '环保部' },
  { id: 4, name: '安全部' },
  { id: 5, name: '综合管理部' }
]

// 人员选项
const userOptions = [
  { id: 1, name: '张三 (生产部经理)' },
  { id: 2, name: '李四 (质量管理部主管)' },
  { id: 3, name: '王五 (环保专员)' },
  { id: 4, name: '赵六 (安全主管)' },
  { id: 5, name: '钱七 (总经理)' }
]

// 角色选项
const roleOptions = [
  { id: 1, name: '管理员' },
  { id: 2, name: '主管' },
  { id: 3, name: '操作员' },
  { id: 4, name: '检测员' },
  { id: 5, name: '审核员' },
  { id: 6, name: '访客' }
]

interface FormData {
  id?: number;
  code: string;
  name: string;
  type: string;
  createDate: string;
  creator: string;
  accessPermission: 'public' | 'restricted';
}

interface PermissionForm {
  accessType: 'public' | 'restricted';
  departments: number[];
  users: number[];
  roles: number[];
  operations: string[];
  validPeriod: 'permanent' | 'temporary';
  validDate: [Date, Date] | null;
  recordAccess: boolean;
  remark: string;
}

// 表单数据
const formData = ref<FormData>({
  code: '',
  name: '',
  type: '',
  createDate: '',
  creator: '',
  accessPermission: 'public'
})

// 权限表单
const permissionForm = ref<PermissionForm>({
  accessType: 'public',
  departments: [],
  users: [],
  roles: [],
  operations: ['view', 'download'],
  validPeriod: 'permanent',
  validDate: null,
  recordAccess: true,
  remark: ''
})

// 表单校验规则
const formRules = reactive<FormRules>({
  accessType: [{ required: true, message: '请选择访问权限类型', trigger: 'change' }],
  operations: [{ required: true, message: '请至少选择一种权限操作', trigger: 'change', type: 'array', min: 1 }],
  departments: [
    { 
      required: true, 
      message: '请至少选择一个授权部门', 
      trigger: 'change', 
      type: 'array',
      min: 1,
      validator: (rule, value, callback) => {
        if (permissionForm.value.accessType === 'restricted' && (!value || value.length === 0)) {
          callback(new Error('请至少选择一个授权部门'))
        } else {
          callback()
        }
      }
    }
  ],
  validDate: [
    { 
      required: true, 
      message: '请选择有效期限', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (permissionForm.value.validPeriod === 'temporary' && (!value || value.length !== 2)) {
          callback(new Error('请选择有效期限'))
        } else {
          callback()
        }
      }
    }
  ]
})

// 获取报告类型名称
const getReportTypeName = (type: string): string => {
  switch (type) {
    case 'water':
      return '水质报告'
    case 'sludge':
      return '污泥报告'
    case 'gas':
      return '气体报告'
    default:
      return '其他报告'
  }
}

const formRef = ref<FormInstance>()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (data: any): Promise<void> => {
  if (!data) return
  
  dialogVisible.value = true
  formData.value = {
    id: data.id,
    code: data.code,
    name: data.name,
    type: data.type,
    createDate: data.createDate,
    creator: data.creator,
    accessPermission: data.accessPermission || 'public'
  }
  
  // 根据当前权限设置表单默认值
  permissionForm.value = {
    accessType: formData.value.accessPermission,
    departments: formData.value.accessPermission === 'restricted' ? [2, 3] : [], // 默认质量管理部和环保部
    users: formData.value.accessPermission === 'restricted' ? [2, 3] : [], // 默认关键人员
    roles: formData.value.accessPermission === 'restricted' ? [2, 5] : [], // 默认主管和审核员
    operations: ['view', 'download'],
    validPeriod: 'permanent',
    validDate: null,
    recordAccess: true,
    remark: ''
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async (): Promise<void> => {
  // 表单校验
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('权限设置成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script>

<style scoped>
.ml-2 {
  margin-left: 0.5rem;
}

.text-gray-500 {
  color: #909399;
}

.text-sm {
  font-size: 0.875rem;
}
</style> 