<template>
  <div class="report-page" v-loading="loading" element-loading-text="数据加载中，请稍候...">
    <div class="report-page-left">
      <!-- 指标选择器 -->
      <IndicatorSelector
        @select="handleIndicatorSelect"
        @drag-start="handleDragStart"
        @drag="handleDrag"
        @drag-end="handleDragEnd"
      />
<!--      <div class="dataset-panel">-->
<!--        <div class="panel-header">-->
<!--          <span>数据集</span>-->
<!--          <el-button type="primary" size="small" @click="openDatasetDialog">新增</el-button>-->
<!--        </div>-->
<!--        <div class="dataset-list">-->
<!--          <el-scrollbar>-->
<!--            <div-->
<!--              v-for="dataset in selectedDatasets"-->
<!--              :key="dataset.setCode"-->
<!--              class="dataset-item"-->
<!--              :class="{ active: dataset.isExpanded }"-->
<!--            >-->
<!--              <div class="dataset-header" @click="selectDataset(dataset)">-->
<!--                <div class="dataset-name">-->
<!--                  <el-icon class="expand-icon" :class="{ 'is-expanded': dataset.isExpanded }">-->
<!--                    <ArrowRight/>-->
<!--                  </el-icon>-->
<!--                  {{ dataset.setName }}-->
<!--                </div>-->
<!--                <el-icon class="delete-icon" @click.stop="removeDataset(dataset)">-->
<!--                  <Close/>-->
<!--                </el-icon>-->
<!--              </div>-->
<!--              <div class="dataset-params" v-show="dataset.isExpanded">-->
<!--                <div v-for="paramName in dataset.setParamList" :key="paramName" class="param-item">-->
<!--                  <span class="param-name">{{ paramName }}</span>-->
<!--                  <el-icon class="copy-icon" @click="copyParam(dataset.setCode, paramName)">-->
<!--                    <DocumentCopy/>-->
<!--                  </el-icon>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </el-scrollbar>-->
<!--        </div>-->
<!--      </div>-->

      <!-- 数据集选择对话框 -->
      <el-dialog v-model="datasetDialogVisible" title="选择数据集" width="50%">
        <div class="dataset-dialog-content">
          <el-table
            :data="dataSetList"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"/>
            <el-table-column prop="setName" label="数据集名称"/>
            <el-table-column prop="setCode" label="数据集编码"/>
          </el-table>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancelDatasetSelection">取消</el-button>
            <el-button type="primary" @click="confirmDatasetSelection">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
    <div class="report-page-middle">
      <div class="luckysheet-toolbar">
        <el-button
          type="primary"
          title="预览"
          size="small"
          class="toolbar-btn"
          @click="handlePreview"
        >
          <img :src="PreviewIcon" class="icon"/>
          <span class="btn-text">预览</span>
        </el-button>
        <el-button type="success" title="保存" size="small" class="toolbar-btn" @click="handleSave">
          <img :src="SaveIcon" class="icon"/>
          <span class="btn-text">保存</span>
        </el-button>
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          accept=".xlsx"
          @change="handleImport"
        >
          <el-tooltip content="仅支持导入.xlsx格式文件" placement="top">
            <el-button type="warning" title="导入xlsx" size="small" class="toolbar-btn">
              <img :src="ImportIcon" class="icon"/>
              <span class="btn-text">导入</span>
            </el-button>
          </el-tooltip>
        </el-upload>
        <el-button type="info" size="small" title="导出Excel" @click="handleExport">
          <img :src="ExportIcon" class="icon"/>
          <span class="btn-text">导出</span>
        </el-button>
      </div>
      <div class="luckysheet-container">
        <LuckySheetContainer
          ref="luckysheetRef"
          :preview-data="previewData"
          :report-data="reportData"
        />
      </div>
    </div>
    <div class="report-page-right">
      <div class="panel-container">
        <el-tabs v-model="activeTab" class="panel-tabs">
          <el-tab-pane label="单元格信息" name="cellInfo">
            <div class="cell-info-panel">
              <div class="panel-content">
                <div class="info-section">
                  <div class="section-title">行信息</div>
                  <div class="info-item">
                    <span class="label">行号：</span>
                    <span class="value">{{ currentCell?.row || '-' }}</span>
                  </div>
                </div>
                <div class="info-section">
                  <div class="section-title">列信息</div>
                  <div class="info-item">
                    <span class="label">列号：</span>
                    <span class="value">{{ currentCell?.col || '-' }}</span>
                  </div>
                </div>
                <div class="info-section">
                  <div class="section-title">扩展方向</div>
                  <div class="info-item">
                    <span class="label">方向：</span>
                    <el-select v-model="currentCell.ed" size="small" style="width: 100px">
                      <el-option label="不扩展" :value="0"/>
                      <el-option label="向下扩展" :value="1"/>
                      <el-option label="向右扩展" :value="2"/>
                    </el-select>
                  </div>
                </div>
                <div class="info-section">
                  <div class="section-title">只读设置</div>
                  <div class="info-item">
                    <span class="label">只读：</span>
                    <el-select v-model="currentCell.isOR" size="small" style="width: 100px">
                      <el-option label="是" :value="1"/>
                      <el-option label="否" :value="0"/>
                    </el-select>
                  </div>
                </div>
                <div class="info-section">
                  <div class="section-title">向右复制增加偏移量</div>
                  <div class="info-item">
                    <span class="label">增加列数：</span>
                    <el-input-number
                      v-model="offsetNum"
                      :min="1"
                      :max="100"
                      size="small"
                      style="width: 100px"
                    />
                  </div>
                  <div class="info-item">
                    <el-button
                      type="primary"
                      size="small"
                      @click="copyCellToRight"
                      style="width: 100%"
                    >
                      执行复制
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="报表配置" name="reportConfig">
            <div class="report-config-panel">
              <div class="panel-header">
                <span>筛选条件</span>
              </div>
              <div class="panel-content">
                <el-input
                  v-model="reportConfig.config"
                  type="textarea"
                  :rows="10"
                  placeholder="请输入JSON格式的配置"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {ArrowRight, DocumentCopy, Close} from '@element-plus/icons-vue'
import LuckySheetContainer from './components/luckysheet.vue'
import PreviewIcon from '@/assets/icons/preview.svg'
import SaveIcon from '@/assets/icons/save.svg'
import ExportIcon from '@/assets/icons/export.svg'
import ImportIcon from '@/assets/icons/import.svg'
import {ref, onMounted, nextTick, onUnmounted, watch, reactive} from 'vue'
import {ElMessage} from 'element-plus'
import {useRouter, useRoute} from 'vue-router'
import emitter from '@/utils/eventBus'
import {ReportApi} from '@/api/report/reportInfo/index'
import {DataSetApi} from '@/api/report/dataset/index'
import LuckyExcel from 'luckyexcel'
import {v4 as uuidv4} from 'uuid'
import type {CellChangeEvent} from '@/utils/eventBus'
import IndicatorSelector from './components/IndicatorSelector.vue'

const route = useRoute()
const router = useRouter()
const luckysheetRef = ref()

const reportCode = ref('')
const reportName = ref('')
const delSheetsIndexList = ref<number[]>([])
const previewData = ref<any>(null)
const reportData = ref<any>(null)
const excelId = ref<number | null>(null)

// 数据集相关状态
const datasetDialogVisible = ref(false)
const selectedDatasets = ref<DataSetItem[]>([])
const currentDataset = ref<DataSetItem | null>(null)
const selectedDatasetCodes = ref<string[]>([])
const dataSetList = ref<DataSetItem[]>([])
const tempSelectedCodes = ref<string[]>([]) // 临时存储对话框中的选择

// 添加单元格信息相关的响应式变量
const currentCell = ref<CellInfo>({})
const offsetNum = ref<number>(1) // 添加复制次数状态

// 添加拖拽相关的状态
const isDragging = ref(false)
const dragData = ref(null)
const dragPosition = ref({ x: 0, y: 0 })
// 添加记录选择参数的状态
const selectedParams = reactive({
  factoryId: null,
  labelId: null,
  labelName: null,
  type: null
})

// 添加报表配置相关的响应式变量
const activeTab = ref('cellInfo')
const reportConfig = ref<ReportConfig>({
  config: ''
})

// 添加loading状态变量
const loading = ref(false)

// 监听单元格属性的变化
watch(() => currentCell.value.ed, (newValue) => {
  currentCell.value.ed = newValue
  luckysheet.setCellFormat(currentCell.value.row, currentCell.value.col, "ed", newValue)
})

watch(() => currentCell.value.isOR, (newValue) => {
  luckysheet.setCellFormat(currentCell.value.row, currentCell.value.col, "isOR", newValue)
})

// 定义所有luckySheet钩子函数
const luckysheetHooks = {
  // 删除sheet后的钩子
  sheetDeleteAfter: (sheet: any) => {
    delSheetsIndexList.value.push(sheet.sheet.index)
  },
  cellMousedown: (cell: any, position: any) => {
    // console.log('cellMousedown:', cell, position)
    currentCell.value.row = position.r
    currentCell.value.col = position.c
    if (cell) {
      currentCell.value.ed = cell.ed != null ? cell.ed : 0
      currentCell.value.isOR = cell.isOR != null ? cell.isOR : 0
    } else {
      currentCell.value.ed = 0
      currentCell.value.isOR = 0
    }
    currentCell.value.value = cell ? cell.v : ''
  },
  // 鼠标拖拽文件到Luckysheet内部的结束事件
  cellDragStop: (cell, position, sheetFile, ctx, event) => {
    if (dragData.value && position) {
      try {
        // 构建要设置的数据
        const dataToUse = dragData.value;
        const completedData = {
          factoryId: dataToUse.factoryId || '',
          labelId: dataToUse.labelId || '',
          labelName: dataToUse.labelName || '',
          type: dataToUse.type || '',
          internal: dataToUse.internal || ''
        };

        // 过滤掉空值
        const filteredData = {};
        Object.entries(completedData).forEach(([key, value]) => {
          if (value) {
            filteredData[key] = value;
          }
        });

        if (Object.keys(filteredData).length === 0) {
          ElMessage.warning('没有足够的数据可填充到单元格');
          return;
        }

        // 转换为JSON字符串格式
        const jsonData = JSON.stringify(filteredData);
        // 设置单元格值
        luckysheet.setCellValue(position.r, position.c, '$'+jsonData);
      } catch (error: any) {
        console.error('设置单元格数据失败:', error);
        ElMessage.error('无法设置单元格数据: ' + (error.message || '未知错误'));
      }
    }
  }
}

const designOption = ref({
  container: 'luckysheet',
  title: '',
  lang: 'zh',
  showinfobar: true,
  gridKey: '',
  hook: luckysheetHooks,
  showsheetbar: false,  //是否显示底部sheet页按钮
  data: [
    {
      hide: 0,
      column: 18,
      name: 'Sheet1',
      row: 36,
      celldata: [],
      isPivotTable: false,
      config: {},
      order: 0
    }
  ]
})

// 类型定义
interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

interface ReportDesignData {
  excelId: number
  reportName: string
  sheetConfigList: any[]
  setCodes?: string
}

interface DataSetListData {
  id: number
  setCode: string
  setName: string
}

interface DataSetParamsData {
  setParamList: string[]
}

interface CellInfo {
  row?: number
  col?: number
  value?: string
  rowHeight?: number
  colWidth?: number
  ed?: number
  isOR?: number
}

interface DataSetItem {
  id: number
  setCode: string
  setName: string
  setParamList?: string[]
  isExpanded?: boolean
}

interface SaveOrEditData {
  excelId?: number | null
  reportCode: string
  data: any
  delSheetsIndex: number[]
  setParam?: string
  setCodes?: string
}

interface ReportConfig {
  config: string
}

interface PlantOption {
  id: number
  name: string
}

interface AttributeOption {
  value: string
  label: string
}

// 添加API响应类型转换函数
const transformApiResponse = <T, >(response: any): ApiResponse<T> => {
  return {
    code: response.code || 0,
    msg: response.msg || '',
    data: response.data
  }
}

/**
 * 获取路由参数报表编码reportCode
 */
const getReportData = async () => {
  const code = route.query.reportCode
  const name = route.query.reportName
  if (code) {
    reportCode.value = code as string
  }
  if (name) {
    reportName.value = name as string
  }
}

/**
 * 获取报表设计数据
 */
const handleDesignData = async () => {
  try {
    // 获取设计数据  loadType 1加载第一个sheet 2全部加载
    const res = await ReportApi.getReportDesignByCode(reportCode.value, 2)
    const response = transformApiResponse<ReportDesignData>(res)
    if (response.code === 0) {
      let data = response.data
      if (data != null) {
        // 说明已经初始化设计过
        excelId.value = data.excelId
        designOption.value.title = data.reportName
        designOption.value.gridKey = data.excelId.toString() // 转换为字符串
        designOption.value.data = data.sheetConfigList
        reportConfig.value.config = JSON.stringify(data.selectConfig);
        // // 如果有保存的数据集编码，加载对应的数据集
        // if (data.setCodes) {
        //   const savedDatasetCodes = data.setCodes.split('|')
        //   // 查询所有可用的数据集
        //   DataSetApi.queryAllEnableDataSet().then((res) => {
        //     const datasetResponse = transformApiResponse<DataSetListData[]>(res)
        //     if (datasetResponse.code === 0) {
        //       // 过滤出已保存的数据集
        //       const savedDatasets = datasetResponse.data.filter((dataset) =>
        //         savedDatasetCodes.includes(dataset.setCode)
        //       )
        //       // 获取每个数据集的参数
        //       savedDatasets.forEach(async (dataset) => {
        //         try {
        //           const paramRes = await DataSetApi.getDataSetParams(dataset.setCode)
        //           const paramResponse = transformApiResponse<DataSetParamsData>(paramRes)
        //           if (paramResponse.code === 0) {
        //             const datasetWithParams = {
        //               ...dataset,
        //               setParamList: paramResponse.data.setParamList || [],
        //               isExpanded: false
        //             }
        //             selectedDatasets.value.push(datasetWithParams)
        //             selectedDatasetCodes.value.push(dataset.setCode)
        //           }
        //         } catch (error) {
        //           console.error('获取数据集参数失败:', error)
        //           ElMessage.error(`获取数据集 ${dataset.setName} 参数失败`)
        //         }
        //       })
        //     }
        //   })
        // }
      } else {
        // 该报表第一次设计，初始化空报表
        designOption.value.title = reportName.value
      }
    } else {
      console.error('Failed to fetch report:', response.msg || 'Unknown error')
      ElMessage.error('获取报表设计数据失败：' + (response.msg || '未知错误'))
    }
  } catch (error) {
    console.error('Error fetching report:', error)
    ElMessage.error('加载报表设计数据失败，请稍后重试')
  } finally {
    // 数据加载完成后，设置loading为false
    nextTick(() => {
      if (luckysheetRef.value) {
        luckysheetRef.value.initLuckysheet(designOption.value)
      }
      loading.value = false
    })
  }
}

const handleSave = async () => {
  if (!luckysheetRef.value) return
  loading.value = true
  try {
    const excelJson = luckysheet.toJson()
    const saveOrEditData: SaveOrEditData = {
      excelId: excelId.value,
      reportCode: reportCode.value,
      data: excelJson.data,
      delSheetsIndex: delSheetsIndexList.value,
      setCodes: selectedDatasetCodes.value.join('|'),
      selectConfig: reportConfig.value.config
    }
    // 处理数据集编码与参数
    // const setParams: Record<string, Record<string, string>> = {}
    // selectedDatasets.value.forEach((dataset) => {
    //   const paramList = dataset.setParamList
    //   if (paramList && paramList.length > 0) {
    //     const dataSetParam: Record<string, string> = {}
    //     paramList.forEach((value) => {
    //       dataSetParam[value] = ''
    //     })
    //     setParams[dataset.setCode] = dataSetParam
    //   }
    // })

    // saveOrEditData.setParam = JSON.stringify(setParams)

    if (excelId.value == null) {
      // 新增保存
      const saveRes = await ReportApi.saveExcelByDesign(saveOrEditData)
      const saveResponse = transformApiResponse<number>(saveRes)
      if (saveResponse.code === 0) {
        excelId.value = saveResponse.data
        ElMessage.success('保存成功')
        handleDesignData()
      } else {
        ElMessage.error(saveResponse.msg)
      }
    } else {
      const updateRes = await ReportApi.updateExcelByDesign(saveOrEditData)
      const updateResponse = transformApiResponse(updateRes)
      if (updateResponse.code === 0) {
        ElMessage.success('保存成功')
        handleDesignData()
      } else {
        ElMessage.error(updateResponse.msg)
      }
    }
  } catch (error) {
    loading.value = false
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

/**
 * 预览按钮点击事件
 */
const handlePreview = () => {
  router.push({
    path: '/report/reportPreview',
    query: {
      reportCode: reportCode.value
    }
  })
}

/**
 * 导入excel事件
 */
const handleImport = (file: any) => {
  if (!file) return

  const files = file.raw
  if (!files) {
    ElMessage.warning('请选择文件')
    return
  }

  const name = files.name
  const suffixArr = name.split('.')
  const suffix = suffixArr[suffixArr.length - 1]

  if (suffix !== 'xlsx') {
    ElMessage.warning('目前仅支持导入xlsx文件')
    return
  }

  LuckyExcel.transformExcelToLucky(files, (exportJson: any, _luckysheetfile: any) => {
    if (!exportJson.sheets || exportJson.sheets.length === 0) {
      ElMessage.error('读取excel文件内容失败，目前不支持xls文件！')
      return
    }

    // 销毁当前luckysheet实例
    if (luckysheetRef.value) {
      // 销毁之前，把原有的sheetIndex存入删除列表中
      let sheets = luckysheetRef.value.getTableData()
      sheets.forEach((sheet) => {
        delSheetsIndexList.value.push(sheet.index)
      })
      luckysheetRef.value.destroyLuckysheet()
    }

    // 导入的sheet页手动设置索引，防索引太简单且重复，如1,2,3
    exportJson.sheets.forEach((sheet) => {
      sheet.index = uuidv4()
    })

    // 创建新的luckysheet实例
    designOption.value = {
      container: 'luckysheet',
      title: exportJson.info.name,
      lang: 'zh',
      showinfobar: true,
      gridKey: '',
      hook: luckysheetHooks,
      data: exportJson.sheets
    }

    nextTick(() => {
      if (luckysheetRef.value) {
        luckysheetRef.value.initLuckysheet(designOption.value)
        // 清除已选的数据集
        selectedDatasets.value = []
        selectedDatasetCodes.value = []
      }
    })
  })
}

/* 导出 */
const handleExport = () => {
  if (luckysheetRef.value) {
    luckysheetRef.value.exportExcel(`${route.query.reportName}.xlsx`)
  }
}

/**
 * 查询数据集列表方法
 */
const queryDataSetList = async () => {
  try {
    const res = await DataSetApi.queryAllEnableDataSet()
    const response = transformApiResponse<DataSetListData[]>(res)
    if (response.code === 0) {
      dataSetList.value = response.data.map((item) => ({
        id: item.id,
        setCode: item.setCode,
        setName: item.setName
      }))
    }
  } catch (error) {
    console.error('查询数据集列表失败:', error)
    ElMessage.error('查询数据集列表失败')
  }
}

// 打开数据集选择对话框
const openDatasetDialog = () => {
  datasetDialogVisible.value = true
  // 初始化临时选择为当前选中的数据集
  tempSelectedCodes.value = [...selectedDatasetCodes.value]
  // 设置表格的选中状态
  nextTick(() => {
    const table = document.querySelector('.el-table__body-wrapper tbody')
    if (table) {
      const rows = table.querySelectorAll('tr')
      rows.forEach((row) => {
        const checkbox = row.querySelector('.el-checkbox')
        if (checkbox) {
          const datasetCode = row.querySelector('td:nth-child(3)')?.textContent?.trim()
          if (datasetCode && tempSelectedCodes.value.includes(datasetCode)) {
            checkbox.classList.add('is-checked')
          } else {
            checkbox.classList.remove('is-checked')
          }
        }
      })
    }
  })
}

// 处理表格选择变化
const handleSelectionChange = (selection: DataSetItem[]) => {
  // 只更新临时选中的数据集编码
  tempSelectedCodes.value = selection.map((item) => item.setCode)
}

// 确认数据集选择
const confirmDatasetSelection = async () => {
  // 更新选中的数据集编码
  selectedDatasetCodes.value = [...tempSelectedCodes.value]

  // 清空当前选中的数据集
  selectedDatasets.value = []

  // 获取选中数据集的参数
  for (const datasetCode of selectedDatasetCodes.value) {
    try {
      // 从dataSetList中找到对应的数据集
      const dataset = dataSetList.value.find((d) => d.setCode === datasetCode)
      if (dataset) {
        const res = await DataSetApi.getDataSetParams(datasetCode)
        const response = transformApiResponse<DataSetParamsData>(res)
        if (response.code === 0) {
          const datasetWithParams = {
            ...dataset,
            setParamList: response.data.setParamList || [],
            isExpanded: false
          }
          selectedDatasets.value.push(datasetWithParams)
        }
      }
    } catch (error) {
      console.error('获取数据集参数失败:', error)
      ElMessage.error(`获取数据集参数失败`)
    }
  }

  datasetDialogVisible.value = false
}

// 取消选择
const cancelDatasetSelection = () => {
  datasetDialogVisible.value = false
  // 重置临时选择
  tempSelectedCodes.value = []
}

// 选择数据集
const selectDataset = (dataset: DataSetItem) => {
  // 切换展开状态
  dataset.isExpanded = !dataset.isExpanded
  // 如果展开，则设置为当前数据集
  if (dataset.isExpanded) {
    currentDataset.value = dataset
  } else {
    currentDataset.value = null
  }
}

// 添加复制功能
const copyParam = (setCode: string, paramName: string) => {
  const paramValue = `#{${setCode}.${paramName}}`
  navigator.clipboard
    .writeText(paramValue)
    .then(() => {
      ElMessage.success('复制成功')
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}

// 添加删除数据集方法
const removeDataset = (dataset: DataSetItem) => {
  // 从选中的数据集列表中移除
  const index = selectedDatasets.value.findIndex((d) => d.setCode === dataset.setCode)
  if (index !== -1) {
    selectedDatasets.value.splice(index, 1)
  }
  // 从选中的数据集编码列表中移除
  const codeIndex = selectedDatasetCodes.value.indexOf(dataset.setCode)
  if (codeIndex !== -1) {
    selectedDatasetCodes.value.splice(codeIndex, 1)
  }
  // 如果删除的是当前选中的数据集，清空当前数据集
  if (currentDataset.value?.setCode === dataset.setCode) {
    currentDataset.value = null
  }
}

// 添加监听单元格变化的函数
const handleCellChange = (cell: CellChangeEvent) => {
  currentCell.value = {
    row: cell?.r,
    col: cell?.c,
    value: cell?.v,
    rowHeight: cell?.rowHeight,
    colWidth: cell?.colWidth,
  }
}

// 拖拽开始事件处理
const handleDragStart = (data) => {
  console.log('拖拽开始:', data)
  isDragging.value = true
  dragData.value = data
}

// 拖拽过程事件处理
const handleDrag = ({ data, position }) => {
  if (data) {
    dragData.value = data
  }
  dragPosition.value = position
}

// 拖拽结束事件处理
const handleDragEnd = ({ data, success }) => {
  console.log('拖拽结束，接收到的数据:', data, success)
  isDragging.value = false

  // 延迟重置拖拽数据，确保数据处理完成
  setTimeout(() => {
    dragData.value = null
  }, 100)
}

// 指标选择处理函数
const handleIndicatorSelect = ({ type, value }) => {
  console.log('handleIndicatorSelect:', { type, value })
  const luckysheet = luckysheetRef.value?.luckysheet
  if (!luckysheet) {
    console.warn('Luckysheet 实例未初始化')
    return
  }
  const currentSheet = luckysheet.getSheet?.()
  if (!currentSheet) {
    console.warn('无法获取当前 Sheet')
    return
  }
  const selection = luckysheet.getSelection?.()
  if (!selection || selection.length === 0) {
    console.warn('没有选中的单元格')
    return
  }
  const { row: [r], column: [c] } = selection[0]
  luckysheet.setCellValue?.(r, c, value)

  // 更新selectedParams
  if (type && value) {
    selectedParams[type] = value
  }
}

const copyCellToRight = () => {
  let row = currentCell.value.row;
  let col = currentCell.value.col;
  let value = currentCell.value.value; // 原始单元格值，形如 ${"labelId":"22","labelName":"碳源用量（Kg）","type":"3","step":1,"offset":1}

  // 去掉 $ 符号后解析 JSON 字符串
  let json;
  try {
    json = JSON.parse(value.substring(1)); // 去掉 $ 后直接解析
  } catch (e) {
    console.error("解析 JSON 失败:", e);
    return;
  }

  for (let i = 1; i <= offsetNum.value; i++) {
    col += 1; // 向右移动一列
    json.offset += 1; // 更新 offset 值

    // 将更新后的 JSON 重新包装为字符串并设置到新单元格
    luckysheet.setCellValue(row, col, '$' + JSON.stringify(json));
  }
};

onMounted(() => {
  getReportData()
  handleDesignData()
  // 查询数据集列表
  // queryDataSetList()
  // 监听单元格变化
  emitter.on('cellChange', handleCellChange as any)
})

onUnmounted(() => {
  emitter.off('cellChange', handleCellChange as any)
})
</script>
<style scoped lang="scss">
.report-page {
  width: 100%;
  height: calc(100vh - 170px);
  background-color: #fff;
  display: flex;

  .report-page-left {
    flex: 1;
    width: 300px;
    height: 100%;
    padding: 5px;
    background-color: #fff;
    border-right: 1px solid #ebeef5;
  }

  .report-page-middle {
    flex: 5;
    width: 1000px;
    height: 100%;
    padding: 0;
    position: relative;
    display: flex;
    flex-direction: column;

    .luckysheet-toolbar {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      gap: 12px;
      margin-left: 10px;
      padding: 0 10px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;

      .toolbar-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        padding: 8px 16px;
        border-radius: 4px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .icon {
          width: 16px;
          height: 16px;
        }

        .btn-text {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    .luckysheet-container {
      flex: 1;
      width: 100%;
      height: 100%;
    }
  }

  .report-page-right {
    flex: 1;
    height: 100%;
    padding: 5px;
    background-color: #fff;
    border-left: 1px solid #ebeef5;
  }
}

.icon {
  width: 16px;
  height: 16px;
}

.dataset-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }

  .dataset-list {
    flex: 1;
    overflow: hidden;
    padding: 8px;

    .dataset-item {
      padding: 8px;
      border-radius: 4px;
      margin-bottom: 4px;
      background-color: #fff;
      border: 1px solid #ebeef5;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
        border-color: #dcdfe6;
      }

      &.active {
        background-color: #ecf5ff;
        border-color: #c6e2ff;
      }

      .dataset-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 5px;

        .dataset-name {
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 8px;
          color: #303133;
        }

        .delete-icon {
          cursor: pointer;
          color: #909399;
          font-size: 16px;
          padding: 4px;
          border-radius: 50%;
          transition: all 0.3s;

          &:hover {
            color: #f56c6c;
            background-color: #fef0f0;
            transform: scale(1.1);
          }
        }

        .expand-icon {
          transition: transform 0.3s;
          font-size: 14px;
          color: #909399;

          &.is-expanded {
            transform: rotate(90deg);
          }
        }
      }

      .dataset-params {
        margin-left: 20px;
        transition: all 0.3s;

        .param-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin: 4px 0;
          font-size: 13px;
          padding: 6px 8px;
          border-radius: 4px;
          background-color: #f5f7fa;
          border: 1px solid #ebeef5;

          &:hover {
            background-color: #f0f2f5;
            border-color: #dcdfe6;
          }

          .param-name {
            color: #606266;
          }

          .copy-icon {
            cursor: pointer;
            color: #909399;
            font-size: 14px;
            margin-left: 8px;
            padding: 2px;
            border-radius: 50%;
            transition: all 0.3s;

            &:hover {
              color: #409eff;
              background-color: #ecf5ff;
            }
          }
        }
      }
    }
  }
}

.dataset-dialog-content {
  max-height: 400px;
  overflow-y: auto;
}

.luckysheet-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.cell-info-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .panel-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }

  .panel-content {
    flex: 1;
    overflow: hidden;
    padding: 8px;

    .info-section {
      margin-bottom: 16px;

      .section-title {
        font-weight: 500;
        margin-bottom: 8px;
        color: #303133;
      }

      .info-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 4px 0;
        font-size: 13px;
        padding: 6px 8px;
        border-radius: 4px;
        background-color: #f5f7fa;
        border: 1px solid #ebeef5;

        &:hover {
          background-color: #f0f2f5;
          border-color: #dcdfe6;
        }

        .label {
          color: #606266;
          min-width: 80px;
        }

        .value {
          color: #303133;
        }
      }
    }
  }
}

.upload-demo {
  display: inline-block;
}

.panel-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.el-tabs) {
    .el-tabs__header {
      margin: 0;
      padding: 0 16px;
    }
  }
}

.report-config-panel {
  .panel-header {
    justify-content: flex-end;
    padding: 12px 16px;
  }

  .panel-content {
    padding: 16px;

    :deep(.el-textarea) {
      .el-textarea__inner {
        font-family: monospace;
        line-height: 1.5;
      }
    }
  }
}

.panel-tabs {
  flex: 1;
}
</style>
