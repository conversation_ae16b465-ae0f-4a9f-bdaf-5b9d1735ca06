<template>
  <div class="h-full w-full flex flex-col">
    <el-card class="h-full">

      <!-- 视频+左侧控制区域 -->
      <div class="w-full h-full flex">
        <!-- 左侧控制区域 -->
        <div class="w-[300px] flex flex-col justify-between pr-4">
          <!-- 监控点位选择 -->
          <div class="mb-4 flex flex-col">
            <span class="mr-2 leading-[32px] block font-medium">监控点位</span>
            <div class="overflow-auto" style="height: 300px;">
              <el-tree :data="pointTreeData" :props="treeProps" @node-click="handleNodeClick" node-key="id"
                highlight-current :expand-on-click-node="false" class="tree-container">
                <template #default="{ node, data }">
                  <div class="custom-tree-node">
                    <span v-if="data.isPoint === 1">
                      <el-icon v-if="data.isUse === 0" class="mr-1 text-gray-400">
                        <VideoCamera />
                      </el-icon>
                      <el-icon v-else class="mr-1 text-green-600">
                        <VideoCamera />
                      </el-icon>
                    </span>
                    <span v-else class="mr-1">
                      <el-icon>
                        <Folder />
                      </el-icon>
                    </span>
                    <el-tooltip :content="node.label" placement="right" :show-after="500" effect="light">
                      <span :class="{ 'text-gray-400': data.isPoint === 1 && data.isUse === 0 }"
                        class="tree-node-label">{{ node.label }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-tree>
            </div>
          </div>

          <!-- 云台控制区域 -->
          <div id="pan-tilt" class="w-full h-[280px]" :class="{ 'disabled-pan-tilt': !isPtzEnabled }"></div>
        </div>

        <!-- 视频区域 -->
        <div class="flex-1">
          <div class="video-container" id="video"></div>
        </div>
      </div>
    </el-card>
  </div>
</template>


<script lang="ts" setup>
import { MonitorApi } from '@/api/monitor'
import { Folder, VideoCamera } from '@element-plus/icons-vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import PlayerManager from '../../icc/PlayerManager'
import PanTilt from '../../PanTilt/panTilt'
import '../../PanTilt/panTilt.css'

import { MONITOR_STATIC_URL_PREFIX, MONITOR_URL_PREFIX } from '@/config/url'

const isRecording = ref(false)
// 控制云台是否可用
const isPtzEnabled = ref(false)
// 声明PlayerManager的类型为any以避免TypeScript类型错误
let videoPlayer: any = null
let panTilt: any = null

// 新增：云台token变量
const ptzToken = ref('1:CkE97EKEX8KC5r9dG3BFPZ994fqVRa6'); // 可为空或初始token

// 新增：带自动刷新token的请求方法
async function ptzRequest(url: string, params: any) {
  let token = ptzToken.value;
  let response;
  try {
    response = await axios.post(
      url,
      params,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token.startsWith('bearer ') ? token : `bearer ${token}`
        }
      }
    );
    // 检查token失效
    if (
      response.data &&
      response.data.code === '27001007' &&
      response.data.errMsg?.includes('invalid access_token')
    ) {
      // 重新获取token
      const newToken = await MonitorApi.getToken();
      ptzToken.value = newToken;
      // 重试一次
      response = await axios.post(
        url,
        params,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': newToken.startsWith('bearer ') ? newToken : `bearer ${newToken}`
          }
        }
      );
    }
    return response;
  } catch (err) {
    throw err;
  }
}

const props = defineProps<{
  cameraId?: string;
}>();

const emit = defineEmits(['closeCameraMonitor']);

const state = reactive({
  cameraId: props.cameraId || '',
  title: '摄像头视频',
  F_Url: '',
  F_Token: '',
  channelId: '',
  wsUrl: '',
});

// 监听cameraId变化，重新初始化播放器
watch(
  () => props.cameraId,
  (newVal) => {
    if (newVal) {
      state.cameraId = newVal;
      destroyPlayer();
      getCameraDetail(state.cameraId).then(data => {
        if (data) {
          initPlayer(data);
        }
      });
    }
  }
);

// 销毁播放器实例
function destroyPlayer() {
  if (videoPlayer) {
    try {
      videoPlayer.destroy();
    } catch (error) {
      console.error('销毁播放器失败:', error);
    }
    videoPlayer = null;
  }
}

// 启动播放器
function startPlayer(rtspUrl: string) {
  console.log('RTSP URL:', rtspUrl);
  if (!videoPlayer) {
    console.error('播放器实例未创建');
    return;
  }

  try {
    let arr = rtspUrl.match(/^rtsp:\/\/([^\/:]+)(:\d*)?/);
    // 后端返回的rtspUrl中端口是9102，需要转为9100
    // wss: 9102  ws:9100
    // wss 需要加证书， ws不需要
    const rtspUrlFinal = rtspUrl.replace('9102', '9100');
    console.log('arr:', arr);
    if (arr) {
      const host = arr[1];
      const port = 9100;
      state.wsUrl = `ws://${host}:${port}`;
      console.log('WebSocket URL:', state.wsUrl, rtspUrlFinal);

      videoPlayer.realByUrl({
        playType: 'url', // 播放类型
        rtspURL: rtspUrlFinal, // RTSP 地址
        wsURL: state.wsUrl, // WebSocket 地址
        channelId: state.channelId, // 通道 ID
        streamType: '1', // 码流类型 (1-主码流, 2-辅码流)
        selectIndex: '0', // 播放窗口序号
        channelData: {}, // 通道数据
      });
    } else {
      console.error('无效的RTSP URL');
      ElMessage.error('无效的视频地址');
      return;
    }
  } catch (error) {
    console.error('处理RTSP URL失败:', error);
    ElMessage.error('处理视频地址失败');
  }
}

// 初始化播放器
function initPlayer(data: any) {
  console.log('初始化数据:', data);
  // 使用API返回的url和token
  const rtspUrl = data.F_Url + (data.F_Url.includes('?') ? '&' : '?') + 'token=' + data.F_Token;
  console.log('构建的RTSP URL:', rtspUrl);

  videoPlayer = new PlayerManager({
    el: 'video', // 播放器容器
    type: 'real', // 实时预览
    maxNum: 1, // 最大播放路数
    num: 1, // 初始化显示路数
    showControl: true, // 是否显示工具栏
    isIntranet: true, // 固定写死 true
    showRecordProgressBar: true, // 是否显示录像进度条
    prefixUrl: MONITOR_STATIC_URL_PREFIX, // 文件位置
    importLoad: true, // 必填
    receiveMessageFromWSPlayer: (methods: string, data: any) => {
      switch (methods) {
        case 'initializationCompleted':
          console.log('初始化完成:', data);
          startPlayer(rtspUrl); // 初始化完成后启动播放器
          break;
        case 'realSuccess':
          console.log('实时预览成功');
          break;
        case 'realError':
          console.error('实时预览失败:', data);
          break;
        case 'errorInfo':
          console.error('错误信息:', data);
          break;
      }
    },
  });
}
// 获取摄像头详情
const getCameraDetail = async (channelId: string) => {
  try {
    console.log('开始获取摄像头详情，channelId:', channelId);

    const params = {
      dataType: "3",
      streamType: "1",
      channelId: channelId
    };

    const res = await MonitorApi.startVideo(params);
    console.log('获取摄像头详情响应:', res);

    if (res && ((res.code === 0 && res.data) || (res.url && res.token && res.channelId))) {
      // 处理两种可能的返回格式
      const data = res.data || res;
      return {
        F_Url: data.url,
        F_Token: data.token,
        channelId: data.channelId
      };
    } else {
      ElMessage.error('获取视频流失败');
      return null;
    }
  } catch (error) {
    console.error('调用startVideo接口失败:', error);
    ElMessage.error('获取视频流失败');
    return null;
  }
};

// 点位树数据
const pointTreeData = ref<any[]>([]);

// 树形控件配置
const treeProps = {
  label: 'monitorName',
  children: 'childList',
  disabled: (data: any) => data.isPoint !== 1 || data.isUse === 0 // 只有isPoint为1且isUse不为0的节点可以点击
};

// 当前选择的节点
const selectedNode = ref<any>(null);

// 获取点位树数据
const getPointTreeData = async () => {
  try {
    console.log('开始获取监控点位树数据');
    const res = await MonitorApi.getPointTree();
    console.log('获取到的点位树原始数据:', res);

    // 直接处理数组格式的响应
    if (res && Array.isArray(res)) {
      pointTreeData.value = res;
      console.log('成功设置点位树数据:', pointTreeData.value);
    } else if (res && res.data && Array.isArray(res.data)) {
      // 兼容处理，如果返回的是包含data字段的对象
      pointTreeData.value = res.data;
      console.log('成功设置点位树数据(从data提取):', pointTreeData.value);
    } else {
      console.warn('API返回的数据格式异常:', res);
      pointTreeData.value = [];
      ElMessage.error('获取监控点位树失败: 数据格式异常');
    }
  } catch (error: any) {
    console.error('获取监控点位树出错:', error);
    pointTreeData.value = [];
    ElMessage.error(`获取监控点位树出错: ${error.message || '未知错误'}`);
  }
};

// 点击树节点
function handleNodeClick(data: any) {
  console.log('点击节点:', data);

  // 如果点击的不是监控点位或isUse为0，则不进行操作
  if (data.isPoint !== 1 || !data.channelId || data.isUse === 0) {
    ElMessage.info('请选择可用的监控点位');
    return;
  }

  selectedNode.value = data;
  destroyPlayer();

  // 调用接口获取摄像头详情
  getCameraDetail(data.channelId).then(cameraData => {
    if (cameraData) {
      initPlayer(cameraData);

      // 根据摄像头类型设置云台功能状态
      // cameraType为2表示球机，云台可用
      // cameraType为1表示枪机，云台禁用
      isPtzEnabled.value = data.cameraType === 2;

      // 在点击具体监控点位时初始化云台
      if (!panTilt) {
        // 自定义镜头控制函数，使用ptzRequest
        const customPtzCamera = (cameraParam) => {
          const params = {
            project: "PSDK",
            method: "DMS.Ptz.OperateCamera",
            data: {
              channelId: cameraParam.data.channelId,
              operateType: cameraParam.data.operateType,
              direct: cameraParam.data.direct,
              step: '1',
              command: cameraParam.data.command,
              optModule: "ADMIN_001031",
              extend: ''
            }
          };
          return ptzRequest(`/${MONITOR_URL_PREFIX}/evo-apigw/admin/API/DMS/Ptz/OperateCamera`, params);
        };

        // 自定义方向控制函数，使用ptzRequest
        const customPtzDirection = (directionParam) => {
          const params = {
            project: "PSDK",
            method: "DMS.Ptz.OperateDirect",
            data: {
              channelId: directionParam.data.channelId,
              direct: directionParam.data.direct,
              command: directionParam.data.command,
              stepX: '1',
              stepY: '1',
              optModule: "ADMIN_001031",
              extend: ''
            }
          };
          return ptzRequest(`/${MONITOR_URL_PREFIX}/evo-apigw/admin/API/DMS/Ptz/OperateDirect`, params);
        };

        panTilt = new PanTilt({
          el: 'pan-tilt',
          setPtzDirection: customPtzDirection,
          setPtzCamera: customPtzCamera
        });
      }

      // 设置云台参数
      panTilt.setChannel({
        id: data.channelId,
        capability: data.capability || '',
        cameraType: data.cameraType || ''
      });

      // 如果是枪机类型，禁用云台控制功能
      if (data.cameraType !== 2 && panTilt) {
        panTilt.disable();
      } else if (panTilt) {
        panTilt.enable();
      }
    }
  });
}

onMounted(async () => {
  // 获取点位树数据
  getPointTreeData();
  // 启动时先获取一次token
  try {
    ptzToken.value = await MonitorApi.getToken();
  } catch { }

  // 初始化云台控件，默认处于禁用状态
  setTimeout(() => {
    if (!panTilt) {
      // 自定义镜头控制函数，使用ptzRequest
      const customPtzCamera = (cameraParam) => {
        const params = {
          project: "PSDK",
          method: "DMS.Ptz.OperateCamera",
          data: {
            channelId: cameraParam.data.channelId,
            operateType: cameraParam.data.operateType,
            direct: cameraParam.data.direct,
            step: '1',
            command: cameraParam.data.command,
            optModule: "ADMIN_001031",
            extend: ''
          }
        };
        return ptzRequest(`/${MONITOR_URL_PREFIX}/evo-apigw/admin/API/DMS/Ptz/OperateCamera`, params);
      };

      // 自定义方向控制函数，使用ptzRequest
      const customPtzDirection = (directionParam) => {
        const params = {
          project: "PSDK",
          method: "DMS.Ptz.OperateDirect",
          data: {
            channelId: directionParam.data.channelId,
            direct: directionParam.data.direct,
            command: directionParam.data.command,
            stepX: '1',
            stepY: '1',
            optModule: "ADMIN_001031",
            extend: ''
          }
        };
        return ptzRequest(`/${MONITOR_URL_PREFIX}/evo-apigw/admin/API/DMS/Ptz/OperateDirect`, params);
      };

      panTilt = new PanTilt({
        el: 'pan-tilt',
        setPtzDirection: customPtzDirection,
        setPtzCamera: customPtzCamera
      });

      // 初始状态下禁用云台控制
      panTilt.disable();
    }
  }, 500); // 稍微延迟初始化，确保DOM已渲染
});

onBeforeUnmount(() => {
  // 组件销毁前清理资源
  destroyPlayer();
  // 清理云台实例
  panTilt = null;
});
</script>

<style scoped lang="scss">
.video-container {
  width: 100%;
  height: 100%;
  background-color: #000;
  position: relative;
}

.tree-container {
  overflow-x: auto;
  width: 100%;
}

:deep(.el-tree) {
  min-width: 350px;
  width: max-content;
}

:deep(.el-tree-node__content) {
  height: 32px;
  min-width: 320px;
  display: flex;
  align-items: center;
}

.custom-tree-node {
  display: flex;
  align-items: center;
}

.tree-node-label {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  margin-left: 2px;
}

.disabled-pan-tilt {
  opacity: 0.6;
  pointer-events: none;
}

:deep(.el-tooltip__trigger) {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
