<template>
  <div class="safety-exam-container">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="培训计划" name="trainingPlan">
        <training-plan />
      </el-tab-pane>
      <el-tab-pane label="签到管理" name="attendance">
        <attendance />
      </el-tab-pane>
      <el-tab-pane label="培训资料" name="materials">
        <materials />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import TrainingPlan from './components/TrainingPlan.vue'
import Attendance from './components/Attendance.vue'
import Materials from './components/Materials.vue'

export default {
  name: 'SafetyExam',
  components: {
    TrainingPlan,
    Attendance,
    Materials
  },
  data() {
    return {
      activeTab: 'trainingPlan'
    }
  }
}
</script>

<style lang="scss" scoped>
.safety-exam-container {
  padding: 20px;
  height: 100%;
  
  :deep(.el-tabs) {
    height: 100%;
  }
  
  :deep(.el-tabs__content) {
    padding: 20px;
    height: calc(100% - 55px);
    overflow-y: auto;
  }
}
</style>
