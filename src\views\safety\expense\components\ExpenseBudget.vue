<template>
  <div class="expense-budget">
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">新增预算</el-button>
      <el-select v-model="currentYear" placeholder="选择年份" class="year-select">
        <el-option
          v-for="year in yearOptions"
          :key="year"
          :label="year + '年'"
          :value="year"
        />
      </el-select>
    </div>
    <el-table :data="budgetList" border style="width: 100%">
      <el-table-column prop="categoryName" label="费用类别" width="180" />
      <el-table-column prop="amount" label="预算金额" width="150">
        <template #default="{ row }">
          {{ row.amount.toLocaleString() }} 元
        </template>
      </el-table-column>
      <el-table-column prop="usedAmount" label="已使用金额" width="150">
        <template #default="{ row }">
          {{ row.usedAmount.toLocaleString() }} 元
        </template>
      </el-table-column>
      <el-table-column label="使用进度" width="200">
        <template #default="{ row }">
          <el-progress
            :percentage="(row.usedAmount / row.amount * 100).toFixed(2)"
            :status="getProgressStatus(row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <budget-dialog
      v-model="dialogVisible"
      :type="dialogType"
      :form-data="currentRow"
      @success="handleSuccess"
    />
  </div>
</template>

<script>
import BudgetDialog from '../dialogs/BudgetDialog.vue'

export default {
  name: 'ExpenseBudget',
  components: {
    BudgetDialog
  },
  data() {
    return {
      currentYear: new Date().getFullYear(),
      yearOptions: [],
      budgetList: [
        { id: 1, categoryId: 1, categoryName: '安全设备', amount: 500000, usedAmount: 320000, remark: '包含所有部门安全设备采购及维护费用' },
        { id: 2, categoryId: 2, categoryName: '安全培训', amount: 200000, usedAmount: 185000, remark: '全年度安全培训预算' },
        { id: 3, categoryId: 3, categoryName: '安全检查', amount: 150000, usedAmount: 92000, remark: '季度安全检查及专项检查' },
        { id: 4, categoryId: 4, categoryName: '应急演练', amount: 100000, usedAmount: 45000, remark: '年度消防及应急演练' },
        { id: 5, categoryId: 5, categoryName: '咨询服务', amount: 300000, usedAmount: 300000, remark: '第三方安全评估及咨询服务' }
      ],
      dialogVisible: false,
      dialogType: 'add',
      currentRow: null
    }
  },
  created() {
    this.initYearOptions()
  },
  methods: {
    initYearOptions() {
      const currentYear = new Date().getFullYear()
      this.yearOptions = [
        currentYear - 1,
        currentYear,
        currentYear + 1
      ]
    },
    getProgressStatus(row) {
      const percentage = row.usedAmount / row.amount * 100
      if (percentage >= 100) return 'exception'
      if (percentage >= 80) return 'warning'
      return 'success'
    },
    handleAdd() {
      this.dialogType = 'add'
      this.currentRow = null
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentRow = row
      this.dialogVisible = true
    },
    handleDelete(row) {
      // 实现删除逻辑
    },
    handleSuccess() {
      this.dialogVisible = false
      // 重新加载列表数据
    }
  }
}
</script>

<style lang="scss" scoped>
.expense-budget {
  .operation-bar {
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
  }

  .year-select {
    width: 120px;
  }
}
</style> 