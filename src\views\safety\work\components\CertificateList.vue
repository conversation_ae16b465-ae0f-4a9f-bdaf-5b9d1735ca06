<template>
  <div class="certificate-list">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="证照类型">
          <el-select v-model="searchForm.type" placeholder="请选择证照类型">
            <el-option label="企业资质证书" value="enterprise" />
            <el-option label="特种作业证" value="special" />
            <el-option label="安全生产许可证" value="safety" />
            <el-option label="职业健康证" value="health" />
          </el-select>
        </el-form-item>
        <el-form-item label="证照状态">
          <el-select v-model="searchForm.status" placeholder="请选择证照状态">
            <el-option label="有效" value="valid" />
            <el-option label="即将过期" value="expiring" />
            <el-option label="已过期" value="expired" />
          </el-select>
        </el-form-item>
        <el-form-item label="持证人">
          <el-input v-model="searchForm.holderName" placeholder="请输入持证人姓名" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleAdd">新增证照</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="type" label="证照类型" width="150">
        <template #default="scope">
          <el-tag :type="getTypeTag(scope.row.type)">{{ getTypeText(scope.row.type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="certificateNo" label="证照编号" width="180" />
      <el-table-column prop="holderName" label="持证人" width="120" />
      <el-table-column prop="issueDate" label="发证日期" width="120" />
      <el-table-column prop="expiryDate" label="有效期至" width="120">
        <template #default="scope">
          <el-tag :type="getExpiryTag(scope.row.expiryDate)">
            {{ scope.row.expiryDate }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTag(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="notes" label="备注" min-width="200" show-overflow-tooltip />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="success" link @click="handleRenew(scope.row)">更新</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 证照编辑弹窗 -->
    <certificate-dialog
      v-model="dialogVisible"
      :type="dialogType"
      :form-data="currentForm"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script>
import CertificateDialog from '../dialogs/CertificateDialog.vue'

export default {
  name: 'CertificateList',
  components: {
    CertificateDialog
  },
  data() {
    return {
      searchForm: {
        type: '',
        status: '',
        holderName: ''
      },
      tableData: [
        {
          id: 1,
          type: 'enterprise',
          certificateNo: '*********',
          holderName: '某某科技有限公司',
          issueDate: '2024-01-01',
          expiryDate: '2025-01-01',
          status: 'valid',
          notes: '企业安全生产资质证书'
        },
        {
          id: 2,
          type: 'special',
          certificateNo: '*********',
          holderName: '张三',
          issueDate: '2023-06-01',
          expiryDate: '2024-06-01',
          status: 'expiring',
          notes: '高处作业操作证'
        },
        {
          id: 3,
          type: 'safety',
          certificateNo: 'AQ2024003',
          holderName: '李四',
          issueDate: '2023-01-01',
          expiryDate: '2024-01-01',
          status: 'expired',
          notes: '安全生产许可证'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3,
      dialogVisible: false,
      dialogType: 'add',
      currentForm: null
    }
  },
  methods: {
    getTypeTag(type) {
      const tags = {
        enterprise: 'success',
        special: 'warning',
        safety: 'danger',
        health: 'info'
      }
      return tags[type] || 'info'
    },
    getTypeText(type) {
      const texts = {
        enterprise: '企业资质证书',
        special: '特种作业证',
        safety: '安全生产许可证',
        health: '职业健康证'
      }
      return texts[type] || type
    },
    getStatusTag(status) {
      const tags = {
        valid: 'success',
        expiring: 'warning',
        expired: 'danger'
      }
      return tags[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        valid: '有效',
        expiring: '即将过期',
        expired: '已过期'
      }
      return texts[status] || status
    },
    getExpiryTag(date) {
      const now = new Date()
      const expiryDate = new Date(date)
      const diffDays = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24))
      
      if (diffDays < 0) return 'danger' // 已过期
      if (diffDays <= 30) return 'warning' // 30天内过期
      return 'success' // 正常
    },
    handleSearch() {
      // 实现搜索逻辑
      console.log('搜索条件：', this.searchForm)
      this.fetchData()
    },
    resetSearch() {
      this.searchForm = {
        type: '',
        status: '',
        holderName: ''
      }
    },
    handleAdd() {
      this.dialogType = 'add'
      this.currentForm = null
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentForm = { ...row }
      this.dialogVisible = true
    },
    handleRenew(row) {
      this.dialogType = 'renew'
      this.currentForm = { ...row }
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除该证照记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('删除记录：', row)
        this.$message.success('删除成功')
        this.fetchData()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    handleDialogSuccess() {
      this.dialogVisible = false
      this.fetchData()
      this.$message.success(
        this.dialogType === 'add' 
          ? '新增成功' 
          : this.dialogType === 'renew' 
            ? '更新成功' 
            : '修改成功'
      )
    },
    fetchData() {
      // 实现获取表格数据的逻辑
      console.log('获取数据，页码：', this.currentPage, '每页条数：', this.pageSize)
    }
  }
}
</script>

<style lang="scss" scoped>
.certificate-list {
  .search-area {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 