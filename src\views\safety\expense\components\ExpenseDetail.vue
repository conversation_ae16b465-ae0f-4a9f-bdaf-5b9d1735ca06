<template>
  <div class="expense-detail">
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="费用类别">
          <el-select v-model="searchForm.categoryId" placeholder="请选择" clearable>
            <el-option
              v-for="item in categoryOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">新增费用</el-button>
      <el-button type="success" @click="handleExport">导出明细</el-button>
    </div>

    <el-table :data="detailList" border style="width: 100%">
      <el-table-column prop="date" label="使用日期" width="120" />
      <el-table-column prop="categoryName" label="费用类别" width="150" />
      <el-table-column prop="amount" label="金额" width="120">
        <template #default="{ row }">
          {{ row.amount.toLocaleString() }} 元
        </template>
      </el-table-column>
      <el-table-column prop="purpose" label="用途说明" />
      <el-table-column prop="department" label="使用部门" width="150" />
      <el-table-column prop="applicant" label="申请人" width="120" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button 
            type="primary" 
            link 
            @click="handleDelete(row)"
            v-if="row.status === 'draft'"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <detail-dialog
      v-model="dialogVisible"
      :type="dialogType"
      :form-data="currentRow"
      @success="handleSuccess"
    />
  </div>
</template>

<script>
import DetailDialog from '../dialogs/DetailDialog.vue'
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'

export default {
  name: 'ExpenseDetail',
  components: {
    DetailDialog
  },
  data() {
    return {
      searchForm: {
        categoryId: null,
        dateRange: null
      },
      categoryOptions: [
        { id: 1, name: '安全设备' },
        { id: 2, name: '安全培训' },
        { id: 3, name: '安全检查' },
        { id: 4, name: '应急演练' },
        { id: 5, name: '咨询服务' }
      ],
      detailList: [
        { 
          id: 1, 
          date: '2023-08-15', 
          categoryId: 1, 
          categoryName: '安全设备', 
          amount: 58000, 
          purpose: '购买车间安全防护设备', 
          department: '生产部', 
          applicant: '张三', 
          status: 'approved' 
        },
        { 
          id: 2, 
          date: '2023-09-05', 
          categoryId: 2, 
          categoryName: '安全培训', 
          amount: 35000, 
          purpose: '全员安全意识培训', 
          department: '人力资源部', 
          applicant: '李四', 
          status: 'approved' 
        },
        { 
          id: 3, 
          date: '2023-10-10', 
          categoryId: 3, 
          categoryName: '安全检查', 
          amount: 25000, 
          purpose: '第三季度安全检查', 
          department: '安全部', 
          applicant: '王五', 
          status: 'pending' 
        },
        { 
          id: 4, 
          date: '2023-11-20', 
          categoryId: 4, 
          categoryName: '应急演练', 
          amount: 18000, 
          purpose: '消防应急演练', 
          department: '行政部', 
          applicant: '赵六', 
          status: 'pending' 
        },
        { 
          id: 5, 
          date: '2023-12-01', 
          categoryId: 5, 
          categoryName: '咨询服务', 
          amount: 120000, 
          purpose: '安全管理体系咨询', 
          department: '总经办', 
          applicant: '钱七', 
          status: 'draft' 
        }
      ],
      page: 1,
      pageSize: 20,
      total: 5,
      dialogVisible: false,
      dialogType: 'add',
      currentRow: null
    }
  },
  methods: {
    getStatusType(status) {
      const map = {
        draft: 'info',
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      }
      return map[status] || 'info'
    },
    getStatusText(status) {
      const map = {
        draft: '草稿',
        pending: '待审批',
        approved: '已通过',
        rejected: '已拒绝'
      }
      return map[status] || '未知'
    },
    handleSearch() {
      // 实现搜索逻辑
    },
    resetSearch() {
      this.searchForm = {
        categoryId: null,
        dateRange: null
      }
      this.handleSearch()
    },
    handleAdd() {
      this.dialogType = 'add'
      this.currentRow = null
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentRow = row
      this.dialogVisible = true
    },
    handleView(row) {
      this.dialogType = 'view'
      this.currentRow = row
      this.dialogVisible = true
    },
    handleDelete(row) {
      // 实现删除逻辑
    },
    handleExport() {
      // 准备导出数据
      const exportData = this.detailList.map(item => {
        return {
          '使用日期': item.date,
          '费用类别': item.categoryName,
          '金额(元)': item.amount,
          '用途说明': item.purpose,
          '使用部门': item.department,
          '申请人': item.applicant,
          '状态': this.getStatusText(item.status)
        }
      })

      // 创建工作簿
      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, '费用明细')

      // 调整列宽
      const columnWidths = [
        { wch: 12 }, // 使用日期
        { wch: 12 }, // 费用类别
        { wch: 12 }, // 金额
        { wch: 40 }, // 用途说明
        { wch: 12 }, // 使用部门
        { wch: 10 }, // 申请人
        { wch: 10 }  // 状态
      ]
      worksheet['!cols'] = columnWidths

      // 导出文件
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
      const date = new Date()
      const fileName = `安全费用明细_${date.getFullYear()}${(date.getMonth()+1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}.xlsx`
      
      try {
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        saveAs(blob, fileName)
        
        this.$message({
          message: '费用明细导出成功',
          type: 'success'
        })
      } catch (error) {
        console.error('导出Excel失败:', error)
        this.$message({
          message: '导出失败，请重试',
          type: 'error'
        })
      }
    },
    handleSuccess() {
      this.dialogVisible = false
      this.handleSearch()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      this.page = val
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.expense-detail {
  .search-bar {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .operation-bar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 