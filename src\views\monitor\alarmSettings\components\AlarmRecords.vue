<template>
  <div class="w-full h-[calc(100vh-210px)]">
    <div class="w-full h-full flex flex-col">
      <div class="w-full h-[50px] flex p-4">
        <div class="h-full w-4/5 flex items-center gap-2">
          <el-form :inline="true" :model="formInline" class="flex items-center gap-2">
            <el-form-item label="规则名称" class="w-[300px]">
              <el-input v-model="formInline.ruleName" placeholder="请输入规则名称" clearable />
            </el-form-item>
            <el-form-item label="告警级别" class="w-[200px]">
              <el-select v-model="formInline.level" placeholder="请选择告警级别" clearable>
                <el-option label="I级" value="I" />
                <el-option label="II级" value="II" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围" class="w-[450px]">
              <el-date-picker v-model="formInline.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
              <el-button type="info" :icon="RefreshRight" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="w-full flex-1 flex flex-col p-4">
        <div class="relative w-full h-full">
          <div class="absolute w-full h-full">
            <el-table v-loading="loading" :data="tableData" border height="100%">
              <el-table-column prop="alarmTime" label="告警时间" align="center" width="180" />
              <el-table-column prop="ruleName" label="规则名称" align="center" />
              <el-table-column prop="level" label="告警级别" align="center" width="100">
                <template #default="scope">
                  <el-tag :type="getAlarmLevelType(scope.row.level)">{{ scope.row.level }}级</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="触发值" align="center">
                <template #default="scope">
                  <div v-if="scope.row.ruleType === 'single'">
                    {{ scope.row.factor }}: {{ scope.row.value }}{{ scope.row.unit ? ' ' + scope.row.unit : ' /' }}
                    <el-tag size="small" effect="plain" class="ml-2">
                      {{ getOperatorSymbol(scope.row.operator) }} {{ scope.row.threshold }}{{ scope.row.unit ? ' ' +
                      scope.row.unit : ' /' }}
                    </el-tag>
                  </div>
                  <div v-else>
                    <el-tooltip :content="scope.row.expression" placement="top" effect="light">
                      <span class="cursor-pointer text-blue-500">{{ truncateText(scope.row.expression, 30) }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="notifyStatus" label="通知状态" align="center" width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.notifyStatus === 'success' ? 'success' : 'danger'">
                    {{ scope.row.notifyStatus === 'success' ? '已通知' : '通知失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template #default="scope">
                  <el-button size="small" type="primary" link @click="handleViewDetail(scope.row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="flex-1 w-full flex items-center justify-end mt-2">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>

    <!-- Alarm Detail Dialog -->
    <el-dialog v-model="detailDialogVisible" title="告警详情" width="50%">
      <div v-if="currentAlarm" class="p-4">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="告警时间">{{ currentAlarm.alarmTime }}</el-descriptions-item>
          <el-descriptions-item label="告警级别">
            <el-tag :type="getAlarmLevelType(currentAlarm.level)">{{ currentAlarm.level }}级</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="规则名称">{{ currentAlarm.ruleName }}</el-descriptions-item>
          <el-descriptions-item label="规则类型">
            {{ currentAlarm.ruleType === 'single' ? '单因子' : '组合因子' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="触发条件">
            <div v-if="currentAlarm.ruleType === 'single'">
              {{ currentAlarm.factor }} {{ getOperatorSymbol(currentAlarm.operator) }} {{ currentAlarm.threshold }}{{
                currentAlarm.unit ? ' ' + currentAlarm.unit : ' /' }}
            </div>
            <div v-else>{{ currentAlarm.expression }}</div>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="触发值">
            <div v-if="currentAlarm.ruleType === 'single'">
              {{ currentAlarm.factor }}: {{ currentAlarm.value }}{{ currentAlarm.unit ? ' ' + currentAlarm.unit : ' /' }}
            </div>
            <div v-else>条件表达式已满足</div>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="通知记录">
            <el-table :data="currentAlarm.notifyRecords" border size="small">
              <el-table-column prop="method" label="通知方式" />
              <el-table-column prop="time" label="通知时间" />
              <el-table-column prop="receiver" label="接收人" />
              <el-table-column prop="status" label="状态">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="small">
                    {{ scope.row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { RefreshRight, Search } from '@element-plus/icons-vue'
import { onMounted, reactive, ref } from 'vue'
import { getAlarmRecords, getAlarmRecordDetail } from '@/api/alarm'
import { ElMessage } from 'element-plus'

const formInline = reactive({
  ruleName: '',
  level: '',
  dateRange: []
})

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

const tableData = ref([])
const detailDialogVisible = ref(false)
const currentAlarm = ref(null)

const getAlarmLevelType = (level) => {
  const types = {
    'I': 'danger',
    'II': 'warning'
  }
  return types[level] || 'info'
}

const getOperatorSymbol = (operator) => {
  const symbols = {
    '>': '>',
    '<': '<',
    '=': '=',
    '>=': '≥',
    '<=': '≤'
  }
  return symbols[operator] || operator
}

const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const fetchAlarmRecords = async () => {
  loading.value = true
  try {
    let startDate, endDate
    if (formInline.dateRange && formInline.dateRange.length === 2) {
      startDate = formInline.dateRange[0]
      endDate = formInline.dateRange[1]
    }

    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ruleName: formInline.ruleName || undefined,
      level: formInline.level || undefined,
      startDate,
      endDate
    }
    const response = await getAlarmRecords(params)
    tableData.value = response.data
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取告警记录失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const fetchAlarmDetail = async (id) => {
  try {
    const response = await getAlarmRecordDetail(id)
    currentAlarm.value = response
    detailDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取告警详情失败')
    console.error(error)
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchAlarmRecords()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchAlarmRecords()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchAlarmRecords()
}

const resetForm = () => {
  formInline.ruleName = ''
  formInline.level = ''
  formInline.dateRange = []
  handleSearch()
}

const handleViewDetail = (row) => {
  fetchAlarmDetail(row.id)
}

onMounted(() => {
  fetchAlarmRecords()
})
</script>
<style scoped lang="scss"></style> 