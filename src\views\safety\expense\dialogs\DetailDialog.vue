<template>
  <el-dialog
    :title="dialogTitle"
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    width="600px"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="费用类别" prop="categoryId">
        <el-select
          v-model="form.categoryId"
          placeholder="请选择费用类别"
          :disabled="type === 'view'"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="使用日期" prop="date">
        <el-date-picker
          v-model="form.date"
          type="date"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          :disabled="type === 'view'"
        />
      </el-form-item>
      <el-form-item label="金额" prop="amount">
        <el-input-number
          v-model="form.amount"
          :min="0"
          :precision="2"
          :step="100"
          style="width: 200px"
          :disabled="type === 'view'"
        />
      </el-form-item>
      <el-form-item label="使用部门" prop="department">
        <el-select
          v-model="form.department"
          placeholder="请选择使用部门"
          :disabled="type === 'view'"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="form.applicant"
          placeholder="请输入申请人"
          :disabled="type === 'view'"
        />
      </el-form-item>
      <el-form-item label="用途说明" prop="purpose">
        <el-input
          v-model="form.purpose"
          type="textarea"
          :rows="3"
          placeholder="请输入用途说明"
          :disabled="type === 'view'"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">
          {{ type === 'view' ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="type !== 'view'"
          type="primary"
          @click="handleSubmit"
        >确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'DetailDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    type: {
      type: String,
      required: true,
      validator: (value) => ['add', 'edit', 'view'].includes(value)
    },
    formData: {
      type: Object,
      default: () => null
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      form: {
        categoryId: null,
        date: '',
        amount: 0,
        department: null,
        applicant: '',
        purpose: ''
      },
      categoryOptions: [],
      departmentOptions: [],
      rules: {
        categoryId: [
          { required: true, message: '请选择费用类别', trigger: 'change' }
        ],
        date: [
          { required: true, message: '请选择使用日期', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '金额必须大于0', trigger: 'blur' }
        ],
        department: [
          { required: true, message: '请选择使用部门', trigger: 'change' }
        ],
        applicant: [
          { required: true, message: '请输入申请人', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        purpose: [
          { required: true, message: '请输入用途说明', trigger: 'blur' },
          { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      const titles = {
        add: '新增费用',
        edit: '编辑费用',
        view: '查看费用'
      }
      return titles[this.type]
    }
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.loadOptions()
        if (this.formData) {
          this.form = { ...this.formData }
        }
      }
    }
  },
  methods: {
    loadOptions() {
      // 加载费用类别和部门选项
    },
    handleCancel() {
      this.$emit('update:modelValue', false)
    },
    async handleSubmit() {
      if (!this.$refs.formRef) return
      
      try {
        await this.$refs.formRef.validate()
        // 这里实现提交逻辑
        this.$emit('success')
      } catch (error) {
        // 表单验证失败
        return false
      }
    },
    resetForm() {
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
      this.form = {
        categoryId: null,
        date: '',
        amount: 0,
        department: null,
        applicant: '',
        purpose: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.file-item {
  margin-bottom: 8px;
}

.el-upload__tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}
</style> 