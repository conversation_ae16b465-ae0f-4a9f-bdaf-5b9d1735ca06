<template>
  <div class="assess-result-container">
    <!-- 视图切换 -->
    <el-card class="mb-4">
      <el-tabs v-model="activeView" @tab-change="handleViewChange">
        <el-tab-pane label="集团视角" name="group">
          <GroupView />
        </el-tab-pane>

        <el-tab-pane label="厂站视角" name="station">
          <StationView />
        </el-tab-pane>

        <el-tab-pane label="评估知识库" name="knowledge">
          <KnowledgeView />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 评估报告弹窗 -->
    <ReportDialog v-model="reportDialogVisible" :report-data="reportData" @print-report="handlePrintReport" />


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import GroupView from './components/GroupView.vue'
import StationView from './components/StationView.vue'
import KnowledgeView from './components/KnowledgeView.vue'
import ReportDialog from './components/ReportDialog.vue'
import type {
  Station,
  Indicator,
  PeriodComparison,
  ReportData
} from './types'

// 响应式数据
const activeView = ref('group')
const reportDialogVisible = ref(false)



const periodComparisonData = ref<PeriodComparison[]>([
  {
    period: '2025-01',
    value: 0.85,
    score: 89.2,
    trend: 'up',
    status: '正常',
    remark: '运行状态良好'
  },
  {
    period: '2024-12',
    value: 0.88,
    score: 87.9,
    trend: 'down',
    status: '正常',
    remark: '能耗略有上升'
  },
  {
    period: '2024-11',
    value: 0.82,
    score: 90.4,
    trend: 'up',
    status: '优秀',
    remark: '表现优异'
  },
  {
    period: '2024-10',
    value: 0.91,
    score: 85.1,
    trend: 'down',
    status: '偏高',
    remark: '需要关注能耗'
  }
])

// 报告数据
const reportData = ref<ReportData>({
  period: '2025年1月',
  schemeName: '泵站月度评估方案',
  generateTime: '2025-01-21 14:30:00',
  objectName: '城东泵站',
  totalScore: 89.2,
  level: '良好',
  projectAnalysis: [
    {
      projectName: '能耗管理',
      weight: 30,
      score: 88.5,
      status: '正常',
      remark: '能耗控制在合理范围内'
    },
    {
      projectName: '运行稳定性',
      weight: 25,
      score: 92.1,
      status: '优秀',
      remark: '设备运行稳定，故障率低'
    },
    {
      projectName: '维护管理',
      weight: 20,
      score: 86.8,
      status: '正常',
      remark: '维护计划执行良好'
    },
    {
      projectName: '安全管理',
      weight: 25,
      score: 90.3,
      status: '优秀',
      remark: '安全措施完善'
    }
  ],
  conclusion: {
    overall: '城东泵站在2025年1月的运行表现良好，综合得分89.2分，达到良好等级。各项指标基本达标，运行稳定性表现突出。',
    advantages: [
      '设备运行稳定，故障率控制在较低水平',
      '安全管理措施完善，无安全事故发生',
      '维护计划执行到位，设备完好率高'
    ],
    suggestions: [
      '继续优化能耗管理，探索节能降耗新技术',
      '加强预防性维护，延长设备使用寿命',
      '建立更完善的数据监控体系'
    ]
  }
})

// 事件处理方法
const handleViewChange = (viewName: string) => {
  console.log('切换视图:', viewName)
}

const handleGenerateReport = (stationId: string) => {
  const station = stationList.value.find(s => s.id === stationId)
  if (station) {
    reportData.value.objectName = station.name
    reportData.value.generateTime = new Date().toLocaleString()
    reportDialogVisible.value = true
  }
}

const handleExportPDF = () => {
  ElMessage.info('导出PDF功能开发中...')
}

const handleExportExcel = () => {
  ElMessage.info('导出Excel功能开发中...')
}

const handlePrintReport = () => {
  ElMessage.info('打印报告功能开发中...')
}

// 生命周期
onMounted(() => {
  console.log('评估结果展示分析页面已加载')
})
</script>

<style scoped lang="scss">
.assess-result-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    text-align: center;
    margin-bottom: 24px;

    .page-title {
      font-size: 28px;
      font-weight: bold;
      color: #2c3e50;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      font-size: 14px;
      color: #7f8c8d;
      margin: 0;
    }
  }

  .mb-4 {
    margin-bottom: 16px;
  }
}
</style>