<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="资产类型名称(如生产类/非生产类)" prop="typeName">
        <el-input v-model="formData.typeName" placeholder="请输入资产类型名称(如生产类/非生产类)" />
      </el-form-item>
      <el-form-item label="资产类型编码" prop="typeCode">
        <el-input v-model="formData.typeCode" placeholder="请输入资产类型编码" />
      </el-form-item>
      <el-form-item label="资产类型描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
      <el-form-item label="生产类型：1 非生产类型：2" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="污水处理厂ID" prop="sewagePlantId">
        <el-input v-model="formData.sewagePlantId" placeholder="请输入污水处理厂ID" />
      </el-form-item>
      <el-form-item label="逻辑删除标记(0=正常,1=删除)" prop="deleteMark">
        <el-radio-group v-model="formData.deleteMark">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="创建人ID" prop="createById">
        <el-input v-model="formData.createById" placeholder="请输入创建人ID" />
      </el-form-item>
      <el-form-item label="修改人ID" prop="updateById">
        <el-input v-model="formData.updateById" placeholder="请输入修改人ID" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">

/** 资产类型 表单 */
defineOptions({ name: 'AssetTypeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  typeName: undefined,
  typeCode: undefined,
  description: undefined,
  type: undefined,
  sewagePlantId: undefined,
  deleteMark: undefined,
  createById: undefined,
  updateById: undefined,
})
const formRules = reactive({
  typeName: [{ required: true, message: '资产类型名称(如生产类/非生产类)不能为空', trigger: 'blur' }],
  typeCode: [{ required: true, message: '资产类型编码不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '生产类型：1 非生产类型：2不能为空', trigger: 'blur' }],
  sewagePlantId: [{ required: true, message: '污水处理厂ID不能为空', trigger: 'blur' }],
  deleteMark: [{ required: true, message: '逻辑删除标记(0=正常,1=删除)不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      // formData.value = await AssetTypeApi.getAssetType(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    // const data = formData.value as unknown as AssetTypeVO
    // if (formType.value === 'create') {
    //   // await AssetTypeApi.createAssetType(data)
    //   message.success(t('common.createSuccess'))
    // } else {
    //   // await AssetTypeApi.updateAssetType(data)
    //   message.success(t('common.updateSuccess'))
    // }
    // dialogVisible.value = false
    // // 发送操作成功的事件
    // emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    typeName: undefined,
    typeCode: undefined,
    description: undefined,
    type: undefined,
    sewagePlantId: undefined,
    deleteMark: undefined,
    createById: undefined,
    updateById: undefined,
  }
  formRef.value?.resetFields()
}
</script>
