/**
 * 补录配置请求参数
 */
export interface SupplementConfigReq {
  id?: number;
  name: string;
  reportType: string;  // 报表类型
  operationStartTime: number | undefined;  // 允许补录的开始时间 (时间戳)
  operationEndTime: number | undefined;    // 允许补录的截止时间 (时间戳)
  dataStartDate: string;       // 补录数据的开始日期 (YYYY-MM-DD)
  dataEndDate: string;         // 补录数据的结束日期 (YYYY-MM-DD)
  period: string;
  enabled: boolean;
  remark?: string;
}

/**
 * 补录配置响应对象
 */
export interface DataSupplementConfigRespVO {
  id: number;
  name: string;
  reportType: string;  // 报表类型
  operationStartTime: number;  // 允许补录的开始时间 (时间戳)
  operationEndTime: number;    // 允许补录的截止时间 (时间戳)
  dataStartDate: string;       // 补录数据的开始日期 (YYYY-MM-DD)
  dataEndDate: string;         // 补录数据的结束日期 (YYYY-MM-DD)
  period: string;
  enabled: boolean;
  creator: string;            // 创建人
  createTime: string;          // 创建时间
  updater: string;            // 更新人
  updateTime?: string;         // 更新时间
  remark?: string;
}

/**
 * 补录配置分页查询参数
 */
export interface DataSupplementConfigPageReqVO {
  pageNo: number;
  pageSize: number;
  name?: string;
  reportType?: string;  // 报表类型
  enabled?: boolean;
}

/**
 * 分页结果类型
 */
export interface PageResult<T> {
  list: T[];
  total: number;
}

/**
 * 通用结果类型
 */
export interface CommonResult<T> {
  code: number;
  data: T;
  msg: string;
} 