<template>
    <div>
        <el-dialog v-model="dialogVisible" title="告警通知设置" width="50%" :before-close="handleClose">
            <div class="w-full p-4">
                <el-form ref="formRef" :model="form" label-width="120px" label-position="right">
                    <el-form-item label="告警级别" prop="level"
                        :rules="[{ required: true, message: '请选择告警级别', trigger: 'change' }]">
                        <el-select v-model="form.level" placeholder="请选择告警级别">
                            <el-option label="I级" value="I" />
                            <el-option label="II级" value="II" />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="通知方式">
                        <el-checkbox-group v-model="selectedMethods">
                            <el-checkbox v-for="method in notifyMethods" :key="method.type" :label="method.type">
                                {{ method.name }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item label="接收角色">
                        <el-select v-model="form.receiverRoles" multiple collapse-tags collapse-tags-tooltip
                            placeholder="请选择接收角色">
                            <el-option v-for="role in roleOptions" :key="role.value" :label="role.label"
                                :value="role.value" />
                        </el-select>
                        <div v-if="form.receiverRoles.length > 0" class="mt-2 text-xs text-gray-500">
                            当前选择: {{form.receiverRoles.map(roleId => getRoleName(roleId)).join(', ')}}
                        </div>
                    </el-form-item>

                    <el-divider content-position="left" v-if="false">通知方式详细设置</el-divider>

                    <!-- SMS Settings -->
                    <div v-if="false" class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h3 class="font-bold mb-3">短信通知设置</h3>
                        <el-form-item label="发送时间段">
                            <div class="flex items-center gap-2">
                                <el-time-picker v-model="form.smsSettings.startTime" format="HH:mm"
                                    placeholder="开始时间" />
                                <span>至</span>
                                <el-time-picker v-model="form.smsSettings.endTime" format="HH:mm" placeholder="结束时间" />
                            </div>
                        </el-form-item>
                        <el-form-item label="发送频率">
                            <el-input-number v-model="form.smsSettings.frequency" :min="1" :max="24" />
                            <span class="ml-2">小时/次</span>
                        </el-form-item>
                    </div>

                    <!-- APP Push Settings -->
                    <div v-if="isAppEnabled" class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h3 class="font-bold mb-3">APP推送设置</h3>
                        <el-form-item label="推送方式">
                            <el-radio-group v-model="form.appSettings.pushType">
                                <el-radio label="immediate">即时推送</el-radio>
                                <el-radio label="batch">批量推送</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </div>

                    <!-- Internal Message Settings -->
                    <div v-if="false" class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h3 class="font-bold mb-3">内部消息设置</h3>
                        <el-form-item label="消息级别">
                            <el-select v-model="form.internalSettings.priority">
                                <el-option label="高" value="high" />
                                <el-option label="中" value="medium" />
                                <el-option label="低" value="low" />
                            </el-select>
                        </el-form-item>
                    </div>

                    <!-- Video Linkage Settings -->
                    <div v-if="isVideoEnabled" class="bg-gray-50 p-4 rounded-lg mb-4">
                        <h3 class="font-bold mb-3">视频联动设置</h3>
                        <el-form-item label="联动摄像头">
                            <el-select v-model="form.videoSettings.cameras" multiple collapse-tags
                                placeholder="请选择联动摄像头">
                                <el-option label="前门摄像头" value="camera1" />
                                <el-option label="水处理区域1" value="camera2" />
                                <el-option label="水处理区域2" value="camera3" />
                                <el-option label="控制室" value="camera4" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="录像时长">
                            <el-input-number v-model="form.videoSettings.recordDuration" :min="1" :max="30" />
                            <span class="ml-2">分钟</span>
                        </el-form-item>
                    </div>
                </el-form>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { computed, reactive, ref, watch, onMounted } from 'vue'
import { createNotifyConfig, updateNotifyConfig, getNotifyRoles } from '@/api/alarm'

const emit = defineEmits(['save-notify'])

const dialogVisible = ref(false)
const formRef = ref()

interface NotifyMethod {
    type: string;
    name: string;
    enabled?: boolean;
}

interface NotifyConfig {
    id: string;
    level: string;
    notifyMethods: NotifyMethod[];
    receiverRoles: (string | number)[];
    smsSettings: {
        startTime: Date;
        endTime: Date;
        frequency: number;
    };
    appSettings: {
        pushType: string;
    };
    internalSettings: {
        priority: string;
    };
    videoSettings: {
        cameras: string[];
        recordDuration: number;
    };
}

const notifyMethods: NotifyMethod[] = [
    { type: 'sms', name: '短信', enabled: false },
    { type: 'internal', name: '站内信', enabled: false }
]

// 角色选项列表，通过接口获取
const roleOptions = ref<{ value: string | number, label: string }[]>([])

// 加载角色列表数据
const loading = ref(false)
// 角色ID到名称的映射，用于数字ID显示名称
const roleMap = ref<Record<string | number, string>>({})

// 将角色ID转换为名称的内部工具函数
const getStringIdFromValue = (value: any): string => {
    if (value === null || value === undefined) return '';
    return String(value);
}

const fetchRoleOptions = async () => {
    try {
        loading.value = true
        const response = await getNotifyRoles()
        if (response && response.data) {
            // 根据接口返回格式进行处理
            const roles = response.data || []
            if (Array.isArray(roles)) {
                roleOptions.value = roles.map(role => {
                    // 如果是对象格式，提取value和label
                    if (typeof role === 'object' && role !== null) {
                        // 这里要取出ID和名称，ID用作选择的值，名称用于显示
                        // 优先使用id字段作为值
                        const id = role.id !== undefined ? role.id : (role.value || role.name || role.code);
                        const name = role.label || role.name || (typeof role.value === 'string' ? role.value : '') || (typeof role.code === 'string' ? role.code : '') || String(id || '');

                        // 如果id是数字字符串，转成数字类型
                        let finalId = id;
                        if (typeof id === 'string' && !isNaN(Number(id)) && id.trim() !== '') {
                            finalId = Number(id);
                        }

                        // 保存ID到名称的映射
                        if (finalId !== undefined) {
                            roleMap.value[finalId] = name;
                            console.log(`添加角色映射: [${finalId}] -> "${name}"`);
                        }

                        return {
                            value: finalId,
                            label: name
                        }
                    }

                    // 如果是简单类型（数字或字符串），使用适当的值作为value和label
                    let value = role;
                    // 如果是数字字符串，转成数字类型
                    if (typeof role === 'string' && !isNaN(Number(role)) && role.trim() !== '') {
                        value = Number(role);
                    }

                    const label = typeof value === 'number' ? String(value) : value;
                    // 保存到映射
                    roleMap.value[value] = label;
                    console.log(`添加简单角色: [${value}] -> "${label}"`);
                    return { value, label };
                })
            }
            console.log('角色选项:', roleOptions.value);
            console.log('角色ID到名称映射:', roleMap.value);
        }
    } catch (error) {
        console.error('获取角色列表失败:', error)
        ElMessage.error('获取角色列表失败')
    } finally {
        loading.value = false
    }
}

const form = reactive<NotifyConfig>({
    id: '',
    level: '',
    notifyMethods: [],
    receiverRoles: [],
    smsSettings: {
        startTime: new Date(2000, 0, 1, 8, 0),
        endTime: new Date(2000, 0, 1, 20, 0),
        frequency: 1
    },
    appSettings: {
        pushType: 'immediate'
    },
    internalSettings: {
        priority: 'medium'
    },
    videoSettings: {
        cameras: [],
        recordDuration: 5
    }
})

const selectedMethods = ref<string[]>([])

// 计算属性，用于判断各通知方式是否启用
const isSmsEnabled = computed(() => selectedMethods.value.includes('sms'))
const isAppEnabled = computed(() => selectedMethods.value.includes('app'))
const isInternalEnabled = computed(() => selectedMethods.value.includes('internal'))
const isVideoEnabled = computed(() => selectedMethods.value.includes('video'))

// 监听selectedMethods变化，更新form.notifyMethods
watch(selectedMethods, (newVal: string[]) => {
    form.notifyMethods = notifyMethods.map(method => ({
        ...method,
        enabled: newVal.includes(method.type)
    }))
}, { deep: true })

const getAlarmLevelType = (level) => {
    const types = {
        'I': 'danger',
        'II': 'warning',
        'III': 'info'
    }
    return types[level] || 'info'
}

// 获取角色名称的方法
const getRoleName = (roleId: string | number): string => {
    return roleMap.value[roleId] || String(roleId)
}

const resetForm = () => {
    form.id = ''
    form.level = ''
    form.notifyMethods = []
    form.receiverRoles = []
    form.smsSettings = {
        startTime: new Date(2000, 0, 1, 8, 0),
        endTime: new Date(2000, 0, 1, 20, 0),
        frequency: 1
    }
    form.appSettings = {
        pushType: 'immediate'
    }
    form.internalSettings = {
        priority: 'medium'
    }
    form.videoSettings = {
        cameras: [],
        recordDuration: 5
    }

    selectedMethods.value = []
}

const openDialog = async (row?: Partial<NotifyConfig>) => {
    resetForm()

    // 每次打开对话框时获取最新的角色列表
    await fetchRoleOptions()

    if (row) {
        form.id = row.id || ''
        form.level = row.level || ''

        // 处理接收角色 - 可能是字符串或数组
        if (row.receiverRoles) {
            if (typeof row.receiverRoles === 'string') {
                // 如果是字符串，按逗号分隔并尝试转换为数字（如果可能）
                form.receiverRoles = (row.receiverRoles as string).split(',')
                    .map(r => {
                        const trimmed = r.trim();
                        // 尝试将字符串转换为数字（如果它是一个有效的数字）
                        const num = Number(trimmed);
                        return !isNaN(num) && trimmed !== '' ? num : trimmed;
                    });
            } else if (Array.isArray(row.receiverRoles)) {
                // 保持原始类型（数字或字符串）
                form.receiverRoles = [...row.receiverRoles]
            }
        }

        console.log('编辑对话框接收到的角色:', form.receiverRoles);

        // 处理通知方式 - 可能是字符串JSON或对象数组
        if (row.notifyMethods) {
            if (typeof row.notifyMethods === 'string') {
                try {
                    // 尝试解析JSON字符串
                    const methodsObj = JSON.parse(row.notifyMethods);
                    form.notifyMethods = notifyMethods.map(method => ({
                        ...method,
                        enabled: !!methodsObj[method.type]
                    }));

                    // 设置selectedMethods
                    selectedMethods.value = Object.keys(methodsObj).filter(key => methodsObj[key]);
                } catch (e) {
                    console.error('解析notifyMethods失败:', e);
                    form.notifyMethods = [...notifyMethods]; // 使用默认值
                    selectedMethods.value = [];
                }
            } else if (Array.isArray(row.notifyMethods)) {
                form.notifyMethods = [...row.notifyMethods];

                // 设置selectedMethods
                selectedMethods.value = row.notifyMethods
                    .filter(method => method.enabled)
                    .map(method => method.type);
            }
        }
    }

    dialogVisible.value = true
}

const handleClose = () => {
    dialogVisible.value = false
}

const handleSubmit = async () => {
    if (form.receiverRoles.length === 0) {
        return ElMessage.error('请至少选择一个接收角色')
    }

    try {
        // 准备请求数据
        const requestData = {
            id: form.id || undefined, // 主键ID，可选
            level: form.level, // 告警等级，必需
            notifyMethods: JSON.stringify(form.notifyMethods.reduce((acc, method) => {
                if (method.enabled) {
                    acc[method.type] = true;
                }
                return acc;
            }, {})), // 通知方式JSON配置
            receiverRoles: form.receiverRoles.map(r => String(r)).join(',') // 接收角色，先转字符串再用逗号分隔
        };

        let result;

        // 根据是否有ID判断是新增还是更新
        if (form.id) {
            // 编辑模式 - 调用更新接口
            result = await updateNotifyConfig(requestData);
            if (result) {
                ElMessage.success('告警通知配置更新成功');
            }
        } else {
            // 新增模式 - 调用创建接口
            result = await createNotifyConfig(requestData);
            if (result) {
                ElMessage.success('告警通知配置创建成功');
            }
        }

        if (result) {
            // 提交表单
            emit('save-notify', { ...form });
            dialogVisible.value = false;
        } else {
            ElMessage.error('保存失败');
        }
    } catch (error) {
        console.error('保存告警通知配置失败:', error);
        ElMessage.error('保存告警通知配置失败');
    }
}

// 组件挂载时获取角色列表
onMounted(() => {
    fetchRoleOptions()
})

defineExpose({
    openDialog
})
</script>

<style scoped>
.el-form-item {
    margin-bottom: 20px;
}
</style>