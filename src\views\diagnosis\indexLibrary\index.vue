<template>
  <div class="index-library-container">
    <el-card class="index-library-card">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold text-lg">评估指标库管理</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleAdd">
              <el-icon>
                <Plus />
              </el-icon>
              新建指标
            </el-button>

            <el-button @click="handleCategoryManage">
              <el-icon>
                <FolderOpened />
              </el-icon>
              分类管理
            </el-button>
          </div>
        </div>
      </template>

      <div class="table-container">
        <!-- 搜索表单 -->
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="关键词">
              <el-input v-model="searchForm.keyword" placeholder="请输入指标名称或定义关键词" clearable style="width: 15rem"
                @keyup.enter="handleSearch" />
            </el-form-item>
            <el-form-item label="指标分类">
              <el-tree-select v-model="searchForm.category" :data="categoryTreeData" placeholder="请选择指标分类" clearable
                style="width: 10rem" node-key="id" :props="{ label: 'label', children: 'children' }" check-strictly
                :render-after-expand="false" />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 7.5rem">
                <el-option label="启用" :value="1" />
                <el-option label="停用" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon>
                  <Search />
                </el-icon>
                查询
              </el-button>
              <el-button @click="resetSearch">
                <el-icon>
                  <Refresh />
                </el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>



        <div class="table-wrapper">
          <el-table ref="tableRef" v-loading="loading" :data="paginatedTableData" border class="index-table"
            height="100%">
            <el-table-column align="center" prop="name" label="指标名称" show-overflow-tooltip />
            <el-table-column prop="category" label="分类" align="center">
              <template #default="{ row }">
                <el-tag :type="getCategoryType(row.category)" size="small">{{ getCategoryText(row.category) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" align="center" />
            <el-table-column prop="evaluationPeriod" label="评估周期" align="center">
              <template #default="{ row }">
                <el-tag type="primary" size="small">{{ getEvaluationPeriodText(row.evaluationPeriod) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="threshold" label="阈值" align="center" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" align="center">
              <template #default="{ row }">
                <el-switch v-model="row.status" :active-value="1" :inactive-value="0"
                  @change="handleStatusChange(row)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" fixed="right" width="150">
              <template #default="{ row }">
                <el-button type="primary" link size="small" @click="handleEdit(row)">
                  编辑
                </el-button>
                <el-button type="info" link size="small" @click="handleDetail(row)">
                  详情
                </el-button>
                <el-button type="danger" link size="small" @click="handleDelete(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-wrapper">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>
  </div>

  <!-- 指标表单对话框 -->
  <IndexForm v-model="dialogVisible" :dialog-type="dialogType" :edit-data="selectedItem"
    :category-tree-data="categoryTreeData" @submit="handleFormSubmit" />


  <!-- 指标详情对话框 -->
  <IndexDetail v-model="detailDialogVisible" :selected-item="selectedItem" @edit="handleEdit" />



  <!-- 分类管理对话框 -->
  <CategoryManagement v-model="categoryDialogVisible" @save="handleCategorySave" />
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  FolderOpened
} from '@element-plus/icons-vue'
import CategoryManagement from './components/CategoryManagement.vue'
import IndexForm from './components/IndexForm.vue'
import IndexDetail from './components/IndexDetail.vue'


// 定义接口类型
interface IndexItem {
  id: string
  name: string
  definition: string
  unit: string
  category: string
  threshold: string
  status: number
  evaluationPeriod: string
  baseData: string[]
  calculation: string
  remark: string
  createTime: string
  creator: string
}

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  status: undefined as number | undefined
})

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<IndexItem[]>([
  {
    id: '1',
    name: '功率因数',
    definition: '有功功率与视在功率的比值，反映电力系统的效率',
    unit: '无',
    category: '1-1',
    threshold: '≥0.9',
    status: 1,
    evaluationPeriod: 'hour',
    baseData: ['有功功率', '视在功率'],
    calculation: '有功功率 / 视在功率',
    remark: '用于评估电力系统的效率，是重要的电能质量指标',
    createTime: '2024-04-21 10:00:00',
    creator: '张三'
  },
  {
    id: '2',
    name: '水质达标率',
    definition: '水质指标达标次数与总检测次数的比值',
    unit: '%',
    category: '2-1',
    threshold: '≥95%',
    status: 1,
    evaluationPeriod: 'day',
    baseData: ['达标次数', '总检测次数'],
    calculation: '达标次数 / 总检测次数 × 100',
    remark: '用于评估水处理系统的处理效果和环保达标情况',
    createTime: '2024-04-21 11:30:00',
    creator: '李四'
  },
  {
    id: '3',
    name: '设备运行率',
    definition: '设备正常运行时间与总时间的比值',
    unit: '%',
    category: '4',

    threshold: '≥98%',
    status: 0,
    evaluationPeriod: 'month',
    baseData: ['运行时间', '总时间'],
    calculation: '运行时间 / 总时间 × 100',
    remark: '反映设备可靠性和维护水平的重要指标',
    createTime: '2024-04-21 14:15:00',
    creator: '王五'
  },
  {
    id: '4',
    name: '环境噪声达标率',
    definition: '环境噪声监测达标次数与总监测次数的比值',
    unit: '%',
    category: '3-1',
    threshold: '≥90%',
    status: 1,
    evaluationPeriod: 'week',
    baseData: ['达标次数', '总检测次数'],
    calculation: '达标次数 / 总检测次数 × 100',
    remark: '用于评估环境噪声控制效果',
    createTime: '2024-04-21 16:45:00',
    creator: '赵六'
  },
  {
    id: '5',
    name: '电压合格率',
    definition: '电压在合格范围内的时间占总监测时间的比例',
    unit: '%',
    category: '1-1',

    threshold: '≥95%',
    status: 1,
    evaluationPeriod: 'hour',
    baseData: ['电压', '合格上限', '合格下限'],
    calculation: '(电压 >= 合格下限 && 电压 <= 合格上限) ? 1 : 0',
    remark: '监测电压质量，确保用电设备正常运行',
    createTime: '2024-04-22 09:15:00',
    creator: '陈七'
  },
  {
    id: '6',
    name: 'COD去除率',
    definition: '化学需氧量的去除效率，反映污水处理效果',
    unit: '%',
    category: '2-2',

    threshold: '≥85%',
    status: 1,
    evaluationPeriod: 'day',
    baseData: ['进水COD', '出水COD'],
    calculation: '(进水COD - 出水COD) / 进水COD × 100',
    remark: '评估污水处理工艺的有机物去除效果',
    createTime: '2024-04-22 10:30:00',
    creator: '刘八'
  },
  {
    id: '7',
    name: '泵站效率',
    definition: '泵站实际输出功率与输入功率的比值',
    unit: '%',
    category: '4',
    threshold: '≥75%',
    status: 1,
    evaluationPeriod: 'day',
    baseData: ['流量', '扬程', '功率'],
    calculation: '(流量 × 扬程 × 9.8) / (功率 × 3600) × 100',
    remark: '评估泵站运行效率，指导节能优化',
    createTime: '2024-04-22 13:45:00',
    creator: '周九'
  },
  {
    id: '8',
    name: '温度稳定性',
    definition: '温度波动范围相对于设定值的偏差程度',
    unit: '℃',
    category: '3-2',

    threshold: '±2℃',
    status: 1,
    evaluationPeriod: 'hour',
    baseData: ['实际温度', '设定温度'],
    calculation: 'abs(实际温度 - 设定温度)',
    remark: '监控工艺温度稳定性，确保处理效果',
    createTime: '2024-04-22 15:20:00',
    creator: '吴十'
  },
  {
    id: '9',
    name: '安全事故率',
    definition: '安全事故次数与总作业次数的比值',
    unit: '‰',
    category: '5',

    threshold: '≤1‰',
    status: 1,
    evaluationPeriod: 'quarter',
    baseData: ['事故次数', '作业次数'],
    calculation: '事故次数 / 作业次数 × 1000',
    remark: '监控安全管理水平，预防安全事故',
    createTime: '2024-04-22 16:50:00',
    creator: '郑十一'
  },
  {
    id: '10',
    name: '能耗强度',
    definition: '单位处理水量的能源消耗量',
    unit: 'kWh/m³',
    category: '1-2',

    threshold: '≤0.35',
    status: 0,
    evaluationPeriod: 'month',
    baseData: ['总耗电量', '处理水量'],
    calculation: '总耗电量 / 处理水量',
    remark: '评估能源利用效率，指导节能降耗',
    createTime: '2024-04-22 17:30:00',
    creator: '孙十二'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => filteredTableData.value.length)

// 过滤后的表格数据
const filteredTableData = computed(() => {
  let data = tableData.value

  // 关键词搜索
  if (searchForm.keyword) {
    const keyword = searchForm.keyword.toLowerCase()
    data = data.filter(item =>
      item.name.toLowerCase().includes(keyword) ||
      item.definition.toLowerCase().includes(keyword)
    )
  }

  // 分类筛选
  if (searchForm.category) {
    data = data.filter(item => item.category === searchForm.category)
  }

  // 状态筛选
  if (searchForm.status !== undefined) {
    data = data.filter(item => item.status === searchForm.status)
  }

  return data
})
// 分页后的表格数据
const paginatedTableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTableData.value.slice(start, end)
})







// 对话框
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const selectedItem = ref<IndexItem | null>(null)

// 表格引用
const tableRef = ref()





// 分类管理相关
const categoryDialogVisible = ref(false)

// 分类树数据（与分类管理组件保持一致）
const categoryTreeData = ref([
  {
    id: '1',
    label: '电力系统',
    children: [
      { id: '1-1', label: '电能质量' },
      { id: '1-2', label: '设备效率' }
    ]
  },
  {
    id: '2',
    label: '水处理系统',
    children: [
      { id: '2-1', label: '水质监测' },
      { id: '2-2', label: '处理效果' }
    ]
  },
  {
    id: '3',
    label: '环境监测',
    children: [
      { id: '3-1', label: '噪声监测' },
      { id: '3-2', label: '温度监测' }
    ]
  },
  {
    id: '4',
    label: '设备运行'
  },
  {
    id: '5',
    label: '安全管理'
  }
])

// 兼容性：从树数据生成平铺的选项数据（用于向后兼容）
const categoryOptions = computed(() => {
  return convertTreeToOptions(categoryTreeData.value)
})

// 从分类管理组件获取分类数据并转换为选项格式
const convertTreeToOptions = (treeData: any[]): Array<{ label: string, value: string }> => {
  const options: Array<{ label: string, value: string }> = []

  const traverse = (nodes: any[]) => {
    nodes.forEach(node => {
      // 将分类名称作为 label，生成对应的 value
      const value = node.label.toLowerCase().replace(/[^a-z0-9]/g, '')
      options.push({
        label: node.label,
        value: value
      })

      // 递归处理子分类
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }

  traverse(treeData)
  return options
}

// 工具方法
const getCategoryType = (category: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  // 根据分类 ID 或标签查找对应的类型
  const categoryLabel = getCategoryText(category)

  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '电力系统': 'danger',
    '电能质量': 'danger',
    '设备效率': 'danger',
    '水处理系统': 'primary',
    '水质监测': 'primary',
    '处理效果': 'primary',
    '环境监测': 'success',
    '噪声监测': 'success',
    '温度监测': 'success',
    '设备运行': 'warning',
    '安全管理': 'info'
  }

  return typeMap[categoryLabel] || 'info'
}

const getCategoryText = (category: string) => {
  // 在树形数据中查找对应的标签
  const findLabel = (nodes: any[], id: string): string => {
    for (const node of nodes) {
      if (node.id === id) {
        return node.label
      }
      if (node.children) {
        const childResult = findLabel(node.children, id)
        if (childResult) {
          return childResult
        }
      }
    }
    return ''
  }

  const label = findLabel(categoryTreeData.value, category)
  return label || category
}

const getEvaluationPeriodText = (period: string) => {
  const labelMap: Record<string, string> = {
    hour: '时',
    day: '天',
    week: '周',
    month: '月度',
    quarter: '季度',
    year: '年度'
  }
  return labelMap[period] || '未设置'
}



// 搜索和重置
const handleSearch = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 500)
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.category = ''
  searchForm.status = undefined
  handleSearch()
}









// 表单提交处理
const handleFormSubmit = (formData: any) => {
  try {
    if (dialogType.value === 'add') {
      // 新增指标
      const newItem: IndexItem = {
        id: Date.now().toString(),
        name: formData.name,
        definition: formData.definition,
        unit: formData.unit,
        category: formData.category,
        threshold: formData.threshold,
        status: formData.status,
        evaluationPeriod: formData.evaluationPeriod,
        baseData: formData.baseData,
        calculation: formData.calculation,
        remark: formData.remark,
        createTime: new Date().toLocaleString(),
        creator: '当前用户'
      }
      tableData.value.unshift(newItem)
      ElMessage.success('新增指标成功')
    } else {
      // 编辑指标
      const index = tableData.value.findIndex(item => item.id === selectedItem.value?.id)
      if (index !== -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          ...formData
        }
        ElMessage.success('编辑指标成功')
      }
    }
  } catch (error) {
    ElMessage.error('操作失败')
    console.error('Form submit error:', error)
  }
}







// 表单操作
const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
}

const handleEdit = (row: IndexItem) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  selectedItem.value = row
}

const handleDetail = (row: IndexItem) => {
  selectedItem.value = row
  detailDialogVisible.value = true
}

const handleDelete = (row: IndexItem) => {
  ElMessageBox.confirm('确定要删除该指标吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      tableData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  })
}

// 状态变更
const handleStatusChange = (row: IndexItem) => {
  const statusText = row.status === 1 ? '启用' : '停用'
  ElMessage.success(`${row.name} ${statusText}成功`)
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}





// 分类管理相关方法
const handleCategoryManage = () => {
  categoryDialogVisible.value = true
}

// 分类管理保存处理
const handleCategorySave = (data: any) => {
  // 更新分类树数据
  categoryTreeData.value = data
  console.log('保存分类数据:', data)
  console.log('更新后的分类选项:', categoryOptions.value)
  ElMessage.success('分类保存成功')
}

// validateFormula 函数使用示例：
//
// 1. 基础语法校验：
// const result1 = validateFormula('A + B * C')
// console.log(result1.isValid) // false (缺少变量值)
// console.log(result1.usedVariables) // ['A', 'B', 'C']
//
// 2. 完整计算：
// const result2 = validateFormula('A + B * C', { A: 10, B: 5, C: 2 })
// console.log(result2.isValid) // true
// console.log(result2.result) // 20
//
// 3. 错误处理：
// const result3 = validateFormula('A / B', { A: 10, B: 0 })
// console.log(result3.isValid) // false
// console.log(result3.error) // "公式中存在除零错误"
//
// 4. 复杂公式：
// const result4 = validateFormula('(A + B) / (C - D) * 100', { A: 80, B: 20, C: 120, D: 20 })
// console.log(result4.result) // 100
</script>

<style scoped lang="scss">
// 主容器样式
.index-library-container {
  width: 100%;
  height: calc(100vh - 8rem); // 减少顶部空间，给表格更多空间
  min-height: 40rem; // 确保最小高度
}

.index-library-card {
  height: 100%;

  :deep(.el-card__body) {
    height: calc(100% - 3.5rem); // 减去卡片头部高度
    padding: 1rem;
    overflow: hidden;
  }
}

.table-container {
  height: calc(100% - 8rem); // 减去搜索区域和其他元素的高度
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: 25rem; // 确保最小高度
}

.table-wrapper {
  flex: 1;
  min-height: 20rem; // 确保表格有最小高度

  .index-table {
    height: 100%;
    width: 100%;
  }
}

.pagination-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 0.0625rem solid #e4e7ed;
}

// 响应式表格优化
@media (max-width: 75rem) {
  .index-library-container {
    height: calc(100vh - 8rem);
  }

  .table-container {
    height: calc(100vh - 12rem);
  }
}

@media (max-width: 48rem) {
  .index-library-container {
    height: calc(100vh - 6rem);
  }

  .table-container {
    height: calc(100vh - 10rem);
  }

  .pagination-wrapper {
    justify-content: center;
    margin-top: 0.75rem;
    padding-top: 0.75rem;
  }
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

// 按钮图标样式优化
.el-button {
  .el-icon {
    margin-right: 0.25rem;
  }
}

// 表格样式优化
.el-table {

  // 表格头部样式
  :deep(.el-table__header-wrapper) {
    .el-table__header {
      th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
      }
    }
  }

  // 表格行样式
  :deep(.el-table__body-wrapper) {
    .el-table__row {
      &:hover {
        background-color: #f8f9fa;
      }

      // 斑马纹效果
      &:nth-child(even) {
        background-color: #fdfdfd;
      }
    }
  }

  // 操作按钮样式
  :deep(.el-button--link) {
    padding: 4px 8px;
    margin: 0 2px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
    }

    &.el-button--primary:hover {
      background-color: rgba(64, 158, 255, 0.1);
      color: #409eff;
    }

    &.el-button--info:hover {
      background-color: rgba(144, 147, 153, 0.1);
      color: #909399;
    }

    &.el-button--danger:hover {
      background-color: rgba(245, 108, 108, 0.1);
      color: #f56c6c;
    }
  }

  // 分类标签样式
  :deep(.el-tag) {
    border-radius: 12px;
    font-weight: 500;
    font-size: 12px;
    padding: 2px 8px;
  }

  // 状态开关样式
  :deep(.el-switch) {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #dcdfe6;
  }
}



.formula-test-card {
  background-color: #f8f9fa;

  :deep(.el-card__header) {
    background-color: #e9ecef;
    border-bottom: 0.0625rem solid #dee2e6;
  }

  .variable-inputs {
    background-color: #fff;
    padding: 1rem;
    border-radius: 0.375rem;
    border: 0.0625rem solid #e4e7ed;
  }

  .result-success {
    text-align: center;

    .result-tag {
      font-size: 1.125rem;
      padding: 0.5rem 1rem;
      font-weight: bold;
    }

    .result-details {
      background-color: #f0f9ff;
      padding: 0.75rem;
      border-radius: 0.375rem;
      border-left: 0.25rem solid #409eff;
    }
  }

  .result-error {
    :deep(.el-alert) {
      margin: 0;
    }
  }

  .result-placeholder {
    text-align: center;
    padding: 1.25rem;
    color: #909399;
  }
}



.calculation-result {
  text-align: center;
  padding: 1rem;
  background-color: #fff;
  border-radius: 0.375rem;
  border: 0.0625rem solid #e4e7ed;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}



// 分类管理样式
.category-management {
  .category-toolbar {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .category-tree {
    border: 0.0625rem solid #e4e7ed;
    border-radius: 0.375rem;
    padding: 1rem;
    max-height: 25rem;
    overflow-y: auto;

    :deep(.el-tree-node__content) {
      height: auto;
      padding: 0.5rem 0;
    }

    .tree-node {
      display: flex;
      align-items: center;
      width: 100%;

      .node-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .node-label {
          flex: 1;
          font-size: 0.875rem;
          color: #303133;
        }

        .node-actions {
          display: flex;
          gap: 0.25rem;
          opacity: 0;
          transition: opacity 0.3s;

          .el-button {
            padding: 0.25rem;
            min-height: auto;
          }
        }

        &:hover .node-actions {
          opacity: 1;
        }
      }

      .node-edit {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        width: 100%;

        .el-input {
          flex: 1;
        }

        .edit-actions {
          display: flex;
          gap: 0.25rem;

          .el-button {
            padding: 0.25rem;
            min-height: auto;
          }
        }
      }
    }
  }

  .category-info {
    :deep(.el-alert__content) {
      p {
        margin: 0.25rem 0;
        font-size: 0.8125rem;
      }
    }
  }
}
</style>
