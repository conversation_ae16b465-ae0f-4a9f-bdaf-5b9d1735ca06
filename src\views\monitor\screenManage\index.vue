<template>
  <div class="screen-manage-container" style="padding: 0;">
    <!-- 左侧画面列表 -->
    <ScreenList :screens="screens" :active-screen-id="currentScreen?.id" @add="handleAddScreen"
      @select="handleSelectScreen" @delete="handleDeleteScreen" />

    <!-- 右侧内容区域 -->
    <div class="screen-content" v-if="currentScreen">
      <!-- 顶部操作区 -->
      <ScreenHeader :screen="currentScreen" @update-name="handleUpdateScreenName" @upload-svg="handleUploadSvg"
        @export-svg="handleExportSvg" @save="handleSaveScreen" />

      <div class="flow-chart-container">
        <!-- 工具栏 -->
        <FlowChartToolbar v-model:zoom="zoom" :points="points" :adding-element-type="addingElementType"
          @reset-position="handleResetPosition" @add-element="handleAddElement" @cancel-add="cancelAddElement" />

        <!-- SVG显示区域 -->
        <div class="flow-chart-content">
          <!-- 加载状态覆盖层 -->
          <div class="screen-loading-overlay" v-if="screenLoading">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载工艺画面...</div>
          </div>

          <FlowChartSvgViewer ref="svgViewerRef" :svg-content="currentScreen.svgContent || currentScreen.svgUrl"
            v-model:zoom="zoom" :points="points.map(p => ensurePointShowType(p))"
            :adding-element-type="addingElementType" @select-point="handleSelectPoint" @edit-point="handleEditPoint"
            @drag-end="handleDragEnd" />

          <!-- 右侧监测点列表（暂时隐藏） -->
          <!-- <MonitoringPointsManager ref="pointsManagerRef" :points="points" :selected-point-id="selectedPoint?.id"
            @select="handleSelectPoint" @edit="handleEditPoint" @delete="handleDeletePoint"
            @refresh-data="fetchPointsData" /> -->
        </div>
      </div>

      <!-- 监测点编辑对话框 -->
      <MonitoringPointEditor ref="pointEditorRef" @save="handleSavePoint" @delete="handleDeletePoint"
        @close="handleCloseEditor" @preview="handlePreviewPoint" />
    </div>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <svg class="empty-icon" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24C0 10.7452 10.7452 0 24 0ZM24 3.2C12.5131 3.2 3.2 12.5131 3.2 24C3.2 35.4869 12.5131 44.8 24 44.8C35.4869 44.8 44.8 35.4869 44.8 24C44.8 12.5131 35.4869 3.2 24 3.2ZM24 14.4C24.8837 14.4 25.6 15.1163 25.6 16L25.6 25.6L35.2 25.6C36.0837 25.6 36.8 26.3163 36.8 27.2C36.8 28.0837 36.0837 28.8 35.2 28.8L24 28.8C23.1163 28.8 22.4 28.0837 22.4 27.2L22.4 16C22.4 15.1163 23.1163 14.4 24 14.4Z"
            fill="#DCE0E6" />
        </svg>
        <div class="empty-text">暂无画面数据</div>
        <el-button type="primary" @click="screens.length > 0 ? handleUploadSvg() : handleAddScreen()">
          {{ screens.length > 0 ? '上传SVG' : '新建画面' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getScreenById, saveScreen, deleteScreen, getScreenListByFactoryId, updateScreen, getPointsDataByScreenId } from '@/api/monitor/screenManage/index'
import { convertApiPointsToInternalFormat, convertPointsToApiFormat } from './utils/PointsDataConverter'
import { handleUploadSvg as uploadSvgFile, exportSvg as exportSvgFile } from './utils/SvgUtils'
import { useAppStore } from '@/store/modules/app'
// 导入URL配置
import { SVG_URL } from '@/config/url'


// 导入组件
import ScreenList from './components/ScreenList.vue'
import ScreenHeader from './components/ScreenHeader.vue'
import FlowChartToolbar from './components/FlowChartToolbar.vue'
import FlowChartSvgViewer from './components/FlowChartSvgViewer.vue'
// import MonitoringPointsManager from './components/MonitoringPointsManager.vue'
import MonitoringPointEditor from './components/MonitoringPointEditor.vue'

// 确保点位有showType属性的助手函数
const ensurePointShowType = (point: Point): Point => {
  if (!point.showType) {
    if (point.type) {
      // 如果有type属性，使用type作为showType
      console.log("使用type作为showType:", point.type);
      return { ...point, showType: point.type };
    } else {
      // 如果既没有showType也没有type，根据isText添加默认值
      const defaultType = point.isText ? 'text' : 'circle';
      console.log("设置默认showType:", defaultType);
      return { ...point, showType: defaultType };
    }
  }
  return point;
};

// 定义接口
interface Screen {
  id: string | number;
  name: string;
  svgContent?: string;
  svgUrl?: string;  // 添加svgUrl字段，用于存储SVG的URL
  points?: any[];
  isModified?: boolean;
  isNew?: boolean;
  updateTime?: string;  // 添加更新时间字段
}

interface Point {
  id: string;
  x: number;
  y: number;
  name: string;
  type?: string;
  showType: string;
  data?: any;
  isText: boolean;
}

// 界面状态管理
const screens = ref<Screen[]>([])
const currentScreen = ref<Screen | null>(null)
const zoom = ref(1)
const points = ref<Point[]>([])
const selectedPoint = ref<Point | null>(null)
const addingElementType = ref('')
const appStore = useAppStore()
// 添加一个标志，用于控制是否忽略点位变化
const ignorePointsChange = ref(false);
// 添加画面加载状态
const screenLoading = ref(false);

// 组件引用
const svgViewerRef = ref<InstanceType<typeof FlowChartSvgViewer> | null>(null)
// 暂时注释掉监测点管理器引用
// const pointsManagerRef = ref<InstanceType<typeof MonitoringPointsManager> | null>(null)
const pointEditorRef = ref<InstanceType<typeof MonitoringPointEditor> | null>(null)

// 初始化标志，避免重复加载
const initialized = ref(false)

// 初始化数据
onMounted(async () => {
  console.log("组件挂载，初始加载画面列表")

  // 获取当前水厂ID
  const currentFactoryId = appStore.getCurrentStation?.id
  console.log("初始化时获取到的水厂ID:", currentFactoryId)

  // 直接调用一次fetchScreenList
  await fetchScreenList()

  // 标记为已初始化
  initialized.value = true

  // 如果初始化后仍然没有获取到水厂ID，可以添加一个延迟重试机制
  if (!currentFactoryId) {
    console.log("初始化后未获取到水厂ID，将在500ms后尝试重新获取")
    setTimeout(async () => {
      const newFactoryId = appStore.getCurrentStation?.id
      if (newFactoryId && newFactoryId !== currentFactoryId) {
        console.log("延迟获取到新的水厂ID:", newFactoryId, "将重新加载数据")
        await fetchScreenList()
      }
    }, 500)
  }
})

// 监听水厂变化，重新加载数据
watch(() => appStore.getCurrentStation?.id, async (newFactoryId, oldFactoryId) => {
  console.log("水厂ID变化:", oldFactoryId, "->", newFactoryId, "初始化状态:", initialized.value)

  // 如果ID相同或者没有新ID，不重复加载
  if ((newFactoryId === oldFactoryId && initialized.value) || !newFactoryId) {
    console.log("水厂ID未变化或为空，不重新加载数据")
    return
  }

  // 在初始化完成前但已有水厂ID的情况下，确保能正确加载数据
  if (!initialized.value && newFactoryId) {
    console.log("初始化阶段水厂ID变化，等待初始化完成后自动加载数据")
    // 初始化阶段的变化会在onMounted中通过fetchScreenList处理
    return
  }

  if (newFactoryId) {
    console.log("水厂ID变化，开始重新加载数据:", newFactoryId)

    // 如果当前有未保存的修改，提示用户
    if (currentScreen.value?.isModified) {
      try {
        await ElMessageBox.confirm(
          '当前画面有未保存的修改，切换水厂将丢失这些修改，是否继续？',
          '未保存的修改',
          {
            confirmButtonText: '确认',
            type: 'warning'
          }
        )

        // 用户确认切换，处理原画面的状态
        // 由于要切换水厂，我们需要处理所有未保存的画面
        for (let i = 0; i < screens.value.length; i++) {
          if (screens.value[i].isModified) {
            if (screens.value[i].isNew) {
              // 新建但未保存的画面应该从列表中移除
              screens.value.splice(i, 1);
              i--; // 调整索引
            } else {
              // 已有的画面标记为已保存
              screens.value[i].isModified = false;
            }
          }
        }

      } catch (e) {
        // 用户取消切换，但我们不能阻止水厂切换，因为这是全局行为
        console.log("用户取消了水厂切换，但全局水厂已变更")
        return
      }
    }

    // 重置当前选中的画面
    currentScreen.value = null

    // 设置标志位，忽略点位变化的监听
    ignorePointsChange.value = true;

    // 重置点位和状态
    points.value = []
    selectedPoint.value = null
    addingElementType.value = ''

    // 如果SVG查看器已加载，清空监测点容器
    if (svgViewerRef.value?.clearAllPoints) {
      svgViewerRef.value.clearAllPoints()
    }

    // 重新加载画面列表 - fetchScreenList内部会自动选择第一个画面
    await fetchScreenList()
  }
})

// 获取画面列表
const fetchScreenList = async () => {
  try {
    console.log("fetchScreenList - 开始获取画面列表")

    // 从store获取当前工厂ID
    const factoryId = appStore.getCurrentStation?.id
    if (!factoryId) {
      console.warn("fetchScreenList - 未选择水厂，无法获取画面列表")
      // 不在初始阶段显示警告，因为可能水厂选择器还未初始化完成
      if (initialized.value) {
        ElMessage.warning('请先选择水厂')
      } else {
        console.log("初始化阶段未获取到水厂ID，等待水厂选择完成")
      }
      return
    }

    console.log(`fetchScreenList - 正在获取工厂ID=${factoryId}的画面列表`)

    // 调用API获取画面列表，传递工厂ID
    const response = await getScreenListByFactoryId(factoryId)
    console.log(`fetchScreenList - 获取到${response?.length || 0}个画面`)

    if (response) {
      // 确保所有画面的初始状态为已保存
      screens.value = response.map((screen: any) => ({
        ...screen,
        isModified: false // 从API获取的画面是已保存状态
      }))

      // 如果有画面数据，默认选中第一个
      if (screens.value.length > 0) {
        // 默认选中第一个工艺画面
        const firstScreen = screens.value[0]

        // 如果当前没有选中画面或者水厂变化了，则选中第一个画面
        if (!currentScreen.value) {
          console.log("选中第一个画面:", firstScreen.id, firstScreen.name)
          handleSelectScreen(firstScreen)
        }
      } else {
        console.log("当前水厂没有工艺画面数据")
        ElMessage.info('当前水厂没有工艺画面数据')
      }
    }
  } catch (error) {
    console.error('获取画面列表失败:', error)
    ElMessage.error('获取画面列表失败')
  }
}

// 处理选择画面
const handleSelectScreen = async (screen: Screen) => {
  // 如果当前有未保存的修改，提示用户
  if (currentScreen.value?.isModified) {
    try {
      // 针对新增但未保存的画面，提供更明确的提示
      const message = currentScreen.value.isNew
        ? '当前是新增画面且未保存，切换后将被销毁，是否确认切换？'
        : '当前画面有未保存的修改，切换将丢失这些修改，是否继续？'

      await ElMessageBox.confirm(
        message,
        '未保存的修改',
        {
          confirmButtonText: '确认切换',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 用户确认切换，处理原画面的状态
      const oldScreenId = currentScreen.value.id;
      const oldScreenIndex = screens.value.findIndex(s => s.id === oldScreenId);

      if (currentScreen.value.isNew) {
        // 如果是新建但未保存的画面，从列表中移除
        if (oldScreenIndex !== -1) {
          console.log(`用户确认切换画面，移除新建但未保存的画面(ID=${oldScreenId})`);
          screens.value.splice(oldScreenIndex, 1);
        }
      } else {
        // 对于已保存的画面，清除其未保存状态
        if (oldScreenIndex !== -1) {
          console.log(`用户确认切换画面，清除原画面(ID=${oldScreenId})的未保存状态`);
          screens.value[oldScreenIndex].isModified = false;
        }
      }
    } catch (e) {
      // 用户取消切换
      return
    }
  }

  try {
    console.log(`handleSelectScreen - 选择画面: ID=${screen.id}, 名称=${screen.name}`)

    // 开启加载状态
    screenLoading.value = true;

    // 确保不会无限等待
    ensureLoadingTimeout(15000); // 设置15秒超时保护 - 保存操作可能需要更长时间

    // 设置标志位，忽略点位变化的监听
    ignorePointsChange.value = true;

    // 先保存要加载的画面的基本信息，使用初始化SVG内容，避免闪烁
    currentScreen.value = {
      ...screen,
      svgContent: `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
        <text x="20" y="300" text-anchor="start" font-size="24" fill="#999">
          正在加载工艺画面...
        </text>
        <g id="monitoring-points" class="monitoring-points-layer"></g>
      </svg>`, // 设置加载中的提示内容
      svgUrl: '' // 先清空URL，确保使用内联内容显示加载状态
    }

    // 先清空当前点位数据，避免旧点位被错误显示在新SVG上
    points.value = []
    selectedPoint.value = null

    // 如果SVG查看器已加载，清空监测点容器
    if (svgViewerRef.value?.clearAllPoints) {
      svgViewerRef.value.clearAllPoints()
    }

    // 重置缩放
    zoom.value = 1

    // 重置选中状态
    addingElementType.value = ''

    // 第一步：获取SVG图片信息
    console.log(`handleSelectScreen - 获取画面信息: ID=${screen.id}`)

    try {
      const screenDetail = await getScreenById(screen.id)
      console.log("SVG画面信息:", screenDetail);

      if (screenDetail) {
        // 更新当前画面的SVG内容和URL
        // 优先使用内容形式，确保能正确显示SVG
        currentScreen.value.svgContent = screenDetail.svgContent || ''
        
        // 处理SVG URL，如果是相对路径则拼接SVG_URL前缀
        if (screenDetail.svgUrl) {
          // 判断是否已经是完整URL（以http://或https://开头）
          if (screenDetail.svgUrl.startsWith('http://') || screenDetail.svgUrl.startsWith('https://')) {
            currentScreen.value.svgUrl = screenDetail.svgUrl;
          } else {
            // 使用SVG_URL配置拼接相对路径
            currentScreen.value.svgUrl = SVG_URL + screenDetail.svgUrl;
          }
        } else {
          currentScreen.value.svgUrl = '';
        }

        // 安全地记录SVG内容
        if (currentScreen.value.svgContent && typeof currentScreen.value.svgContent === 'string') {
          console.log("使用内联SVG内容:", currentScreen.value.svgContent.substring(0, 100) + "...")
        } else if (currentScreen.value.svgUrl) {
          console.log("使用SVG URL:", currentScreen.value.svgUrl)
        } else {
          console.log("SVG内容和URL均为空或无效")
        }

        // 更新画面状态为已保存 - 确保当前画面和列表中的画面状态一致
        if (currentScreen.value.isModified !== false) {
          currentScreen.value.isModified = false

          // 同步更新列表中的画面状态
          const index = screens.value.findIndex(s => s.id === screen.id)
          if (index !== -1) {
            screens.value[index].isModified = false
          }
        }
      } else {
        console.warn('未找到工艺画面详细数据');
        ElMessage.warning('未找到工艺画面详细数据');

        // 设置一个默认的空白SVG
        currentScreen.value.svgContent = `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
          <text x="20" y="300" text-anchor="start" font-size="24" fill="#999">
            未找到工艺画面详细数据
          </text>
          <g id="monitoring-points" class="monitoring-points-layer"></g>
        </svg>`;
      }
    } catch (svgError) {
      console.error('获取SVG图片信息失败:', svgError);
      ElMessage.error('获取SVG图片信息失败');

      // 设置一个默认的空白SVG
      currentScreen.value.svgContent = `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
        <text x="20" y="300" text-anchor="start" font-size="24" fill="#999">
          获取SVG图片信息失败
        </text>
        <g id="monitoring-points" class="monitoring-points-layer"></g>
      </svg>`;
    }

    // 第二步：获取点位数据
    console.log(`handleSelectScreen - 获取点位数据: ID=${screen.id}`)
    try {
      const pointsResponse = await getPointsDataByScreenId(screen.id)
      console.log("获取到点位数据:", pointsResponse);

      if (pointsResponse && Array.isArray(pointsResponse)) {
        console.log("加载监测点数据", pointsResponse.length, "个点位");

        // 设置标志位，忽略点位变化的监听
        ignorePointsChange.value = true;

        // 更新点位数据
        points.value = convertApiPointsToInternalFormat(pointsResponse);

        // 使用setTimeout延迟重置标志位，确保points的监听器不会立即触发
        setTimeout(() => {
          ignorePointsChange.value = false;
          console.log("重置点位变化监听状态");
        }, 300);
      } else {
        console.log("未找到点位数据或数据格式不正确");

        // 设置标志位，忽略点位变化的监听
        ignorePointsChange.value = true;

        // 清空点位数据
        points.value = [];

        // 使用setTimeout延迟重置标志位
        setTimeout(() => {
          ignorePointsChange.value = false;
          console.log("重置点位变化监听状态");
        }, 300);
      }
    } catch (pointsError) {
      console.error("获取点位数据失败:", pointsError);
      ElMessage.warning("获取点位数据失败，将显示空白画面");

      // 清空点位数据
      points.value = [];

      // 重置忽略点位变化标志
      ignorePointsChange.value = false;
    }

    // 重置SVG视图位置 - 在延迟后执行，确保SVG已加载完成
    setTimeout(() => {
      console.log("准备重置SVG视图位置");
      if (svgViewerRef.value) {
        console.log("SVG查看器已加载，重置位置");
        if (typeof svgViewerRef.value.resetPosition === 'function') {
          svgViewerRef.value.resetPosition();
        } else {
          console.warn("SVG查看器缺少resetPosition方法");
        }

        // 再次延迟检查，确保SVG内容已经正确渲染
        setTimeout(() => {
          // 安全检查SVG元素
          let svgExists = false;

          try {
            if (svgViewerRef.value && svgViewerRef.value.$el) {
              const svgElement = document.evaluate('//svg', svgViewerRef.value.$el, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
              svgExists = !!svgElement;

              if (svgExists) {
                console.log("SVG元素已正确加载");
              } else {
                console.warn("SVG元素未正确加载");
              }
            }
          } catch (e) {
            console.error("检查SVG元素时出错:", e);
          }

          // 重置SVG位置
          if (typeof svgViewerRef.value?.resetPosition === 'function') {
            svgViewerRef.value.resetPosition();
          }

          // 无论如何，确保关闭loading状态
          screenLoading.value = false;
          console.log("画面加载完成，关闭loading状态");
        }, 500);
      } else {
        console.warn("SVG查看器尚未加载，无法重置位置");
        // 即使SVG查看器未加载，也关闭loading状态
        screenLoading.value = false;
      }
    }, 300);

    // 画面加载完成后，预加载指标数据
    if (pointEditorRef.value && typeof pointEditorRef.value.preloadIndicatorData === 'function') {
      console.log('预加载指标数据');
      pointEditorRef.value.preloadIndicatorData().catch(err => {
        console.warn('预加载指标数据失败，但不影响正常使用:', err);
      });
    }

    // 确保加载状态不会无限等待 - 添加最大加载时间
    setTimeout(() => {
      if (screenLoading.value) {
        console.warn("画面加载超时，强制关闭加载状态");
        screenLoading.value = false;
      }
    }, 5000); // 最长等待5秒
  } catch (error) {
    console.error('加载工艺画面详情失败:', error)
    ElMessage.error('加载工艺画面详情失败')

    // 出错时仍然设置基本画面信息，但不加载点位
    currentScreen.value = {
      ...screen,
      svgContent: `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
        <text x="20" y="300" text-anchor="start" font-size="24" fill="#999">
          加载失败
        </text>
        <g id="monitoring-points" class="monitoring-points-layer"></g>
      </svg>` // 添加一个错误提示
    }
    points.value = []

    // 重置忽略点位变化标志
    ignorePointsChange.value = false;

    // 关闭loading状态
    screenLoading.value = false;
  }
}

// 处理重置SVG位置
const handleResetPosition = () => {
  if (svgViewerRef.value?.resetPosition) {
    svgViewerRef.value.resetPosition()
  }
}

// 处理添加元素
const handleAddElement = (type: string) => {
  console.log("设置添加元素类型:", type);
  addingElementType.value = type
  ElMessage.info(`请在SVG上点击位置添加${type === 'text' ? '文本标签' : '监测点'}`)
}

// 取消添加元素
const cancelAddElement = () => {
  addingElementType.value = ''
}

// 处理选中点位
const handleSelectPoint = (point: Point | null) => {
  selectedPoint.value = point
}

// 处理编辑点位
const handleEditPoint = (point: Point, isNew = false) => {
  try {
    console.log("处理编辑点位", point.id, isNew, "坐标:", point.x, point.y);

    if (!pointEditorRef.value) {
      console.error("编辑器引用不存在");
      ElMessage.error("编辑器组件未加载，请刷新页面重试");
      return;
    }

    if (!pointEditorRef.value.open) {
      console.error("编辑器引用存在但没有open方法");
      ElMessage.error("编辑器组件方法缺失，请刷新页面重试");
      return;
    }

    console.log("调用编辑器的open方法", pointEditorRef.value);

    // 确保点位数据完整
    if (!point.id || !point.showType) {
      console.warn("点位数据不完整，尝试补全");
      point = ensurePointShowType(point);
    }

    // 使用nextTick确保组件已完全渲染
    nextTick(() => {
      try {
        // 打开编辑器
        console.log("使用nextTick确保组件已渲染后调用open", point);
        pointEditorRef.value?.open(point, isNew);

        // 添加确认弹窗是否成功打开的检查
        setTimeout(() => {
          const isEditorOpen = document.querySelector('.el-drawer__open');
          console.log("编辑器弹窗状态检查:", isEditorOpen ? "已打开" : "未打开");

          if (!isEditorOpen) {
            console.warn("编辑器弹窗未成功打开，尝试再次调用");
            pointEditorRef.value?.open(point, isNew);
          }
        }, 100);
      } catch (innerError) {
        console.error("nextTick中调用open方法失败:", innerError);
        ElMessage.error("打开编辑器失败，请重试");
      }
    });

    // 记录编辑状态
    console.log("编辑器已打开，等待用户操作");
  } catch (error) {
    console.error("编辑点位失败:", error);
    ElMessage.error("编辑点位失败，请重试");
  }
}

// 处理保存点位
const handleSavePoint = (point: Point, isNew: boolean) => {
  console.log("保存点位:", isNew ? "新增" : "更新", point, "位置:", point.x, point.y);

  try {
    // 验证点位坐标有效性
    if (isNaN(point.x) || isNaN(point.y)) {
      ElMessage.error('点位坐标无效，请检查X和Y的值');
      return;
    }

    // 确保showType属性存在
    const pointWithType = ensurePointShowType(point);
    console.log("确保点位类型属性:", pointWithType);

    // 无论是否为新点位，都需要更新SVG中的点位
    // 查找点位在数组中的索引
    const index = points.value.findIndex(p => p.id === point.id)

    if (index !== -1) {
      // 更新现有点位
      points.value[index] = pointWithType;
      console.log("更新现有点位在数组中的索引:", index, "新位置:", pointWithType.x, pointWithType.y);
    } else if (isNew) {
      // 新增点位添加到数组
      points.value.push(pointWithType);
      console.log("新增点位添加到数组，位置:", pointWithType.x, pointWithType.y);
    }

    // 在SVG中更新点位
    if (svgViewerRef.value?.addOrUpdatePointInSVG) {
      console.log("保存点位到SVG", pointWithType, isNew, "位置:", pointWithType.x, pointWithType.y);

      // 先设置标志位，忽略点位变化的监听，避免循环触发
      ignorePointsChange.value = true;

      // 如果是新增的点位，传入true
      svgViewerRef.value.addOrUpdatePointInSVG(pointWithType, isNew);

      // 使用setTimeout延迟重置标志位
      setTimeout(() => {
        ignorePointsChange.value = false;
        console.log("重置点位变化监听状态");
      }, 300);

      // 添加额外验证
      setTimeout(() => {
        // 检查点位是否成功添加到SVG中
        // 由于exportSvgWithPoints是异步的，这里不能直接使用它来验证
        console.log(`点位${pointWithType.id}已添加到SVG中，但无法立即验证内容长度`);
        // 如果需要验证，应该使用异步方式，但这里简化处理
      }, 500);
    } else {
      console.error("SVG查看器引用不存在或缺少addOrUpdatePointInSVG方法");
    }

    // 标记画面为已修改
    if (currentScreen.value) {
      currentScreen.value.isModified = true;

      // 更新列表中的画面
      const index = screens.value.findIndex(s => s.id === currentScreen.value?.id);
      if (index !== -1) {
        screens.value[index].isModified = true;
      }
    }

    // 清除添加状态
    addingElementType.value = '';

    ElMessage.success(`${isNew ? '添加' : '更新'}点位成功`);
  } catch (error) {
    console.error("保存点位失败:", error);
    ElMessage.error(`保存点位失败: ${error}`);
  }
}

// 处理删除点位
const handleDeletePoint = (point: Point) => {
  try {
    console.log("删除点位:", point.id, "位置:", point.x, point.y);

    // 先设置标志位，忽略点位变化的监听，避免循环触发
    ignorePointsChange.value = true;

    // 从数组中移除
    const index = points.value.findIndex(p => p.id === point.id)
    if (index !== -1) {
      points.value.splice(index, 1)
      console.log("从数组中移除点位, 索引:", index);
    }

    // 如果是当前选中的点位，清除选中状态
    if (selectedPoint.value?.id === point.id) {
      selectedPoint.value = null
    }

    // 从SVG中移除点位
    if (svgViewerRef.value?.removePointFromSVG) {
      svgViewerRef.value.removePointFromSVG(ensurePointShowType(point))
      console.log("从SVG中移除点位");
    }

    // 标记画面为已修改
    if (currentScreen.value) {
      currentScreen.value.isModified = true

      // 更新列表中的画面
      const index = screens.value.findIndex(s => s.id === currentScreen.value?.id)
      if (index !== -1) {
        screens.value[index].isModified = true
      }
    }

    // 使用setTimeout延迟重置标志位
    setTimeout(() => {
      ignorePointsChange.value = false;
      console.log("重置点位变化监听状态");
    }, 300);

    ElMessage.success('删除点位成功');
  } catch (error) {
    console.error("删除点位失败:", error);
    ignorePointsChange.value = false; // 确保错误情况下也重置标志位
    ElMessage.error(`删除点位失败: ${error}`);
  }
}

// 处理拖拽结束事件
const handleDragEnd = (dragInfo: any) => {
  try {
    console.log("处理拖拽结束事件:", dragInfo);

    // 如果拖拽距离太小（小于1像素），认为没有实际移动，不处理
    if (dragInfo.distance <= 1) {
      return;
    }

    // 获取被拖拽的点位
    const draggedPoint = dragInfo.point;
    if (!draggedPoint || !draggedPoint.id) {
      console.error("拖拽事件缺少点位信息", dragInfo);
      return;
    }

    console.log("拖拽的点位ID:", draggedPoint.id, "新坐标:", draggedPoint.x, draggedPoint.y);

    // 先设置标志位，忽略点位变化的监听，避免循环触发
    ignorePointsChange.value = true;

    // 在点位数组中找到并更新该点位的坐标
    const pointIndex = points.value.findIndex(p => p.id === draggedPoint.id);
    if (pointIndex !== -1) {
      // 更新点位坐标
      points.value[pointIndex].x = draggedPoint.x;
      points.value[pointIndex].y = draggedPoint.y;
      console.log("点位数组中更新坐标成功:", points.value[pointIndex]);

      // 创建一个点位的深拷贝，确保后续操作不影响原始数据
      const pointToUpdate = JSON.parse(JSON.stringify(points.value[pointIndex]));

      // 在SVG中更新点位位置
      if (svgViewerRef.value?.addOrUpdatePointInSVG) {
        // 确保点位有正确的showType
        const pointWithType = ensurePointShowType(pointToUpdate);

        // 确保isText属性存在并正确
        if (draggedPoint.isText) {
          pointWithType.isText = true;
        }

        // 使用false表示更新现有点位而非创建新点位
        svgViewerRef.value.addOrUpdatePointInSVG(pointWithType, false);
        console.log("SVG中更新点位位置成功", pointWithType);
      }
    } else {
      console.warn("在点位数组中未找到拖拽的点位:", draggedPoint.id);
    }



    // 如果当前画面存在，标记为已修改
    if (currentScreen.value) {
      console.log("拖拽操作完成，标记画面为未保存状态");
      currentScreen.value.isModified = true;

      // 同步更新列表中的画面状态
      const index = screens.value.findIndex(s => s.id === currentScreen.value?.id);
      if (index !== -1) {
        screens.value[index].isModified = true;
      }
    }

    // 使用setTimeout延迟重置标志位
    setTimeout(() => {
      ignorePointsChange.value = false;
      console.log("拖拽结束后重置点位变化监听状态");
    }, 300);
  } catch (error) {
    console.error("处理拖拽结束事件失败:", error);
    ignorePointsChange.value = false; // 确保错误情况下也重置标志位
  }
}

// 处理点位预览
const handlePreviewPoint = (point: Point) => {
  try {
    // 验证坐标有效性
    if (isNaN(point.x) || isNaN(point.y)) {
      console.warn("预览点位坐标无效:", point);
      return;
    }

    // 预览不影响原始数据，只更新SVG显示
    console.log("预览点位:", point, "位置:", point.x, point.y);

    // 确保showType属性存在
    const pointWithType = ensurePointShowType(point);

    if (svgViewerRef.value?.addOrUpdatePointInSVG) {
      // 添加 isPreview 标记，表示这是预览操作
      svgViewerRef.value.addOrUpdatePointInSVG(pointWithType, false, true);
      console.log("预览点位已更新，位置:", point.x, point.y, "类型:", pointWithType.showType);
    } else {
      console.error("SVG查看器引用不存在或缺少addOrUpdatePointInSVG方法");
    }
  } catch (error) {
    console.error("预览点位失败:", error);
  }
}

// 处理关闭编辑器
const handleCloseEditor = () => {
  console.log("关闭编辑器", addingElementType.value);

  // 在点击取消按钮时，关闭弹窗不需要删除点位
  // 取消下面的点位删除逻辑，让用户保存或删除点位
  /* 
  if (addingElementType.value && svgViewerRef.value) {
    console.log("当前正在添加元素", addingElementType.value);
    console.log("当前点位数组", points.value);
    
    const lastAddedPoint = points.value[points.value.length - 1];
    if (lastAddedPoint) {
      console.log("移除最后添加的点位", lastAddedPoint);
      svgViewerRef.value.removePointFromSVG(ensurePointShowType(lastAddedPoint));

      const index = points.value.findIndex(p => p.id === lastAddedPoint.id);
      if (index !== -1) {
        points.value.splice(index, 1);
        console.log("从数组中移除点位", index);
      }
    }
  }
  */

  // 清除添加状态
  addingElementType.value = ''
}

// 获取点位实时数据 (暂时注释掉)
/*
const fetchPointsData = async () => {
  if (!currentScreen.value || points.value.length === 0) return

  try {
    // 收集所有点位的指标编码
    const indicatorCodes = points.value
      .filter(point => point.data?.indicator)
      .map(point => point.data.indicator)

    if (indicatorCodes.length === 0) return

    // 这里模拟获取实时数据，实际项目中应调用实时数据API
    const mockData: Record<string, number> = {}

    // 为每个点位生成模拟数据
    points.value.forEach(point => {
      if (point.data?.indicator) {
        // 生成随机数据
        mockData[point.id] = Math.floor(Math.random() * 100)
      }
    })

    // 更新点位数据
    if (pointsManagerRef.value?.updatePointsData) {
      pointsManagerRef.value.updatePointsData(mockData)
    }

    ElMessage.success('实时数据更新成功')
  } catch (error) {
    console.error('获取实时数据失败:', error)
    ElMessage.error('获取实时数据失败')
  }
}
*/

// 监听点位变化，标记画面为已修改
watch(points, () => {
  // 如果正在忽略点位变化，不执行任何操作
  if (ignorePointsChange.value) {
    console.log("忽略点位变化，不标记为未保存");
    return;
  }

  if (currentScreen.value) {
    // 如果当前画面存在且状态未修改，则更新状态
    if (!currentScreen.value.isModified) {
      console.log("检测到点位变化，标记画面为未保存");
      currentScreen.value.isModified = true;

      // 同步更新列表中的画面状态
      const index = screens.value.findIndex(s => s.id === currentScreen.value?.id);
      if (index !== -1) {
        screens.value[index].isModified = true;
      }
    }
  }
}, { deep: true })

// 添加一个超时确保函数，确保加载状态不会无限等待
const ensureLoadingTimeout = (duration = 5000) => {
  // 创建一个超时计时器，确保loading状态会在一定时间后关闭
  setTimeout(() => {
    if (screenLoading.value) {
      console.warn(`画面加载超时(${duration}ms)，强制关闭加载状态`);
      screenLoading.value = false;
    }
  }, duration);
}

// 处理导出SVG
const handleExportSvg = async () => {
  if (!currentScreen.value) {
    ElMessage.error('没有选中的画面，无法导出SVG');
    return;
  }

  try {
    console.log('准备导出SVG');

    // 获取带点位的SVG内容
    let svgContent = '';

    if (svgViewerRef.value?.exportSvgWithPoints) {
      console.log('使用SVG查看器的导出方法');

      // 如果是URL格式的SVG，需要先获取完整内容
      if (currentScreen.value.svgUrl && !currentScreen.value.svgContent) {
        console.log('检测到URL格式的SVG，获取完整SVG内容');
        svgContent = await svgViewerRef.value.exportSvgWithPoints(currentScreen.value.svgUrl);
      } else {
        // 确保等待异步操作完成
        svgContent = await svgViewerRef.value.exportSvgWithPoints();
      }
    } else if (currentScreen.value.svgContent) {
      console.log('使用当前画面的SVG内容');
      svgContent = currentScreen.value.svgContent;
    } else if (currentScreen.value.svgUrl) {
      console.log('使用当前画面的SVG URL');
      // 尝试通过URL获取内容
      try {
        const response = await fetch(currentScreen.value.svgUrl);
        if (response.ok) {
          svgContent = await response.text();
          console.log('成功从URL获取SVG内容');
        }
      } catch (fetchError) {
        console.error('从URL获取SVG内容失败:', fetchError);
      }
    }

    // 确保SVG内容非空
    if (!svgContent || svgContent.trim() === '') {
      console.error('SVG内容为空');
      ElMessage.error('SVG内容为空，无法导出');
      return;
    }

    console.log('导出SVG内容长度:', svgContent.length);

    // 导出SVG文件
    exportSvgFile(svgContent, currentScreen.value.name || '工艺画面', true);

  } catch (error) {
    console.error('导出SVG失败:', error);
    ElMessage.error('导出SVG失败: ' + (error instanceof Error ? error.message : String(error)));
  }
}

// 处理上传SVG
const handleUploadSvg = (svgContent?: string) => {
  if (!currentScreen.value) {
    ElMessage.error('没有选中的画面，无法上传SVG');
    return;
  }

  console.log('处理SVG上传:', svgContent ? '通过事件传递内容' : '直接调用打开文件选择器');

  // 如果直接提供了SVG内容（通过事件传递），则直接处理
  if (svgContent) {
    processSvgContent(svgContent);
    return;
  }

  // 没有提供SVG内容，打开文件选择对话框
  // 开启加载状态
  screenLoading.value = true;

  // 调用工具函数上传SVG，添加onCancel回调函数
  uploadSvgFile(
    // 成功回调
    (content) => {
      if (!content) {
        screenLoading.value = false;
        return;
      }

      // 处理上传的SVG内容
      processSvgContent(content);
    },
    // 配置选项
    {
      acceptTypes: '.svg',
      showSuccessMessage: false, // 不显示成功消息，由processSvgContent中显示
      // 取消上传的回调
      onCancel: () => {
        console.log('用户取消了SVG上传');
        screenLoading.value = false;
      }
    }
  );

  // 提取处理SVG内容的逻辑到单独的函数
  function processSvgContent(content: string) {
    console.log('SVG上传成功，更新画面内容');

    // 开启加载状态（如果还没开启）
    if (!screenLoading.value) {
      screenLoading.value = true;
    }

    // 设置标志位，忽略点位变化的监听
    ignorePointsChange.value = true;

    // 清空当前点位数据，避免旧点位显示在新SVG上
    points.value = [];
    selectedPoint.value = null;

    // 如果SVG查看器已加载，清空监测点容器
    if (svgViewerRef.value?.clearAllPoints) {
      console.log("清空SVG中的所有点位");
      svgViewerRef.value.clearAllPoints();
    }

    // 更新当前画面的SVG内容
    if (currentScreen.value) {
      // 清空URL，确保使用内容显示
      currentScreen.value.svgUrl = '';
      currentScreen.value.svgContent = content;

      // 标记为已修改
      currentScreen.value.isModified = true;

      // 更新列表中的画面
      const index = screens.value.findIndex(s => s.id === currentScreen.value?.id);
      if (index !== -1) {
        screens.value[index].svgUrl = '';
        screens.value[index].svgContent = content;
        screens.value[index].isModified = true;
      }
    }

    // 重置SVG视图位置和缩放
    nextTick(() => {
      try {
        // 重置缩放
        zoom.value = 1;

        // 重置选中状态
        addingElementType.value = '';

        // 重置SVG位置
        if (svgViewerRef.value && typeof svgViewerRef.value.resetPosition === 'function') {
          console.log("重置SVG视图位置");
          svgViewerRef.value.resetPosition();
        }
      } catch (err) {
        console.error("重置SVG位置时出错:", err);
      }

      // 延迟重置点位变化监听，确保所有操作完成
      setTimeout(() => {
        ignorePointsChange.value = false;
        console.log("重置点位变化监听状态");
      }, 300);

      // 延迟关闭loading状态，确保SVG已加载完成
      setTimeout(() => {
        screenLoading.value = false;
        console.log("上传SVG加载完成，关闭loading状态");
      }, 500);
    });

    ElMessage.success('SVG上传成功，原有点位已清除');
  }
}

// 处理添加画面
const handleAddScreen = async () => {
  console.log("新建画面 - 直接进入上传SVG流程");

  // 开启加载状态
  screenLoading.value = true;

  try {
    // 直接调用上传SVG文件的对话框
    uploadSvgFile(
      // 成功回调 - 上传成功后创建新画面并设置SVG内容
      (svgContent, fileName) => {
        if (!svgContent) {
          screenLoading.value = false;
          return;
        }

        console.log("SVG上传成功，创建新画面");

        // 生成一个临时ID
        const tempId = `temp_${Date.now()}`;

        // 从文件名提取画面名称（去除.svg后缀）
        let screenName = '新建画面';
        if (fileName) {
          screenName = fileName.replace(/\.svg$/i, '') || '新建画面';
        }

        // 创建一个新的画面对象，直接使用上传的SVG内容
        const newScreen = {
          id: tempId,
          name: screenName,
          svgContent: svgContent,
          isModified: true,
          isNew: true
        };

        // 添加到画面列表
        screens.value.unshift(newScreen);

        // 选择该画面
        currentScreen.value = { ...newScreen };

        // 清空点位数据
        points.value = [];
        selectedPoint.value = null;

        // 清空添加元素状态
        addingElementType.value = '';

        // 设置标志位，忽略点位变化的监听
        ignorePointsChange.value = true;

        // 如果SVG查看器已加载，清空监测点容器
        if (svgViewerRef.value?.clearAllPoints) {
          console.log("清空SVG中的所有点位");
          svgViewerRef.value.clearAllPoints();
        }

        // 重置缩放
        zoom.value = 1;

        // 延迟重置SVG位置，确保SVG已加载
        setTimeout(() => {
          if (svgViewerRef.value?.resetPosition) {
            console.log("重置新建画面SVG位置");
            svgViewerRef.value.resetPosition();
          }

          // 重置点位变化标记
          ignorePointsChange.value = false;

          // 关闭加载状态
          screenLoading.value = false;
          console.log("新建画面加载完成，关闭loading状态");

          ElMessage.success('新建画面成功，SVG已上传');
        }, 300);
      },
      // 配置选项
      {
        acceptTypes: '.svg',
        showSuccessMessage: false,
        onCancel: () => {
          console.log('用户取消了SVG上传，中止新建画面');
          screenLoading.value = false;
          ElMessage.info('已取消新建画面');
        }
      }
    );
  } catch (error) {
    console.error('新建画面过程中出错:', error);
    screenLoading.value = false;
    ElMessage.error('新建画面失败: ' + (error instanceof Error ? error.message : String(error)));
  }
}

// 处理保存画面
const handleSaveScreen = async () => {
  if (!currentScreen.value) {
    ElMessage.error('没有选中的画面，无法保存');
    return;
  }

  try {
    console.log('准备保存画面:', currentScreen.value.id, currentScreen.value.name);

    // 开启加载状态
    screenLoading.value = true;

    // 获取最新的完整SVG内容（包含点位）
    let fullSvgContent: string = '';
    try {
      // 强制从SVG查看器获取完整的SVG内容（包含点位）
      if (svgViewerRef.value?.exportSvgWithPoints) {
        console.log('从SVG查看器获取完整SVG内容（包含点位）');

        // 确保获取完整SVG内容，不管是URL还是内联格式
        try {
          // 始终传递当前SVG内容或URL，让组件内部处理
          console.log('准备获取完整SVG内容，当前画面SVG类型:',
            currentScreen.value.svgUrl ? 'URL' : (currentScreen.value.svgContent ? '内联内容' : '无'));

          // 定义一个明确的字符串变量来存储结果
          let svgResult: string = '';

          // 优先使用内联内容，因为它可能已经包含了最新的点位
          if (currentScreen.value.svgContent) {
            console.log('使用内联SVG内容获取完整内容');
            // 使用类型断言确保TypeScript知道这是一个字符串
            svgResult = await svgViewerRef.value.exportSvgWithPoints(currentScreen.value.svgContent) as string;
          }
          // 如果没有内联内容但有URL，则使用URL
          else if (currentScreen.value.svgUrl) {
            console.log('使用SVG URL获取完整内容:', currentScreen.value.svgUrl);
            svgResult = await svgViewerRef.value.exportSvgWithPoints(currentScreen.value.svgUrl) as string;
          }
          // 如果两者都没有，则不传参数
          else {
            console.log('没有SVG内容或URL，使用默认导出方法');
            svgResult = await svgViewerRef.value.exportSvgWithPoints() as string;
          }

          // 将结果赋值给fullSvgContent
          fullSvgContent = svgResult;

          if (fullSvgContent && typeof fullSvgContent === 'string' && fullSvgContent.length > 0) {
            console.log('成功获取完整SVG内容，长度:', fullSvgContent.length);
          } else {
            console.warn('SVG查看器返回的内容为空或无效，尝试其他方法');
          }
        } catch (exportError) {
          console.error('从SVG查看器获取内容失败:', exportError);
        }
      }

      // 如果从查看器获取失败，尝试使用画面的内联内容
      if (!fullSvgContent && currentScreen.value.svgContent) {
        console.log('使用画面的内联SVG内容');
        fullSvgContent = currentScreen.value.svgContent;
      }

      // 确保SVG内容非空 - 如果以上方法都失败，尝试从URL直接获取
      if (!fullSvgContent || fullSvgContent.trim() === '') {
        console.warn('尝试从SVG URL直接获取内容');

        // 如果有URL，尝试直接获取
        if (currentScreen.value.svgUrl) {
          try {
            const response = await fetch(currentScreen.value.svgUrl);
            if (response.ok) {
              fullSvgContent = await response.text();
              console.log('通过直接请求URL成功获取SVG内容，长度:', fullSvgContent.length);
            } else {
              console.error('URL请求失败，状态码:', response.status);
            }
          } catch (fetchError) {
            console.error('从URL获取SVG内容失败:', fetchError);
          }
        }
      }

      // 最终检查SVG内容
      if (typeof fullSvgContent === 'string' && fullSvgContent.length > 0) {
        console.log('最终获取到的SVG内容长度:', fullSvgContent.length);
        console.log('SVG内容前100个字符:', fullSvgContent.substring(0, 100));
      } else {
        console.error('所有方法都无法获取到有效的SVG内容');
      }
    } catch (svgError) {
      console.error('获取SVG内容过程中出错:', svgError);
      ElMessage.error('获取SVG内容失败，请重试');
      // 关闭加载状态
      screenLoading.value = false;
      return; // 中断保存操作
    }

    // 再次确认SVG内容非空
    if (!fullSvgContent || fullSvgContent.trim() === '') {
      console.error('SVG内容为空，无法保存');
      ElMessage.error('无法获取有效的SVG内容，保存失败');
      screenLoading.value = false;
      return;
    }

    // 1. 准备保存的数据
    const screenData: any = {
      name: currentScreen.value.name,
      factoryId: appStore.getCurrentStation?.id, // 确保保存当前水厂ID
      svgContent: fullSvgContent // 使用完整的SVG内容，包含点位
    };

    // 如果不是新建画面，则添加ID字段
    if (!currentScreen.value.isNew) {
      screenData.id = currentScreen.value.id;
    } else {
      // 新建画面不传递ID，让后端生成
      console.log('新建画面，不传递前端临时ID');
    }

    // 处理点位数据 - 如果是新建画面，不传递screenId
    if (currentScreen.value.isNew) {
      // 新建画面不传递screenId
      screenData.points = convertPointsToApiFormat(points.value);
    } else {
      // 更新画面传递screenId
      screenData.points = convertPointsToApiFormat(points.value, currentScreen.value.id);
    }

    // 创建要发送的数据对象
    const screenDataToSend = { ...screenData };

    // 打印实际发送的数据
    console.log('实际发送给后端的数据:', {
      ...screenDataToSend,
      svgContent: screenDataToSend.svgContent ? `长度为${screenDataToSend.svgContent.length}的SVG内容` : '空',
      points: `${screenDataToSend.points.length}个点位`
    });

    console.log('准备保存的数据:',
      '画面ID:', screenData.id,
      '画面名称:', screenData.name,
      '水厂ID:', screenData.factoryId,
      '点位数量:', screenData.points.length
    );

    // 记录一个变量，用于在保存成功后重新加载
    const screenIdToLoad = currentScreen.value.id;

    let response;
    // 2. 根据画面是否为新建画面，调用不同的API
    if (currentScreen.value.isNew) {
      // 如果是新建画面，调用create API
      console.log('调用新增API保存新建画面');
      response = await saveScreen(screenDataToSend);
    } else {
      // 如果是已存在的画面，调用update API
      console.log('调用更新API保存已存在画面');
      response = await updateScreen(screenDataToSend);
    }

    console.log('保存画面响应:', response);

    // 统一处理API响应
    console.log('API响应完整结构:', JSON.stringify(response));

    // 现在saveScreen和updateScreen的返回格式已经统一
    // 两个函数都返回response.data
    let responseData: any = null;

    if (response) {
      // API响应数据
      responseData = response;
      console.log('响应数据:', JSON.stringify(responseData));
    }

    // 打印最终处理的数据
    console.log('处理后的响应数据:', responseData);

    // 尝试从API响应中提取状态码和消息
    if (responseData) {
      // 检查响应是否成功
      if (responseData.code === 200 || responseData.code === 0 || !responseData.code) {
        console.log('API响应成功，完整响应数据类型:', typeof responseData);

        // 处理直接返回ID值的情况（API直接返回ID数值）
        let newId: string | number = '';

        // 如果responseData本身就是数值或字符串，可能直接是ID
        if (typeof responseData === 'number' || (typeof responseData === 'string' && responseData)) {
          newId = responseData;
          console.log('API直接返回ID值:', newId);
        }
        // 否则尝试从对象中获取ID
        else if (typeof responseData === 'object' && responseData !== null) {
          // 安全地获取ID - 处理不同的响应结构
          if (responseData.id) {
            newId = responseData.id;
          } else if (responseData.screenId) {
            newId = responseData.screenId;
          }
          // 如果responseData.data存在且是简单值，可能直接是ID
          else if (responseData.data !== undefined) {
            if (typeof responseData.data === 'number' || (typeof responseData.data === 'string' && responseData.data)) {
              newId = responseData.data;
              console.log('从responseData.data直接获取ID值:', newId);
            }
            // 如果data是对象，尝试从中获取id
            else if (typeof responseData.data === 'object' && responseData.data !== null) {
              if (responseData.data.id) {
                newId = responseData.data.id;
              } else if (responseData.data.screenId) {
                newId = responseData.data.screenId;
              }
            }
          }
        }

        // 如果是更新操作且没有返回ID，使用当前画面ID
        if ((!newId && newId !== 0) && !currentScreen.value?.isNew) {
          newId = currentScreen.value?.id || '';
          console.log('使用当前画面ID:', newId);
        }

        console.log('最终确定的ID:', newId);

        // 保存成功后，立即重置当前画面的修改状态
        if (currentScreen.value) {
          console.log('重置当前画面的修改状态');
          currentScreen.value.isModified = false;

          // 如果是新建画面，移除isNew标记
          if (currentScreen.value.isNew) {
            currentScreen.value.isNew = false;
          }

          // 同步更新列表中对应画面的状态
          const index = screens.value.findIndex(s => String(s.id) === String(currentScreen.value?.id));
          if (index !== -1) {
            screens.value[index].isModified = false;
            if (screens.value[index].isNew) {
              screens.value[index].isNew = false;
            }
          }
        }

        if (!newId && newId !== 0) {
          console.warn('保存成功但未获取到画面ID，无法继续加载画面详情');
          ElMessage.success(responseData.msg || '保存画面成功，但无法自动刷新数据');
          screenLoading.value = false;
          return;
        }

        console.log('获取到后端返回的画面ID:', newId);

        // 记录是否为新建画面的标志
        const wasNewScreen = currentScreen.value?.isNew === true;

        // 显示初始成功消息
        ElMessage.success(responseData.msg || '保存画面成功');

        // 保存成功后，先重新获取画面列表
        console.log('保存成功，重新获取画面列表');
        try {
          // 获取当前水厂ID
          const factoryId = appStore.getCurrentStation?.id;
          if (!factoryId) {
            console.warn('未获取到水厂ID，无法刷新画面列表');
            screenLoading.value = false;
            return;
          }

          // 重新获取画面列表
          console.log(`开始获取水厂(${factoryId})的画面列表`);
          const screenList = await getScreenListByFactoryId(factoryId);
          console.log('获取到画面列表响应:', screenList);

          if (screenList && Array.isArray(screenList)) {
            console.log(`获取到${screenList.length}个画面，准备更新列表并选中ID=${newId}的画面`);

            // 更新画面列表
            screens.value = screenList.map((screen: any) => ({
              ...screen,
              isModified: false // 重置修改状态
            }));

            // 在新列表中查找保存的画面 - 转换ID类型进行比较
            const savedScreen = screens.value.find(s => String(s.id) === String(newId));
            if (savedScreen) {
              console.log('在画面列表中找到保存的画面，准备选中:', savedScreen);

              // 确保新获取的画面状态为已保存
              savedScreen.isModified = false;
              savedScreen.isNew = false;

              // 选中保存的画面 - 使用handleSelectScreen函数加载详情
              // 先设置一个标志，避免handleSelectScreen中的未保存检查
              const tempScreen = currentScreen.value;
              currentScreen.value = null; // 临时清空当前画面，避免未保存提示
              await handleSelectScreen(savedScreen);

              // 显示成功消息
              if (wasNewScreen) {
                ElMessage.success('新建画面已保存并加载');
              } else {
                ElMessage.success('画面更新已保存并刷新');
              }
            } else {
              console.warn(`未在返回的画面列表中找到ID=${newId}的画面，尝试直接获取详情`);
              console.log('画面列表中的ID:', screens.value.map(s => s.id));

              // 如果在列表中没找到，尝试直接获取画面详情
              try {
                // 获取画面详情
                console.log(`直接获取画面详情，ID=${newId}`);
                const screenDetail = await getScreenById(newId);
                console.log('获取到画面详情:', screenDetail);

                if (screenDetail) {
                  console.log('直接获取到画面详情，添加到列表并选中');

                  // 添加到列表
                  const newScreenItem = {
                    ...screenDetail,
                    id: newId,
                    isModified: false,
                    isNew: false
                  };
                  screens.value.unshift(newScreenItem);

                  // 选中该画面 - 先清空当前画面，避免未保存提示
                  currentScreen.value = null; // 临时清空当前画面，避免未保存提示
                  await handleSelectScreen(newScreenItem);
                } else {
                  console.warn('获取画面详情返回空数据');
                  ElMessage.warning('画面已保存，但获取详情失败，请刷新页面');
                }
              } catch (detailError) {
                console.error('获取画面详情失败:', detailError);
                ElMessage.warning('画面已保存，但无法加载详情，请刷新页面');
              }
            }
          } else {
            console.warn('获取画面列表失败或列表为空:', screenList);
            ElMessage.warning('画面已保存，但无法刷新列表，请刷新页面');
          }
        } catch (refreshError) {
          console.error('刷新画面列表失败:', refreshError);
          ElMessage.warning('画面已保存，但无法刷新数据，请刷新页面');
        }
      } else {
        // API返回错误状态
        const errorMsg = responseData.msg || '保存画面失败，服务器返回错误';
        console.error('API返回错误:', errorMsg, responseData);
        ElMessage.error(errorMsg);
      }
    } else {
      console.error('保存画面失败，响应数据为空');
      ElMessage.error('保存画面失败，未收到有效响应');
    }
  } catch (error) {
    console.error('保存画面失败:', error);
    ElMessage.error('保存画面失败: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    // 关闭加载状态
    screenLoading.value = false;
  }
}

// 处理更新画面名称
const handleUpdateScreenName = (newName: string) => {
  if (!currentScreen.value) {
    ElMessage.error('没有选中的画面，无法更新名称');
    return;
  }

  if (!newName || newName.trim() === '') {
    ElMessage.warning('画面名称不能为空');
    return;
  }

  try {
    console.log(`更新画面名称: ID=${currentScreen.value.id}, 旧名称=${currentScreen.value.name}, 新名称=${newName}`);

    // 更新当前画面名称
    currentScreen.value.name = newName;

    // 更新列表中的画面名称
    const index = screens.value.findIndex(s => s.id === currentScreen.value?.id);
    if (index !== -1) {
      screens.value[index].name = newName;
    }

    // 标记为已修改状态，提示用户需要保存
    if (!currentScreen.value.isModified) {
      currentScreen.value.isModified = true;

      // 同步更新列表中的状态
      if (index !== -1) {
        screens.value[index].isModified = true;
      }
    }

    ElMessage.success('画面名称已更新，请点击保存按钮保存所有修改');
  } catch (error) {
    console.error('更新画面名称失败:', error);
    ElMessage.error('更新画面名称失败: ' + (error instanceof Error ? error.message : String(error)));
  }
}

// 处理删除画面
const handleDeleteScreen = async (screen: Screen) => {
  if (!screen || !screen.id) {
    ElMessage.error('画面ID无效，无法删除');
    return;
  }

  try {
    // 对于新建但未保存的画面，直接从列表中移除
    if (screen.isNew) {
      console.log(`删除新建但未保存的画面: ID=${screen.id}, 名称=${screen.name}`);
      const index = screens.value.findIndex(s => s.id === screen.id);

      if (index !== -1) {
        screens.value.splice(index, 1);

        // 如果当前选中的是被删除的画面，重置选中状态
        if (currentScreen.value?.id === screen.id) {
          currentScreen.value = null;
          points.value = [];
          selectedPoint.value = null;

          // 如果还有其他画面，自动选中第一个
          if (screens.value.length > 0) {
            handleSelectScreen(screens.value[0]);
          }
        }

        ElMessage.success('删除画面成功');
      } else {
        ElMessage.warning('未找到要删除的画面');
      }
      return;
    }

    // 不再显示确认弹窗，因为ScreenList组件中已经有确认弹窗了
    // 直接调用API删除画面
    console.log(`删除画面: ID=${screen.id}, 名称=${screen.name}`);
    const response = await deleteScreen(screen.id);
    console.log('删除画面响应:', response);

    // 从列表中移除该画面
    const index = screens.value.findIndex(s => s.id === screen.id);
    if (index !== -1) {
      screens.value.splice(index, 1);

      // 如果当前选中的是被删除的画面，重置选中状态
      if (currentScreen.value?.id === screen.id) {
        currentScreen.value = null;
        points.value = [];
        selectedPoint.value = null;

        // 如果还有其他画面，自动选中第一个
        if (screens.value.length > 0) {
          handleSelectScreen(screens.value[0]);
        }
      }

      // 不再显示成功消息，因为ScreenList组件中已经显示了
    } else {
      ElMessage.warning('未找到要删除的画面');
    }
  } catch (error) {
    if (error === 'cancel') {
      console.log('用户取消了删除操作');
      return;
    }
    console.error('删除画面失败:', error);
    ElMessage.error('删除画面失败: ' + (error instanceof Error ? error.message : String(error)));
  }
}
</script>

<style scoped>
.screen-manage-container {
  height: 100%;
  display: flex;
  overflow: hidden;
}

.screen-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0;
  padding: 0;
  background-color: #fff;
}

.flow-chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.flow-chart-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
  height: calc(100vh - 126px);
  /* 调整高度: 考虑到顶栏和工具栏的高度 */
  margin: 0;
  padding: 0;
}

/* SVG显示区域占满整个空间（右侧监测点列表已隐藏） */
.flow-chart-content :deep(.svg-container) {
  flex: 1;
  width: 100%;
  min-width: 0;
  min-height: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  margin: 0;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

/* 确保SVG内容正确显示 */
.flow-chart-content :deep(.svg-content svg) {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.empty-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  margin: 0;
  padding: 0;
}

.empty-content {
  text-align: center;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
}

.empty-text {
  font-size: 20px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
}

.empty-desc {
  font-size: 14px;
  color: #909399;
  margin-bottom: 24px;
}

.empty-content .el-button {
  padding: 12px 28px;
  font-size: 16px;
  transition: all 0.3s;
}

/* 添加创建按钮样式 */
.empty-content .el-button.el-button--primary {
  background: linear-gradient(45deg, #409eff, #36d1dc);
  border: none;
  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.3);
  border-radius: 22px;
}

.empty-content .el-button.el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.5);
}

/* 加载状态覆盖层样式 */
.screen-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(64, 158, 255, 0.2);
  border-radius: 50%;
  border-top-color: #409eff;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 18px;
  color: #303133;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
