<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">出库管理</span>
          <div class="flex gap-2">
            <el-button @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>导出
            </el-button>
          </div>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-270px)] flex flex-col">
        <div class="w-full h-[50px] mb-2 gap-2 flex items-center">
          <el-form :model="searchForm" inline>
            <el-form-item label="仓库：">
              <el-select v-model="searchForm.warehouse" placeholder="请选择仓库" style="width: 200px">
                <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="出库状态：">
              <el-select v-model="searchForm.status" placeholder="请选择出库状态" style="width: 200px">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="物料名称：">
              <el-input v-model="searchForm.material" placeholder="请输入物料名称" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border height="100%">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="stockOutNo" label="出库单号" align="center" min-width="100" />
                <el-table-column prop="material" label="物料名称" align="center" min-width="100"
                  :show-overflow-tooltip="true" />
                <el-table-column prop="quantity" label="出库数量" align="center" width="100" />
                <el-table-column prop="applicant" label="申请人" align="center" width="120" />
                <el-table-column prop="applyTime" label="申请时间" align="center" width="160" sortable />
                <el-table-column prop="warehouse" label="出库仓库" align="center" width="140" />
                <el-table-column prop="status" label="状态" align="center" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === '待确认' ? 'warning' : 'success'">
                      {{ scope.row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="180" fixed="right">
                  <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">
                      详情
                    </el-button>
                    <el-button v-if="scope.row.status === '待确认'" type="success" link @click="handleConfirm(scope.row)">
                      确认出库
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end mt-2">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 确认出库弹窗 -->
    <el-dialog v-model="confirmDialogVisible" title="确认出库" width="500px" :close-on-click-modal="false">
      <div class="flex flex-col gap-4">
        <div class="flex items-center">
          <span class="w-[100px]">出库单号：</span>
          <span>{{ currentRow.stockOutNo }}</span>
        </div>
        <div class="flex items-center">
          <span class="w-[100px]">物料名称：</span>
          <span>{{ currentRow.material }}</span>
        </div>
        <div class="flex items-center">
          <span class="w-[100px]">出库数量：</span>
          <span>{{ currentRow.quantity }}</span>
        </div>
        <div class="flex items-center">
          <span class="w-[100px]">申请人：</span>
          <span>{{ currentRow.applicant }}</span>
        </div>
        <div class="flex items-center">
          <span class="w-[100px]">备注：</span>
          <el-input v-model="confirmForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="confirmDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 定义出库单类型接口
interface StockOutItem {
  stockOutNo: string
  material: string
  quantity: number
  applicant: string
  applyTime: string
  warehouse: string
  status: string
}

// 搜索表单数据
const searchForm = reactive({
  warehouse: '',
  status: '',
  material: ''
})

// 仓库选项
const warehouseOptions = [
  { value: '1007仓库名称', label: '1007仓库名称' },
  { value: '广仓库', label: '广仓库' },
  { value: '江东水厂1号仓库', label: '江东水厂1号仓库' }
]

// 出库状态选项
const statusOptions = [
  { value: '待确认', label: '待确认' },
  { value: '已出库', label: '已出库' }
]

// 表格数据
const tableData = ref<StockOutItem[]>([
  {
    stockOutNo: 'OUT-20241030-001',
    material: '三角带',
    quantity: 2,
    applicant: '张三',
    applyTime: '2024-10-30 09:37:56',
    warehouse: '1007仓库名称',
    status: '待确认'
  },
  {
    stockOutNo: 'OUT-20241030-002',
    material: '水泵',
    quantity: 1,
    applicant: '李四',
    applyTime: '2024-10-30 10:37:56',
    warehouse: '广仓库',
    status: '已出库'
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(2)

// 确认出库相关
const confirmDialogVisible = ref(false)
const currentRow = ref<StockOutItem>({
  stockOutNo: '',
  material: '',
  quantity: 0,
  applicant: '',
  applyTime: '',
  warehouse: '',
  status: ''
})
const confirmForm = reactive({
  remark: ''
})

// 搜索方法
const handleSearch = () => {
  console.log('搜索条件：', searchForm)
}

// 重置方法
const handleReset = () => {
  searchForm.warehouse = ''
  searchForm.status = ''
  searchForm.material = ''
}

// 导出方法
const handleExport = () => {
  console.log('导出数据')
}

// 详情方法
const handleDetail = (row: StockOutItem) => {
  console.log('查看详情：', row)
}

// 确认出库方法
const handleConfirm = (row: StockOutItem) => {
  currentRow.value = row
  confirmDialogVisible.value = true
}

// 提交确认
const submitConfirm = () => {
  console.log('确认出库：', {
    ...currentRow.value,
    remark: confirmForm.remark
  })
  ElMessage.success('出库确认成功')
  confirmDialogVisible.value = false
  // 重置表单
  confirmForm.remark = ''
  // 刷新列表
  handleSearch()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
