<template>
  <div class="drain-title">
    <div class="wrapper">
      <div class="content">
        <span class="title">{{ title }}</span>
        <span class="title-content" v-if="unit">{{ unit }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  unit?: string
}

defineProps<Props>()
</script>
<style scoped lang="scss">
.drain-title {
  height: 36px;

  .wrapper {
    padding: 10px 0px 0px !important;

    .content {
      height: 26px;
      line-height: 26px;
      // background-image: url(../../assets/images/title-bg-long.png);
      background-repeat: no-repeat;
      background-size: auto 100%;
      padding-left: 17px;

      .title {
        font-size: 14px;
        color: rgb(3, 94, 187);
        font-weight: 700;
        float: left;
      }

      .title-content {
        font-size: 10px;
        /* color: rgb(144, 147, 153); */
        color: white;
        margin-left: 8px;
        float: right;
      }
    }
  }
}
</style>
