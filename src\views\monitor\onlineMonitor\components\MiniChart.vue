<template>
  <div ref="chartRef" :style="{height: `${height}px`}" class="w-full"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import type { EChartsType } from 'echarts'

interface ChartDataItem {
  time: string;
  value: number;
}

const props = defineProps({
  data: {
    type: Array as () => ChartDataItem[],
    default: () => []
  },
  height: {
    type: Number,
    default: 50
  }
})

const chartRef = ref<HTMLElement | null>(null)
let chart: EChartsType | null = null

function initChart() {
  if (!chartRef.value) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  updateChart()
}

function updateChart() {
  if (!chart) return
  
  const xData = props.data.map(item => item.time)
  const yData = props.data.map(item => item.value)
  
  const option = {
    animation: false,
    grid: {
      top: 5,
      right: 5,
      bottom: 5,
      left: 5,
      containLabel: false
    },
    xAxis: {
      type: 'category',
      data: xData,
      show: false
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        data: yData,
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 2
        },
        areaStyle: {
          opacity: 0.2
        }
      }
    ]
  }
  
  chart.setOption(option)
}

onMounted(() => {
  initChart()
})

watch(() => props.data, () => {
  updateChart()
}, { deep: true })
</script>

<style scoped>
</style> 