<template>
  <div class="screen-list-container">
    <div class="screen-list-header">
      <div class="title">画面列表</div>
      <el-button type="primary" size="small" @click="handleAddScreen">
        <el-icon>
          <Plus />
        </el-icon> 新建画面
      </el-button>
    </div>

    <el-scrollbar class="screen-list-scrollbar">
      <div class="screen-list">
        <template v-if="screens.length === 0">
          <div class="empty-tip">
            <el-empty :image-size="80" description="暂无画面数据"
              :image="'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik05MC42NDcgNTUuMDM0djlINjYuOTh2MzMuNjY3SDMwLjY0N3YtNzBINzguOThjNi40NDcgMCAxMS42NjcgNS4yMiAxMS42NjcgMTEuNjY3djE1LjY2NnoiIGZpbGw9IiNFOUVDRUYiLz48cGF0aCBkPSJNOTcuMzE0IDI3LjcwMXY3MGgtODN2LTcwaDgzem0tMzMgMjguNjY2djlIODAuNjQ3djMzLjY2N0g0NC4zMTR2LTQyLjY2N3oiIGZpbGw9IiNGQUZCRkMiLz48L2c+PC9zdmc+'">
              <el-button type="primary" @click="handleAddScreen">新建画面</el-button>
            </el-empty>
          </div>
        </template>

        <template v-else>
          <div v-for="item in screens" :key="item.id" class="screen-item"
            :class="{ active: activeScreenId === item.id }" @click="handleSelectScreen(item)">
            <div class="screen-item-content">
              <div class="screen-info">
                <span class="screen-name">{{ item.name }}</span>
                <span class="screen-update-time" v-if="item.updateTime">更新: {{ formatTime(item.updateTime) }}</span>
              </div>
              <div class="screen-item-actions">
                <el-icon class="delete-icon" @click.stop="handleDeleteScreen(item)"
                  style="color: red; font-size: 22px;">
                  <Delete />
                </el-icon>
              </div>
            </div>

            <!-- 未保存标记 -->
            <span v-if="item.isModified" class="modified-mark">*</span>
          </div>
        </template>
      </div>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Delete, Document, Plus } from '@element-plus/icons-vue'

// 定义屏幕对象类型
interface Screen {
  id: string | number;
  name: string;
  svgContent?: string;
  svgUrl?: string;
  isModified?: boolean;
  isNew?: boolean;
  updateTime?: string;
}

// 定义组件的props
const props = defineProps({
  screens: {
    type: Array as () => Screen[],
    default: () => []
  },
  activeScreenId: {
    type: [String, Number],
    default: ''
  }
})

// 定义组件的事件
const emit = defineEmits<{
  (e: 'add'): void
  (e: 'select', screen: Screen): void
  (e: 'delete', screen: Screen): void
}>()

// 处理画面选择
const handleSelectScreen = (screen: Screen) => {
  emit('select', screen)
}

// 处理新增画面
const handleAddScreen = () => {
  emit('add')
}

// 处理删除画面
const handleDeleteScreen = (screen: Screen) => {
  // 根据画面是否是新建未保存的，提供不同的提示
  const message = screen.isNew
    ? `画面"${screen.name}"是新建未保存的，删除后将无法恢复，是否确认删除？`
    : `确定要删除画面"${screen.name}"吗？`;

  const title = screen.isNew ? '删除未保存画面' : '删除确认';

  ElMessageBox.confirm(
    message,
    title,
    {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    emit('delete', screen)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除
  })
}

// 格式化时间
const formatTime = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.toLocaleString();
}
</script>

<style scoped>
.screen-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-right: 1px solid #e4e7ed;
  background: #fff;
  width: 250px;
  min-width: 250px;
}

.screen-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  height: 56px;
  box-sizing: border-box;
}

.screen-list-header .title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.screen-list-scrollbar {
  flex: 1;
  overflow: hidden;
}

.screen-list {
  padding: 0;
}

.screen-item {
  position: relative;
  padding: 10px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.3s;
}

.screen-item:hover {
  background-color: #f5f7fa;
}

.screen-item.active {
  background-color: #ecf5ff;
}

.screen-item-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.screen-name {
  font-size: 14px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.screen-item.active .screen-name {
  color: #409eff;
  font-weight: 500;
}

.screen-item-actions {
  display: none;
  margin-left: 8px;
}

.screen-item:hover .screen-item-actions {
  display: flex;
}

.screen-item-actions i {
  color: #909399;
  font-size: 16px;
  padding: 4px;
  transition: color 0.3s;
}

.screen-item-actions i:hover {
  color: #f56c6c;
}

.delete-icon {
  color: #909399;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.delete-icon:hover {
  color: #f56c6c;
  background-color: rgba(245, 108, 108, 0.1);
}

.modified-mark {
  color: #f56c6c;
  margin-left: 4px;
  font-weight: bold;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
  height: 100%;
}

.screen-info {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
  flex: 1;
  overflow: hidden;
}

.screen-update-time {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}
</style>