<template>
    <div>
        <el-dialog v-model="dialogVisible" ref="dialog" :before-close="handleClose" width="50%" center title="新增告警规则">
            <div class="w-full p-4">
                <!-- 选择类型 -->
                <div class="w-full mb-6">
                    <div class="font-bold text-lg mb-4">选择告警级别</div>
                    <div class="flex gap-4">
                        <el-radio-group v-model="selectedType" size="large">
                            <el-radio-button label="device">设备级</el-radio-button>
                            <el-radio-button label="water">水质级</el-radio-button>
                        </el-radio-group>
                    </div>
                </div>

                <!-- 表单部分 -->
                <div v-if="selectedType" class="w-full">
                    <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
                        <el-card class="mb-4">
                            <template #header>
                                <div class="card-header">
                                    <div class="flex items-center">
                                        <span class="font-bold">基本信息</span>
                                    </div>
                                </div>
                            </template>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="指标名称" prop="name">
                                        <el-input v-model="form.name" placeholder="请输入指标名称" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="告警类型">
                                        <el-input v-model="form.type" disabled />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="指标单位">
                                        <el-input v-model="form.unit" placeholder="请输入单位（可选）" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="指标编码">
                                        <el-input v-model="form.code" placeholder="请输入指标编码（可选）" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-card>

                        <el-card class="mb-4">
                            <template #header>
                                <div class="card-header">
                                    <div class="flex items-center">
                                        <span class="font-bold">告警条件设置</span>
                                    </div>
                                </div>
                            </template>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="条件类型" prop="conditionType">
                                        <el-select v-model="form.conditionType" placeholder="请选择条件类型">
                                            <el-option label="大于" value="greater" />
                                            <el-option label="小于" value="less" />
                                            <el-option label="等于" value="equal" />
                                            <el-option label="区间" value="between" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="阈值" prop="value">
                                        <template v-if="form.conditionType === 'between' ">
                                            <el-input v-model="form.minValue" placeholder="最小值" style="width: 100px; margin-right: 8px;" />
                                            <span>~</span>
                                            <el-input v-model="form.maxValue" placeholder="最大值" style="width: 100px; margin-left: 8px;" />
                                        </template>
                                        <template v-else>
                                            <el-input v-model="form.value" placeholder="请输入阈值" style="width: 220px;" />
                                        </template>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="持续时间(分钟)">
                                        <el-input-number v-model="form.duration" :min="1" :max="60" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-card>

                        <el-card>
                            <template #header>
                                <div class="card-header">
                                    <div class="flex items-center">
                                        <span class="font-bold">有效范围设置</span>
                                    </div>
                                </div>
                            </template>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="有效上限" prop="upperLimit">
                                        <el-input v-model="form.upperLimit" placeholder="请输入有效上限">
                                            <template #append v-if="form.unit">{{ form.unit }}</template>
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="有效下限" prop="lowerLimit">
                                        <el-input v-model="form.lowerLimit" placeholder="请输入有效下限">
                                            <template #append v-if="form.unit">{{ form.unit }}</template>
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-card>
                    </el-form>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirm" :disabled="!selectedType">确认添加</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script lang='ts' setup>
import { reactive, ref, watch } from 'vue';
import { ElMessage } from 'element-plus'

// 定义emit
const emit = defineEmits(['add-rules'])

const dialogVisible = ref(false)
const formRef = ref()
const selectedType = ref('')

// 表单数据
const form = reactive({
    name: '',               // 指标名称
    type: '',               // 告警类型
    unit: '',               // 指标单位
    code: '',               // 指标编码
    conditionType: '',      // 条件类型
    value: '',              // 单值
    minValue: '',           // 区间下限
    maxValue: '',           // 区间上限
    upperLimit: '',         // 有效上限
    lowerLimit: '',         // 有效下限
    duration: 5             // 持续时间（分钟）
})

// 表单验证规则
const rules = {
    name: [{ required: true, message: '请输入指标名称', trigger: 'blur' }],
    conditionType: [{ required: true, message: '请选择条件类型', trigger: 'change' }],
    value: [
        { required: true, message: '请输入阈值', trigger: 'blur',
          validator: (rule, value, callback) => {
            if (form.conditionType === 'between') {
              if (!form.minValue || !form.maxValue) {
                callback(new Error('请输入区间阈值'))
              } else {
                callback()
              }
            } else {
              if (!form.value) {
                callback(new Error('请输入阈值'))
              } else {
                callback()
              }
            }
          }
        }
    ],
    upperLimit: [{ required: true, message: '请输入有效上限', trigger: 'blur' }],
    lowerLimit: [{ required: true, message: '请输入有效下限', trigger: 'blur' }]
}

// 监听类型变化，重置表单并设置对应的告警类型
watch(selectedType, () => {
    resetForm()
    if (selectedType.value === 'device') {
        form.type = '设备级'
        form.unit = 'V' // 设备类型默认单位为电压
    } else if (selectedType.value === 'water') {
        form.type = '水质级'
        form.unit = 'mg/L' // 水质类型默认单位
    }
})

const handleConfirm = () => {
    if (!selectedType.value) {
        ElMessage.warning('请先选择告警级别')
        return
    }

    formRef.value.validate((valid) => {
        if (valid) {
            // 生成规则数据
            const newRule = {
                name: form.name,
                type: form.type,
                conditionType: form.conditionType,
                value: form.value,
                minValue: form.minValue,
                maxValue: form.maxValue,
                duration: form.duration,
                code: form.code || '',
                unit: form.unit || '',
                upperLimit: form.unit ? `${form.upperLimit}${form.unit}` : form.upperLimit,
                lowerLimit: form.unit ? `${form.lowerLimit}${form.unit}` : form.lowerLimit,
                category: selectedType.value === 'device' ? '设备级' : '水质级'
            }

            // 触发事件通知父组件
            emit('add-rules', [newRule])

            ElMessage.success('添加告警规则成功')
            resetForm()
            dialogVisible.value = false
        } else {
            ElMessage.error('请完善表单信息')
            return false
        }
    })
}

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields()
    }
    
    Object.keys(form).forEach(key => {
        if (key === 'duration') {
            form[key] = 5
        } else {
            form[key] = ''
        }
    })
}

function handleClose() {
    resetForm()
    selectedType.value = ''
    dialogVisible.value = false
}

function setVisible() {
    dialogVisible.value = true
}

defineExpose({
    setVisible
})
</script>
<style scoped lang='scss'>
:deep(.el-card) {
  border-radius: 4px;
  margin-bottom: 16px;
}

:deep(.el-card__header) {
  padding: 12px;
  font-weight: bold;
  background-color: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}
</style>