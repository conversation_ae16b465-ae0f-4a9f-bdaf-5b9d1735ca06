<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">评估通知</span>
          <el-button type="primary" @click="handleAdd">添加通知</el-button>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex flex-col">
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="评估方案" style="width: 200px;">
              <el-select v-model="searchForm.scheme" placeholder="请选择评估方案" clearable>
                <el-option v-for="item in schemeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="评估对象" style="width: 200px;">
              <el-select v-model="searchForm.target" placeholder="请选择评估对象" clearable>
                <el-option v-for="item in targetOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" style="width: 200px;">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="未开始" value="pending" />
                <el-option label="进行中" value="running" />
                <el-option label="已结束" value="completed" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="relative w-full h-full">
          <div class="absolute w-full h-full">
            <el-table :data="tableData" border style="width: 100%">
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="scheme" label="评估方案" min-width="150" />
              <el-table-column prop="target" label="评估对象" min-width="120" />
              <el-table-column prop="startTime" label="开始时间" width="160" align="center" />
              <el-table-column prop="endTime" label="结束时间" width="160" align="center" />
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getStatusTag(row.status)">{{ getStatusText(row.status) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="creator" label="创建人" width="100" align="center" />
              <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
              <el-table-column label="操作" width="200" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                  <el-button type="primary" link @click="handleDetail(row)">详情</el-button>
                  <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>

    <!-- 通知表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '添加通知' : '编辑通知'" width="600px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="评估方案" prop="scheme">
          <el-select v-model="form.scheme" placeholder="请选择评估方案">
            <el-option v-for="item in schemeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="评估对象" prop="target">
          <el-select v-model="form.target" placeholder="请选择评估对象">
            <el-option v-for="item in targetOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker v-model="form.startTime" type="datetime" placeholder="请选择开始时间" />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker v-model="form.endTime" type="datetime" placeholder="请选择结束时间" />
        </el-form-item>
        <el-form-item label="通知说明" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入通知说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 通知详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="通知详情" width="600px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="评估方案">{{ selectedItem?.scheme }}</el-descriptions-item>
        <el-descriptions-item label="评估对象">{{ selectedItem?.target }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ selectedItem?.startTime }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ selectedItem?.endTime }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTag(selectedItem?.status)">
            {{ getStatusText(selectedItem?.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建人">{{ selectedItem?.creator }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ selectedItem?.createTime }}</el-descriptions-item>
        <el-descriptions-item label="通知说明">{{ selectedItem?.description }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  scheme: '',
  target: '',
  status: ''
})

// 评估方案选项
const schemeOptions = [
  { label: '电力系统运行评估方案', value: 'power' },
  { label: '水处理系统评估方案', value: 'water' },
  { label: '环境监测评估方案', value: 'environment' }
]

// 评估对象选项
const targetOptions = [
  { label: '主变压器', value: 'transformer' },
  { label: '水泵', value: 'pump' },
  { label: '风机', value: 'fan' },
  { label: '污水处理设备', value: 'sewage' }
]

// 表格数据
const tableData = ref([
  {
    id: '1',
    scheme: '电力系统运行评估方案',
    target: '主变压器',
    startTime: '2024-04-21 08:00:00',
    endTime: '2024-04-21 18:00:00',
    status: 'pending',
    creator: '张三',
    createTime: '2024-04-20 10:00:00',
    description: '对主变压器进行运行状态评估'
  },
  {
    id: '2',
    scheme: '水处理系统评估方案',
    target: '水泵',
    startTime: '2024-04-22 09:00:00',
    endTime: '2024-04-22 17:00:00',
    status: 'running',
    creator: '李四',
    createTime: '2024-04-21 14:00:00',
    description: '对水泵进行性能评估'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref<FormInstance>()
const selectedItem = ref<any>(null)

// 表单数据
const form = reactive({
  scheme: '',
  target: '',
  startTime: '',
  endTime: '',
  description: ''
})

// 表单验证规则
const rules = {
  scheme: [{ required: true, message: '请选择评估方案', trigger: 'change' }],
  target: [{ required: true, message: '请选择评估对象', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
}

// 方法
const getStatusTag = (status: string) => {
  switch (status) {
    case 'pending':
      return 'info'
    case 'running':
      return 'success'
    case 'completed':
      return 'warning'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '未开始'
    case 'running':
      return '进行中'
    case 'completed':
      return '已结束'
    default:
      return '未知'
  }
}

const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', searchForm)
}

const resetSearch = () => {
  searchForm.scheme = ''
  searchForm.target = ''
  searchForm.status = ''
}

const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
  Object.assign(form, {
    scheme: '',
    target: '',
    startTime: '',
    endTime: '',
    description: ''
  })
}

const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  Object.assign(form, row)
}

const handleDetail = (row: any) => {
  selectedItem.value = row
  detailDialogVisible.value = true
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该通知吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实现删除逻辑
    ElMessage.success('删除成功')
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      // 实现提交逻辑
      console.log('表单数据:', form)
      dialogVisible.value = false
      ElMessage.success(dialogType.value === 'add' ? '添加成功' : '编辑成功')
    }
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
