<template>
  <div class="screen-config">
    <el-form label-width="100px" label-position="left">
      <el-form-item label="画面名称">
        <el-input v-model="screenData.name" placeholder="请输入画面名称" />
      </el-form-item>

      <el-form-item label="所属站点">
        <el-select v-model="screenData.stationId" placeholder="请选择站点" style="width: 100%">
          <el-option v-for="station in stationOptions" :key="station.id" :label="station.name" :value="station.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="SVG图片">
        <svg-upload v-model="screenData.svg" @svg-size="handleSvgSize" />
      </el-form-item>

      <el-divider content-position="left">颜色配置</el-divider>

      <el-form-item label="状态颜色">
        <div class="color-group">
          <color-picker v-model="screenData.colorConfig.running" label="运行" />
          <color-picker v-model="screenData.colorConfig.stopped" label="停止" />
          <color-picker v-model="screenData.colorConfig.alarm" label="告警" />
        </div>
      </el-form-item>

      <el-form-item label="测点类型颜色">
        <div class="color-group">
          <color-picker v-model="screenData.colorConfig.flow" label="流量" />
          <color-picker v-model="screenData.colorConfig.level" label="液位" />
          <color-picker v-model="screenData.colorConfig.ph" label="PH值" />
          <color-picker v-model="screenData.colorConfig.other" label="其他" />
        </div>
      </el-form-item>
    </el-form>

    <div class="form-actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { reactive, ref, watch } from 'vue';
import ColorPicker from './ColorPicker.vue';
import SvgUpload from './SvgUpload.vue';
import { ColorConfig, Screen, Station } from './types';

// 定义属性
const props = defineProps({
  modelValue: {
    type: Object as () => Screen | null,
    default: null
  },
  stationOptions: {
    type: Array as () => Station[],
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'save', 'cancel']);

// 保存状态
const saving = ref(false);

// 默认颜色配置
const defaultColorConfig: ColorConfig = {
  running: '#67C23A', // 绿色
  stopped: '#909399', // 灰色
  alarm: '#F56C6C',   // 红色
  flow: '#409EFF',    // 蓝色
  level: '#67C23A',   // 绿色
  ph: '#E6A23C',      // 黄色
  other: '#909399'    // 灰色
};

// 屏幕数据
const screenData = reactive<Screen>({
  id: '',
  name: '',
  svg: '',
  points: [],
  stationId: undefined,
  svgWidth: 0,
  svgHeight: 0,
  colorConfig: { ...defaultColorConfig }
});

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 深度复制，避免直接修改props
    screenData.id = newVal.id || '';
    screenData.name = newVal.name || '';
    screenData.svg = newVal.svg || '';
    screenData.points = [...(newVal.points || [])];
    screenData.stationId = newVal.stationId;
    screenData.svgWidth = newVal.svgWidth || 0;
    screenData.svgHeight = newVal.svgHeight || 0;

    // 颜色配置
    if (newVal.colorConfig) {
      screenData.colorConfig = {
        ...defaultColorConfig,
        ...newVal.colorConfig
      };
    } else {
      screenData.colorConfig = { ...defaultColorConfig };
    }
  } else {
    // 重置为默认值
    resetData();
  }
}, { immediate: true, deep: true });

// 重置数据
const resetData = () => {
  screenData.id = '';
  screenData.name = '';
  screenData.svg = '';
  screenData.points = [];
  screenData.stationId = undefined;
  screenData.svgWidth = 0;
  screenData.svgHeight = 0;
  screenData.colorConfig = { ...defaultColorConfig };
};

// 处理SVG尺寸
const handleSvgSize = (size: { width: number, height: number }) => {
  screenData.svgWidth = size.width;
  screenData.svgHeight = size.height;
};

// 验证表单
const validateForm = () => {
  if (!screenData.name) {
    ElMessage.warning('请输入画面名称');
    return false;
  }

  if (!screenData.svg) {
    ElMessage.warning('请上传SVG图片');
    return false;
  }

  return true;
};

// 保存
const handleSave = () => {
  if (!validateForm()) return;

  saving.value = true;

  try {
    // 发送保存事件
    emit('save', JSON.parse(JSON.stringify(screenData)));
  } catch (error) {
    console.error('保存失败', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped lang="scss">
.screen-config {
  padding: 20px;
}

.color-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .color-picker {
    flex: 1;
    min-width: 120px;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
  gap: 10px;
}
</style>