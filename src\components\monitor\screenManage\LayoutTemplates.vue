<template>
  <div class="layout-templates">
    <div v-for="(layout, index) in layoutOptions" :key="index"
      :class="['layout-option', modelValue === layout.value ? 'active' : '']"
      @click="$emit('update:modelValue', layout.value)">
      <div class="layout-icon">
        <svg viewBox="0 0 60 60" width="50" height="50">
          <!-- 图标背景 -->
          <rect x="5" y="5" width="50" height="50" fill="#f5f7fa" rx="4" />

          <!-- 中心点/图标 -->
          <circle cx="30" cy="30" r="6" :fill="modelValue === layout.value ? '#409eff' : '#909399'" />

          <!-- 标签和数值 -->
          <template v-if="layout.value === 'right'">
            <rect x="38" y="22" width="16" height="8" rx="2"
              :fill="modelValue === layout.value ? '#a0cfff' : '#c0c4cc'" />
            <rect x="38" y="32" width="16" height="8" rx="2"
              :fill="modelValue === layout.value ? '#a0cfff' : '#c0c4cc'" />
          </template>

          <template v-if="layout.value === 'left'">
            <rect x="6" y="22" width="16" height="8" rx="2"
              :fill="modelValue === layout.value ? '#a0cfff' : '#c0c4cc'" />
            <rect x="6" y="32" width="16" height="8" rx="2"
              :fill="modelValue === layout.value ? '#a0cfff' : '#c0c4cc'" />
          </template>

          <template v-if="layout.value === 'bottom'">
            <rect x="22" y="38" width="16" height="8" rx="2"
              :fill="modelValue === layout.value ? '#a0cfff' : '#c0c4cc'" />
            <rect x="22" y="48" width="16" height="8" rx="2"
              :fill="modelValue === layout.value ? '#a0cfff' : '#c0c4cc'" />
          </template>

          <template v-if="layout.value === 'top'">
            <rect x="22" y="4" width="16" height="8" rx="2"
              :fill="modelValue === layout.value ? '#a0cfff' : '#c0c4cc'" />
            <rect x="22" y="14" width="16" height="8" rx="2"
              :fill="modelValue === layout.value ? '#a0cfff' : '#c0c4cc'" />
          </template>

          <template v-if="layout.value === 'valueOnly'">
            <rect x="22" y="38" width="16" height="8" rx="2"
              :fill="modelValue === layout.value ? '#a0cfff' : '#c0c4cc'" />
          </template>
        </svg>
      </div>
      <div class="layout-name" :class="{ 'active-text': modelValue === layout.value }">
        {{ layout.label }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { LayoutOption, LayoutTemplateType } from './types';

// 定义属性
const props = defineProps({
  modelValue: {
    type: String as () => LayoutTemplateType,
    default: 'right'
  },
  layoutOptions: {
    type: Array as () => LayoutOption[],
    required: true
  }
});

// 定义事件
defineEmits(['update:modelValue']);
</script>

<style scoped lang="scss">
.layout-templates {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.layout-option {
  cursor: pointer;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  text-align: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f7ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  &.active {
    border-color: #409eff;
    background-color: #ecf5ff;
  }
}

.layout-icon {
  position: relative;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.layout-name {
  font-size: 12px;
  margin-top: 5px;
  color: #606266;

  &.active-text {
    color: #409eff;
    font-weight: bold;
  }
}
</style>