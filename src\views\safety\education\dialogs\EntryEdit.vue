<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="80%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-tabs v-model="activeTabName">
        <el-tab-pane label="基本信息" name="basic">
          <el-row>
            <el-col :span="8">
              <el-form-item label="员工姓名" prop="employeeName">
                <el-input v-model="formData.employeeName" placeholder="请输入员工姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属部门" prop="departmentId">
                <el-tree-select
                  v-model="formData.departmentId"
                  :data="departmentOptions"
                  placeholder="请选择所属部门"
                  check-strictly
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="岗位类型" prop="postType">
                <el-select v-model="formData.postType" placeholder="请选择岗位类型" style="width: 100%">
                  <el-option label="操作工" value="operator" />
                  <el-option label="电工" value="electrician" />
                  <el-option label="焊工" value="welder" />
                  <el-option label="车间管理" value="workshop-manager" />
                  <el-option label="行政人员" value="admin-staff" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="入职日期" prop="entryDate">
                <el-date-picker
                  v-model="formData.entryDate"
                  type="date"
                  placeholder="选择入职日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="教育类型" prop="eduType">
                <el-select v-model="formData.eduType" placeholder="请选择教育类型" style="width: 100%">
                  <el-option label="公司级" value="company" />
                  <el-option label="车间级" value="workshop" />
                  <el-option label="班组级" value="team" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="完成状态" prop="completeStatus">
                <el-select v-model="formData.completeStatus" placeholder="请选择完成状态" style="width: 100%">
                  <el-option label="未开始" value="not-started" />
                  <el-option label="进行中" value="in-progress" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已过期" value="expired" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="教育时间" prop="eduTime">
                <el-input v-model="formData.eduTime" placeholder="例如：2023-10-01 09:00-11:30" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="教育课时" prop="eduHours">
                <el-input-number
                  v-model="formData.eduHours"
                  :min="0.5"
                  :max="100"
                  :step="0.5"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="教育老师" prop="teacher">
                <el-input v-model="formData.teacher" placeholder="请输入教育老师" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核成绩" prop="examScore">
                <el-input-number
                  v-model="formData.examScore"
                  :min="0"
                  :max="100"
                  :step="1"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核时间" prop="examTime">
                <el-date-picker
                  v-model="formData.examTime"
                  type="datetime"
                  placeholder="选择考核时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="教育内容" prop="eduContent">
                <el-input
                  v-model="formData.eduContent"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入教育内容"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        
        <el-tab-pane label="教育资料上传" name="materials">
          <el-row>
            <el-col :span="24">
              <el-form-item label="教育卡上传">
                <el-upload
                  v-model:file-list="eduCardFiles"
                  :action="uploadAction"
                  :before-upload="beforeUpload"
                  :on-success="(response, file) => handleUploadSuccess(response, file, 'edu-card')"
                  :on-error="handleUploadError"
                  :limit="1"
                  :auto-upload="true"
                  accept=".jpg,.jpeg,.png,.pdf"
                >
                  <el-button type="primary">上传教育卡</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持JPG/PNG/PDF格式，单个文件不超过10MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row>
            <el-col :span="24">
              <el-form-item label="考试试卷上传">
                <el-upload
                  v-model:file-list="examPaperFiles"
                  :action="uploadAction"
                  :before-upload="beforeUpload"
                  :on-success="(response, file) => handleUploadSuccess(response, file, 'exam-paper')"
                  :on-error="handleUploadError"
                  :limit="1"
                  :auto-upload="true"
                  accept=".jpg,.jpeg,.png,.pdf"
                >
                  <el-button type="primary">上传考试试卷</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持JPG/PNG/PDF格式，单个文件不超过10MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row>
            <el-col :span="24">
              <el-form-item label="签到表上传">
                <el-upload
                  v-model:file-list="signSheetFiles"
                  :action="uploadAction"
                  :before-upload="beforeUpload"
                  :on-success="(response, file) => handleUploadSuccess(response, file, 'sign-sheet')"
                  :on-error="handleUploadError"
                  :limit="1"
                  :auto-upload="true"
                  accept=".jpg,.jpeg,.png,.pdf"
                >
                  <el-button type="primary">上传签到表</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持JPG/PNG/PDF格式，单个文件不超过10MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row>
            <el-col :span="24">
              <el-form-item label="其他资料上传">
                <el-upload
                  v-model:file-list="otherFiles"
                  :action="uploadAction"
                  :before-upload="beforeUpload"
                  :on-success="(response, file) => handleUploadSuccess(response, file, 'other')"
                  :on-error="handleUploadError"
                  :limit="5"
                  multiple
                  :auto-upload="true"
                  accept=".jpg,.jpeg,.png,.gif,.doc,.docx,.xlsx,.pptx,.pdf,.mp4"
                >
                  <el-button type="primary">上传其他资料</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持多种文件格式，单个文件不超过50MB，最多上传5个文件
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-divider content-position="center">已上传资料</el-divider>
          
          <el-table :data="materialsList" :stripe="true">
            <el-table-column label="资料名称" prop="fileName" />
            <el-table-column label="资料类型" prop="fileType" width="120">
              <template #default="scope">
                <el-tag v-if="scope.row.fileType === 'edu-card'">教育卡</el-tag>
                <el-tag v-else-if="scope.row.fileType === 'exam-paper'" type="success">试卷</el-tag>
                <el-tag v-else-if="scope.row.fileType === 'sign-sheet'" type="warning">签到表</el-tag>
                <el-tag v-else type="info">其他资料</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="上传时间" prop="uploadTime" width="180" />
            <el-table-column label="操作" align="center" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="handleViewFile(scope.row)">预览</el-button>
                <el-button link type="danger" @click="handleDeleteFile(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang='ts' setup>
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { UploadProps, UploadUserFile } from 'element-plus'

/** 三级教育记录编辑 */
defineOptions({ name: 'EntryEdit' })

const emit = defineEmits(['success'])
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中
const formTypeValue = ref('') // 表单的类型：create - 新增；update - 修改
const activeTabName = ref('basic') // 当前激活的Tab

// 上传文件列表
const eduCardFiles = ref<UploadUserFile[]>([])
const examPaperFiles = ref<UploadUserFile[]>([])
const signSheetFiles = ref<UploadUserFile[]>([])
const otherFiles = ref<UploadUserFile[]>([])

// 所有资料列表
const materialsList = ref<any[]>([])

// 部门树形选择数据
const departmentOptions = ref([
  {
    value: 1,
    label: '公司总部',
    children: [
      {
        value: 11,
        label: '安全环保部'
      },
      {
        value: 12,
        label: '行政部'
      }
    ]
  },
  {
    value: 2,
    label: '生产部',
    children: [
      {
        value: 21,
        label: '一号车间'
      },
      {
        value: 22,
        label: '二号车间'
      },
      {
        value: 23,
        label: '三号车间'
      }
    ]
  }
])

const formData = ref({
  id: undefined as number | undefined,
  employeeName: undefined as string | undefined,
  departmentId: undefined as number | undefined,
  departmentName: undefined as string | undefined,
  postType: undefined as string | undefined,
  entryDate: undefined as Date | undefined,
  eduType: undefined as string | undefined,
  eduTime: undefined as string | undefined,
  eduHours: 1 as number,
  teacher: undefined as string | undefined,
  examScore: 0 as number,
  examTime: undefined as Date | undefined,
  completeStatus: 'not-started' as string,
  eduContent: undefined as string | undefined,
  materials: [] as any[]
})

// 表单校验规则
const formRules = reactive({
  employeeName: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
  departmentId: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
  postType: [{ required: true, message: '请选择岗位类型', trigger: 'change' }],
  entryDate: [{ required: true, message: '请选择入职日期', trigger: 'change' }],
  eduType: [{ required: true, message: '请选择教育类型', trigger: 'change' }],
  eduTime: [{ required: true, message: '请输入教育时间', trigger: 'blur' }],
  eduHours: [{ required: true, message: '请输入教育课时', trigger: 'blur' }],
  completeStatus: [{ required: true, message: '请选择完成状态', trigger: 'change' }]
})

// 上传操作的URL
const uploadAction = ref('/api/file/upload') // 实际环境替换为真实的上传接口

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  formTypeValue.value = type
  resetForm()
  
  // 设置标题
  dialogTitle.value = type === 'create' ? '新增教育记录' : '修改教育记录'
  
  // 修改时，设置表单数据
  if (id) {
    formLoading.value = true
    try {
      // 模拟获取数据
      // const data = await EntryApi.getDetail(id)
      // formData.value = data
      
      // 模拟数据
      setTimeout(() => {
        if (id === 1) {
          formData.value = {
            id: 1,
            employeeName: '张三',
            departmentId: 21,
            departmentName: '一号车间',
            postType: 'operator',
            entryDate: new Date('2023-09-01'),
            eduType: 'company',
            eduTime: '2023-09-02 09:00-11:30',
            eduHours: 2.5,
            teacher: '李安全',
            examScore: 85,
            examTime: new Date('2023-09-02 11:30:00'),
            completeStatus: 'completed',
            eduContent: '1. 公司安全生产规章制度培训\n2. 工作场所危险因素和防范措施\n3. 事故案例分析\n4. 个人防护用品使用方法',
            materials: [
              { id: 1, fileName: '张三入职教育卡.pdf', fileType: 'edu-card', uploadTime: '2023-09-02 11:45' },
              { id: 2, fileName: '张三入职考试试卷.pdf', fileType: 'exam-paper', uploadTime: '2023-09-02 11:46' }
            ]
          }
          
          // 设置已上传的资料列表
          materialsList.value = formData.value.materials;
        }
        formLoading.value = false
      }, 500)
    } catch (error) {
      console.error('获取教育记录详情失败:', error)
    } finally {
      formLoading.value = false
    }
  }
}

/** 上传前的校验 */
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 文件大小限制：50MB
  const maxSize = 50 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过50MB!')
    return false
  }
  return true
}

/** 上传成功回调 */
const handleUploadSuccess = (response: any, uploadFile: UploadUserFile, fileType: string) => {
  message.success('文件上传成功')
  
  // 添加到资料列表
  materialsList.value.push({
    id: new Date().getTime(), // 模拟ID
    fileName: uploadFile.name,
    fileType: fileType,
    uploadTime: new Date().toLocaleString()
  })
}

/** 上传失败回调 */
const handleUploadError: UploadProps['onError'] = (error) => {
  message.error('文件上传失败')
  console.error('文件上传失败:', error)
}

/** 预览文件 */
const handleViewFile = (file: any) => {
  message.success(`预览文件: ${file.fileName}`)
  // 实际实现文件预览逻辑
}

/** 删除文件 */
const handleDeleteFile = (file: any) => {
  try {
    message.confirm('是否确认删除该文件?').then(() => {
      // 从资料列表中移除
      const index = materialsList.value.findIndex(item => item.id === file.id)
      if (index !== -1) {
        materialsList.value.splice(index, 1)
        message.success('文件删除成功')
      }
    })
  } catch {
    // 取消删除操作
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    employeeName: undefined,
    departmentId: undefined,
    departmentName: undefined,
    postType: undefined,
    entryDate: undefined,
    eduType: undefined,
    eduTime: undefined,
    eduHours: 1,
    teacher: undefined,
    examScore: 0,
    examTime: undefined,
    completeStatus: 'not-started',
    eduContent: undefined,
    materials: []
  }
  activeTabName.value = 'basic'
  eduCardFiles.value = []
  examPaperFiles.value = []
  signSheetFiles.value = []
  otherFiles.value = []
  materialsList.value = []
}

/** 提交表单 */
const submitForm = async () => {
  // 表单校验
  const formRef = ref()
  try {
    formLoading.value = true
    
    // 校验表单
    await formRef.value?.validate()
    
    // 构建提交的数据，包含资料列表
    const data = {
      ...formData.value,
      materials: materialsList.value
    }
    
    // 保存操作
    if (formTypeValue.value === 'create') {
      // 模拟创建操作
      // await EntryApi.create(data)
      console.log('创建教育记录:', data)
    } else {
      // 模拟更新操作
      // await EntryApi.update(data)
      console.log('更新教育记录:', data)
    }
    
    // 提示信息
    message.success(formTypeValue.value === 'create' ? '创建成功' : '修改成功')
    // 关闭弹窗
    dialogVisible.value = false
    // 通知父组件刷新
    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
  } finally {
    formLoading.value = false
  }
}

// 向父组件暴露方法
defineExpose({ open })
</script>

<style scoped lang='scss'>
.el-form {
  padding: 20px;
}

.el-upload__tip {
  color: #6c757d;
  font-size: 12px;
  margin-top: 5px;
}
</style>