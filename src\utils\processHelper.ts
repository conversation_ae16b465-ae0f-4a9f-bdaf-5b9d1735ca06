import { Router } from 'vue-router'
import * as DefinitionApi from '@/api/bpm/definition'

interface StartProcessOptions {
  processKey: string // 流程定义的 key
  id?: string // 流程定义的 ID
  businessKey?: string // 业务标识
  variables?: Record<string, any> // 流程变量
}

/**
 * 直接发起流程
 * @param router Vue Router 实例
 * @param options 发起流程的选项
 */
export const startProcess = async (router: Router, options: StartProcessOptions) => {
  // 获取流程定义
  const processDefinition = await DefinitionApi.getProcessDefinition(options.id, options.processKey)
  if (!processDefinition) {
    throw new Error(`未找到流程定义: ${options.processKey}`)
  }

  // 跳转到流程发起页面，并自动选择流程
  router.push({
    name: 'BpmProcessInstanceCreate',
    query: {
      processDefinitionId: processDefinition.id,
      processKey: options.processKey,
      businessKey: options.businessKey,
      autoSelectProcess: 'true', // 添加自动选择流程的标记
      ...options.variables // 将其他变量添加到 query 中
    }
  })
}
