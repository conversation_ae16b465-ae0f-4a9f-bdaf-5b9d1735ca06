<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据导出"
    width="600px"
    :before-close="handleClose"
  >
    <div class="export-content">
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="导出内容">
          <el-checkbox-group v-model="exportForm.content">
            <el-checkbox label="personnel">人员安全数据</el-checkbox>
            <el-checkbox label="environment">环境监测数据</el-checkbox>
            <el-checkbox label="equipment">设备设施数据</el-checkbox>
            <el-checkbox label="investment">安全投入数据</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="exportForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="excel">Excel</el-radio>
            <el-radio label="pdf">PDF</el-radio>
            <el-radio label="word">Word</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="报表类型">
          <el-select v-model="exportForm.reportType" placeholder="请选择报表类型">
            <el-option label="详细数据报表" value="detail" />
            <el-option label="统计分析报表" value="analysis" />
            <el-option label="趋势对比报表" value="trend" />
          </el-select>
        </el-form-item>

        <el-form-item label="图表选项">
          <el-checkbox-group v-model="exportForm.charts">
            <el-checkbox label="pie">饼图</el-checkbox>
            <el-checkbox label="bar">柱状图</el-checkbox>
            <el-checkbox label="line">折线图</el-checkbox>
            <el-checkbox label="radar">雷达图</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="其他选项">
          <el-checkbox-group v-model="exportForm.options">
            <el-checkbox label="includeAnalysis">包含分析结论</el-checkbox>
            <el-checkbox label="includeComparison">包含对比数据</el-checkbox>
            <el-checkbox label="includeSuggestions">包含改进建议</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div class="export-preview" v-if="showPreview">
        <div class="preview-header">
          <h3>导出预览</h3>
        </div>
        <el-table :data="previewData" style="width: 100%">
          <el-table-column prop="name" label="数据项" />
          <el-table-column prop="count" label="数据量" />
          <el-table-column prop="size" label="预计大小" />
        </el-table>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handlePreview" :disabled="!isValid">预览</el-button>
        <el-button type="primary" @click="handleExport" :disabled="!isValid">
          开始导出
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'close', 'export'])

const dialogVisible = ref(props.visible)
const showPreview = ref(false)

// 导出表单
const exportForm = reactive({
  content: [],
  timeRange: [],
  format: 'excel',
  reportType: 'detail',
  charts: [],
  options: []
})

// 表单验证
const isValid = computed(() => {
  return exportForm.content.length > 0 &&
    exportForm.timeRange.length === 2 &&
    exportForm.format &&
    exportForm.reportType
})

// 预览数据
const previewData = ref([
  {
    name: '人员安全数据',
    count: '125条',
    size: '2.5MB'
  },
  {
    name: '环境监测数据',
    count: '86条',
    size: '1.8MB'
  },
  {
    name: '设备设施数据',
    count: '94条',
    size: '2.1MB'
  },
  {
    name: '安全投入数据',
    count: '52条',
    size: '1.2MB'
  }
])

// 预览
const handlePreview = () => {
  showPreview.value = true
}

// 导出
const handleExport = () => {
  emit('export', {
    ...exportForm,
    previewData: previewData.value
  })
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  showPreview.value = false
  emit('update:visible', false)
  emit('close')
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})
</script>

<style lang="scss" scoped>
.export-content {
  .export-preview {
    margin-top: 20px;
    border-top: 1px solid #ebeef5;
    padding-top: 20px;

    .preview-header {
      margin-bottom: 15px;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 