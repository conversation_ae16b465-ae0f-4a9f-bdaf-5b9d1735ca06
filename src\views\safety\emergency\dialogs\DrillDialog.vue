<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="60%"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="drill-form"
    >
      <el-form-item label="演练名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入演练名称" :disabled="isView" />
      </el-form-item>
      
      <el-form-item label="演练类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择演练类型" style="width: 100%" :disabled="isView">
          <el-option label="消防演练" value="fire" />
          <el-option label="疏散演练" value="evacuation" />
          <el-option label="综合演练" value="comprehensive" />
        </el-select>
      </el-form-item>

      <el-form-item label="演练时间" prop="drillTime">
        <el-date-picker
          v-model="form.drillTime"
          type="datetime"
          placeholder="请选择演练时间"
          :disabled="isView"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="演练地点" prop="location">
        <el-input v-model="form.location" placeholder="请输入演练地点" :disabled="isView" />
      </el-form-item>

      <el-form-item label="参与人数" prop="participants">
        <el-input-number v-model="form.participants" :min="1" :disabled="isView" />
      </el-form-item>

      <el-form-item label="演练内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入演练内容"
          :disabled="isView"
        />
      </el-form-item>

      <template v-if="dialogType === 'record'">
        <el-form-item label="完成情况" prop="completion">
          <el-rate v-model="form.completion" :max="5" show-score />
        </el-form-item>

        <el-form-item label="存在问题" prop="problems">
          <el-input
            v-model="form.problems"
            type="textarea"
            :rows="3"
            placeholder="请输入演练中发现的问题"
          />
        </el-form-item>

        <el-form-item label="改进建议" prop="improvements">
          <el-input
            v-model="form.improvements"
            type="textarea"
            :rows="3"
            placeholder="请输入改进建议"
          />
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="!isView">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'DrillDialog',
  props: {
    dialogType: {
      type: String,
      default: 'add'
    },
    drillData: {
      type: Object,
      default: () => ({})
    },
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'submit'],
  computed: {
    dialogTitle() {
      const titles = {
        add: '新增演练',
        edit: '编辑演练',
        view: '查看详情',
        start: '开始演练',
        record: '记录结果'
      }
      return titles[this.dialogType] || '演练详情'
    },
    dialogVisible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    },
    isView() {
      return this.dialogType === 'view'
    }
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  data() {
    return {
      form: {
        name: '',
        type: '',
        drillTime: '',
        location: '',
        participants: 0,
        content: '',
        completion: 0,
        problems: '',
        improvements: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入演练名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择演练类型', trigger: 'change' }
        ],
        drillTime: [
          { required: true, message: '请选择演练时间', trigger: 'change' }
        ],
        location: [
          { required: true, message: '请输入演练地点', trigger: 'blur' }
        ],
        participants: [
          { required: true, message: '请输入参与人数', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入演练内容', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    initForm() {
      if (this.dialogType === 'add') {
        this.form = {
          name: '',
          type: '',
          drillTime: '',
          location: '',
          participants: 0,
          content: '',
          completion: 0,
          problems: '',
          improvements: ''
        }
      } else {
        this.form = { ...this.drillData }
      }
    },
    handleClosed() {
      this.$refs.formRef?.resetFields()
    },
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()
        this.$emit('submit', this.form)
        this.dialogVisible = false
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drill-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 20px;
}
</style> 