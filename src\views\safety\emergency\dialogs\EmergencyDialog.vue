<template>
  <el-dialog
    :title="getDialogTitle"
    v-model="dialogVisible"
    width="60%"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="emergency-form"
    >
      <el-form-item label="事件名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入事件名称" />
      </el-form-item>

      <el-form-item label="事件类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择事件类型" style="width: 100%">
          <el-option label="火灾事故" value="fire" />
          <el-option label="设备故障" value="equipment" />
          <el-option label="人员伤害" value="injury" />
          <el-option label="自然灾害" value="disaster" />
        </el-select>
      </el-form-item>

      <el-form-item label="事件等级" prop="level">
        <el-select v-model="form.level" placeholder="请选择事件等级" style="width: 100%">
          <el-option label="轻微" value="low" />
          <el-option label="一般" value="medium" />
          <el-option label="严重" value="high" />
        </el-select>
      </el-form-item>

      <el-form-item label="发生时间" prop="occurTime">
        <el-date-picker
          v-model="form.occurTime"
          type="datetime"
          placeholder="请选择发生时间"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="发生地点" prop="location">
        <el-input v-model="form.location" placeholder="请输入发生地点" />
      </el-form-item>

      <el-form-item label="事件描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入事件描述"
        />
      </el-form-item>

      <template v-if="dialogType === 'process'">
        <el-divider>处理信息</el-divider>

        <el-form-item label="处理方案" prop="solution">
          <el-input
            v-model="form.solution"
            type="textarea"
            :rows="4"
            placeholder="请输入处理方案"
          />
        </el-form-item>

        <el-form-item label="处理措施" prop="measures">
          <el-input
            v-model="form.measures"
            type="textarea"
            :rows="4"
            placeholder="请输入具体处理措施"
          />
        </el-form-item>

        <el-form-item label="处理结果" prop="result">
          <el-input
            v-model="form.result"
            type="textarea"
            :rows="4"
            placeholder="请输入处理结果"
          />
        </el-form-item>

        <el-form-item label="处理状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择处理状态" style="width: 100%">
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
      </template>

      <template v-if="dialogType === 'linkPlan'">
        <el-divider>关联预案</el-divider>

        <el-form-item label="关联预案" prop="linkedPlans">
          <el-select
            v-model="form.linkedPlans"
            multiple
            filterable
            placeholder="请选择关联预案"
            style="width: 100%"
          >
            <el-option
              v-for="plan in planOptions"
              :key="plan.id"
              :label="plan.name"
              :value="plan.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="关联说明" prop="linkDescription">
          <el-input
            v-model="form.linkDescription"
            type="textarea"
            :rows="4"
            placeholder="请输入关联说明"
          />
        </el-form-item>
      </template>

      <el-form-item label="附件上传">
        <el-upload
          class="upload-demo"
          action="/api/upload"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          multiple
          :limit="5"
          :on-exceed="handleExceed"
        >
          <el-button type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持上传现场照片、处理报告等文件，单个文件不超过10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'EmergencyDialog',
  props: {
    dialogType: {
      type: String,
      default: 'add'
    },
    emergencyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        name: '',
        type: '',
        level: '',
        occurTime: '',
        location: '',
        description: '',
        solution: '',
        measures: '',
        result: '',
        status: '',
        linkedPlans: [],
        linkDescription: '',
        attachments: []
      },
      planOptions: [], // 这里需要从后端获取预案列表
      rules: {
        name: [
          { required: true, message: '请输入事件名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择事件类型', trigger: 'change' }
        ],
        level: [
          { required: true, message: '请选择事件等级', trigger: 'change' }
        ],
        occurTime: [
          { required: true, message: '请选择发生时间', trigger: 'change' }
        ],
        location: [
          { required: true, message: '请输入发生地点', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入事件描述', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    getDialogTitle() {
      const titles = {
        add: '新增应急事件',
        edit: '编辑应急事件',
        process: '处理应急事件',
        linkPlan: '关联应急预案',
        view: '查看事件详情'
      }
      return titles[this.dialogType] || '应急事件'
    }
  },
  watch: {
    emergencyData: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.form = { ...val }
        }
      },
      immediate: true
    }
  },
  methods: {
    handlePreview(file) {
      // 处理文件预览
    },
    handleRemove(file, fileList) {
      // 处理文件移除
    },
    handleExceed(files, fileList) {
      this.$message.warning('最多只能上传5个文件')
    },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()
        // 提交表单逻辑
        this.$emit('submit', this.form)
        this.dialogVisible = false
      } catch (error) {
        console.error('表单验证失败', error)
      }
    },
    open() {
      this.dialogVisible = true
      if (this.dialogType === 'linkPlan') {
        this.fetchPlanOptions()
      }
    },
    async fetchPlanOptions() {
      // 获取预案列表
      // this.planOptions = await api.getPlanList()
    }
  }
}
</script>

<style lang="scss" scoped>
.emergency-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 20px;
}
</style> 