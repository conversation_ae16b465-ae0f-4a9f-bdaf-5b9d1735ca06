<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">告警设置</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-1" />添加告警规则
            </el-button>
            <el-button type="success" @click="handleImport">
              <Icon icon="ep:upload" class="mr-1" />导入规则
            </el-button>
            <el-button type="warning" @click="handleExport">
              <Icon icon="ep:download" class="mr-1" />导出规则
            </el-button>
          </div>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex flex-col">
        <!-- 搜索表单 -->
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="告警类型" style="width: 200px;">
              <el-select v-model="searchForm.type" placeholder="请选择告警类型" clearable>
                <el-option label="CPU使用率" value="cpu" />
                <el-option label="磁盘空间" value="disk" />
                <el-option label="内存负载" value="memory" />
                <el-option label="终端离线" value="offline" />
              </el-select>
            </el-form-item>
            <el-form-item label="告警级别" style="width: 200px;">
              <el-select v-model="searchForm.level" placeholder="请选择告警级别" clearable>
                <el-option label="一般" value="normal" />
                <el-option label="重要" value="important" />
                <el-option label="紧急" value="urgent" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" style="width: 200px;">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="启用" value="enabled" />
                <el-option label="禁用" value="disabled" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 告警规则列表 -->
        <div class="flex-1">
          <el-table :data="tableData" border style="width: 100%" height="100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="name" label="规则名称" min-width="150" />
            <el-table-column prop="type" label="告警类型" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getTypeTag(row.type)">{{ getTypeName(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="level" label="告警级别" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getLevelTag(row.level)">{{ getLevelName(row.level) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="threshold" label="告警阈值" width="120" align="center">
              <template #default="{ row }">
                {{ formatThreshold(row) }}
              </template>
            </el-table-column>
            <el-table-column prop="notifyMethods" label="通知方式" min-width="150">
              <template #default="{ row }">
                <el-tag v-for="method in row.notifyMethods" :key="method" class="mr-1">
                  {{ getNotifyMethodName(method) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="receivers" label="接收人" min-width="200" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-switch v-model="row.status" :active-value="'enabled'" :inactive-value="'disabled'"
                  @change="handleStatusChange(row)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                <el-button type="primary" link @click="handleTest(row)">测试</el-button>
                <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>

    <!-- 告警规则编辑对话框 -->
    <el-dialog v-model="editDialogVisible" :title="isEdit ? '编辑告警规则' : '添加告警规则'" width="800px">
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="120px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="告警类型" prop="type">
          <el-select v-model="editForm.type" placeholder="请选择告警类型" @change="handleTypeChange">
            <el-option label="CPU使用率" value="cpu" />
            <el-option label="磁盘空间" value="disk" />
            <el-option label="内存负载" value="memory" />
            <el-option label="终端离线" value="offline" />
          </el-select>
        </el-form-item>
        <el-form-item label="告警级别" prop="level">
          <el-select v-model="editForm.level" placeholder="请选择告警级别">
            <el-option label="一般" value="normal" />
            <el-option label="重要" value="important" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        <el-form-item label="告警阈值" prop="threshold">
          <div class="flex items-center">
            <el-input-number v-model="editForm.threshold.value" :min="0" :max="100" />
            <span class="ml-2">{{ getThresholdUnit(editForm.type) }}</span>
          </div>
        </el-form-item>
        <el-form-item label="持续时间" prop="duration">
          <div class="flex items-center">
            <el-input-number v-model="editForm.duration" :min="1" :max="60" />
            <span class="ml-2">分钟</span>
          </div>
        </el-form-item>
        <el-form-item label="通知方式" prop="notifyMethods">
          <el-checkbox-group v-model="editForm.notifyMethods">
            <el-checkbox label="email">邮件</el-checkbox>
            <el-checkbox label="sms">短信</el-checkbox>
            <el-checkbox label="wechat">微信</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="接收人" prop="receivers">
          <el-select v-model="editForm.receivers" multiple placeholder="请选择接收人">
            <el-option v-for="item in receiverOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="editForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 测试告警对话框 -->
    <el-dialog v-model="testDialogVisible" title="测试告警" width="500px">
      <el-form ref="testFormRef" :model="testForm" :rules="testRules" label-width="100px">
        <el-form-item label="测试值" prop="value">
          <el-input-number v-model="testForm.value" :min="0" :max="100" />
        </el-form-item>
        <el-form-item label="测试结果" v-if="testResult">
          <el-alert :title="testResult" :type="testResult.includes('触发') ? 'success' : 'info'" show-icon />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleTestSubmit">测试</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  type: '',
  level: '',
  status: ''
})

// 表格数据
const tableData = ref([
  {
    id: '1',
    name: 'CPU使用率告警',
    type: 'cpu',
    level: 'important',
    threshold: { value: 80, unit: '%' },
    duration: 5,
    notifyMethods: ['email', 'sms'],
    receivers: '张三,李四',
    status: 'enabled',
    description: 'CPU使用率超过80%持续5分钟触发告警'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框
const editDialogVisible = ref(false)
const testDialogVisible = ref(false)
const isEdit = ref(false)
const editFormRef = ref<FormInstance>()
const testFormRef = ref<FormInstance>()

// 编辑表单
const editForm = reactive({
  name: '',
  type: '',
  level: '',
  threshold: { value: 0, unit: '%' },
  duration: 5,
  notifyMethods: [],
  receivers: [],
  description: ''
})

// 测试表单
const testForm = reactive({
  value: 0
})
const testResult = ref('')

// 接收人选项
const receiverOptions = [
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu' }
]

// 表单验证规则
const editRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择告警类型', trigger: 'change' }],
  level: [{ required: true, message: '请选择告警级别', trigger: 'change' }],
  threshold: [{ required: true, message: '请输入告警阈值', trigger: 'blur' }],
  duration: [{ required: true, message: '请输入持续时间', trigger: 'blur' }],
  notifyMethods: [{ required: true, message: '请选择通知方式', trigger: 'change' }],
  receivers: [{ required: true, message: '请选择接收人', trigger: 'change' }]
}

const testRules = {
  value: [{ required: true, message: '请输入测试值', trigger: 'blur' }]
}

// 方法
const getTypeTag = (type: string) => {
  switch (type) {
    case 'cpu':
      return 'danger'
    case 'disk':
      return 'warning'
    case 'memory':
      return 'info'
    case 'offline':
      return 'danger'
    default:
      return 'info'
  }
}

const getTypeName = (type: string) => {
  switch (type) {
    case 'cpu':
      return 'CPU使用率'
    case 'disk':
      return '磁盘空间'
    case 'memory':
      return '内存负载'
    case 'offline':
      return '终端离线'
    default:
      return type
  }
}

const getLevelTag = (level: string) => {
  switch (level) {
    case 'normal':
      return 'info'
    case 'important':
      return 'warning'
    case 'urgent':
      return 'danger'
    default:
      return 'info'
  }
}

const getLevelName = (level: string) => {
  switch (level) {
    case 'normal':
      return '一般'
    case 'important':
      return '重要'
    case 'urgent':
      return '紧急'
    default:
      return level
  }
}

const getNotifyMethodName = (method: string) => {
  switch (method) {
    case 'email':
      return '邮件'
    case 'sms':
      return '短信'
    case 'wechat':
      return '微信'
    default:
      return method
  }
}

const getThresholdUnit = (type: string) => {
  switch (type) {
    case 'cpu':
    case 'memory':
      return '%'
    case 'disk':
      return 'GB'
    case 'offline':
      return '分钟'
    default:
      return ''
  }
}

const formatThreshold = (row: any) => {
  return `${row.threshold.value}${row.threshold.unit}`
}

const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', searchForm)
}

const resetSearch = () => {
  searchForm.type = ''
  searchForm.level = ''
  searchForm.status = ''
}

const handleAdd = () => {
  isEdit.value = false
  editForm.name = ''
  editForm.type = ''
  editForm.level = ''
  editForm.threshold = { value: 0, unit: '%' }
  editForm.duration = 5
  editForm.notifyMethods = []
  editForm.receivers = []
  editForm.description = ''
  editDialogVisible.value = true
}

const handleEdit = (row: any) => {
  isEdit.value = true
  Object.assign(editForm, row)
  editDialogVisible.value = true
}

const handleTest = (row: any) => {
  testForm.value = 0
  testResult.value = ''
  testDialogVisible.value = true
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该告警规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实现删除逻辑
    ElMessage.success('删除成功')
  }).catch(() => { })
}

const handleImport = () => {
  // 实现导入逻辑
  ElMessage.success('导入成功')
}

const handleExport = () => {
  // 实现导出逻辑
  ElMessage.success('导出成功')
}

const handleStatusChange = (row: any) => {
  // 实现状态切换逻辑
  ElMessage.success(`规则已${row.status === 'enabled' ? '启用' : '禁用'}`)
}

const handleTypeChange = (type: string) => {
  editForm.threshold.unit = getThresholdUnit(type)
}

const handleSave = async () => {
  if (!editFormRef.value) return
  await editFormRef.value.validate((valid) => {
    if (valid) {
      // 实现保存逻辑
      console.log('保存数据:', editForm)
      editDialogVisible.value = false
      ElMessage.success('保存成功')
    }
  })
}

const handleTestSubmit = async () => {
  if (!testFormRef.value) return
  await testFormRef.value.validate((valid) => {
    if (valid) {
      // 实现测试逻辑
      testResult.value = `测试值 ${testForm.value} 触发告警`
    }
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
