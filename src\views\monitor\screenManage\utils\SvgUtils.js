/**
 * SVG处理工具类
 * 用于处理SVG内容的清理、格式化、点位解析和导出
 */

/**
 * 处理SVG上传
 * @param {Function} callback 上传成功后的回调函数，参数为SVG内容
 * @param {Object} options 上传选项
 * @returns {void}
 */
export function handleUploadSvg(callback, options = {}) {
  const { 
    acceptTypes = '.svg', 
    showSuccessMessage = true, 
    messageText = 'SVG上传成功',
    onCancel = null // 新增取消回调选项
  } = options

  // 创建文件上传输入元素
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = acceptTypes
  fileInput.style.display = 'none'
  document.body.appendChild(fileInput)

  // 监听文件选择事件
  fileInput.onchange = async (event) => {
    const file = event.target.files?.[0]
    if (!file) {
      document.body.removeChild(fileInput)
      
      // 用户取消了文件选择，调用取消回调
      if (typeof onCancel === 'function') {
        console.log('用户取消了文件选择')
        onCancel()
      }
      
      return
    }

    try {
      // 读取SVG文件内容
      const svgContent = await file.text()

      // 调用回调函数，传递SVG内容和文件名
      callback(svgContent, file.name)

      // 显示成功消息
      if (showSuccessMessage && window.ElMessage) {
        window.ElMessage.success(messageText)
      }
    } catch (error) {
      console.error('SVG文件处理失败:', error)
      if (window.ElMessage) {
        window.ElMessage.error('SVG文件处理失败')
      }
      
      // 处理错误时也调用取消回调
      if (typeof onCancel === 'function') {
        onCancel(error)
      }
    } finally {
      document.body.removeChild(fileInput)
    }
  }

  // 当用户点击取消按钮或关闭文件选择对话框时
  // 监听dialog关闭事件
  document.addEventListener('click', function handleClickOutside(e) {
    // 如果点击事件不是发生在文件选择对话框内
    if (e.target && !fileInput.contains(e.target)) {
      // 移除事件监听器，避免重复触发
      document.removeEventListener('click', handleClickOutside)
      
      // 延迟一下执行，确保是真的取消了选择
      setTimeout(() => {
        // 如果文件选择对话框仍然存在于DOM中，且没有选择文件
        if (document.body.contains(fileInput) && !fileInput.value) {
          // 调用取消回调
          if (typeof onCancel === 'function') {
            console.log('用户可能通过其他方式取消了文件选择')
            onCancel()
          }
        }
      }, 300)
    }
  }, { once: true, capture: true })

  // 触发文件选择对话框
  fileInput.click()
}

/**
 * 导出SVG内容为文件
 * @param {string} svgContent SVG内容
 * @param {string} fileName 文件名（不包含扩展名）
 * @param {boolean} showSuccessMessage 是否显示成功消息
 * @returns {void}
 */
export function exportSvg(svgContent, fileName = '画面', showSuccessMessage = true) {
  if (!svgContent) {
    if (window.ElMessage) {
      window.ElMessage.warning('没有可导出的SVG内容')
    }
    return
  }

  // 创建Blob对象
  const blob = new Blob([svgContent], { type: 'image/svg+xml' })

  // 创建下载链接
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${fileName}.svg`

  // 触发下载
  document.body.appendChild(link)
  link.click()

  // 清理
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  if (showSuccessMessage && window.ElMessage) {
    window.ElMessage.success('SVG导出成功')
  }
}

/**
 * 检查SVG内容是否是URL格式
 * @param {string} svgContent SVG内容
 * @returns {boolean} 是否是URL格式
 */
export function isSvgUrl(svgContent) {
  if (!svgContent) return false
  const content = svgContent.trim()
  // 检查是否是URL (http, https, //) 或文件路径(.svg/.ubak结尾)或API路径
  return (
    content.startsWith('http://') ||
    content.startsWith('https://') ||
    content.startsWith('//') ||
    content.endsWith('.svg') ||
    content.endsWith('.ubak') ||
    content.includes('/api/')
  )
}

/**
 * 清理SVG内容，确保安全和正确显示
 * @param {string} svgContent 原始SVG内容
 * @returns {string} 清理后的SVG内容
 */
export function sanitizeSvgContent(svgContent) {
  if (!svgContent) return ''

  // 如果不是URL，直接处理内容
  if (!isSvgUrl(svgContent)) {
    return processSvgContent(svgContent)
  }

  // URL格式的SVG会在组件中通过fetchSvgContent处理
  // 这里返回一个加载中的占位SVG
  return `<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 800 600">
    <text x="20" y="300" text-anchor="start" font-size="20" fill="#666">
      正在加载SVG内容...
    </text>
    <g id="monitoring-points" class="monitoring-points-layer"></g>
  </svg>`
}

/**
 * 处理SVG内容，添加必要的属性和元素
 * @param {string} svgContent SVG内容
 * @returns {string} 处理后的SVG内容
 */
export function processSvgContent(svgContent) {
  if (!svgContent) return ''

  try {
    // 使用DOMParser处理SVG内容
    const parser = new DOMParser()
    const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml')

    // 检查解析是否有错误
    const parserError = svgDoc.querySelector('parsererror')
    if (parserError) {
      console.error('SVG解析错误:', parserError.textContent)
      return svgContent
    }

    // 确保SVG有监测点容器
    const rootElement = svgDoc.documentElement
    let pointsGroup = svgDoc.querySelector('#monitoring-points')
    if (!pointsGroup) {
      pointsGroup = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'g')
      pointsGroup.setAttribute('id', 'monitoring-points')
      pointsGroup.setAttribute('class', 'monitoring-points-layer')
      rootElement.appendChild(pointsGroup)
    } else {
      // 确保监测点容器有正确的类名
      pointsGroup.setAttribute('class', 'monitoring-points-layer')

      // 处理现有的监测点，确保属性完整
      const points = pointsGroup.querySelectorAll(
        '[data-id], [data-point="true"], circle, rect, polygon, text'
      )
      points.forEach((point) => {
        // 确保所有点位都有必要属性
        const pointId = point.getAttribute('data-id') || point.getAttribute('id')
        if (pointId) {
          // 确保有data-id属性
          if (!point.hasAttribute('data-id')) {
            point.setAttribute('data-id', pointId)
          }

          // 确保有data-point属性，除text外
          if (!point.hasAttribute('data-point') && point.tagName.toLowerCase() !== 'text') {
            point.setAttribute('data-point', 'true')
          }

          // 确保有data-show-type属性
          if (!point.hasAttribute('data-show-type')) {
            // 根据元素类型确定点位类型
            let showType
            switch (point.tagName.toLowerCase()) {
              case 'circle':
                showType = 'circle'
                break
              case 'rect':
                showType = 'square'
                break
              case 'polygon':
                showType = 'triangle'
                break
              case 'text':
                showType = 'text'
                break
              default:
                showType = 'circle'
            }
            point.setAttribute('data-show-type', showType)
          }
        }
      })
    }

    // 确保SVG有正确的xmlns属性
    if (!svgContent.includes('xmlns=')) {
      rootElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg')
    }

    // 确保SVG有宽高，如果没有则添加
    if (!rootElement.hasAttribute('width') || !rootElement.hasAttribute('height')) {
      rootElement.setAttribute('width', '100%')
      rootElement.setAttribute('height', '100%')
    }

    // 确保SVG有viewBox，如果没有则添加
    if (!rootElement.hasAttribute('viewBox')) {
      // 尝试从宽高属性中获取数值
      const width = parseFloat(rootElement.getAttribute('width') || '1000')
      const height = parseFloat(rootElement.getAttribute('height') || '1000')

      if (
        !isNaN(width) &&
        !isNaN(height) &&
        !rootElement.getAttribute('width')?.includes('%') &&
        !rootElement.getAttribute('height')?.includes('%')
      ) {
        rootElement.setAttribute('viewBox', `0 0 ${width} ${height}`)
      } else {
        // 默认添加一个通用的viewBox
        rootElement.setAttribute('viewBox', '0 0 1000 1000')
      }
    }

    // 将处理后的SVG转回字符串
    const serializer = new XMLSerializer()
    return serializer.serializeToString(svgDoc)
  } catch (error) {
    console.error('处理SVG内容失败:', error)
    return svgContent // 处理失败时返回原始内容
  }
}

/**
 * 使用fetch获取SVG内容
 * @param {string} svgUrl SVG的URL
 * @returns {Promise<string>} 获取到的SVG内容
 */
export async function fetchSvgContent(svgUrl) {
  try {
    // 处理URL - 如果svgUrl不是以http开头的完整URL，添加SVG_URL前缀
    let fullUrl = svgUrl;
    if (!svgUrl.startsWith('http://') && !svgUrl.startsWith('https://')) {
      // 从配置导入SVG_URL
      const { SVG_URL } = require('@/config/url');
      fullUrl = SVG_URL + svgUrl;
    }

    // 使用fetch获取SVG内容
    const response = await fetch(fullUrl)
    if (!response.ok) {
      throw new Error(`Failed to fetch SVG: ${response.status} ${response.statusText}`)
    }

    // 获取文本内容
    const svgContent = await response.text()

    // 检查是否是有效的SVG内容
    if (!svgContent.includes('<svg') || !svgContent.includes('</svg>')) {
      console.warn('Invalid SVG content received')
      return createFallbackSvg(fullUrl)
    }

    // 处理SVG内容，确保有监测点容器
    return ensureMonitoringPointsContainer(svgContent)
  } catch (error) {
    console.error('Error fetching SVG content:', error)
    return createFallbackSvg(svgUrl)
  }
}

/**
 * 确保SVG内容中有监测点容器
 * @param {string} svgContent SVG内容
 * @returns {string} 处理后的SVG内容
 */
function ensureMonitoringPointsContainer(svgContent) {
  // 使用DOMParser处理SVG内容
  const parser = new DOMParser()
  const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml')

  // 检查是否有错误
  const parserError = svgDoc.querySelector('parsererror')
  if (parserError) {
    console.warn('Error parsing SVG content:', parserError.textContent)
    return svgContent
  }

  // 获取SVG根元素
  const rootElement = svgDoc.documentElement

  // 确保有监测点容器
  let pointsGroup = svgDoc.querySelector('#monitoring-points')
  if (!pointsGroup) {
    pointsGroup = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'g')
    pointsGroup.setAttribute('id', 'monitoring-points')
    pointsGroup.setAttribute('class', 'monitoring-points-layer')
    rootElement.appendChild(pointsGroup)
  }

  // 将处理后的SVG转回字符串
  const serializer = new XMLSerializer()
  return serializer.serializeToString(svgDoc)
}

/**
 * 创建备用SVG（当无法获取SVG内容时使用）
 * @param {string} svgUrl SVG的URL
 * @returns {string} 备用SVG内容
 */
function createFallbackSvg(svgUrl) {
  // 处理URL - 如果svgUrl不是以http开头的完整URL，添加SVG_URL前缀
  let fullUrl = svgUrl;
  if (!svgUrl.startsWith('http://') && !svgUrl.startsWith('https://')) {
    // 从配置导入SVG_URL
    const { SVG_URL } = require('@/config/url');
    fullUrl = SVG_URL + svgUrl;
  }

  // 创建基础SVG，使用image标签引用处理后的URL
  return `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
    <!-- 使用原始URL作为底图 -->
    <image href="${fullUrl}" x="0" y="0" width="100%" height="100%" preserveAspectRatio="xMidYMid meet" />
    <g id="monitoring-points" class="monitoring-points-layer"></g>
  </svg>`
}

/**
 * 提取SVG的宽高信息
 * @param {string} svgContent SVG内容
 * @returns {Object} 包含width和height的对象
 */
export function extractSvgDimensions(svgContent) {
  if (!svgContent) return { width: 0, height: 0 }

  try {
    const parser = new DOMParser()
    const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml')
    const svgElement = svgDoc.documentElement

    // 尝试直接从width和height属性获取
    let width = parseFloat(svgElement.getAttribute('width') || '0')
    let height = parseFloat(svgElement.getAttribute('height') || '0')

    // 如果width或height是百分比格式或未设置，尝试从viewBox获取
    if (
      isNaN(width) ||
      isNaN(height) ||
      svgElement.getAttribute('width')?.includes('%') ||
      svgElement.getAttribute('height')?.includes('%')
    ) {
      const viewBox = svgElement.getAttribute('viewBox')
      if (viewBox) {
        const values = viewBox.split(' ').map(Number)
        if (values.length >= 4) {
          width = values[2]
          height = values[3]
        }
      }
    }

    // 确保返回有效的数值
    return {
      width: isNaN(width) ? 800 : width,
      height: isNaN(height) ? 600 : height
    }
  } catch (error) {
    console.error('提取SVG尺寸失败:', error)
    return { width: 800, height: 600 } // 出错时返回默认值
  }
}

/**
 * 从SVG元素解析点位
 * @param {string} svgContent SVG内容
 * @returns {Array} 解析出的点位数组
 */
export function parsePointsFromSvg(svgContent) {
  if (!svgContent) return []
  if (isSvgUrl(svgContent)) return [] // URL格式暂不支持解析

  try {
    const parser = new DOMParser()
    const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml')
    const points = []
    let pointIndex = 1

    // 查找带data-id、data-point=true或data-show-type属性的元素（扩大查询范围）
    const pointElements = svgDoc.querySelectorAll('[data-id], [data-point="true"], [data-show-type], [data-indicator-code]')
    console.log(`从SVG解析到${pointElements.length}个可能的点位元素`)

    pointElements.forEach((element) => {
      try {
        // 获取基本信息，优先使用data-id
        const id = element.getAttribute('data-id') || element.getAttribute('id') || `point-${Date.now()}-${pointIndex}`
        const indicatorCode = element.getAttribute('data-indicator-code') || ''
        const showType = element.getAttribute('data-show-type') || ''
        const name = element.getAttribute('data-name') || 
                   element.getAttribute('data-point-code') || 
                   element.textContent || 
                   `P${pointIndex}`

        // 确定是否为文本类型
        const isText = showType === 'text' || element.tagName.toLowerCase() === 'text' || element.getAttribute('data-text-id') || id.startsWith('text-')
        
        // 检查是否是文本组 - 新的文本点位使用g标签
        const isTextGroup = element.tagName.toLowerCase() === 'g' && (element.getAttribute('data-text-id') || element.id?.startsWith('text-group-'))

        // 确定点位类型
        let type = element.getAttribute('data-type') || element.getAttribute('data-show-type') || 'circle'
        if (!isText && (showType.includes('square') || showType.includes('rect') || element.tagName.toLowerCase() === 'rect')) {
          type = 'square'
        } else if (!isText && (showType.includes('triangle') || element.tagName.toLowerCase() === 'polygon')) {
          type = 'triangle'
        } else if (isText) {
          type = 'text'
        }

        // 获取元素位置
        const { x, y } = isTextGroup ? getGroupPosition(element) : getElementPosition(element)

        // 检查坐标是否有效
        if (isNaN(x) || isNaN(y) || (x === 0 && y === 0)) {
          console.warn(`跳过无效坐标的点位: ${id}`)
          return
        }

        // 解析样式信息
        let data = { type }

        // 设置颜色
        let color
        if (element.hasAttribute('data-color')) {
          color = element.getAttribute('data-color')
        } else if (element.hasAttribute('fill')) {
          const fill = element.getAttribute('fill')
          if (fill && fill !== 'none' && fill !== '#FFFFFF' && fill !== '#fff') {
            color = fill
          }
        }
        
        // 如果没有找到颜色，使用默认值
        if (!color) {
          color = isText ? '#000000' : getPointColorByType(type)
        }
        data.color = color

        // 解析指标信息
        if (indicatorCode) {
          data.indicator = indicatorCode
        }

        // 解析尺寸信息
        if (element.hasAttribute('data-size')) {
          data.size = parseFloat(element.getAttribute('data-size'))
        } else if (element.hasAttribute('r')) {
          data.size = parseFloat(element.getAttribute('r'))
        } else if (element.hasAttribute('width') && element.hasAttribute('height')) {
          const width = parseFloat(element.getAttribute('width'))
          const height = parseFloat(element.getAttribute('height'))
          data.size = Math.min(width, height) / 2
        } else {
          data.size = isText ? 13 : 8 // 默认尺寸
        }

        // 文本特有属性
        if (isText) {
          // 获取父元素或当前元素进行属性读取
          const sourceElement = isTextGroup ? element : (element.parentElement?.tagName.toLowerCase() === 'g' ? element.parentElement : element);
          
          // 解析字体大小
          if (sourceElement.hasAttribute('data-font-size')) {
            data.pointSize = parseFloat(sourceElement.getAttribute('data-font-size'))
          } else if (sourceElement.hasAttribute('data-point-size')) {
            data.pointSize = parseFloat(sourceElement.getAttribute('data-point-size'))
          } else if (element.hasAttribute('font-size')) {
            const fontSize = element.getAttribute('font-size')
            data.pointSize = parseFloat(fontSize.replace('px', ''))
          } else {
            data.pointSize = 13 // 默认字体大小
          }

          // 解析字体粗细
          if (sourceElement.hasAttribute('data-font-weight')) {
            data.textThickness = sourceElement.getAttribute('data-font-weight')
          } else if (sourceElement.hasAttribute('data-text-thickness')) {
            data.textThickness = sourceElement.getAttribute('data-text-thickness')
          } else if (element.hasAttribute('font-weight')) {
            data.textThickness = element.getAttribute('font-weight')
          } else {
            data.textThickness = 'normal' // 默认字体粗细
          }

          // 保留兼容性 - 旧版本字段
          data.fontSize = data.pointSize;
          data.fontWeight = data.textThickness;

          // 解析背景和边框信息 - 从文本组或自身获取
          // 解析背景显示状态
          if (sourceElement.hasAttribute('data-has-background')) {
            data.hasBackground = sourceElement.getAttribute('data-has-background') === 'true' || sourceElement.getAttribute('data-has-background') === '1';
          } else {
            data.hasBackground = false;
          }
          
          // 解析边框信息
          if (sourceElement.hasAttribute('data-has-border')) {
            data.hasBorder = sourceElement.getAttribute('data-has-border') === 'true' || sourceElement.getAttribute('data-has-border') === '1'
          } else if (sourceElement.hasAttribute('data-show-display')) {
            data.hasBorder = sourceElement.getAttribute('data-show-display') === '1'
          } else {
            data.hasBorder = false
          }

          // 解析边框和背景样式信息
          // 解析背景颜色
          if (sourceElement.hasAttribute('data-background-color')) {
            data.backgroundColor = sourceElement.getAttribute('data-background-color')
          } else {
            data.backgroundColor = 'rgba(255, 255, 255, 0.7)'
          }
          
          // 解析边框颜色
          if (sourceElement.hasAttribute('data-border-color')) {
            data.borderColor = sourceElement.getAttribute('data-border-color')
          } else {
            data.borderColor = '#000000'
          }
          
          // 解析边框宽度
          if (sourceElement.hasAttribute('data-border-width')) {
            data.borderWidth = parseInt(sourceElement.getAttribute('data-border-width'), 10) || 1
          } else {
            data.borderWidth = 1
          }
          
          // 解析内边距
          if (sourceElement.hasAttribute('data-padding')) {
            data.padding = parseInt(sourceElement.getAttribute('data-padding'), 10) || 5
          } else {
            data.padding = 5
          }
          
          // 尝试查找背景矩形中的设置
          const backgroundRect = sourceElement.querySelector('rect[data-is-background="true"]');
          if (backgroundRect) {
            try {
              if (backgroundRect.hasAttribute('fill')) {
                data.backgroundColor = backgroundRect.getAttribute('fill');
              }
              if (backgroundRect.hasAttribute('stroke')) {
                data.borderColor = backgroundRect.getAttribute('stroke');
              }
              if (backgroundRect.hasAttribute('stroke-width')) {
                data.borderWidth = parseInt(backgroundRect.getAttribute('stroke-width'), 10) || 1;
              }
            } catch (error) {
              console.warn('解析背景矩形属性失败:', error);
            }
          }
        }

        // 收集所有其他data属性
        Array.from(element.attributes).forEach(attr => {
          if (attr.name.startsWith('data-') && 
              !['data-id', 'data-name', 'data-type', 'data-show-type', 'data-point', 'data-size', 
                'data-color', 'data-indicator-code', 'data-font-size', 'data-font-weight', 
                'data-point-size', 'data-text-thickness',
                'data-has-border', 'data-background-color'].includes(attr.name)) {
            const key = attr.name.substring(5) // 去掉 'data-' 前缀
            data[key] = attr.value
          }
        })

        // 创建点位对象
        const point = {
          id,
          x,
          y,
          name,
          type,
          showType: type,
          data,
          isText
        }

        console.log(`解析SVG点位: ${isText ? '文本' : type}点位 ID=${id}, 位置(${x}, ${y}), 名称: ${name}`)
        points.push(point)
        pointIndex++
      } catch (elementError) {
        console.error('解析单个点位出错:', elementError)
        // 继续处理下一个点位
      }
    })

    console.log(`共从SVG解析出${points.length}个有效点位`)
    return points
  } catch (error) {
    console.error('解析SVG点位失败:', error)
    return []
  }
}

/**
 * 获取元素组的位置（处理transform属性）
 * @param {Element} group SVG组元素
 * @returns {Object} 包含x和y坐标的对象
 */
function getGroupPosition(group) {
  try {
    // 首先检查是否有data-x和data-y属性
    if (group.hasAttribute('data-x') && group.hasAttribute('data-y')) {
      const x = parseFloat(group.getAttribute('data-x'));
      const y = parseFloat(group.getAttribute('data-y'));
      if (!isNaN(x) && !isNaN(y)) {
        console.log(`从data属性获取组位置: (${x}, ${y})`);
        return { x, y };
      }
    }

    // 检查是否有transform属性
    const transform = group.getAttribute('transform');
    if (transform) {
      // 解析transform属性，提取translate值
      const match = transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
      if (match && match.length >= 3) {
        const x = parseFloat(match[1]);
        const y = parseFloat(match[2]);
        if (!isNaN(x) && !isNaN(y)) {
          console.log(`从transform属性获取组位置: (${x}, ${y})`);
          // 保存到data属性，便于后续使用
          group.setAttribute('data-x', x.toString());
          group.setAttribute('data-y', y.toString());
          return { x, y };
        }
      }
    }
    
    // 如果没有找到有效的transform，尝试查找group的第一个文本元素
    const textElement = group.querySelector('text');
    if (textElement) {
      const position = getElementPosition(textElement);
      console.log(`从文本元素获取组位置: (${position.x}, ${position.y})`);
      // 保存到data属性，便于后续使用
      group.setAttribute('data-x', position.x.toString());
      group.setAttribute('data-y', position.y.toString());
      return position;
    }
    
    // 回退到默认位置
    console.warn('无法从组元素获取位置信息，使用默认值');
    return { x: 0, y: 0 };
  } catch (error) {
    console.error('获取组位置出错:', error);
    return { x: 0, y: 0 };
  }
}

/**
 * 获取元素在SVG中的位置
 * @param {Element} element SVG元素
 * @returns {Object} 包含x和y坐标的对象
 */
function getElementPosition(element) {
  let x = 0
  let y = 0

  // 首先尝试从data-x和data-y属性获取位置
  if (element.hasAttribute('data-x') && element.hasAttribute('data-y')) {
    x = parseFloat(element.getAttribute('data-x'))
    y = parseFloat(element.getAttribute('data-y'))
    if (!isNaN(x) && !isNaN(y)) {
      console.log(`从data属性获取元素位置: (${x}, ${y})`)
      return { x, y }
    }
  }

  if (element instanceof SVGElement) {
    // 对于SVG元素，尝试获取cx/cy或x/y属性
    if ('cx' in element && 'cy' in element) {
      x = parseFloat(element.getAttribute('cx') || '0')
      y = parseFloat(element.getAttribute('cy') || '0')
      console.log(`从cx/cy属性获取元素位置: (${x}, ${y})`)
    } else if ('x' in element && 'y' in element) {
      const width = parseFloat(element.getAttribute('width') || '0')
      const height = parseFloat(element.getAttribute('height') || '0')
      x = parseFloat(element.getAttribute('x') || '0') + width / 2
      y = parseFloat(element.getAttribute('y') || '0') + height / 2
      console.log(`从x/y+宽高属性计算元素位置: (${x}, ${y})`)
    } else {
      // 尝试从transform属性中获取位置
      const transform = element.getAttribute('transform')
      if (transform && transform.includes('translate')) {
        const match = transform.match(/translate\s*\(\s*([^,)]+)(?:,\s*([^)]+))?\s*\)/)
        if (match) {
          x = parseFloat(match[1] || '0')
          y = parseFloat(match[2] || '0')
          console.log(`从transform属性获取元素位置: (${x}, ${y})`)
        }
      } else {
        // 使用getBBox获取元素包围盒的中心
        try {
          if ('getBBox' in element) {
            const bbox = element.getBBox()
            x = bbox.x + bbox.width / 2
            y = bbox.y + bbox.height / 2
            console.log(`从getBBox计算元素位置: (${x}, ${y})`)
          }
        } catch (e) {
          console.warn('无法获取元素位置', e)
        }
      }
    }
    
    // 保存计算结果到data属性，便于后续使用
    if (x !== 0 || y !== 0) {
      element.setAttribute('data-x', x.toString())
      element.setAttribute('data-y', y.toString())
    }
  }

  return { x, y }
}

/**
 * 将客户端坐标转换为SVG坐标
 * @param {number} clientX 客户端X坐标
 * @param {number} clientY 客户端Y坐标
 * @param {SVGSVGElement} svgElement SVG元素
 * @param {number} [baseFitScale=1] 基准适应视图的缩放比例，缺省为1
 * @returns {Object} 包含x和y的SVG坐标对象
 */
export function convertClientToSvgCoordinates(clientX, clientY, svgElement, baseFitScale = 1) {
  // 打印详细的调试信息
  console.log("开始坐标转换", {
    clientX,
    clientY,
    svgElement: svgElement?.tagName,
    baseFitScale
  });

  // 获取SVG元素的位置和尺寸信息
  const svgRect = svgElement.getBoundingClientRect();
  console.log("SVG矩形:", svgRect);

  // 尝试使用SVG的CTM (当前变换矩阵)来计算坐标
  try {
    // 首选方法：使用SVG的内置转换方法，最为精确
    const svgPoint = svgElement.createSVGPoint();
    svgPoint.x = clientX;
    svgPoint.y = clientY;

    // 获取当前变换矩阵
    const ctm = svgElement.getScreenCTM();
    if (ctm) {
      // 打印CTM信息
      console.log("变换矩阵:", {
        a: ctm.a, // 水平缩放
        b: ctm.b, // 水平倾斜
        c: ctm.c, // 垂直倾斜
        d: ctm.d, // 垂直缩放
        e: ctm.e, // 水平平移
        f: ctm.f  // 垂直平移
      });

      // 获取当前变换矩阵的逆矩阵
      const inverseCtm = ctm.inverse();
      if (inverseCtm) {
        const transformedPoint = svgPoint.matrixTransform(inverseCtm);
        console.log("变换后的点:", transformedPoint);

        // 基准缩放比例调整，如果baseFitScale不为1
        const adjustedX = transformedPoint.x;
        const adjustedY = transformedPoint.y;

        // 返回调整后的坐标，保留2位小数
        const result = {
          x: Number(adjustedX.toFixed(2)),
          y: Number(adjustedY.toFixed(2))
        };
        
        console.log("最终计算结果:", result);
        return result;
      }
    }
  } catch (error) {
    console.warn('SVG矩阵转换失败，使用备用方法', error);
  }

  // 备用方法：手动计算坐标转换
  console.log("使用备用方法计算坐标");
  
  // 获取SVG的viewBox
  const viewBox = svgElement.viewBox?.baseVal || {
    x: 0,
    y: 0,
    width: svgRect.width,
    height: svgRect.height
  };
  console.log("ViewBox:", viewBox);

  // 计算SVG的内部变换（包括平移）
  let translateX = 0;
  let translateY = 0;
  
  // 检查SVG是否被包裹在有transform样式的容器中
  const svgWrapper = svgElement.closest('.svg-content')?.parentElement;
  if (svgWrapper) {
    const transform = window.getComputedStyle(svgWrapper).transform;
    console.log("父容器变换:", transform);
    
    // 从transform矩阵提取平移值
    if (transform && transform !== 'none' && transform.includes('matrix')) {
      const matrix = transform.match(/matrix\(([^)]+)\)/)?.[1].split(',').map(Number);
      if (matrix && matrix.length >= 6) {
        translateX = matrix[4];
        translateY = matrix[5];
        console.log("提取的平移值:", translateX, translateY);
      }
    }
  }

  // 计算从客户端坐标到SVG坐标的转换
  // 计算缩放比例，考虑viewBox和SVG实际大小的关系
  const scaleX = viewBox.width / svgRect.width;
  const scaleY = viewBox.height / svgRect.height;

  // 应用变换：客户端坐标 -> SVG用户空间坐标
  // 1. 减去SVG元素的客户端位置，得到相对于SVG的客户端坐标
  // 2. 考虑可能的平移
  // 3. 应用缩放比例
  // 4. 添加viewBox的偏移
  const relativeX = clientX - svgRect.left;
  const relativeY = clientY - svgRect.top;
  
  const svgX = ((relativeX - translateX) * scaleX) + viewBox.x;
  const svgY = ((relativeY - translateY) * scaleY) + viewBox.y;

  const result = {
    x: Number(svgX.toFixed(2)),
    y: Number(svgY.toFixed(2))
  };

  console.log("备用方法计算结果:", result);
  return result;
}

/**
 * 获取SVG中的元素
 * @param {Element} svgElement SVG元素
 * @param {string} id 元素ID
 * @returns {Element|null} 找到的元素
 */
export function getElementFromSvg(svgElement, id) {
  if (!svgElement || !id) return null;
  
  // 优先使用data-id属性查找
  let element = svgElement.querySelector(`[data-id="${id}"]`);
  
  // 如果未找到且ID是纯数字，使用getElementById
  if (!element && /^\d+$/.test(id.toString())) {
    element = svgElement.getElementById(id);
    
    // 如果仍未找到，尝试使用更多选择器
    if (!element) {
      // 对于纯数字ID，尝试使用特定的选择器
      const selectors = [
        `[data-id="${id}"]`,
        `#point-${id}`,
        `#text-${id}`,
        `#text-group-${id}`
      ];
      
      for (const selector of selectors) {
        try {
          element = svgElement.querySelector(selector);
          if (element) break;
        } catch (e) {
          console.warn(`选择器 ${selector} 查询失败:`, e);
        }
      }
    }
  }
  
  // 仍未找到，尝试使用更多选择器
  if (!element) {
    const tagNames = ['circle', 'rect', 'polygon', 'text', 'g'];
    for (const tagName of tagNames) {
      try {
        element = svgElement.querySelector(`${tagName}[data-id="${id}"]`);
        if (element) break;
      } catch (e) {
        console.warn(`选择器 ${tagName}[data-id="${id}"] 查询失败:`, e);
      }
    }
  }
  
  return element;
}
