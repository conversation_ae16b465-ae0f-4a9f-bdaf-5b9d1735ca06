<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="80%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-tabs v-model="activeTabName">
        <el-tab-pane label="基本信息" name="basic">
          <el-row>
            <el-col :span="8">
              <el-form-item label="主题名称" prop="themeName">
                <el-input v-model="formData.themeName" placeholder="请输入主题名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主题类型" prop="themeType">
                <el-select v-model="formData.themeType" placeholder="请选择主题类型" style="width: 100%">
                  <el-option label="安全月活动" value="safety-month" />
                  <el-option label="节假日教育" value="holiday" />
                  <el-option label="专项培训" value="special" />
                  <el-option label="安全技能" value="skill" />
                  <el-option label="应急演练" value="emergency" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="组织部门" prop="departmentId">
                <el-tree-select
                  v-model="formData.departmentId"
                  :data="departmentOptions"
                  placeholder="请选择组织部门"
                  check-strictly
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="负责人" prop="organizer">
                <el-input v-model="formData.organizer" placeholder="请输入负责人" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开始时间" prop="startTime">
                <el-date-picker
                  v-model="formData.startTime"
                  type="datetime"
                  placeholder="选择开始时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结束时间" prop="endTime">
                <el-date-picker
                  v-model="formData.endTime"
                  type="datetime"
                  placeholder="选择结束时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="培训课时" prop="duration">
                <el-input-number
                  v-model="formData.duration"
                  :min="0.5"
                  :max="100"
                  :step="0.5"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="活动状态" prop="status">
                <el-select v-model="formData.status" placeholder="请选择活动状态" style="width: 100%">
                  <el-option label="未开始" value="not-started" />
                  <el-option label="进行中" value="in-progress" />
                  <el-option label="已结束" value="finished" />
                  <el-option label="已取消" value="canceled" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地点" prop="location">
                <el-input v-model="formData.location" placeholder="请输入活动地点" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="主题描述" prop="description">
                <el-input
                  v-model="formData.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入主题描述"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        
        <el-tab-pane label="参与人员" name="participants">
          <el-row style="margin-bottom: 20px;">
            <el-col :span="24">
              <el-button type="primary" @click="handleOpenSelectParticipants">选择参与人员</el-button>
            </el-col>
          </el-row>
          
          <el-table :data="participantsList" :stripe="true">
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column label="姓名" prop="name" align="center" />
            <el-table-column label="部门" prop="departmentName" align="center" />
            <el-table-column label="岗位" prop="postName" align="center" />
            <el-table-column label="参与状态" prop="participateStatus" align="center">
              <template #default="scope">
                <el-select v-model="scope.row.participateStatus" placeholder="选择状态" style="width: 100%">
                  <el-option label="已参加" value="attended" />
                  <el-option label="未参加" value="not-attended" />
                  <el-option label="请假" value="leave" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="考核评分" prop="score" align="center">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.score"
                  :min="0"
                  :max="100"
                  :step="1"
                  :disabled="scope.row.participateStatus !== 'attended'"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template #default="scope">
                <el-button
                  link
                  type="danger"
                  @click="handleRemoveParticipant(scope.$index)"
                >移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="资料上传" name="attachments">
          <el-upload
            v-model:file-list="fileList"
            :action="uploadAction"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-exceed="handleExceed"
            :limit="5"
            multiple
            :auto-upload="true"
            list-type="picture"
          >
            <el-button type="primary">上传资料</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传图片、视频、文档等多种格式文件，单个文件不超过50MB，最多上传5个文件
              </div>
            </template>
          </el-upload>
          
          <el-divider content-position="center">已上传资料</el-divider>
          
          <el-table :data="attachmentsList" :stripe="true">
            <el-table-column label="资料名称" prop="fileName" />
            <el-table-column label="资料类型" prop="fileType" width="120">
              <template #default="scope">
                <el-tag v-if="scope.row.fileType === 'pdf'">PDF文档</el-tag>
                <el-tag v-else-if="scope.row.fileType === 'image'" type="success">图片</el-tag>
                <el-tag v-else-if="scope.row.fileType === 'video'" type="warning">视频</el-tag>
                <el-tag v-else-if="scope.row.fileType === 'doc'" type="danger">Word文档</el-tag>
                <el-tag v-else type="info">其他</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="上传时间" prop="uploadTime" width="180" />
            <el-table-column label="上传人" prop="uploader" />
            <el-table-column label="操作" align="center" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="handleViewFile(scope.row)">预览</el-button>
                <el-button link type="danger" @click="handleDeleteFile(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    
    <!-- 选择参与人员弹窗 -->
    <Dialog v-model="selectParticipantsDialogVisible" title="选择参与人员" width="70%">
      <el-form :inline="true" class="search-form">
        <el-form-item label="姓名">
          <el-input v-model="participantSearchName" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="participantSearchDepartment"
            :data="departmentOptions"
            placeholder="请选择部门"
            clearable
            check-strictly
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchEmployees">搜索</el-button>
          <el-button @click="resetEmployeeSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table
        ref="multipleTableRef"
        :data="employeeList"
        :stripe="true"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="姓名" prop="name" align="center" />
        <el-table-column label="部门" prop="departmentName" align="center" />
        <el-table-column label="岗位" prop="postName" align="center" />
        <el-table-column label="入职日期" prop="entryDate" align="center" />
      </el-table>
      
      <div style="margin-top: 20px; text-align: right;">
        <el-button @click="selectParticipantsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelectParticipants">确定</el-button>
      </div>
    </Dialog>
    
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { ElTable, UploadProps, UploadUserFile } from 'element-plus'

/** 主题教育编辑 */
defineOptions({ name: 'ThemeEdit' })

const emit = defineEmits(['success'])
const message = useMessage() // 消息弹窗

// 基础状态
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中
const formTypeValue = ref('') // 表单的类型：create - 新增；update - 修改
const activeTabName = ref('basic') // 当前激活的Tab

// 员工选择弹窗相关
const selectParticipantsDialogVisible = ref(false)
const participantSearchName = ref('')
const participantSearchDepartment = ref()
const employeeList = ref<any[]>([])
const multipleSelection = ref<any[]>([])
const multipleTableRef = ref<InstanceType<typeof ElTable>>()

// 上传资料相关
const fileList = ref<UploadUserFile[]>([])
const attachmentsList = ref<any[]>([])
const uploadAction = ref('/api/file/upload') // 实际环境替换为真实的上传接口

// 参与人员列表
const participantsList = ref<any[]>([])

// 部门树形选择数据
const departmentOptions = ref([
  {
    value: 1,
    label: '公司总部',
    children: [
      {
        value: 11,
        label: '安全环保部'
      },
      {
        value: 12,
        label: '行政部'
      }
    ]
  },
  {
    value: 2,
    label: '生产部',
    children: [
      {
        value: 21,
        label: '一号车间'
      },
      {
        value: 22,
        label: '二号车间'
      },
      {
        value: 23,
        label: '三号车间'
      }
    ]
  }
])

const formData = ref({
  id: undefined as number | undefined,
  themeName: undefined as string | undefined,
  themeType: undefined as string | undefined,
  departmentId: undefined as number | undefined,
  departmentName: undefined as string | undefined,
  organizer: undefined as string | undefined,
  startTime: undefined as Date | undefined,
  endTime: undefined as Date | undefined,
  duration: 1 as number,
  status: 'not-started' as string,
  location: undefined as string | undefined,
  description: undefined as string | undefined,
  participantCount: 0 as number,
  attachments: [] as any[]
})

// 表单校验规则
const formRules = reactive({
  themeName: [{ required: true, message: '请输入主题名称', trigger: 'blur' }],
  themeType: [{ required: true, message: '请选择主题类型', trigger: 'change' }],
  departmentId: [{ required: true, message: '请选择组织部门', trigger: 'change' }],
  organizer: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  duration: [{ required: true, message: '请输入培训课时', trigger: 'blur' }],
  status: [{ required: true, message: '请选择活动状态', trigger: 'change' }]
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  formTypeValue.value = type
  resetForm()
  
  // 设置标题
  dialogTitle.value = type === 'create' ? '新增主题教育' : '修改主题教育'
  
  // 修改时，设置表单数据
  if (id) {
    formLoading.value = true
    try {
      // 模拟获取数据
      // const data = await ThemeApi.getDetail(id)
      
      // 模拟数据
      setTimeout(() => {
        if (id === 1) {
          formData.value = {
            id: 1,
            themeName: '安全生产月知识培训',
            themeType: 'safety-month',
            departmentId: 11,
            departmentName: '安全环保部',
            organizer: '张经理',
            startTime: new Date('2023-06-01 09:00:00'),
            endTime: new Date('2023-06-30 18:00:00'),
            duration: 8,
            status: 'finished',
            location: '公司大会议室',
            description: '为提高员工安全意识，举办本次安全生产月系列活动，包括安全培训、安全知识竞赛等环节。',
            participantCount: 45,
            attachments: [
              { id: 1, fileName: '安全生产月活动方案.pdf', fileType: 'pdf', uploadTime: '2023-05-25 10:15:30', uploader: '张经理' },
              { id: 2, fileName: '安全培训PPT.pptx', fileType: 'doc', uploadTime: '2023-05-26 14:20:15', uploader: '李安全' }
            ]
          }
          
          // 设置附件列表
          attachmentsList.value = formData.value.attachments
          
          // 模拟参与人员数据
          participantsList.value = [
            { id: 1, name: '张三', departmentId: 21, departmentName: '一号车间', postName: '操作工', participateStatus: 'attended', score: 88 },
            { id: 2, name: '李四', departmentId: 21, departmentName: '一号车间', postName: '电工', participateStatus: 'attended', score: 92 },
            { id: 3, name: '王五', departmentId: 22, departmentName: '二号车间', postName: '车间管理', participateStatus: 'not-attended', score: null },
            { id: 4, name: '赵六', departmentId: 23, departmentName: '三号车间', postName: '焊工', participateStatus: 'leave', score: null }
          ]
        }
        formLoading.value = false
      }, 500)
    } catch (error) {
      console.error('获取主题教育详情失败:', error)
    } finally {
      formLoading.value = false
    }
  }
}

/** 上传前的校验 */
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 文件大小限制：50MB
  const maxSize = 50 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过50MB!')
    return false
  }
  return true
}

/** 上传成功回调 */
const handleUploadSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  message.success('文件上传成功')
  
  // 确定文件类型
  let fileType = 'other'
  const fileName = uploadFile.name.toLowerCase()
  if (fileName.endsWith('.pdf')) {
    fileType = 'pdf'
  } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png') || fileName.endsWith('.gif')) {
    fileType = 'image'
  } else if (fileName.endsWith('.mp4') || fileName.endsWith('.avi') || fileName.endsWith('.mov')) {
    fileType = 'video'
  } else if (fileName.endsWith('.doc') || fileName.endsWith('.docx') || fileName.endsWith('.ppt') || 
             fileName.endsWith('.pptx') || fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) {
    fileType = 'doc'
  }
  
  // 添加到资料列表
  attachmentsList.value.push({
    id: new Date().getTime(), // 模拟ID
    fileName: uploadFile.name,
    fileType: fileType,
    uploadTime: new Date().toLocaleString(),
    uploader: '当前用户' // 实际环境替换为真实的用户
  })
}

/** 上传失败回调 */
const handleUploadError: UploadProps['onError'] = (error) => {
  message.error('文件上传失败')
  console.error('文件上传失败:', error)
}

/** 文件超出限制回调 */
const handleExceed: UploadProps['onExceed'] = (files) => {
  message.warning(`最多只能上传5个文件，本次选择了 ${files.length} 个文件`)
}

/** 预览文件 */
const handleViewFile = (file: any) => {
  message.success(`预览文件: ${file.fileName}`)
  // 实际实现文件预览逻辑
}

/** 删除文件 */
const handleDeleteFile = (file: any) => {
  try {
    message.confirm('是否确认删除该文件?').then(() => {
      // 从资料列表中移除
      const index = attachmentsList.value.findIndex(item => item.id === file.id)
      if (index !== -1) {
        attachmentsList.value.splice(index, 1)
        message.success('文件删除成功')
      }
    })
  } catch {
    // 取消删除操作
  }
}

/** 打开选择参与人员弹窗 */
const handleOpenSelectParticipants = () => {
  selectParticipantsDialogVisible.value = true
  searchEmployees()
}

/** 搜索员工 */
const searchEmployees = () => {
  // 模拟搜索员工数据
  setTimeout(() => {
    employeeList.value = [
      { id: 5, name: '陈七', departmentId: 21, departmentName: '一号车间', postName: '操作工', entryDate: '2022-01-15' },
      { id: 6, name: '钱八', departmentId: 22, departmentName: '二号车间', postName: '电工', entryDate: '2022-03-01' },
      { id: 7, name: '孙九', departmentId: 23, departmentName: '三号车间', postName: '焊工', entryDate: '2022-05-10' },
      { id: 8, name: '周十', departmentId: 11, departmentName: '安全环保部', postName: '安全员', entryDate: '2021-11-05' }
    ]
  }, 300)
}

/** 重置员工搜索条件 */
const resetEmployeeSearch = () => {
  participantSearchName.value = ''
  participantSearchDepartment.value = undefined
  searchEmployees()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

/** 确认选择参与人员 */
const confirmSelectParticipants = () => {
  if (multipleSelection.value.length === 0) {
    message.warning('请至少选择一名员工')
    return
  }
  
  // 将选中的员工添加到参与人员列表，并避免重复添加
  multipleSelection.value.forEach(employee => {
    const isExist = participantsList.value.some(item => item.id === employee.id)
    if (!isExist) {
      participantsList.value.push({
        ...employee,
        participateStatus: 'not-attended',
        score: null
      })
    }
  })
  
  // 更新参与人数
  formData.value.participantCount = participantsList.value.length
  
  // 关闭弹窗
  selectParticipantsDialogVisible.value = false
  message.success(`已添加${multipleSelection.value.length}名参与人员`)
}

/** 移除参与人员 */
const handleRemoveParticipant = (index: number) => {
  participantsList.value.splice(index, 1)
  // 更新参与人数
  formData.value.participantCount = participantsList.value.length
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    themeName: undefined,
    themeType: undefined,
    departmentId: undefined,
    departmentName: undefined,
    organizer: undefined,
    startTime: undefined,
    endTime: undefined,
    duration: 1,
    status: 'not-started',
    location: undefined,
    description: undefined,
    participantCount: 0,
    attachments: []
  }
  activeTabName.value = 'basic'
  fileList.value = []
  attachmentsList.value = []
  participantsList.value = []
}

/** 提交表单 */
const submitForm = async () => {
  // 表单校验
  const formRef = ref()
  try {
    formLoading.value = true
    
    // 校验表单
    await formRef.value?.validate()
    
    // 构建提交的数据
    const data = {
      ...formData.value,
      attachments: attachmentsList.value,
      participants: participantsList.value
    }
    
    // 保存操作
    if (formTypeValue.value === 'create') {
      // 模拟创建操作
      // await ThemeApi.create(data)
      console.log('创建主题教育:', data)
    } else {
      // 模拟更新操作
      // await ThemeApi.update(data)
      console.log('更新主题教育:', data)
    }
    
    // 提示信息
    message.success(formTypeValue.value === 'create' ? '创建成功' : '修改成功')
    // 关闭弹窗
    dialogVisible.value = false
    // 通知父组件刷新
    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
  } finally {
    formLoading.value = false
  }
}

// 向父组件暴露方法
defineExpose({ open })
</script>

<style scoped lang="scss">
.el-form {
  padding: 20px;
}

.search-form {
  margin-bottom: 16px;
}

.el-upload__tip {
  color: #6c757d;
  font-size: 12px;
  margin-top: 5px;
}
</style>