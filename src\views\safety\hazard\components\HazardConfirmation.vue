<template>
  <div class="hazard-confirmation">
    <div class="operation-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="请输入关键字搜索"
        style="width: 200px"
        clearable
      />
      <el-select v-model="status" placeholder="验收状态" style="width: 120px; margin-left: 16px">
        <el-option label="全部" value="" />
        <el-option label="待验收" value="waiting" />
        <el-option label="验收通过" value="passed" />
        <el-option label="验收不通过" value="failed" />
      </el-select>
    </div>

    <el-table :data="confirmationList" style="width: 100%; margin-top: 16px">
      <el-table-column type="expand">
        <template #default="{ row }">
          <el-form label-position="left" inline class="confirmation-detail">
            <el-form-item label="整改前照片">
              <el-image
                style="width: 100px; height: 100px"
                :src="row.beforeImage"
                :preview-src-list="[row.beforeImage]"
              />
            </el-form-item>
            <el-form-item label="整改后照片">
              <el-image
                style="width: 100px; height: 100px"
                :src="row.afterImage"
                :preview-src-list="[row.afterImage]"
              />
            </el-form-item>
            <el-form-item label="整改措施">
              <div class="measures-list">
                <p v-for="(measure, index) in row.measures" :key="index">{{ index + 1 }}. {{ measure }}</p>
              </div>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column prop="code" label="任务编号" width="120" />
      <el-table-column prop="hazardName" label="隐患名称" width="180" />
      <el-table-column prop="department" label="责任部门" width="120" />
      <el-table-column prop="completionTime" label="整改完成时间" width="180" />
      <el-table-column prop="inspector" label="验收人" width="120" />
      <el-table-column prop="status" label="验收状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="confirmationTime" label="验收时间" width="180" />
      <el-table-column prop="remarks" label="验收意见" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button 
            v-if="row.status === '待验收'"
            link 
            type="primary" 
            @click="handleConfirm(row)"
          >验收确认</el-button>
          <el-button link type="primary" @click="handleView(row)">查看详情</el-button>
          <el-button link type="success" @click="handleExport(row)">导出报告</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const searchKeyword = ref('')
const status = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 模拟数据
const confirmationList = ref([
  {
    code: 'RECT001',
    hazardName: '设备防护装置缺失',
    department: '生产部',
    completionTime: '2024-04-15 14:30:00',
    inspector: '李四',
    status: '待验收',
    confirmationTime: '',
    remarks: '',
    beforeImage: 'path/to/before/image.jpg',
    afterImage: 'path/to/after/image.jpg',
    measures: [
      '采购防护装置',
      '安装调试',
      '安全培训'
    ]
  }
])

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    '待验收': 'warning',
    '验收通过': 'success',
    '验收不通过': 'danger'
  }
  return types[status] || 'info'
}

const handleConfirm = (row: any) => {
  // TODO: 打开验收确认弹窗
}

const handleView = (row: any) => {
  // TODO: 打开查看详情弹窗
}

const handleExport = (row: any) => {
  // TODO: 导出验收报告
  ElMessage.success('验收报告导出成功')
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  // TODO: 重新加载数据
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // TODO: 重新加载数据
}
</script>

<style lang="scss" scoped>
.hazard-confirmation {
  .operation-bar {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .confirmation-detail {
    padding: 20px;

    .el-form-item {
      margin-right: 40px;
    }

    .measures-list {
      p {
        margin: 5px 0;
      }
    }
  }
}
</style> 