<script lang="ts" setup>
import routerSearch from '@/components/RouterSearch/index.vue'
import ScaleController from '@/components/ScaleController/index.vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { useDesign } from '@/hooks/web/useDesign'
import { useAppStore } from '@/store/modules/app'
import { isDark } from '@/utils/is'
import { onMounted, computed } from 'vue'
import { applyScreenScaling, initScreenDetection } from '@/utils/screenScaling'

defineOptions({ name: 'APP' })

const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('app')
const appStore = useAppStore()
const currentSize = computed(() => appStore.getCurrentSize)
const greyMode = computed(() => appStore.getGreyMode)
const { wsCache } = useCache()

// 根据浏览器当前主题设置系统主题色
const setDefaultTheme = () => {
  let isDarkTheme = wsCache.get(CACHE_KEY.IS_DARK)
  if (isDarkTheme === null) {
    isDarkTheme = isDark()
  }
  appStore.setIsDark(isDarkTheme)
}
setDefaultTheme()


// 获取缩放倍数（1*系统缩放倍数*浏览器缩放倍数）
function getZoom() {
  let zoom = 1;
  const ua = navigator.userAgent.toLowerCase();

  if (window.devicePixelRatio !== undefined) {
    zoom = window.devicePixelRatio;
  } else if (ua.indexOf('msie') !== -1) {
    // 使用类型断言处理IE特有属性
    const screenAny = window.screen as any;
    if (screenAny.deviceXDPI && screenAny.logicalXDPI) {
      zoom = screenAny.deviceXDPI / screenAny.logicalXDPI;
    }
  } else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {
    zoom = window.outerWidth / window.innerWidth;
  }
  return getDecimal(zoom);
}

const getDecimal = (num) => {
  return Math.round(num * 100) / 100;
};

function getAllZoom() {
  // 总缩放倍数
  const zoom = getZoom();
  // 屏幕分辨率
  const screenResolution = window.screen.width;
  // 获取浏览器内部宽度
  const browserWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  // 浏览器缩放倍数
  // 浏览器外部宽度不受浏览器缩放影响，浏览器内部宽度受影响,所以根据这个可以计算出浏览器缩放倍数（F12调试工具的占位会影响该值）
  const browserZoom = getDecimal(window.outerWidth / browserWidth);
  // 系统缩放倍数
  const systemZoom = getDecimal(zoom / browserZoom);
  // 系统分辨率
  const systemResolution = Math.round(screenResolution * systemZoom);

  console.log('系统分辨率', systemResolution, '屏幕分辨率', screenResolution, '浏览器外部宽度', window.outerWidth, '浏览器内部宽度', browserWidth, '总缩放倍数', zoom, '浏览器缩放倍数', browserZoom, '系统缩放倍数', systemZoom);

  return {
    zoom,
    browserZoom,
    systemZoom,
    systemResolution
  }
}




onMounted(() => {
  // 检查是否是登录后的首次加载
  const isFirstLoadAfterLogin = sessionStorage.getItem('isFirstLoadAfterLogin')

  if (!isFirstLoadAfterLogin) {
    // 登录后的首次加载，清除之前的水厂信息
    localStorage.removeItem('currentStation')
    appStore.currentStation = null
    // 标记为已初始化
    sessionStorage.setItem('isFirstLoadAfterLogin', 'true')
  } else {
    // 非首次加载，正常初始化当前水厂状态
    appStore.initCurrentStation()
  }

  // 应用屏幕分辨率适配的缩放
  applyScreenScaling()

  // 初始化屏幕变化检测
  initScreenDetection()
})
</script>
<template>
  <ConfigGlobal :size="currentSize">
    <RouterView :class="greyMode ? `${prefixCls}-grey-mode` : ''" />
    <routerSearch />
    <ScaleController />
  </ConfigGlobal>
</template>
<style lang="scss">
$prefix-cls: #{$namespace}-app;

.size {
  width: 100%;
  height: 100%;
}

html,
body {
  @extend .size;

  padding: 0 !important;
  margin: 0;
  overflow: hidden;

  #app {
    @extend .size;
  }
}

.#{$prefix-cls}-grey-mode {
  filter: grayscale(100%);
}
</style>
