<template>
  <div class="hazard-list">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="category-card">
          <template #header>
            <div class="card-header">
              <span>隐患分类</span>
              <el-button link type="primary" @click="handleAddCategory">新增分类</el-button>
            </div>
          </template>
          <el-tree
            :data="categoryTree"
            :props="defaultProps"
            @node-click="handleNodeClick"
            default-expand-all
          />
        </el-card>
      </el-col>
      
      <el-col :span="18">
        <div class="operation-bar">
          <el-button type="primary" @click="handleAdd">新增隐患</el-button>
          <el-input
            v-model="searchKeyword"
            placeholder="请输入关键字搜索"
            style="width: 200px; margin-left: 16px"
            clearable
          />
          <el-select v-model="severity" placeholder="危险等级" style="width: 120px; margin-left: 16px">
            <el-option label="全部" value="" />
            <el-option label="高危" value="high" />
            <el-option label="中危" value="medium" />
            <el-option label="低危" value="low" />
          </el-select>
        </div>

        <el-table :data="hazardList" style="width: 100%; margin-top: 16px">
          <el-table-column prop="code" label="隐患编号" width="120" />
          <el-table-column prop="name" label="隐患名称" width="180" />
          <el-table-column prop="category" label="所属分类" width="120" />
          <el-table-column prop="severity" label="危险等级" width="100">
            <template #default="{ row }">
              <el-tag :type="getSeverityType(row.severity)">{{ row.severity }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="隐患描述" />
          <el-table-column prop="solution" label="建议解决方案" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
              <el-button link type="primary" @click="handleView(row)">查看</el-button>
              <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const searchKeyword = ref('')
const severity = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 分类树数据
const categoryTree = ref([
  {
    label: '设备类',
    children: [
      { label: '机械设备' },
      { label: '电气设备' },
      { label: '特种设备' }
    ]
  },
  {
    label: '环境类',
    children: [
      { label: '作业环境' },
      { label: '消防安全' },
      { label: '职业卫生' }
    ]
  }
])

const defaultProps = {
  children: 'children',
  label: 'label'
}

// 模拟数据
const hazardList = ref([
  {
    code: 'HZD001',
    name: '设备防护装置缺失',
    category: '机械设备',
    severity: '高危',
    description: '设备运转部件的防护罩缺失或损坏',
    solution: '立即安装或修复防护装置，并定期检查维护'
  }
])

const handleNodeClick = (data: any) => {
  // TODO: 根据选中的分类筛选数据
}

const handleAddCategory = () => {
  // TODO: 打开新增分类弹窗
}

const handleAdd = () => {
  // TODO: 打开新增隐患弹窗
}

const handleEdit = (row: any) => {
  // TODO: 打开编辑隐患弹窗
}

const handleView = (row: any) => {
  // TODO: 打开查看隐患弹窗
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确认删除该隐患吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
  }).catch(() => {})
}

const getSeverityType = (severity: string) => {
  const types: Record<string, string> = {
    '高危': 'danger',
    '中危': 'warning',
    '低危': 'info'
  }
  return types[severity] || 'info'
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  // TODO: 重新加载数据
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // TODO: 重新加载数据
}
</script>

<style lang="scss" scoped>
.hazard-list {
  .category-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .operation-bar {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 