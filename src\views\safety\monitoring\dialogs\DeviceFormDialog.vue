<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备类型" prop="deviceType">
            <el-select v-model="formData.deviceType" placeholder="请选择设备类型" class="w-full">
              <el-option
                v-for="item in deviceTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编号" prop="deviceCode">
            <el-input v-model="formData.deviceCode" placeholder="请输入设备编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="formData.deviceName" placeholder="请输入设备名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规格型号" prop="specifications">
            <el-input v-model="formData.specifications" placeholder="请输入规格型号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="安装位置" prop="location">
            <el-input v-model="formData.location" placeholder="请输入安装位置" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检测周期" prop="inspectionCycle">
            <el-input v-model="formData.inspectionCycle" placeholder="例如: 6个月" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上次检测日期" prop="lastInspectionDate">
            <el-date-picker
              v-model="formData.lastInspectionDate"
              type="date"
              placeholder="选择日期"
              class="w-full"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="下次检测日期" prop="nextInspectionDate">
            <el-date-picker
              v-model="formData.nextInspectionDate"
              type="date"
              placeholder="选择日期"
              class="w-full"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="安装日期" prop="installationDate">
            <el-date-picker
              v-model="formData.installationDate"
              type="date"
              placeholder="选择日期"
              class="w-full"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="制造商" prop="manufacturer">
            <el-input v-model="formData.manufacturer" placeholder="请输入制造商" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任人" prop="responsiblePerson">
            <el-input v-model="formData.responsiblePerson" placeholder="请输入责任人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" class="w-full">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 不同设备类型的特殊字段 -->
      <div v-if="formData.deviceType === '流量计'">
        <el-divider content-position="center">流量计特殊参数</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="口径" prop="diameter">
              <el-input v-model="formData.diameter" placeholder="请输入口径" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="量程" prop="range">
              <el-input v-model="formData.range" placeholder="请输入量程" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div v-if="formData.deviceType === '压力表'">
        <el-divider content-position="center">压力表特殊参数</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="精度等级" prop="accuracyClass">
              <el-input v-model="formData.accuracyClass" placeholder="请输入精度等级" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="量程范围" prop="pressureRange">
              <el-input v-model="formData.pressureRange" placeholder="请输入量程范围" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div v-if="formData.deviceType === '压力容器'">
        <el-divider content-position="center">压力容器特殊参数</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="容积" prop="volume">
              <el-input v-model="formData.volume" placeholder="请输入容积" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设计压力" prop="designPressure">
              <el-input v-model="formData.designPressure" placeholder="请输入设计压力" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="容器类别" prop="vesselCategory">
              <el-select v-model="formData.vesselCategory" placeholder="请选择容器类别" class="w-full">
                <el-option label="一类" value="一类" />
                <el-option label="二类" value="二类" />
                <el-option label="三类" value="三类" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="介质" prop="medium">
              <el-input v-model="formData.medium" placeholder="请输入介质" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <el-divider />

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

const emit = defineEmits(['success'])

// 对话框状态
const dialogVisible = ref(false)
const isEdit = ref(false)
const editId = ref(null)

// 对话框标题
const dialogTitle = computed(() => (isEdit.value ? '编辑设备' : '新增设备'))

// 表单引用
const formRef = ref<FormInstance>()

// 设备类型选项
const deviceTypeOptions = [
  { label: '流量计', value: '流量计' },
  { label: '压力表', value: '压力表' },
  { label: '防雷设备', value: '防雷设备' },
  { label: '预防性试验', value: '预防性试验' },
  { label: '起重设备', value: '起重设备' },
  { label: '压力容器', value: '压力容器' },
  { label: '安全工器具', value: '安全工器具' },
  { label: '常规检测', value: '常规检测' },
  { label: '非常规检测', value: '非常规检测' }
]

// 状态选项
const statusOptions = [
  { label: '正常', value: 'normal' },
  { label: '即将到期', value: 'comingDue' },
  { label: '已过期', value: 'expired' },
  { label: '维护中', value: 'maintenance' }
]

// 表单数据
const defaultFormData = {
  deviceType: '',
  deviceCode: '',
  deviceName: '',
  specifications: '',
  location: '',
  inspectionCycle: '',
  lastInspectionDate: '',
  nextInspectionDate: '',
  installationDate: '',
  manufacturer: '',
  responsiblePerson: '',
  status: 'normal',
  remarks: '',
  // 流量计特殊字段
  diameter: '',
  range: '',
  // 压力表特殊字段
  accuracyClass: '',
  pressureRange: '',
  // 压力容器特殊字段
  volume: '',
  designPressure: '',
  vesselCategory: '',
  medium: ''
}

const formData = reactive({ ...defaultFormData })

// 验证规则
const rules = reactive<FormRules>({
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  deviceCode: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  specifications: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
  location: [{ required: true, message: '请输入安装位置', trigger: 'blur' }],
  inspectionCycle: [{ required: true, message: '请输入检测周期', trigger: 'blur' }],
  lastInspectionDate: [{ required: true, message: '请选择上次检测日期', trigger: 'change' }],
  nextInspectionDate: [{ required: true, message: '请选择下次检测日期', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
})

// 打开对话框
const open = (row?: any) => {
  dialogVisible.value = true
  reset()

  if (row && row.id) {
    // 编辑模式
    isEdit.value = true
    editId.value = row.id

    // 填充表单数据
    Object.keys(formData).forEach((key) => {
      if (row[key] !== undefined) {
        formData[key] = row[key]
      }
    })
  } else {
    // 新建模式
    isEdit.value = false
    editId.value = null

    // 生成设备编号
    generateDeviceCode()
  }
}

// 生成设备编号
const generateDeviceCode = () => {
  if (!formData.deviceType) return

  const typeCodeMap = {
    '流量计': 'FLM',
    '压力表': 'PG',
    '防雷设备': 'LP',
    '预防性试验': 'PT',
    '起重设备': 'LE',
    '压力容器': 'PV',
    '安全工器具': 'ST',
    '常规检测': 'RD',
    '非常规检测': 'NRD'
  }

  const typeCode = typeCodeMap[formData.deviceType] || 'DEV'
  const dateCode = new Date().getFullYear().toString()
  const randomNum = Math.floor(Math.random() * 999).toString().padStart(3, '0')
  
  formData.deviceCode = `${typeCode}-${dateCode}${randomNum}`
}

// 重置表单
const reset = () => {
  formRef.value?.resetFields()
  Object.assign(formData, defaultFormData)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构建提交数据
    const submitData = {
      ...formData,
      id: isEdit.value ? editId.value : undefined
    }

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500))
    // const res = isEdit.value
    //   ? await updateDeviceApi(submitData)
    //   : await createDeviceApi(submitData)

    ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 暴露给父组件的方法
defineExpose({ open })
</script>

<style scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
}
</style> 