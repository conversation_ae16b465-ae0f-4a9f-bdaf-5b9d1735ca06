<template>
  <div class="entry-list">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="员工姓名">
          <el-input v-model="searchForm.employeeName" placeholder="请输入员工姓名" />
        </el-form-item>
        <el-form-item label="教育级别">
          <el-select v-model="searchForm.level" placeholder="请选择教育级别">
            <el-option label="厂级" value="厂级" />
            <el-option label="车间级" value="车间级" />
            <el-option label="班组级" value="班组级" />
          </el-select>
        </el-form-item>
        <el-form-item label="教育日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleAdd">新增记录</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="employeeName" label="员工姓名" width="120" />
      <el-table-column prop="level" label="教育级别" width="100">
        <template #default="scope">
          <el-tag :type="getLevelTag(scope.row.level)">
            {{ getLevelText(scope.row.level) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="education_date" label="教育日期" width="120" />
      <el-table-column prop="educator" label="教育人" width="120" />
      <el-table-column prop="notes" label="备注" min-width="200" show-overflow-tooltip />
      <el-table-column label="附件" width="120">
        <template #default="scope">
          <el-button 
            type="primary" 
            link 
            @click="handleDownload(scope.row)"
            v-if="scope.row.attachment_url"
          >
            下载附件
          </el-button>
          <span v-else>无附件</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增教育记录' : '编辑教育记录'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="员工" prop="employee_id">
          <el-select 
            v-model="form.employee_id" 
            placeholder="请选择员工"
            filterable
            remote
            :remote-method="remoteSearchEmployee"
            :loading="employeeLoading"
            style="width: 100%"
          >
            <el-option
              v-for="item in employeeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="教育级别" prop="level">
          <el-select v-model="form.level" placeholder="请选择教育级别" style="width: 100%">
            <el-option label="厂级" value="厂级" />
            <el-option label="车间级" value="车间级" />
            <el-option label="班组级" value="班组级" />
          </el-select>
        </el-form-item>
        <el-form-item label="教育日期" prop="education_date">
          <el-date-picker
            v-model="form.education_date"
            type="date"
            placeholder="请选择教育日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="教育人" prop="educator">
          <el-input v-model="form.educator" placeholder="请输入教育人" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        <el-form-item label="附件">
          <el-upload
            class="upload-demo"
            action="/api/upload"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            :limit="1"
            :file-list="fileList"
            :on-remove="handleRemove"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持任意格式文件，单个文件不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'EntryList',
  data() {
    return {
      searchForm: {
        employeeName: '',
        level: '',
        dateRange: []
      },
      tableData: [
        {
          id: 1,
          employee_id: 1001,
          employeeName: '张三',
          level: '厂级',
          education_date: '2024-03-15',
          educator: '李主管',
          notes: '完成公司级安全生产培训',
          attachment_url: '/files/safety/training-record-001.pdf'
        },
        {
          id: 2,
          employee_id: 1002,
          employeeName: '李四',
          level: '车间级',
          education_date: '2024-03-14',
          educator: '王班长',
          notes: '完成车间安全操作规程培训',
          attachment_url: ''
        },
        {
          id: 3,
          employee_id: 1003,
          employeeName: '王五',
          level: '班组级',
          education_date: '2024-03-13',
          educator: '赵组长',
          notes: '完成岗位安全操作培训',
          attachment_url: '/files/safety/training-record-003.pdf'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3,
      dialogVisible: false,
      dialogType: 'add',
      form: {
        id: null,
        employee_id: '',
        level: '',
        education_date: '',
        educator: '',
        notes: '',
        attachment_url: ''
      },
      rules: {
        employee_id: [
          { required: true, message: '请选择员工', trigger: 'change' }
        ],
        level: [
          { required: true, message: '请选择教育级别', trigger: 'change' }
        ],
        education_date: [
          { required: true, message: '请选择教育日期', trigger: 'change' }
        ],
        educator: [
          { required: true, message: '请输入教育人', trigger: 'blur' },
          { max: 100, message: '教育人长度不能超过100个字符', trigger: 'blur' }
        ],
        notes: [
          { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
        ]
      },
      employeeOptions: [],
      employeeLoading: false,
      fileList: []
    }
  },
  methods: {
    getLevelTag(level) {
      const tags = {
        '厂级': 'danger',
        '车间级': 'warning',
        '班组级': 'success'
      }
      return tags[level] || 'info'
    },
    getLevelText(level) {
      return level || '未知'
    },
    handleSearch() {
      // 实现搜索逻辑
      console.log('搜索条件：', this.searchForm)
      this.fetchData()
    },
    resetSearch() {
      this.searchForm = {
        employeeName: '',
        level: '',
        dateRange: []
      }
    },
    handleAdd() {
      this.dialogType = 'add'
      this.form = {
        id: null,
        employee_id: '',
        level: '',
        education_date: '',
        educator: '',
        notes: '',
        attachment_url: ''
      }
      this.fileList = []
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.form = { ...row }
      this.fileList = row.attachment_url ? [{
        name: `教育记录附件-${row.employeeName}-${row.education_date}`,
        url: row.attachment_url
      }] : []
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除该教育记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实现删除逻辑
        console.log('删除记录：', row)
        this.$message.success('删除成功')
        this.fetchData()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleDownload(row) {
      // 实现附件下载逻辑
      if (row.attachment_url) {
        const link = document.createElement('a')
        link.href = row.attachment_url
        link.download = `教育记录附件-${row.employeeName}-${row.education_date}`
        link.click()
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    handleRemove() {
      this.form.attachment_url = ''
      this.fileList = []
    },
    handleUploadSuccess(response, file) {
      this.form.attachment_url = response.url
      this.fileList = [{
        name: file.name,
        url: response.url
      }]
      this.$message.success('附件上传成功')
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
      }
      return isLt10M
    },
    async remoteSearchEmployee(query) {
      if (query) {
        this.employeeLoading = true
        // 实现远程搜索员工的逻辑
        try {
          // 模拟API调用
          setTimeout(() => {
            this.employeeOptions = [
              { value: 1001, label: '张三' },
              { value: 1002, label: '李四' },
              { value: 1003, label: '王五' }
            ].filter(item => item.label.includes(query))
            this.employeeLoading = false
          }, 500)
        } catch (error) {
          console.error('搜索员工失败：', error)
          this.employeeLoading = false
        }
      } else {
        this.employeeOptions = []
      }
    },
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()
        // 实现提交逻辑
        console.log('提交数据：', this.form)
        this.$message.success(this.dialogType === 'add' ? '新增成功' : '修改成功')
        this.dialogVisible = false
        this.fetchData()
      } catch (error) {
        console.error('表单验证失败', error)
      }
    },
    fetchData() {
      // 实现获取表格数据的逻辑
      console.log('获取数据，页码：', this.currentPage, '每页条数：', this.pageSize)
    }
  }
}
</script>

<style lang="scss" scoped>
.entry-list {
  .search-area {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>