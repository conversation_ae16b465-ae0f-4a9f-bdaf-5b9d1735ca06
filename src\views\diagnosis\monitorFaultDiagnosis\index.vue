<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">监测故障诊断</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleRefresh">刷新</el-button>
            <el-button type="warning" @click="handleExport">导出报告</el-button>
          </div>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex">
        <!-- 左侧网络拓扑图 -->
        <div class="w-2/3 h-full pr-4">
          <el-card shadow="never" class="h-full">
            <template #header>
              <div class="flex justify-between items-center">
                <span class="font-bold">网络拓扑图</span>
                <el-radio-group v-model="viewMode" size="small">
                  <el-radio-button label="overview">总览</el-radio-button>
                  <el-radio-button label="detail">详细</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="h-[calc(100%-60px)] relative">
              <!-- 这里使用网络拓扑图组件 -->
              <div class="w-full h-full bg-[var(--el-bg-color-overlay)] flex items-center justify-center">
                <div class="text-gray-400">网络拓扑图</div>
              </div>
              <!-- 故障标记 -->
              <div v-if="faultDevices.length > 0" class="absolute top-4 right-4">
                <el-tag v-for="device in faultDevices" :key="device.id" :type="getFaultType(device.status)"
                  class="mb-2 cursor-pointer" @click="handleDeviceClick(device)">
                  {{ device.name }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 右侧信息面板 -->
        <div class="w-1/3 h-full">
          <el-card shadow="never" class="h-full">
            <template #header>
              <div class="flex justify-between items-center">
                <span class="font-bold">故障信息</span>
                <el-select v-model="filterStatus" size="small" placeholder="筛选状态">
                  <el-option label="全部" value="all" />
                  <el-option label="正常" value="normal" />
                  <el-option label="断线" value="disconnected" />
                  <el-option label="丢包" value="packetLoss" />
                  <el-option label="不稳定" value="unstable" />
                </el-select>
              </div>
            </template>
            <div class="h-[calc(100%-60px)] overflow-y-auto">
              <!-- 故障统计 -->
              <div class="mb-4">
                <el-row :gutter="16">
                  <el-col :span="8">
                    <div class="flex flex-col items-center p-4 bg-[var(--el-bg-color-overlay)] rounded">
                      <span class="text-gray-500 mb-2">断线设备</span>
                      <span class="text-2xl font-bold text-red-500">{{ faultStats.disconnected }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="flex flex-col items-center p-4 bg-[var(--el-bg-color-overlay)] rounded">
                      <span class="text-gray-500 mb-2">丢包设备</span>
                      <span class="text-2xl font-bold text-orange-500">{{ faultStats.packetLoss }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="flex flex-col items-center p-4 bg-[var(--el-bg-color-overlay)] rounded">
                      <span class="text-gray-500 mb-2">不稳定设备</span>
                      <span class="text-2xl font-bold text-yellow-500">{{ faultStats.unstable }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 故障列表 -->
              <el-timeline>
                <el-timeline-item v-for="fault in filteredFaults" :key="fault.id" :type="getFaultType(fault.status)"
                  :timestamp="fault.time">
                  <el-card class="w-full">
                    <template #header>
                      <div class="flex justify-between items-center">
                        <span class="font-bold">{{ fault.deviceName }}</span>
                        <el-tag :type="getFaultType(fault.status)">{{ fault.status }}</el-tag>
                      </div>
                    </template>
                    <div class="text-sm">
                      <p class="mb-2">{{ fault.description }}</p>
                      <div class="text-gray-500">
                        <p>建议处理：{{ fault.suggestion }}</p>
                        <p>影响范围：{{ fault.impact }}</p>
                      </div>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- 设备详情对话框 -->
    <el-dialog v-model="deviceDialogVisible" :title="selectedDevice?.name" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备ID">{{ selectedDevice?.id }}</el-descriptions-item>
        <el-descriptions-item label="设备类型">{{ selectedDevice?.type }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ selectedDevice?.ip }}</el-descriptions-item>
        <el-descriptions-item label="MAC地址">{{ selectedDevice?.mac }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getFaultType(selectedDevice?.status)">{{ selectedDevice?.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最后通信时间">{{ selectedDevice?.lastCommunication }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deviceDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleRepair">修复</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import type { FormInstance } from 'element-plus'

// 视图模式
const viewMode = ref('overview')

// 筛选状态
const filterStatus = ref('all')

// 故障统计
const faultStats = reactive({
  disconnected: 3,
  packetLoss: 5,
  unstable: 2
})

// 故障设备列表
const faultDevices = ref([
  {
    id: '1',
    name: '设备A',
    type: '传感器',
    ip: '*************',
    mac: '00:1A:2B:3C:4D:5E',
    status: '断线',
    lastCommunication: '2024-04-21 10:00:00'
  },
  {
    id: '2',
    name: '设备B',
    type: '控制器',
    ip: '*************',
    mac: '00:1A:2B:3C:4D:5F',
    status: '丢包',
    lastCommunication: '2024-04-21 10:01:00'
  }
])

// 故障记录
const faultRecords = ref([
  {
    id: '1',
    deviceName: '设备A',
    status: '断线',
    time: '2024-04-21 10:00:00',
    description: '设备通信中断，超过30秒未收到数据',
    suggestion: '检查网络连接和电源状态',
    impact: '影响数据采集和监控'
  },
  {
    id: '2',
    deviceName: '设备B',
    status: '丢包',
    time: '2024-04-21 10:01:00',
    description: '数据包丢失率超过5%',
    suggestion: '检查网络带宽和信号强度',
    impact: '影响数据准确性'
  }
])

// 筛选后的故障记录
const filteredFaults = computed(() => {
  if (filterStatus.value === 'all') {
    return faultRecords.value
  }
  return faultRecords.value.filter(fault => fault.status === filterStatus.value)
})

// 设备详情对话框
const deviceDialogVisible = ref(false)
const selectedDevice = ref<any>(null)

// 方法
const getFaultType = (status: string) => {
  switch (status) {
    case '断线':
      return 'danger'
    case '丢包':
      return 'warning'
    case '不稳定':
      return 'info'
    default:
      return 'success'
  }
}

const handleDeviceClick = (device: any) => {
  selectedDevice.value = device
  deviceDialogVisible.value = true
}

const handleRefresh = () => {
  // 实现刷新逻辑
  console.log('刷新数据')
}

const handleExport = () => {
  // 实现导出逻辑
  console.log('导出报告')
}

const handleRepair = () => {
  // 实现修复逻辑
  console.log('修复设备:', selectedDevice.value)
  deviceDialogVisible.value = false
}
</script>

<style scoped lang="scss">
.el-card {
  :deep(.el-card__body) {
    height: calc(100% - 60px);
  }
}
</style>
