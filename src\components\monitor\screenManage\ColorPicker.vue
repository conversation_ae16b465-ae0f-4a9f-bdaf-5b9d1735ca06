<template>
  <div class="color-picker-container">
    <div class="color-picker-header">
      <span>{{ label }}</span>
    </div>
    <div class="color-picker-content">
      <div v-for="color in availableColors" :key="color" class="color-item" :class="{ active: modelValue === color }"
        :style="{ backgroundColor: color }" @click="updateColor(color)"></div>
    </div>
    <div class="color-picker-custom">
      <el-color-picker v-model="customColor" show-alpha @change="updateColor" />
      <span>自定义颜色</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

// 定义属性
const props = defineProps({
  modelValue: {
    type: String,
    default: '#409EFF'
  },
  label: {
    type: String,
    default: '颜色选择'
  },
  availableColors: {
    type: Array as () => string[],
    default: () => [
      '#409EFF', // 蓝色
      '#67C23A', // 绿色
      '#E6A23C', // 黄色
      '#F56C6C', // 红色
      '#909399', // 灰色
      '#303133', // 黑色
      '#FFFFFF', // 白色
      'rgba(0,0,0,0)' // 透明
    ]
  }
});

const emit = defineEmits(['update:modelValue']);

// 自定义颜色
const customColor = ref(props.modelValue);

// 当自定义颜色变化时更新
watch(() => props.modelValue, (newVal) => {
  customColor.value = newVal;
});

// 更新颜色
const updateColor = (color: string) => {
  emit('update:modelValue', color);
  customColor.value = color;
};
</script>

<style scoped lang="scss">
.color-picker-container {
  margin-bottom: 20px;
}

.color-picker-header {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.color-picker-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.color-item {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #dcdfe6;

  &:hover {
    transform: scale(1.1);
  }

  &.active {
    box-shadow: 0 0 0 2px #409eff;
  }
}

.color-picker-custom {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #909399;

  .el-color-picker {
    margin-right: 8px;
  }
}
</style>