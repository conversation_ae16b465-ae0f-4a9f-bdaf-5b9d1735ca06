/**
 * 格式化时间
 * @param time 时间
 * @param pattern 格式
 * @returns 格式化后的时间
 */
export function parseTime(time: string | number | Date, pattern?: string): string {
  if (arguments.length === 0 || !time) {
    return ''
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/')
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time *= 1000
    }
    date = new Date(time)
  }
  const formatObj: Record<string, string | number> = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result: string, key: string): string => {
    let value = formatObj[key]
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value as number] || '日'
    }
    if (result.length > 0 && (value as number) < 10) {
      value = '0' + value
    }
    return String(value || 0)
  })
  return time_str
}

/**
 * 添加日期范围
 * @param params 参数
 * @param dateRange 日期范围
 * @returns 添加日期范围后的参数
 */
export function addDateRange(params: any, dateRange: [string, string]): any {
  const search = { ...params }
  search.beginTime = dateRange[0]
  search.endTime = dateRange[1]
  return search
} 