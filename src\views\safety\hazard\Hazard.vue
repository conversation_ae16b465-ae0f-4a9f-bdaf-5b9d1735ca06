<template>
  <div class="hazard-container">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="隐患标准" name="standard">
        <hazard-standard />
      </el-tab-pane>
      <el-tab-pane label="隐患清单" name="list">
        <hazard-list />
      </el-tab-pane>
      <el-tab-pane label="隐患整改" name="rectification">
        <hazard-rectification />
      </el-tab-pane>
      <el-tab-pane label="隐患闭环" name="confirmation">
        <hazard-confirmation />
      </el-tab-pane>
      <el-tab-pane label="历史记录" name="history">
        <hazard-history />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import HazardStandard from './components/HazardStandard.vue'
import HazardList from './components/HazardList.vue'
import HazardRectification from './components/HazardRectification.vue'
import HazardConfirmation from './components/HazardConfirmation.vue'
import HazardHistory from './components/HazardHistory.vue'

const activeTab = ref('standard')
</script>

<style lang="scss" scoped>
.hazard-container {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  
  :deep(.el-tabs) {
    height: 100%;
    
    .el-tabs__content {
      height: calc(100% - 55px);
      padding: 20px;
      overflow-y: auto;
    }
  }
}
</style>
