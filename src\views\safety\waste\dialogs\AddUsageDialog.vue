<template>
  <el-dialog :title="dialogTitle" :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)"
    width="500px">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right">
      <el-form-item label="危化品名称" prop="chemicalName">
        <el-select v-model="form.chemicalName" placeholder="请选择危化品" style="width: 100%">
          <el-option v-for="item in chemicalOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="使用时间" prop="date">
        <el-date-picker v-model="form.date" type="datetime" placeholder="选择使用时间" style="width: 100%"
          value-format="YYYY-MM-DD HH:mm:ss" />
      </el-form-item>

      <el-form-item label="使用量" prop="amount">
        <el-input-number v-model="form.amount" :min="0" :precision="2" style="width: 100%" />
      </el-form-item>

      <el-form-item label="单位" prop="unit">
        <el-select v-model="form.unit" placeholder="请选择单位" style="width: 100%">
          <el-option label="毫升(ml)" value="ml" />
          <el-option label="升(L)" value="L" />
          <el-option label="克(g)" value="g" />
          <el-option label="千克(kg)" value="kg" />
        </el-select>
      </el-form-item>

      <el-form-item label="使用人员" prop="user">
        <el-input v-model="form.user" placeholder="请输入使用人员" />
      </el-form-item>

      <el-form-item label="使用用途" prop="purpose">
        <el-input v-model="form.purpose" type="textarea" rows="3" placeholder="请输入使用用途" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  editData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const formRef = ref(null)
const dialogTitle = ref('新增使用记录')

// 危化品选项
const chemicalOptions = [
  { label: '硫酸', value: '硫酸' },
  { label: '盐酸', value: '盐酸' },
  { label: '硝酸', value: '硝酸' },
  { label: '乙醇', value: '乙醇' }
]

const form = reactive({
  chemicalName: '',
  date: '',
  amount: 0,
  unit: '',
  user: '',
  purpose: ''
})

const rules = {
  chemicalName: [{ required: true, message: '请选择危化品', trigger: 'change' }],
  date: [{ required: true, message: '请选择使用时间', trigger: 'change' }],
  amount: [{ required: true, message: '请输入使用量', trigger: 'blur' }],
  unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
  user: [{ required: true, message: '请输入使用人员', trigger: 'blur' }],
  purpose: [{ required: true, message: '请输入使用用途', trigger: 'blur' }]
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', { ...form })
      handleCancel()
    }
  })
}

const handleCancel = () => {
  emit('update:modelValue', false)
  formRef.value?.resetFields()
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>