<template>
  <div class="materials">
    <div class="filter-bar">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="培训计划">
          <el-select v-model="filterForm.planId" placeholder="请选择培训计划" @change="handleSearch">
            <el-option
              v-for="item in planOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="资料类型">
          <el-select v-model="filterForm.type" placeholder="请选择资料类型" @change="handleSearch">
            <el-option label="培训课件" value="courseware" />
            <el-option label="现场照片" value="photo" />
            <el-option label="培训视频" value="video" />
            <el-option label="其他资料" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="operation-bar">
      <el-button type="primary" @click="openUploadDialog">上传资料</el-button>
    </div>

    <el-table :data="materialsList" border style="width: 100%">
      <el-table-column prop="planName" label="培训计划" />
      <el-table-column prop="fileName" label="文件名称" />
      <el-table-column prop="fileType" label="资料类型">
        <template #default="{ row }">
          <el-tag>{{ row.fileType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="fileSize" label="文件大小" />
      <el-table-column prop="uploadTime" label="上传时间" />
      <el-table-column prop="uploader" label="上传人" />
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button type="success" link @click="handleDownload(row)">下载</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 上传资料弹窗 -->
    <el-dialog
      v-model="uploadDialog.visible"
      title="上传培训资料"
      width="500px"
      destroy-on-close
    >
      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef" label-width="100px">
        <el-form-item label="培训计划" prop="planId">
          <el-select v-model="uploadForm.planId" placeholder="请选择培训计划" style="width: 100%">
            <el-option
              v-for="item in planOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="资料类型" prop="fileType">
          <el-select v-model="uploadForm.fileType" placeholder="请选择资料类型" style="width: 100%">
            <el-option label="培训课件" value="培训课件" />
            <el-option label="现场照片" value="现场照片" />
            <el-option label="培训视频" value="培训视频" />
            <el-option label="其他资料" value="其他资料" />
          </el-select>
        </el-form-item>
        <el-form-item label="文件描述" prop="description">
          <el-input 
            v-model="uploadForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入文件描述信息"
          />
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            class="upload-file"
            action="/api/materials/upload"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
            :file-list="uploadForm.fileList"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                文件大小不超过100MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploadDialog.loading">上传</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Materials',
  data() {
    return {
      filterForm: {
        planId: '',
        type: ''
      },
      planOptions: [],
      materialsList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      uploadDialog: {
        visible: false,
        loading: false
      },
      uploadForm: {
        planId: '',
        fileType: '',
        description: '',
        file: null,
        fileList: []
      },
      uploadRules: {
        planId: [
          { required: true, message: '请选择培训计划', trigger: 'change' }
        ],
        fileType: [
          { required: true, message: '请选择资料类型', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入文件描述', trigger: 'blur' }
        ],
        file: [
          { required: true, message: '请选择文件', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleSearch() {
      this.currentPage = 1
      this.fetchData()
    },
    handleReset() {
      this.filterForm = {
        planId: '',
        type: ''
      }
      this.fetchData()
    },
    openUploadDialog() {
      this.uploadForm = {
        planId: '',
        fileType: '',
        description: '',
        file: null,
        fileList: []
      }
      this.uploadDialog.visible = true
    },
    handleFileChange(file) {
      if (file) {
        const isLt100M = file.size / 1024 / 1024 < 100
        if (!isLt100M) {
          this.$message.error('上传文件大小不能超过 100MB!')
          this.uploadForm.fileList = []
          return false
        }
        this.uploadForm.file = file
      }
    },
    handleExceed() {
      this.$message.warning('只能上传一个文件')
    },
    submitUpload() {
      this.$refs.uploadFormRef.validate(valid => {
        if (valid) {
          if (!this.uploadForm.file) {
            this.$message.error('请选择要上传的文件')
            return
          }
          
          this.uploadDialog.loading = true
          
          // 模拟上传过程
          setTimeout(() => {
            this.uploadDialog.loading = false
            this.uploadDialog.visible = false
            this.$message.success('文件上传成功')
            this.fetchData()
          }, 1500)
        }
      })
    },
    handleDownload(row) {
      this.$message.success(`开始下载：${row.fileName}`)
      // 这里应该是实际的文件下载逻辑
      window.open(row.downloadUrl, '_blank')
    },
    handleDelete(row) {
      this.$confirm('确认删除该文件吗？删除后将无法恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.fetchData()
      }).catch(() => {})
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    fetchData() {
      // 模拟获取资料列表
      let mockData = [
        {
          id: '1',
          planName: '2024年度安全生产培训计划',
          fileName: '安全生产操作规程.pdf',
          fileType: '培训课件',
          fileSize: '2.5MB',
          uploadTime: '2024-03-01 10:30',
          uploader: '张安全',
          downloadUrl: 'https://example.com/files/safety-manual.pdf'
        },
        {
          id: '2',
          planName: '特种设备操作人员培训',
          fileName: '特种设备安全培训PPT.pptx',
          fileType: '培训课件',
          fileSize: '5.8MB',
          uploadTime: '2024-03-05 14:20',
          uploader: '李工程',
          downloadUrl: 'https://example.com/files/special-equipment.pptx'
        },
        {
          id: '3',
          planName: '消防安全知识培训',
          fileName: '消防演习现场照片.zip',
          fileType: '现场照片',
          fileSize: '158MB',
          uploadTime: '2024-03-10 16:45',
          uploader: '王消防',
          downloadUrl: 'https://example.com/files/fire-drill-photos.zip'
        },
        {
          id: '4',
          planName: '新员工安全培训',
          fileName: '安全培训视频教程.mp4',
          fileType: '培训视频',
          fileSize: '256MB',
          uploadTime: '2024-03-15 09:15',
          uploader: '赵培训',
          downloadUrl: 'https://example.com/files/safety-training-video.mp4'
        },
        {
          id: '5',
          planName: '危险化学品安全管理培训',
          fileName: '危化品管理制度汇编.docx',
          fileType: '其他资料',
          fileSize: '1.2MB',
          uploadTime: '2024-03-20 11:30',
          uploader: '钱工程',
          downloadUrl: 'https://example.com/files/chemical-management.docx'
        }
      ]
      
      // 根据筛选条件过滤数据
      if (this.filterForm.planId) {
        const planName = this.planOptions.find(option => option.value === this.filterForm.planId)?.label
        mockData = mockData.filter(item => item.planName === planName)
      }
      
      if (this.filterForm.type) {
        const typeMap = {
          'courseware': '培训课件',
          'photo': '现场照片',
          'video': '培训视频',
          'other': '其他资料'
        }
        mockData = mockData.filter(item => item.fileType === typeMap[this.filterForm.type])
      }
      
      this.total = mockData.length // 总记录数
      this.materialsList = mockData
    },
    fetchPlanOptions() {
      // 模拟获取培训计划选项
      this.planOptions = [
        { value: '1', label: '2024年度安全生产培训计划' },
        { value: '2', label: '特种设备操作人员培训' },
        { value: '3', label: '消防安全知识培训' },
        { value: '4', label: '新员工安全培训' },
        { value: '5', label: '危险化学品安全管理培训' }
      ]
    }
  },
  created() {
    this.fetchPlanOptions()
    this.fetchData()
  }
}
</script>

<style lang="scss" scoped>
.materials {
  .filter-bar {
    margin-bottom: 20px;
  }

  .operation-bar {
    margin-bottom: 20px;
  }

  .el-pagination {
    margin-top: 20px;
    justify-content: flex-end;
  }
  
  .upload-file {
    width: 100%;
  }
}
</style> 