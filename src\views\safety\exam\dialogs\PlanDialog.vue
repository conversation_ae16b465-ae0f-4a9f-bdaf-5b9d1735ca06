<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑培训计划' : '新增培训计划'"
    width="600px"
    destroy-on-close
    @closed="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="计划名称" prop="planName">
        <el-input v-model="form.planName" placeholder="请输入计划名称" />
      </el-form-item>
      <el-form-item label="培训部门" prop="department">
        <el-select v-model="form.department" placeholder="请选择培训部门" style="width: 100%">
          <el-option
            v-for="item in departmentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训岗位" prop="position">
        <el-select v-model="form.position" placeholder="请选择培训岗位" style="width: 100%">
          <el-option
            v-for="item in positionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="培训讲师" prop="trainer">
        <el-input v-model="form.trainer" placeholder="请输入培训讲师" />
      </el-form-item>
      <el-form-item label="培训时间" prop="timeRange">
        <el-date-picker
          v-model="form.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="培训地点" prop="location">
        <el-input v-model="form.location" placeholder="请输入培训地点" />
      </el-form-item>
      <el-form-item label="培训内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="3"
          placeholder="请输入培训内容"
        />
      </el-form-item>
      <el-form-item label="提醒方式" prop="notifyTypes">
        <el-checkbox-group v-model="form.notifyTypes">
          <el-checkbox label="email">邮件</el-checkbox>
          <el-checkbox label="sms">短信</el-checkbox>
          <el-checkbox label="wechat">微信</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'PlanDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      form: {
        planName: '',
        department: '',
        position: '',
        trainer: '',
        timeRange: [],
        location: '',
        content: '',
        notifyTypes: []
      },
      rules: {
        planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
        department: [{ required: true, message: '请选择培训部门', trigger: 'change' }],
        position: [{ required: true, message: '请选择培训岗位', trigger: 'change' }],
        trainer: [{ required: true, message: '请输入培训讲师', trigger: 'blur' }],
        timeRange: [{ required: true, message: '请选择培训时间', trigger: 'change' }],
        location: [{ required: true, message: '请输入培训地点', trigger: 'blur' }],
        content: [{ required: true, message: '请输入培训内容', trigger: 'blur' }],
        notifyTypes: [{ required: true, message: '请选择提醒方式', trigger: 'change' }]
      },
      departmentOptions: [
        { label: '生产部', value: 'production' },
        { label: '安全部', value: 'safety' },
        { label: '质量部', value: 'quality' }
      ],
      positionOptions: [
        { label: '操作工', value: 'operator' },
        { label: '技术员', value: 'technician' },
        { label: '安全员', value: 'safety_officer' }
      ]
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    modelValue(val) {
      if (val && this.isEdit) {
        this.initEditData()
      }
    }
  },
  methods: {
    initEditData() {
      Object.keys(this.form).forEach(key => {
        this.form[key] = this.editData[key]
      })
    },
    handleSubmit() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          // TODO: 调用API保存数据
          this.$emit('success')
          this.visible = false
        }
      })
    },
    handleClose() {
      this.$refs.formRef?.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 