<template>
  <div class="drill-management">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="演练名称">
          <el-input v-model="searchForm.name" placeholder="请输入演练名称" />
        </el-form-item>
        <el-form-item label="演练状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="未开始" value="pending" />
            <el-option label="进行中" value="ongoing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="演练时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleAdd">新增演练</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="name" label="演练名称" />
      <el-table-column prop="type" label="演练类型" />
      <el-table-column prop="drillTime" label="演练时间" />
      <el-table-column prop="location" label="演练地点" />
      <el-table-column prop="participants" label="参与人数" />
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280">
        <template #default="scope">
          <el-button 
            type="primary" 
            link 
            @click="handleEdit(scope.row)"
            v-if="scope.row.status === 'pending'"
          >编辑</el-button>
          <el-button 
            type="success" 
            link 
            @click="handleStart(scope.row)"
            v-if="scope.row.status === 'pending'"
          >开始演练</el-button>
          <el-button 
            type="warning" 
            link 
            @click="handleRecord(scope.row)"
            v-if="scope.row.status === 'ongoing'"
          >记录结果</el-button>
          <el-button 
            type="info" 
            link 
            @click="handleView(scope.row)"
          >查看详情</el-button>
          <el-button 
            type="danger" 
            link 
            @click="handleDelete(scope.row)"
            v-if="scope.row.status === 'pending'"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 弹窗 -->
    <drill-dialog
      v-model="dialogVisible"
      :dialog-type="dialogType"
      :drill-data="currentDrill"
      @submit="handleDialogSubmit"
    />
  </div>
</template>

<script>
import DrillDialog from '../dialogs/DrillDialog.vue'

export default {
  name: 'DrillManagement',
  components: {
    DrillDialog
  },
  data() {
    return {
      searchForm: {
        name: '',
        status: '',
        dateRange: []
      },
      tableData: [
        {
          id: 1,
          name: '2024年第一季度消防演练',
          type: 'fire',
          drillTime: '2024-03-20 14:30:00',
          location: '公司总部',
          participants: 150,
          departments: ['safety', 'production', 'admin'],
          status: 'pending',
          content: '模拟火灾发生时的疏散和灭火过程'
        },
        {
          id: 2,
          name: '化学品泄露应急演练',
          type: 'comprehensive',
          drillTime: '2024-03-18 09:30:00',
          location: '化学品仓库',
          participants: 30,
          departments: ['safety', 'production'],
          status: 'ongoing',
          content: '模拟危险品泄露的应急处置流程'
        },
        {
          id: 3,
          name: '2024年春季地震应急演练',
          type: 'evacuation',
          drillTime: '2024-04-15 10:00:00',
          location: '全公司范围',
          participants: 300,
          departments: ['safety', 'production', 'admin', 'hr'],
          status: 'completed',
          content: '模拟地震发生时的紧急疏散',
          completion: 4,
          problems: '部分员工对避难场所位置不熟悉',
          improvements: '加强日常宣传教育，设置明显的指示标识'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3,
      dialogVisible: false,
      dialogType: 'add',
      currentDrill: {}
    }
  },
  methods: {
    getStatusType(status) {
      const types = {
        pending: 'info',
        ongoing: 'warning',
        completed: 'success',
        cancelled: 'danger'
      }
      return types[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        pending: '未开始',
        ongoing: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return texts[status] || status
    },
    handleSearch() {
      // 实现搜索逻辑
    },
    resetSearch() {
      this.searchForm = {
        name: '',
        status: '',
        dateRange: []
      }
    },
    handleAdd() {
      this.dialogType = 'add'
      this.currentDrill = {}
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentDrill = { ...row }
      this.dialogVisible = true
    },
    handleStart(row) {
      this.dialogType = 'start'
      this.currentDrill = { ...row }
      this.dialogVisible = true
    },
    handleRecord(row) {
      this.dialogType = 'record'
      this.currentDrill = { ...row }
      this.dialogVisible = true
    },
    handleView(row) {
      this.dialogType = 'view'
      this.currentDrill = { ...row }
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除该演练吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('删除演练：', row)
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    fetchData() {
      // 获取表格数据
    },
    handleDialogSubmit(formData) {
      // 处理弹窗提交
      console.log('提交数据：', formData)
      if (this.dialogType === 'add') {
        this.$message.success('新增成功')
      } else if (this.dialogType === 'edit') {
        this.$message.success('修改成功')
      } else if (this.dialogType === 'start') {
        this.$message.success('演练已开始')
      } else if (this.dialogType === 'record') {
        this.$message.success('结果已记录')
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.drill-management {
  .search-area {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 