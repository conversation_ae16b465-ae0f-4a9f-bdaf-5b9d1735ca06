<template>
  <div class="w-full h-[calc(100vh-210px)]">
    <div class="w-full h-full flex flex-col">
      <div class="w-full h-[50px] flex p-4">
        <div class="h-full w-4/5 flex items-center gap-2">
          <el-form :inline="true" :model="formInline" class="flex items-center gap-2">
            <el-form-item label="水厂" class="w-[300px]">
              <el-select v-model="formInline.factoryId" placeholder="请选择水厂" class="w-full" filterable clearable>
                <template v-for="group in groupedFactoryList">
                  <el-option-group v-if="group.childrenLevel3 && group.childrenLevel3.length" :key="'group-' + group.id"
                    :label="group.name">
                    <el-option v-for="item in group.childrenLevel3" :key="'item-' + item.id" :label="item.name"
                      :value="item.id" class="child-factory-option" />
                  </el-option-group>
                  <el-option v-else-if="group.level === 3" :key="'item-' + group.id" :label="group.name"
                    :value="group.id" />
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="处理人" class="w-[200px]">
              <el-input v-model="formInline.handler" placeholder="请输入处理人" clearable />
            </el-form-item>
            <el-form-item label="告警级别" class="w-[200px]">
              <el-select v-model="formInline.level" placeholder="请选择告警级别" clearable>
                <el-option label="I级" value="I" />
                <el-option label="II级" value="II" />
              </el-select>
            </el-form-item>
            <el-form-item label="处理时间" class="w-[450px]">
              <el-date-picker v-model="formInline.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
              <el-button type="info" :icon="RefreshRight" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="w-full flex-1 flex flex-col p-4">
        <div class="relative w-full h-full">
          <div class="absolute w-full h-full">
            <el-table v-loading="loading" :data="tableData" border height="100%">
              <el-table-column prop="factoryName" label="水厂名称" align="center" />
              <el-table-column prop="alarmTime" label="告警时间" align="center" width="180" />
              <el-table-column prop="processTime" label="处理时间" align="center" width="180" />
              <el-table-column prop="ruleName" label="规则名称" align="center" />
              <el-table-column prop="level" label="告警级别" align="center" width="100">
                <template #default="scope">
                  <el-tag :type="getAlarmLevelType(scope.row.level)">{{ scope.row.level }}级</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="handler" label="处理人" align="center" width="120" />
              <el-table-column prop="processStatus" label="处理状态" align="center" width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.processStatus === 'resolved' ? 'success' : 'warning'">
                    {{ scope.row.processStatus === 'resolved' ? '已解决' : '已确认' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template #default="scope">
                  <el-button size="small" type="primary" link @click="handleViewDetail(scope.row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="flex-1 w-full flex items-center justify-end mt-2">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>

    <!-- 处理详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="处理详情" width="60%">
      <div v-if="currentProcess" class="p-4">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="水厂名称">{{ currentProcess.factoryName }}</el-descriptions-item>
          <el-descriptions-item label="告警时间">{{ currentProcess.alarmTime }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">{{ currentProcess.processTime }}</el-descriptions-item>
          <el-descriptions-item label="告警级别">
            <el-tag :type="getAlarmLevelType(currentProcess.level)">{{ currentProcess.level }}级</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="规则名称">{{ currentProcess.ruleName }}</el-descriptions-item>
          <el-descriptions-item label="处理人">{{ currentProcess.handler }}</el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="currentProcess.processStatus === 'resolved' ? 'success' : 'warning'">
              {{ currentProcess.processStatus === 'resolved' ? '已解决' : '已确认' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="告警详情">
            <div v-if="currentProcess.ruleType === 'single'">
              {{ currentProcess.factor }} {{ getOperatorSymbol(currentProcess.operator) }} {{ currentProcess.threshold }}
              {{ currentProcess.unit ? ' ' + currentProcess.unit : '' }}
              (实际值: {{ currentProcess.value }}{{ currentProcess.unit ? ' ' + currentProcess.unit : '' }})
            </div>
            <div v-else>{{ currentProcess.expression }}</div>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="根本原因分析">
            {{ currentProcess.rootCause || '无' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="处理措施">
            {{ currentProcess.actions || '无' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="后续行动">
            {{ currentProcess.followUpActions || '无' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="相关维护记录">
            {{ currentProcess.maintenanceRecords || '无' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="备注">
            {{ currentProcess.remark || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { RefreshRight, Search } from '@element-plus/icons-vue'
import { computed, reactive, ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getAlarmProcessRecords, getAlarmProcessDetail } from '@/api/alarm'
import { FactoryApi } from '@/api/report/factory/index'
import { useUserStore } from '@/store/modules/user'

// 用户信息
const userStore = useUserStore()
const userId = computed(() => userStore.getUser.id)

const formInline = reactive({
  factoryId: '',
  handler: '',
  level: '',
  dateRange: []
})

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const tableData = ref([])
const detailDialogVisible = ref(false)
const currentProcess = ref(null)

// 水厂列表和选中的水厂
const factoryList = ref<any[]>([])

// 递归找到第一个 level=3 的节点
function findFirstLevel3(tree: any[]): any | null {
  for (const node of tree) {
    if (node.level === 3) return node;
    if (node.children) {
      const found = findFirstLevel3(node.children);
      if (found) return found;
    }
  }
  return null;
}

// 生成用于分组展示的工厂树（只保留有 level=3 子节点的父节点和所有 level=3 节点）
const groupedFactoryList = computed(() => {
  function filterTree(tree: any[]): any[] {
    return tree
      .map(node => {
        let childrenLevel3 = [];
        if (node.children && node.children.length) {
          childrenLevel3 = node.children.filter(child => child.level === 3);
        }
        return {
          ...node,
          childrenLevel3
        };
      })
      .filter(node => (node.childrenLevel3 && node.childrenLevel3.length) || node.level === 3)
  }
  return filterTree(factoryList.value)
})

// 获取水厂树形列表
const queryFactoryList = async () => {
  try {
    // 添加userId参数
    let res = await FactoryApi.queryFactoryListByUser({ userId: userId.value });
    if (res && res.data?.length > 0) {
      factoryList.value = res.data; // 保持原始树结构
      // 默认选中第一个 level=3
      const first = findFirstLevel3(res.data);
      if (!formInline.factoryId && first) {
        formInline.factoryId = first.id;
      }
    }
  } catch (error) {
    console.error('获取水厂列表失败:', error);
    ElMessage.error('获取水厂列表失败');
  }
}

// 递归查找水厂
const findFactoryById = (factories: any[], id: number): any => {
  for (const factory of factories) {
    if (factory.id === id) {
      return factory;
    }

    if (factory.children && factory.children.length) {
      const found = findFactoryById(factory.children, id);
      if (found) return found;
    }
  }

  return null;
};

// 获取当前选中水厂的名称
const getFactoryName = (factoryId: number): string => {
  if (!factoryId) return '';

  // 在工厂列表中查找匹配的水厂
  for (const group of factoryList.value) {
    // 检查当前节点
    if (group.id === factoryId) {
      return group.name;
    }

    // 检查子节点
    if (group.childrenLevel3 && group.childrenLevel3.length) {
      const found = group.childrenLevel3.find(item => item.id === factoryId);
      if (found) return found.name;
    }

    // 检查children
    if (group.children && group.children.length) {
      const found = findFactoryById(group.children, factoryId);
      if (found) return found.name;
    }
  }

  return '';
};

const getAlarmLevelType = (level) => {
  const types = {
    'I': 'danger',
    'II': 'warning'
  }
  return types[level] || 'info'
}

const getOperatorSymbol = (operator) => {
  const symbols = {
    '>': '>',
    '<': '<',
    '=': '=',
    '>=': '≥',
    '<=': '≤'
  }
  return symbols[operator] || operator
}

const fetchProcessRecords = async () => {
  loading.value = true
  try {
    let startDate, endDate
    if (formInline.dateRange && formInline.dateRange.length === 2) {
      startDate = formInline.dateRange[0]
      endDate = formInline.dateRange[1]
    }

    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      factoryId: formInline.factoryId || undefined,
      handler: formInline.handler || undefined,
      level: formInline.level || undefined,
      startDate,
      endDate
    }
    const response = await getAlarmProcessRecords(params)
    // 添加水厂名称
    if (response.data && Array.isArray(response.data)) {
      tableData.value = response.data.map(item => ({
        ...item,
        factoryName: getFactoryName(item.factoryId)
      }))
    } else {
      tableData.value = []
    }
    total.value = response.total || 0
  } catch (error) {
    ElMessage.error('获取告警处理记录失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const fetchProcessDetail = async (id) => {
  try {
    const response = await getAlarmProcessDetail(id)
    if (response) {
      // 添加水厂名称
      currentProcess.value = {
        ...response,
        factoryName: getFactoryName(response.factoryId)
      }
      detailDialogVisible.value = true
    }
  } catch (error) {
    ElMessage.error('获取处理详情失败')
    console.error(error)
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchProcessRecords()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchProcessRecords()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchProcessRecords()
}

const resetForm = () => {
  formInline.handler = ''
  formInline.level = ''
  formInline.dateRange = []
  // 不重置水厂选择
  handleSearch()
}

const handleViewDetail = (row) => {
  fetchProcessDetail(row.id)
}

// 监听水厂变化
watch(() => formInline.factoryId, (newVal) => {
  if (newVal) {
    handleSearch()
  }
})

onMounted(async () => {
  await queryFactoryList()
  fetchProcessRecords()
})
</script>
<style scoped lang="scss">
.child-factory-option {
  padding-left: 40px !important;
}

:deep(.el-select-group__title) {
  font-size: 15px;
  font-weight: bold;
}
</style>

<style>
.el-table__header thead tr th {
  background-color: #f5f7fa;
}
</style> 