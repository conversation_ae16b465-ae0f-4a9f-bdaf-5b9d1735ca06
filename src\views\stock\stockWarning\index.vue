<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">存量预警</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleAdd">
              <el-icon>
                <Plus />
              </el-icon>新增预警
            </el-button>
            <el-button @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>导出
            </el-button>
          </div>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-270px)] flex flex-col">
        <div class="w-full h-[50px] mb-2 gap-2 flex items-center">
          <el-form :model="searchForm" inline>
            <el-form-item label="仓库：">
              <el-select v-model="searchForm.warehouse" placeholder="请选择仓库" style="width: 200px">
                <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="物料：">
              <el-input v-model="searchForm.material" placeholder="请输入物料名称" />
            </el-form-item>
            <el-form-item label="预警状态：">
              <el-select v-model="searchForm.warningStatus" placeholder="请选择预警状态" style="width: 200px">
                <el-option v-for="item in warningStatusOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border height="100%">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="warehouse" label="仓库" align="center" width="140" />
                <el-table-column prop="material" label="物料名称" align="center" min-width="120"
                  :show-overflow-tooltip="true" />
                <el-table-column prop="currentStock" label="当前库存" align="center" width="100" />
                <el-table-column prop="minStock" label="最小库存" align="center" width="100" />
                <el-table-column prop="maxStock" label="最大库存" align="center" width="100" />
                <el-table-column prop="unit" label="单位" align="center" width="80" />
                <el-table-column prop="warningStatus" label="预警状态" align="center" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.warningStatus === '正常'
                      ? 'success'
                      : scope.row.warningStatus === '库存不足'
                        ? 'danger'
                        : 'warning'
                      ">
                      {{ scope.row.warningStatus }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="更新时间" align="center" width="160" />
                <el-table-column label="操作" align="center" width="180" fixed="right">
                  <template #default="scope">
                    <el-button type="primary" link @click="handleEdit(scope.row)"> 编辑 </el-button>
                    <el-button type="danger" link @click="handleDelete(scope.row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end mt-2">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 新增/编辑预警对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增预警' : '编辑预警'" width="500px">
      <el-form :model="formData" label-width="100px">
        <el-form-item label="仓库" required>
          <el-select v-model="formData.warehouse" placeholder="请选择仓库" style="width: 100%">
            <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料名称" required>
          <el-input v-model="formData.material" placeholder="请输入物料名称" />
        </el-form-item>
        <el-form-item label="最小库存" required>
          <el-input-number v-model="formData.minStock" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="最大库存" required>
          <el-input-number v-model="formData.maxStock" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="单位" required>
          <el-input v-model="formData.unit" placeholder="请输入单位" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Plus, Download } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单数据
const searchForm = reactive({
  warehouse: '',
  material: '',
  warningStatus: ''
})

// 仓库选项
const warehouseOptions = [
  { value: '1007仓库名称', label: '1007仓库名称' },
  { value: '广仓库', label: '广仓库' },
  { value: '江东水厂1号仓库', label: '江东水厂1号仓库' }
]

// 预警状态选项
const warningStatusOptions = [
  { value: '正常', label: '正常' },
  { value: '库存不足', label: '库存不足' },
  { value: '库存过高', label: '库存过高' }
]

// 表格数据
const tableData = ref([
  {
    warehouse: '1007仓库名称',
    material: '三角带',
    currentStock: 50,
    minStock: 20,
    maxStock: 100,
    unit: '个',
    warningStatus: '正常',
    updateTime: '2024-03-20 10:00:00'
  },
  {
    warehouse: '广仓库',
    material: '泵-2水泵次氯酸钠',
    currentStock: 15,
    minStock: 20,
    maxStock: 100,
    unit: '台',
    warningStatus: '库存不足',
    updateTime: '2024-03-20 09:30:00'
  },
  {
    warehouse: '江东水厂1号仓库',
    material: '滤芯',
    currentStock: 150,
    minStock: 20,
    maxStock: 100,
    unit: '个',
    warningStatus: '库存过高',
    updateTime: '2024-03-20 09:00:00'
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(3)

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const formData = reactive({
  warehouse: '',
  material: '',
  minStock: 0,
  maxStock: 0,
  unit: ''
})

// 搜索方法
const handleSearch = () => {
  console.log('搜索条件：', searchForm)
}

// 重置方法
const handleReset = () => {
  searchForm.warehouse = ''
  searchForm.material = ''
  searchForm.warningStatus = ''
}

// 新增方法
const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
  // 重置表单数据
  Object.assign(formData, {
    warehouse: '',
    material: '',
    minStock: 0,
    maxStock: 0,
    unit: ''
  })
}

// 编辑方法
const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  // 填充表单数据
  Object.assign(formData, {
    warehouse: row.warehouse,
    material: row.material,
    minStock: row.minStock,
    maxStock: row.maxStock,
    unit: row.unit
  })
}

// 删除方法
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确认删除该预警设置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      console.log('删除预警：', row)
      ElMessage.success('删除成功')
    })
    .catch(() => { })
}

// 提交表单
const handleSubmit = () => {
  console.log('提交表单：', formData)
  ElMessage.success(dialogType.value === 'add' ? '新增成功' : '编辑成功')
  dialogVisible.value = false
}

// 导出方法
const handleExport = () => {
  console.log('导出数据')
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}
</script>

<style scoped lang="scss">
.stock-warning {
  padding: 16px;

  .search-bar {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;

    .search-input {
      width: 280px;
    }
  }

  .operation-bar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  :deep(.el-table) {
    margin-top: 20px;

    .el-button {
      padding: 0 5px;
      height: auto;
    }
  }
}
</style>
