<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'

defineOptions({ name: 'IFrame' })

const props = defineProps({
  src: propTypes.string.def('')
})
const emit = defineEmits(['load', 'error'])
const loading = ref(true)
const frameRef = ref<HTMLElement | null>(null)
const isLoadSuccess = ref(true) // 是否加载成功

const init = () => {
  nextTick(() => {
    loading.value = true
    isLoadSuccess.value = true

    if (!frameRef.value) return

    // 监听加载完成事件
    frameRef.value.onload = () => {
      loading.value = false

      try {
        // 尝试访问iframe内容，如果能访问并且没有错误页面标识，则认为加载成功
        const iframeWindow = (frameRef.value as HTMLIFrameElement).contentWindow
        const iframeDocument = (frameRef.value as HTMLIFrameElement).contentDocument

        // 如果是空页面或者URL无效（about:blank等），认为加载失败
        if (!iframeWindow || !iframeDocument ||
          iframeDocument.URL === 'about:blank' ||
          iframeDocument.body.innerHTML === '') {
          console.warn('IFrame内容为空或无效')
          isLoadSuccess.value = false
          emit('error', { type: 'empty_content' })
          return
        }

        // 如果能正常访问内容，认为加载成功
        console.log('IFrame加载成功')
        emit('load', { success: true })
      } catch (error) {
        // 如果出现跨域错误，iframe内容不可访问，但可能是正常的跨域限制
        // 在这种情况下，我们假设它成功加载了，但会通过参数告知调用者有跨域限制
        console.warn('IFrame可能存在跨域限制，无法验证内容:', error)
        emit('load', { success: true, crossOrigin: true })
      }
    }

    // 监听加载错误
    frameRef.value.onerror = (event) => {
      console.error('IFrame加载失败:', event)
      loading.value = false
      isLoadSuccess.value = false
      emit('error', { type: 'load_failed', event })
    }
  })
}

onMounted(() => {
  init()
})

watch(
  () => props.src,
  () => {
    init()
  }
)
</script>
<template>
  <div v-loading="loading"
    class="w-full h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-2px)]">
    <iframe ref="frameRef" :src="props.src" frameborder="0" scrolling="auto" height="100%" width="100%"
      allowfullscreen="true" webkitallowfullscreen="true" mozallowfullscreen="true"></iframe>
  </div>
</template>
