<template>
  <div class="position-group">
    <div class="form-label">
      <span>{{ label }}</span>
      <span class="coordinate-badge">{{ coordinateType }}</span>
    </div>
    <div class="position-inputs">
      <div class="axis-input">
        <span class="axis-label">X:</span>
        <div class="input-with-buttons">
          <el-button size="small" @click="adjustPosition('x', -1)">-</el-button>
          <el-input v-model="xValue" size="small" type="number" @change="handleChange" />
          <el-button size="small" @click="adjustPosition('x', 1)">+</el-button>
        </div>
      </div>
      <div class="axis-input">
        <span class="axis-label">Y:</span>
        <div class="input-with-buttons">
          <el-button size="small" @click="adjustPosition('y', -1)">-</el-button>
          <el-input v-model="yValue" size="small" type="number" @change="handleChange" />
          <el-button size="small" @click="adjustPosition('y', 1)">+</el-button>
        </div>
      </div>
      <el-button size="small" @click="resetPosition" class="reset-btn">重置位置</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { Position } from './types';

// 定义属性
const props = defineProps({
  modelValue: {
    type: Object as () => Position,
    required: true
  },
  label: {
    type: String,
    required: true
  },
  coordinateType: {
    type: String,
    default: '(绝对坐标)'
  },
  defaultValue: {
    type: Object as () => Position,
    default: () => ({ x: 0, y: 0 })
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'reset']);

// x坐标计算属性
const xValue = computed({
  get: () => props.modelValue?.x ?? props.defaultValue.x,
  set: (value) => {
    const newPosition = {
      ...props.modelValue,
      x: Number(value)
    };
    emit('update:modelValue', newPosition);
  }
});

// y坐标计算属性
const yValue = computed({
  get: () => props.modelValue?.y ?? props.defaultValue.y,
  set: (value) => {
    const newPosition = {
      ...props.modelValue,
      y: Number(value)
    };
    emit('update:modelValue', newPosition);
  }
});

// 调整位置
const adjustPosition = (axis: 'x' | 'y', value: number) => {
  if (axis === 'x') {
    xValue.value = Number(xValue.value) + value;
  } else {
    yValue.value = Number(yValue.value) + value;
  }
  handleChange();
};

// 重置位置
const resetPosition = () => {
  emit('reset');
};

// 处理变更
const handleChange = () => {
  // 可以在这里添加额外的验证逻辑
};
</script>

<style scoped lang="scss">
.position-group {
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.form-label {
  margin-bottom: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.coordinate-badge {
  background-color: #909399;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  margin-left: 5px;
}

.position-inputs {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.axis-input {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.axis-label {
  width: 20px;
  font-weight: bold;
  color: #606266;
}

.input-with-buttons {
  display: flex;
  align-items: center;

  :deep(.el-input) {
    width: 80px;
    margin: 0 5px;
  }
}

.reset-btn {
  margin-left: auto;
}
</style>