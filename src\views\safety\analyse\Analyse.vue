<template>
  <div class="safety-analyse-container">
    <el-card class="analyse-card">
      <div class="card-header">
        <div class="header-left">
          <h2>安全管理数据分析</h2>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="openCompare">数据对比</el-button>
          <el-button type="success" @click="openExport">数据导出</el-button>
        </div>
      </div>
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="人员安全分析" name="personnel">
          <personnel-analysis @view-detail="openDetail('personnel', $event)" />
        </el-tab-pane>
        <el-tab-pane label="环境安全分析" name="environment">
          <environment-analysis @view-detail="openDetail('environment', $event)" />
        </el-tab-pane>
        <el-tab-pane label="设备设施分析" name="equipment">
          <equipment-analysis @view-detail="openDetail('equipment', $event)" />
        </el-tab-pane>
        <el-tab-pane label="安全投入分析" name="investment">
          <investment-analysis @view-detail="openDetail('investment', $event)" />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 详情弹窗 -->
    <detail-dialog
      v-model:visible="detailVisible"
      :type="dialogType"
      :detail-data="detailData"
      @close="handleDetailClose"
      @export="handleDetailExport"
    />

    <!-- 对比弹窗 -->
    <compare-dialog
      v-model:visible="compareVisible"
      @close="handleCompareClose"
      @export="handleCompareExport"
    />

    <!-- 导出弹窗 -->
    <export-dialog
      v-model:visible="exportVisible"
      @close="handleExportClose"
      @export="handleDataExport"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import PersonnelAnalysis from './components/PersonnelAnalysis.vue'
import EnvironmentAnalysis from './components/EnvironmentAnalysis.vue'
import EquipmentAnalysis from './components/EquipmentAnalysis.vue'
import InvestmentAnalysis from './components/InvestmentAnalysis.vue'
import DetailDialog from './dialogs/DetailDialog.vue'
import CompareDialog from './dialogs/CompareDialog.vue'
import ExportDialog from './dialogs/ExportDialog.vue'

const activeTab = ref('personnel')

// 弹窗控制
const detailVisible = ref(false)
const compareVisible = ref(false)
const exportVisible = ref(false)

// 详情弹窗数据
const dialogType = ref('personnel')
const detailData = ref({})

// 打开详情弹窗
const openDetail = (type: string, data: any) => {
  dialogType.value = type
  detailData.value = data
  detailVisible.value = true
}

// 打开对比弹窗
const openCompare = () => {
  compareVisible.value = true
}

// 打开导出弹窗
const openExport = () => {
  exportVisible.value = true
}

// 详情弹窗处理
const handleDetailClose = () => {
  detailData.value = {}
}

const handleDetailExport = (data: any) => {
  console.log('导出详情数据:', data)
  ElMessage.success('详情数据导出成功')
}

// 对比弹窗处理
const handleCompareClose = () => {
  // 可以在这里清理对比相关的数据
}

const handleCompareExport = (data: any) => {
  console.log('导出对比数据:', data)
  ElMessage.success('对比数据导出成功')
}

// 导出弹窗处理
const handleExportClose = () => {
  // 可以在这里清理导出相关的数据
}

const handleDataExport = (data: any) => {
  console.log('导出数据:', data)
  ElMessage.success('数据导出成功')
}
</script>

<style lang="scss" scoped>
.safety-analyse-container {
  padding: 20px;
  
  .analyse-card {
    .card-header {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        h2 {
          margin: 0;
          font-size: 24px;
          color: #303133;
        }
      }

      .header-right {
        display: flex;
        gap: 12px;
      }
    }
  }
}

:deep(.el-tabs__content) {
  padding: 20px;
}
</style>
