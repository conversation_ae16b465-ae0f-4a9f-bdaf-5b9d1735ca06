import request from '@/config/axios'
import { DataSupplementConfigPageReqVO, DataSupplementConfigRespVO, PageResult, SupplementConfigReq } from './types'

/**
 * 分页查询补录配置列表
 * @param params 查询参数
 * @returns 分页结果
 */
export const getConfigPage = (params: DataSupplementConfigPageReqVO): Promise<PageResult<DataSupplementConfigRespVO>> => {
  return request.get({
    url: '/report/data-supplement/config/page',
    params
  })
}

/**
 * 获取当前启用状态的补录配置
 * @returns 启用状态的补录配置列表
 */
export const getEnabledConfigs = (): Promise<DataSupplementConfigRespVO[]> => {
  return request.get({
    url: '/report/data-supplement/config/enabled'
  })
}

/**
 * 根据报表类型获取补录配置
 * @param reportType 报表类型
 * @returns 补录配置对象
 */
export const getConfigByReportType = (reportType: string): Promise<DataSupplementConfigRespVO> => {
  return request.get({
    url: '/report/data-supplement/config',
    params: { reportType }
  })
}

/**
 * 设置补录配置
 * @param data 补录配置数据
 * @returns 操作结果
 */
export const setConfig = (data: SupplementConfigReq): Promise<boolean> => {
  return request.post({
    url: '/report/data-supplement/config/create',
    data
  })
}

/**
 * 更新补录配置
 * @param data 补录配置数据
 * @returns 操作结果
 */
export const updateConfig = (data: SupplementConfigReq): Promise<boolean> => {
  return request.post({
    url: '/report/data-supplement/config/update',
    data
  })
}

/**
 * 删除补录配置
 * @param id 配置ID
 * @returns 操作结果
 */
export const deleteConfig = (id: number): Promise<boolean> => {
  return request.delete({
    url: `/report/data-supplement/config/delete/${id}`
  })
} 