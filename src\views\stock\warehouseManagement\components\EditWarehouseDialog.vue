<template>
  <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑仓库' : '新增仓库'" width="500px" :close-on-click-modal="false"
    @close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="warehouse-form">
      <el-form-item label="仓库编码" prop="warehouseCode">
        <el-input v-model="form.warehouseCode" placeholder="请输入仓库编码" :disabled="isEdit" />
      </el-form-item>
      <el-form-item label="仓库名称" prop="warehouseName">
        <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" />
      </el-form-item>
      <el-form-item label="仓库地址" prop="address">
        <el-input v-model="form.address" placeholder="请输入仓库地址" />
      </el-form-item>
      <el-form-item label="负责人" prop="manager">
        <el-input v-model="form.manager" placeholder="请输入负责人姓名" />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 定义接口
interface WarehouseForm {
  warehouseCode: string;
  warehouseName: string;
  address: string;
  manager: string;
  phone: string;
  remark: string;
}

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  warehouseData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(props.visible)
const formRef = ref(null)

// 表单数据
const form = reactive<WarehouseForm>({
  warehouseCode: '',
  warehouseName: '',
  address: '',
  manager: '',
  phone: '',
  remark: ''
})

// 表单验证规则
const rules = {
  warehouseCode: [{ required: true, message: '请输入仓库编码', trigger: 'blur' }],
  warehouseName: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }],
  address: [{ required: true, message: '请输入仓库地址', trigger: 'blur' }],
  manager: [{ required: true, message: '请输入负责人姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 监听visible属性变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal && props.isEdit) {
      // 编辑模式下，填充表单数据
      Object.keys(form).forEach(key => {
        if (key in props.warehouseData) {
          form[key as keyof WarehouseForm] = props.warehouseData[key] || ''
        }
      })
    }
  }
)

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  if (formRef.value) {
    (formRef.value as any).resetFields()
  }

  Object.keys(form).forEach(key => {
    form[key as keyof WarehouseForm] = ''
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await (formRef.value as any).validate((valid: boolean, fields: any) => {
    if (valid) {
      // 这里可以调用API保存数据
      ElMessage.success(props.isEdit ? '修改成功' : '添加成功')
      emit('success', { ...form })
      handleClose()
    } else {
      console.error('表单验证失败:', fields)
    }
  })
}
</script>

<style scoped>
.warehouse-form {
  margin: 20px 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__wrapper) {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__wrapper:hover) {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.el-form-item {
  margin-bottom: 20px !important;
}
</style>