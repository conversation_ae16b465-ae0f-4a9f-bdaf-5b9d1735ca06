<template>
  <div class="w-full h-[calc(100vh-210px)]">
    <div class="w-full h-full flex flex-col">
      <div class="w-full h-[50px] flex p-4">
        <div class="h-full w-4/5 flex items-center gap-2">
          <el-form :inline="true" :model="formInline" class="flex items-center gap-2">
            <el-form-item label="处理人" class="w-[200px]">
              <el-input v-model="formInline.handler" placeholder="请输入处理人" clearable />
            </el-form-item>
            <el-form-item label="告警级别" class="w-[200px]">
              <el-select v-model="formInline.level" placeholder="请选择告警级别" clearable>
                <el-option label="I级" value="I" />
                <el-option label="II级" value="II" />
              </el-select>
            </el-form-item>
            <el-form-item label="处理时间" class="w-[450px]">
              <el-date-picker v-model="formInline.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
              <el-button type="info" :icon="RefreshRight" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="w-full flex-1 flex flex-col p-4">
        <div class="relative w-full h-full">
          <div class="absolute w-full h-full">
            <el-table v-loading="loading" :data="tableData" border height="100%">
              <el-table-column prop="alarmTime" label="告警时间" align="center" width="180" />
              <el-table-column prop="processTime" label="处理时间" align="center" width="180" />
              <el-table-column prop="ruleName" label="规则名称" align="center" />
              <el-table-column prop="level" label="告警级别" align="center" width="100">
                <template #default="scope">
                  <el-tag :type="getAlarmLevelType(scope.row.level)">{{ scope.row.level }}级</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="handler" label="处理人" align="center" width="120" />
              <el-table-column prop="processStatus" label="处理状态" align="center" width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.processStatus === 'resolved' ? 'success' : 'warning'">
                    {{ scope.row.processStatus === 'resolved' ? '已解决' : '已确认' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template #default="scope">
                  <el-button size="small" type="primary" link @click="handleViewDetail(scope.row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="flex-1 w-full flex items-center justify-end mt-2">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>

    <!-- 处理详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="处理详情" width="60%">
      <div v-if="currentProcess" class="p-4">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="告警时间">{{ currentProcess.alarmTime }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">{{ currentProcess.processTime }}</el-descriptions-item>
          <el-descriptions-item label="告警级别">
            <el-tag :type="getAlarmLevelType(currentProcess.level)">{{ currentProcess.level }}级</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="规则名称">{{ currentProcess.ruleName }}</el-descriptions-item>
          <el-descriptions-item label="处理人">{{ currentProcess.handler }}</el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="currentProcess.processStatus === 'resolved' ? 'success' : 'warning'">
              {{ currentProcess.processStatus === 'resolved' ? '已解决' : '已确认' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="告警详情">
            <div v-if="currentProcess.ruleType === 'single'">
              {{ currentProcess.factor }} {{ getOperatorSymbol(currentProcess.operator) }} {{ currentProcess.threshold }}
              {{ currentProcess.unit ? ' ' + currentProcess.unit : ' /' }}
              (实际值: {{ currentProcess.value }}{{ currentProcess.unit ? ' ' + currentProcess.unit : ' /' }})
            </div>
            <div v-else>{{ currentProcess.expression }}</div>
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="根本原因分析">
            {{ currentProcess.rootCause || '无' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="处理措施">
            {{ currentProcess.actions || '无' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="后续行动">
            {{ currentProcess.followUpActions || '无' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="相关维护记录">
            {{ currentProcess.maintenanceRecords || '无' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="备注">
            {{ currentProcess.remark || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { RefreshRight, Search } from '@element-plus/icons-vue'
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getAlarmProcessRecords, getAlarmProcessDetail } from '@/api/alarm'

const formInline = reactive({
  handler: '',
  level: '',
  dateRange: []
})

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const tableData = ref([])
const detailDialogVisible = ref(false)
const currentProcess = ref(null)

const getAlarmLevelType = (level) => {
  const types = {
    'I': 'danger',
    'II': 'warning'
  }
  return types[level] || 'info'
}

const getOperatorSymbol = (operator) => {
  const symbols = {
    '>': '>',
    '<': '<',
    '=': '=',
    '>=': '≥',
    '<=': '≤'
  }
  return symbols[operator] || operator
}

const fetchProcessRecords = async () => {
  loading.value = true
  try {
    let startDate, endDate
    if (formInline.dateRange && formInline.dateRange.length === 2) {
      startDate = formInline.dateRange[0]
      endDate = formInline.dateRange[1]
    }

    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      handler: formInline.handler || undefined,
      level: formInline.level || undefined,
      startDate,
      endDate
    }
    const response = await getAlarmProcessRecords(params)
    tableData.value = response.data
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取告警处理记录失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const fetchProcessDetail = async (id) => {
  try {
    const response = await getAlarmProcessDetail(id)
    currentProcess.value = response
    detailDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取处理详情失败')
    console.error(error)
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchProcessRecords()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchProcessRecords()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchProcessRecords()
}

const resetForm = () => {
  formInline.handler = ''
  formInline.level = ''
  formInline.dateRange = []
  handleSearch()
}

const handleViewDetail = (row) => {
  fetchProcessDetail(row.id)
}

onMounted(() => {
  fetchProcessRecords()
})
</script>
<style scoped lang="scss"></style> 