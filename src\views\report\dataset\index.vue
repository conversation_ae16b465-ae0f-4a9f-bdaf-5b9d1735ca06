<template>
<!--  <ContentWrap>-->
<!--    <el-dropdown @command="operateDataset">-->
<!--      <el-button type="primary">-->
<!--        <Icon icon="ep:plus" class="button-icon" /> 新增-->
<!--        <Icon icon="ep:arrow-down" class="el-icon&#45;&#45;right" />-->
<!--      </el-button>-->
<!--      <template #dropdown>-->
<!--        <el-dropdown-menu>-->
<!--          <el-dropdown-item command="sql">SQL</el-dropdown-item>-->
<!--          <el-dropdown-item command="http">HTTP</el-dropdown-item>-->
<!--        </el-dropdown-menu>-->
<!--      </template>-->
<!--    </el-dropdown>-->
<!--  </ContentWrap>-->
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="search-form"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="数据集编码" prop="setCode">
        <el-input
          v-model="queryParams.setCode"
          placeholder="请输入数据集编码"
          clearable
          @keyup.enter="handleQuery"
          class="search-input"
        />
      </el-form-item>
      <el-form-item label="数据集名称" prop="setName">
        <el-input
          v-model="queryParams.setName"
          placeholder="请输入数据集名称"
          clearable
          @keyup.enter="handleQuery"
          class="search-input"
        />
      </el-form-item>

<!--      <el-form-item label="数据集类型" prop="setType">-->
<!--        <el-select-->
<!--          v-model="queryParams.setType"-->
<!--          placeholder="请选择数据集类型"-->
<!--          clearable-->
<!--          class="search-input"-->
<!--        >-->
<!--          <el-option label="请选择字典生成" value="" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="数据集状态" prop="enableFlag">
        <el-select
          v-model="queryParams.enableFlag"
          placeholder="请选择数据集状态"
          clearable
          class="search-input"
        >
          <el-option label="已禁用" value="0" />
          <el-option label="已启用" value="1" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="button-icon" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="button-icon" /> 重置
        </el-button>
        <el-dropdown @command="operateDataset" style="margin-left: 12px">
          <el-button type="primary">
            <Icon icon="ep:plus" class="button-icon" /> 新增
            <Icon icon="ep:arrow-down" class="el-icon--right" />
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="sql">SQL</el-dropdown-item>
              <el-dropdown-item command="http">HTTP</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['report:data-set:create']"
        >
          <Icon icon="ep:plus" class="button-icon" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['report:data-set:export']"
        >
          <Icon icon="ep:download" class="button-icon" /> 导出
        </el-button> -->
<!--        <el-button type="success" plain @click="handleExport" :loading="exportLoading">-->
<!--          <Icon icon="ep:download" class="button-icon" /> 导出-->
<!--        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="数据集编码" align="center" prop="setCode" />
      <el-table-column label="数据集名称" align="center" prop="setName" />
      <el-table-column label="数据集描述" align="center" prop="setDesc">
        <template #default="{ row }">
          <div class="cell-wrap">{{ row.setDesc }}</div>
        </template>
      </el-table-column>
      <el-table-column label="数据源编码" align="center" prop="sourceCode" />
      <el-table-column label="动态查询sql或者接口中的请求体" align="center" prop="dynSentence">
        <template #default="{ row }">
          <div class="cell-wrap">{{ row.dynSentence }}</div>
        </template>
      </el-table-column>
<!--      <el-table-column label="结果案例" align="center" prop="caseResult" />-->
      <el-table-column label="数据集状态" align="center" prop="enableFlag">
        <template #default="scope">
          <el-tag v-if="scope.row.enableFlag === 1" type="success">已启用</el-tag>
          <el-tag v-else type="danger">已禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
<!--      <el-table-column label="版本" align="center" prop="version" />-->
      <el-table-column label="数据集类型" align="center" prop="setType" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['report:data-set:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['report:data-set:delete']"
          >
            删除
          </el-button> -->
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id, scope.row.setType)"
            >编辑</el-button
          >
          <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DataSetForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { DataSetApi, DataSetVO } from '@/api/report/dataset'
import DataSetForm from './components/DataSetForm.vue'

/** 数据集管理 列表 */
defineOptions({ name: 'DataSet' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DataSetVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  setCode: undefined,
  setName: undefined,
  setDesc: undefined,
  sourceCode: undefined,
  dynSentence: undefined,
  caseResult: undefined,
  enableFlag: undefined,
  createTime: [],
  version: undefined,
  setType: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DataSetApi.getDataSetPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number, datasetType?: string) => {
  formRef.value.open(type, id, datasetType)
}

/** 处理数据集操作 */
const operateDataset = (command: string) => {
  openForm('create', undefined, command)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DataSetApi.deleteDataSet(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DataSetApi.exportDataSet(queryParams)
    download.excel(data, '数据集管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.search-form {
  margin-bottom: -15px;
  padding: 15px 0;
}

.search-input {
  width: 240px;
}

.date-picker {
  width: 220px;
}

.button-icon {
  margin-right: 5px;
}

.cell-wrap {
  white-space: normal;
  word-break: break-word;
  line-height: 20px;
}

:deep(.el-form-item--default .el-form-item__label) {
  height: 50px;
  line-height: 20px;
  display: flex;
  align-items: center;
}
</style>
