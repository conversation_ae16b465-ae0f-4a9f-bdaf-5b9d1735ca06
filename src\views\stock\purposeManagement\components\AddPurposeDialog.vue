<template>
    <el-dialog v-model="dialogVisible" title="新增用途" width="600px">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
            <el-form-item label="用途名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入用途名称" />
            </el-form-item>
            <el-form-item label="关联仓库" prop="warehouse">
                <el-select v-model="formData.warehouse" placeholder="请选择关联仓库" multiple clearable>
                    <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="商品类型" prop="goodsType">
                <el-select v-model="formData.goodsType" placeholder="请选择商品类型" multiple clearable>
                    <el-option v-for="item in goodsTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="入库流程" prop="inboundProcess">
                <el-select v-model="formData.inboundProcess" placeholder="请选择入库流程">
                    <el-option v-for="item in processOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="领用流程" prop="outboundProcess">
                <el-select v-model="formData.outboundProcess" placeholder="请选择领用流程">
                    <el-option v-for="item in processOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="库存限制">
                <el-switch v-model="formData.stockLimit" active-text="需要库存" inactive-text="不需要库存" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

const props = defineProps({
    visible: {
        type: Boolean,
        required: true
    }
})

const emit = defineEmits(['update:visible', 'submit'])

const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})

const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
    name: '',
    warehouse: [],
    goodsType: [],
    inboundProcess: '',
    outboundProcess: '',
    stockLimit: false
})

// 表单验证规则
const rules: FormRules = {
    name: [{ required: true, message: '请输入用途名称', trigger: 'blur' }],
    warehouse: [{ required: true, message: '请选择关联仓库', trigger: 'change' }],
    goodsType: [{ required: true, message: '请选择商品类型', trigger: 'change' }],
    inboundProcess: [{ required: true, message: '请选择入库流程', trigger: 'change' }],
    outboundProcess: [{ required: true, message: '请选择领用流程', trigger: 'change' }]
}

// 选项数据
const warehouseOptions = [
    { value: '1', label: '主仓库' },
    { value: '2', label: '备用仓库' }
]

const goodsTypeOptions = [
    { value: '1', label: '文具' },
    { value: '2', label: '电子设备' }
]

const processOptions = [
    { value: '1', label: '直接入库' },
    { value: '2', label: '审批入库' },
    { value: '3', label: '审批领用' }
]

// 方法
const handleClose = () => {
    dialogVisible.value = false
    resetForm()
}

const handleSubmit = async () => {
    if (!formRef.value) return
    await formRef.value.validate((valid) => {
        if (valid) {
            emit('submit', { ...formData })
            dialogVisible.value = false
            resetForm()
        }
    })
}

const resetForm = () => {
    Object.assign(formData, {
        name: '',
        warehouse: [],
        goodsType: [],
        inboundProcess: '',
        outboundProcess: '',
        stockLimit: false
    })
}
</script>
<style scoped>
.el-form-item {
    margin-bottom: 20px !important;
}
</style>