<template>
  <div class="monitoring-points-panel">
    <div class="panel-header">
      <div class="title">监测点列表</div>
      <div class="actions">
        <el-tooltip content="刷新实时数据" placement="top">
          <el-button type="primary" size="small" circle @click="refreshPointsData">
            <i class="el-icon-refresh"></i>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <el-scrollbar class="points-scrollbar">
      <div v-if="points.length === 0" class="empty-tip">
        暂无监测点，请点击工具栏中的"添加监测点"按钮添加
      </div>

      <div v-else class="points-list">
        <div v-for="point in points" :key="point.id" class="point-item"
          :class="{ active: selectedPointId === point.id }" @click="selectPoint(point)" @dblclick="editPoint(point)">
          <div class="point-item-header">
            <div class="point-type">
              <span class="point-icon" :class="[`point-${point.type}`, point.isText ? 'point-text' : '']"
                :style="getPointStyle(point)"></span>
              <span class="point-name">{{ point.name || '未命名点位' }}</span>
            </div>

            <div class="point-actions">
              <el-tooltip content="编辑" placement="top">
                <i class="el-icon-edit-outline" @click.stop="editPoint(point)"></i>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <i class="el-icon-delete" @click.stop="deletePoint(point)"></i>
              </el-tooltip>
            </div>
          </div>

          <div class="point-details">
            <div class="point-detail-item">
              <span class="detail-label">类型:</span>
              <span class="detail-value">{{ getPointTypeLabel(point) }}</span>
            </div>
            <div class="point-detail-item">
              <span class="detail-label">位置:</span>
              <span class="detail-value">X:{{ Math.round(point.x) }} Y:{{ Math.round(point.y) }}</span>
            </div>
            <div class="point-detail-item" v-if="point.data?.indicator">
              <span class="detail-label">指标:</span>
              <span class="detail-value">{{ point.data.indicator }}</span>
            </div>
            <div class="point-detail-item" v-if="pointsData[point.id]">
              <span class="detail-label">数值:</span>
              <span class="detail-value" :style="{ color: getValueColor(point, pointsData[point.id]) }">
                {{ pointsData[point.id] }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { getPointColorByType } from '../utils/PointsDataConverter'

// 点位类型
interface Point {
  id: string;
  x: number;
  y: number;
  name: string;
  type: string;
  data?: {
    indicator?: string;
    color?: string;
    size?: number;
    [key: string]: any;
  };
  isText: boolean;
}

// 定义props
const props = defineProps({
  points: {
    type: Array as () => Point[],
    default: () => []
  },
  selectedPointId: {
    type: String,
    default: ''
  }
})

// 定义emit
const emit = defineEmits(['select', 'edit', 'delete', 'refresh-data'])

// 实时数据
const pointsData = ref<Record<string, string | number>>({})

// 点位类型映射
const pointTypeLabels = {
  circle: '圆形',
  square: '方形',
  triangle: '三角形',
  text: '文本标签'
}

// 获取点位类型标签
const getPointTypeLabel = (point: Point) => {
  if (point.isText) return '文本标签';
  return pointTypeLabels[point.type as keyof typeof pointTypeLabels] || '未知类型';
}

// 获取点位样式
const getPointStyle = (point: Point) => {
  if (point.isText) {
    return {
      color: point.data?.color || '#333333'
    }
  }

  const color = point.data?.color || getPointColorByType(point.type);
  return {
    backgroundColor: color
  }
}

// 获取数值颜色
const getValueColor = (point: Point, value: string | number) => {
  // 这里可以根据阈值等自定义颜色逻辑
  if (typeof value === 'number') {
    if (value > 80) return '#F56C6C'; // 红色-报警
    if (value > 60) return '#E6A23C'; // 橙色-预警
    return '#67C23A'; // 绿色-正常
  }
  return '#409EFF'; // 默认蓝色
}

// 选择点位
const selectPoint = (point: Point) => {
  emit('select', point)
}

// 编辑点位
const editPoint = (point: Point) => {
  emit('edit', point)
}

// 删除点位
const deletePoint = (point: Point) => {
  ElMessageBox.confirm(
    `确定要删除${point.isText ? '文本' : '监测点'} "${point.name}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    emit('delete', point)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除
  })
}

// 刷新点位数据
const refreshPointsData = () => {
  emit('refresh-data')
  ElMessage.success('正在刷新监测点实时数据...')
}

// 提供更新点位数据的方法
const updatePointsData = (data: Record<string, string | number>) => {
  pointsData.value = data
}

// 暴露方法
defineExpose({
  updatePointsData
})
</script>

<style scoped>
.monitoring-points-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-left: 1px solid #e4e7ed;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header .title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.points-scrollbar {
  flex: 1;
  overflow: hidden;
}

.empty-tip {
  padding: 24px 16px;
  text-align: center;
  color: #909399;
}

.points-list {
  padding: 8px;
}

.point-item {
  margin-bottom: 12px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.point-item:hover {
  background-color: #ecf5ff;
}

.point-item.active {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

.point-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.point-type {
  display: flex;
  align-items: center;
}

.point-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: 8px;
}

.point-circle {
  border-radius: 50%;
}

.point-square {
  border-radius: 2px;
}

.point-triangle {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 12px solid #409eff;
  background: none !important;
}

.point-text:before {
  content: "T";
  font-weight: bold;
}

.point-name {
  font-weight: 500;
  color: #303133;
}

.point-actions {
  display: flex;
  gap: 8px;
}

.point-actions i {
  color: #909399;
  font-size: 16px;
  cursor: pointer;
}

.point-actions i:hover {
  color: #409eff;
}

.point-details {
  margin-top: 8px;
  font-size: 12px;
}

.point-detail-item {
  display: flex;
  margin-bottom: 4px;
  color: #606266;
}

.detail-label {
  min-width: 50px;
  color: #909399;
}

.detail-value {
  flex: 1;
}
</style>