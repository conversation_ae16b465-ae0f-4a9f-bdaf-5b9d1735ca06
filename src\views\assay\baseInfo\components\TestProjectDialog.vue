<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="检测项目名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入检测项目名称，如：有机物检测" />
      </el-form-item>
      
      <el-form-item label="检测项目代码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入检测项目代码，如：WATER_ORGANIC" />
      </el-form-item>
      
      <el-form-item label="所属类型" prop="categoryId">
        <el-select v-model="formData.categoryId" placeholder="请选择所属类型" style="width: 100%">
          <el-option label="水质检测" :value="1" />
          <el-option label="污泥检测" :value="2" />
          <el-option label="气体检测" :value="3" />
          <el-option label="噪声检测" :value="4" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="检测项目描述" prop="description">
        <el-input 
          v-model="formData.description" 
          type="textarea" 
          :rows="3"
          placeholder="请输入检测项目描述" 
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="isEnabled">
        <el-radio-group v-model="formData.isEnabled">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">停用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'
import { AssayBasicApi } from '@/api/assay/basic'

defineOptions({ name: 'TestProjectDialog' })

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')

// 表单数据
const formData = ref({
  id: undefined as number | undefined,
  factoryId: undefined as number | undefined,
  name: '',
  code: '',
  categoryId: undefined as number | undefined,
  description: '',
  isEnabled: true
})

// 表单校验规则
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '检测项目名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '检测项目代码不能为空', trigger: 'blur' }],
  categoryId: [{ required: true, message: '所属类型不能为空', trigger: 'change' }]
})

const formRef = ref()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (type: string, data?: any, factoryId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增检测项目' : '编辑检测项目'
  formType.value = type
  resetForm()

  // 设置水厂ID
  if (factoryId) {
    formData.value.factoryId = factoryId
  }

  // 如果是新增模式，设置类型ID
  if (type === 'create' && data?.categoryId) {
    formData.value.categoryId = data.categoryId
  }

  // 如果是编辑模式，设置表单数据
  if (type === 'update' && data) {
    formData.value = JSON.parse(JSON.stringify(data))
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  if (!formData.value.factoryId) {
    ElMessage.error('水厂ID未设置，无法提交')
    return
  }

  formLoading.value = true
  try {
    if (formType.value === 'create') {
      await AssayBasicApi.createTestProject({
        factoryId: formData.value.factoryId,
        name: formData.value.name,
        code: formData.value.code,
        categoryId: formData.value.categoryId!,
        description: formData.value.description,
        isEnabled: formData.value.isEnabled
      })
    } else {
      await AssayBasicApi.updateTestProject({
        id: formData.value.id!,
        factoryId: formData.value.factoryId,
        name: formData.value.name,
        code: formData.value.code,
        categoryId: formData.value.categoryId!,
        description: formData.value.description,
        isEnabled: formData.value.isEnabled
      })
    }

    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    factoryId: undefined,
    name: '',
    code: '',
    categoryId: undefined,
    description: '',
    isEnabled: true
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}
</style>
