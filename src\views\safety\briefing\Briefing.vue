<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>安全生产简报管理</span>
        </div>
      </template>
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="简报录入" name="briefingInput">
          <BriefingInput />
        </el-tab-pane>
        <el-tab-pane label="简报审核" name="briefingReview">
          <BriefingReview />
        </el-tab-pane>
        <el-tab-pane label="简报查询" name="briefingQuery">
          <BriefingQuery />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script lang='ts' setup>
import { ref } from 'vue'
import BriefingInput from './components/BriefingInput.vue'
import BriefingQuery from './components/BriefingQuery.vue'
import BriefingReview from './components/BriefingReview.vue'

const activeTab = ref('briefingInput')
</script>

<style scoped lang='scss'>
.app-container {
  // padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>