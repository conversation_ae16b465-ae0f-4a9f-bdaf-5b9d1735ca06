<template>
  <el-dialog
    :title="props.type === 'add' ? '新增分类' : '编辑分类'"
    v-model="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="category-form"
    >
      <el-form-item label="上级分类" prop="parentId">
        <el-cascader
          v-model="formData.parentId"
          :options="categoryOptions"
          :props="{ checkStrictly: true }"
          placeholder="请选择上级分类"
          clearable
        />
      </el-form-item>

      <el-form-item label="分类名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入分类名称" />
      </el-form-item>

      <el-form-item label="分类编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入分类编码" />
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" :max="999" />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入分类描述"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'

const props = defineProps({
  type: {
    type: String as () => 'add' | 'edit',
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  },
  categoryOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive({
  parentId: null,
  name: '',
  code: '',
  sort: 0,
  description: ''
})

const rules: FormRules = {
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入分类编码', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入排序号', trigger: 'change' }]
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      // TODO: 实现提交逻辑
      ElMessage.success('提交成功')
      dialogVisible.value = false
      emit('success', formData)
    }
  })
}

const handleClosed = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  Object.assign(formData, {
    parentId: null,
    name: '',
    code: '',
    sort: 0,
    description: ''
  })
}

// 暴露方法给父组件
defineExpose({
  open: (data?: any) => {
    dialogVisible.value = true
    if (props.type === 'edit' && data) {
      Object.assign(formData, data)
    }
  }
})
</script>

<style lang="scss" scoped>
.category-form {
  .el-input-number {
    width: 120px;
  }
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style> 