<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">终端总览</span>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex flex-col">
        <!-- 终端概览 -->
        <div class="mb-4">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>总终端数</span>
                  <Icon icon="ep:monitor" class="text-[32px] text-blue-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">128</span>
                  <span class="text-green-500">↑ 5.2%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>在线终端数</span>
                  <Icon icon="ep:connection" class="text-[32px] text-green-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">120</span>
                  <span class="text-green-500">↑ 3.5%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>离线终端数</span>
                  <Icon icon="ep:disconnect" class="text-[32px] text-red-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">8</span>
                  <span class="text-red-500">↑ 1.2%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>平均在线率</span>
                  <Icon icon="ep:data-line" class="text-[32px] text-yellow-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">93.8%</span>
                  <span class="text-green-500">↑ 0.8%</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 搜索表单 -->
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="终端类型" style="width: 200px;">
              <el-select v-model="searchForm.type" placeholder="请选择终端类型" clearable>
                <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" style="width: 200px;">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="在线" value="online" />
                <el-option label="离线" value="offline" />
                <el-option label="故障" value="fault" />
              </el-select>
            </el-form-item>
            <el-form-item label="IP地址" style="width: 200px;">
              <el-input v-model="searchForm.ip" placeholder="请输入IP地址" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 终端列表 -->
        <div class="flex-1">
          <el-table :data="tableData" border style="width: 100%" height="100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="name" label="终端名称" min-width="150" />
            <el-table-column prop="type" label="终端类型" width="120" align="center" />
            <el-table-column prop="ip" label="IP地址" width="150" align="center" />
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cpu" label="CPU使用率" width="120" align="center">
              <template #default="{ row }">
                <el-progress :percentage="row.cpu" :color="getProgressColor(row.cpu)" />
              </template>
            </el-table-column>
            <el-table-column prop="memory" label="内存使用率" width="120" align="center">
              <template #default="{ row }">
                <el-progress :percentage="row.memory" :color="getProgressColor(row.memory)" />
              </template>
            </el-table-column>
            <el-table-column prop="disk" label="磁盘使用率" width="120" align="center">
              <template #default="{ row }">
                <el-progress :percentage="row.disk" :color="getProgressColor(row.disk)" />
              </template>
            </el-table-column>
            <el-table-column prop="version" label="软件版本" width="120" align="center" />
            <el-table-column prop="lastOnline" label="最后在线时间" width="160" align="center" />
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleViewDetail(row)">查看详情</el-button>
                <el-button type="primary" link @click="handleConfig(row)">配置</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>

    <!-- 终端详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="终端详情" width="800px" class="detail-dialog">
      <div v-if="selectedTerminal" class="space-y-6">
        <!-- 基本信息 -->
        <div class="border rounded p-4">
          <h3 class="text-lg font-bold mb-4">基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="终端名称">{{ selectedTerminal.name }}</el-descriptions-item>
            <el-descriptions-item label="终端类型">{{ selectedTerminal.type }}</el-descriptions-item>
            <el-descriptions-item label="IP地址">{{ selectedTerminal.ip }}</el-descriptions-item>
            <el-descriptions-item label="MAC地址">{{ selectedTerminal.mac }}</el-descriptions-item>
            <el-descriptions-item label="软件版本">{{ selectedTerminal.version }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(selectedTerminal.status)">{{ selectedTerminal.status }}</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 性能监控 -->
        <div class="border rounded p-4">
          <h3 class="text-lg font-bold mb-4">性能监控</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="text-center">
                <div class="text-gray-500 mb-2">CPU使用率</div>
                <el-progress type="dashboard" :percentage="selectedTerminal.cpu"
                  :color="getProgressColor(selectedTerminal.cpu)" />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="text-center">
                <div class="text-gray-500 mb-2">内存使用率</div>
                <el-progress type="dashboard" :percentage="selectedTerminal.memory"
                  :color="getProgressColor(selectedTerminal.memory)" />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="text-center">
                <div class="text-gray-500 mb-2">磁盘使用率</div>
                <el-progress type="dashboard" :percentage="selectedTerminal.disk"
                  :color="getProgressColor(selectedTerminal.disk)" />
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 接入设备 -->
        <div class="border rounded p-4">
          <h3 class="text-lg font-bold mb-4">接入设备</h3>
          <el-table :data="selectedTerminal.devices" border style="width: 100%">
            <el-table-column prop="name" label="设备名称" min-width="150" />
            <el-table-column prop="type" label="设备类型" width="120" align="center" />
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="ip" label="IP地址" width="150" align="center" />
            <el-table-column prop="lastOnline" label="最后在线时间" width="160" align="center" />
          </el-table>
        </div>

        <!-- 网络配置 -->
        <div class="border rounded p-4">
          <h3 class="text-lg font-bold mb-4">网络配置</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="子网掩码">{{ selectedTerminal.netmask }}</el-descriptions-item>
            <el-descriptions-item label="默认网关">{{ selectedTerminal.gateway }}</el-descriptions-item>
            <el-descriptions-item label="DNS服务器">{{ selectedTerminal.dns }}</el-descriptions-item>
            <el-descriptions-item label="网络带宽">{{ selectedTerminal.bandwidth }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

    <!-- 终端配置对话框 -->
    <el-dialog v-model="configDialogVisible" title="终端配置" width="800px">
      <el-form ref="configFormRef" :model="configForm" :rules="configRules" label-width="120px">
        <el-form-item label="终端名称" prop="name">
          <el-input v-model="configForm.name" placeholder="请输入终端名称" />
        </el-form-item>
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="configForm.ip" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="子网掩码" prop="netmask">
          <el-input v-model="configForm.netmask" placeholder="请输入子网掩码" />
        </el-form-item>
        <el-form-item label="默认网关" prop="gateway">
          <el-input v-model="configForm.gateway" placeholder="请输入默认网关" />
        </el-form-item>
        <el-form-item label="DNS服务器" prop="dns">
          <el-input v-model="configForm.dns" placeholder="请输入DNS服务器" />
        </el-form-item>
        <el-form-item label="网络带宽" prop="bandwidth">
          <el-input v-model="configForm.bandwidth" placeholder="请输入网络带宽">
            <template #append>Mbps</template>
          </el-input>
        </el-form-item>
        <el-form-item label="软件版本" prop="version">
          <el-select v-model="configForm.version" placeholder="请选择软件版本">
            <el-option v-for="item in versionOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="configDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveConfig">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  type: '',
  status: '',
  ip: ''
})

// 终端类型选项
const typeOptions = [
  { label: '视频监控终端', value: 'video' },
  { label: '数据采集终端', value: 'data' },
  { label: '控制终端', value: 'control' }
]

// 软件版本选项
const versionOptions = [
  { label: 'V1.0.0', value: '1.0.0' },
  { label: 'V1.1.0', value: '1.1.0' },
  { label: 'V1.2.0', value: '1.2.0' }
]

// 表格数据
const tableData = ref([
  {
    id: '1',
    name: '终端A',
    type: '视频监控终端',
    ip: '*************',
    status: 'online',
    cpu: 45,
    memory: 60,
    disk: 30,
    version: 'V1.1.0',
    lastOnline: '2024-04-21 10:00:00',
    mac: '00:11:22:33:44:55',
    netmask: '*************',
    gateway: '***********',
    dns: '*******',
    bandwidth: '100',
    devices: [
      {
        name: '摄像头1',
        type: '摄像头',
        status: 'online',
        ip: '*************',
        lastOnline: '2024-04-21 10:00:00'
      }
    ]
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框
const detailDialogVisible = ref(false)
const configDialogVisible = ref(false)
const selectedTerminal = ref<any>(null)
const configFormRef = ref<FormInstance>()

// 配置表单
const configForm = reactive({
  name: '',
  ip: '',
  netmask: '',
  gateway: '',
  dns: '',
  bandwidth: '',
  version: ''
})

// 配置表单验证规则
const configRules = {
  name: [{ required: true, message: '请输入终端名称', trigger: 'blur' }],
  ip: [{ required: true, message: '请输入IP地址', trigger: 'blur' }],
  netmask: [{ required: true, message: '请输入子网掩码', trigger: 'blur' }],
  gateway: [{ required: true, message: '请输入默认网关', trigger: 'blur' }],
  dns: [{ required: true, message: '请输入DNS服务器', trigger: 'blur' }],
  bandwidth: [{ required: true, message: '请输入网络带宽', trigger: 'blur' }],
  version: [{ required: true, message: '请选择软件版本', trigger: 'change' }]
}

// 方法
const getStatusType = (status: string) => {
  switch (status) {
    case 'online':
      return 'success'
    case 'offline':
      return 'info'
    case 'fault':
      return 'danger'
    default:
      return 'info'
  }
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#F56C6C'
  if (percentage >= 60) return '#E6A23C'
  return '#67C23A'
}

const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', searchForm)
}

const resetSearch = () => {
  searchForm.type = ''
  searchForm.status = ''
  searchForm.ip = ''
}

const handleViewDetail = (row: any) => {
  selectedTerminal.value = row
  detailDialogVisible.value = true
}

const handleConfig = (row: any) => {
  selectedTerminal.value = row
  Object.assign(configForm, {
    name: row.name,
    ip: row.ip,
    netmask: row.netmask,
    gateway: row.gateway,
    dns: row.dns,
    bandwidth: row.bandwidth,
    version: row.version
  })
  configDialogVisible.value = true
}

const handleSaveConfig = async () => {
  if (!configFormRef.value) return
  await configFormRef.value.validate((valid) => {
    if (valid) {
      // 实现保存逻辑
      console.log('配置数据:', configForm)
      configDialogVisible.value = false
      ElMessage.success('配置保存成功')
    }
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

.detail-dialog {
  :deep(.el-dialog__body) {
    max-height: 70vh;
    overflow-y: auto;
  }
}
</style>
