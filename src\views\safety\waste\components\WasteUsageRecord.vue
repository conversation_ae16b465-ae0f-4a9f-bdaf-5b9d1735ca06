<template>
  <div class="waste-usage-record">
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">新增使用记录</el-button>
      <el-form :inline="true" :model="queryForm" class="search-form">
        <el-form-item label="使用时间">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="使用人员">
          <el-input v-model="queryForm.user" placeholder="请输入使用人员" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="date" label="使用时间" width="180" />
      <el-table-column prop="chemicalName" label="危化品名称" width="180" />
      <el-table-column prop="amount" label="使用量" width="120" />
      <el-table-column prop="unit" label="单位" width="100" />
      <el-table-column prop="user" label="使用人员" width="120" />
      <el-table-column prop="purpose" label="使用用途" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      class="pagination"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'

const emit = defineEmits(['showDialog'])

const queryForm = reactive({
  dateRange: [],
  user: ''
})

// 模拟数据
const tableData = ref([
  {
    date: '2024-03-20',
    chemicalName: '硫酸',
    amount: 100,
    unit: 'ml',
    user: '张三',
    purpose: '实验室使用'
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(1)

const handleAdd = () => {
  emit('showDialog', true)
}

const handleSearch = () => {
  // 实现查询逻辑
  console.log('查询条件：', queryForm)
}

const handleReset = () => {
  queryForm.dateRange = []
  queryForm.user = ''
}

const handleEdit = (row) => {
  console.log('编辑行：', row)
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    console.log('删除行：', row)
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
  })
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  // 重新加载数据
}
</script>

<style scoped>
.waste-usage-record {
  padding: 20px;
}

.operation-bar {
  margin-bottom: 20px;
}

.search-form {
  margin-top: 15px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style> 