<template>
  <ContentWrap v-if="!showProcessDetail">
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="资产编码" prop="assetCode">
        <el-input
          v-model="queryParams.assetCode"
          placeholder="请输入资产编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="资产名称" prop="assetName">
        <el-input
          v-model="queryParams.assetName"
          placeholder="请输入资产名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="资产类型" prop="assetTypeId">
        <el-select
          v-model="queryParams.assetTypeId"
          placeholder="请选择资产类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in assetTypeList"
            :key="item.id"
            :label="item.typeName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资产状态" prop="statusId">
        <el-select
          v-model="queryParams.statusId"
          placeholder="请选择资产状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in assetStatusList"
            :key="item.id"
            :label="item.statusName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="固定资产" prop="isFixed">
        <el-select
          v-model="queryParams.isFixed"
          placeholder="是否固定资产"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in isFixedTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px"/>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px"/>
          重置
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['asset:asset:create']"
        >
          <Icon icon="ep:plus" class="mr-5px"/>
          新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['asset:asset:export']"
        >
          <Icon icon="ep:download" class="mr-5px"/>
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap v-if="!showProcessDetail">
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!--      <el-table-column label="主键ID" align="center" prop="id"/>-->
      <el-table-column label="资产编码" align="center" prop="assetCode"/>
      <el-table-column label="资产名称" align="center" prop="assetName"/>
      <el-table-column label="资产类型" align="center" prop="assetTypeId">
        <template #default="scope">
          {{ assetTypeList?.find(item => item.id === scope.row.assetTypeId)?.typeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="资产状态" align="center" prop="statusId">
        <template #default="scope">
          {{ assetStatusList?.find(item => item.id === scope.row.statusId)?.statusName || '-' }}
        </template>
      </el-table-column>

      <el-table-column label="固定资产" align="center" prop="isFixed">
        <template #default="scope">
          {{ isFixedTypeList?.find(item => item.value === scope.row.isFixed)?.label || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="使用部门" align="center" prop="usageDeptName"/>
      <el-table-column label="使用人" align="center" prop="usageUserName"/>
      <el-table-column label="存放地点" align="center" prop="location"/>
      <el-table-column label="购置日期" align="center" prop="purchaseDate"/>
      <el-table-column label="资产原值/采购金额" align="center" prop="originalValue"/>
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['asset:asset:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="success"
            @click="createWorkflow(scope.row.id)"
            v-hasPermi="['asset:asset:change']"
          >
            变更
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['asset:asset:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="资产编码" prop="assetCode">
        <el-input v-model="formData.assetCode" placeholder="请输入资产编码"/>
      </el-form-item>
      <el-form-item label="资产名称" prop="assetName">
        <el-input v-model="formData.assetName" placeholder="请输入资产名称"/>
      </el-form-item>
      <el-form-item label="资产类型" prop="assetTypeId">
        <el-select v-model="formData.assetTypeId" placeholder="请选择资产类型">
          <el-option
            v-for="item in assetTypeList"
            :key="item.id"
            :label="item.typeName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资产状态" prop="statusId">
        <el-select v-model="formData.statusId" placeholder="请选择资产状态">
          <el-option
            v-for="item in assetStatusList"
            :key="item.id"
            :label="item.statusName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="固定资产" prop="isFixed">
        <el-radio-group v-model="formData.isFixed">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="使用部门" prop="usageDeptId">
        <el-input v-model="formData.usageDeptId" placeholder="请选则使用部门"/>
      </el-form-item>
      <el-form-item label="使用人" prop="usageUserId">
        <el-input v-model="formData.usageUserId" placeholder="请选则使用人"/>
      </el-form-item>
      <el-form-item label="存放地点" prop="location">
        <el-input v-model="formData.location" placeholder="请输入存放地点"/>
      </el-form-item>
      <el-form-item label="购置日期" prop="purchaseDate">
        <el-date-picker
          v-model="formData.purchaseDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择购置日期"
        />
      </el-form-item>
      <el-form-item label="资产原值" prop="originalValue">
        <el-input v-model="formData.originalValue" placeholder="请输入资产原值/采购金额"/>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 流程详情显示区 -->
  <ProcessDefinitionDetail
    v-if="showProcessDetail"
    ref="processDefDetailRef"
    :select-process-definition="processDefinition"
    @cancel="handleCancelWorkFlow"
  />

</template>

<script setup lang="ts">
import download from '@/utils/download'
import {AssetApi, AssetVO} from '@/api/asset/asset'
import {AssetTypeApi, AssetTypeVO} from '@/api/asset/type'
import {AssetStatusApi, AssetStatusVO} from '@/api/asset/status'
import ProcessDefinitionDetail from '@/views/bpm/processInstance/create/ProcessDefinitionDetail.vue'
import {getProcessDefinition} from '@/api/bpm/definition'

const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<AssetVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const assetTypeList = ref<AssetTypeVO[]>([]) // 资产类型列表
const assetStatusList = ref<AssetStatusVO[]>([]) // 资产状态列表
const isFixedTypeList = [
  {label: '是', value: 1},
  {label: '否', value: 0}
]

// 流程控制显示状态
const showProcessDetail = ref(false)
const processDefDetailRef = ref()
const processDefinition = ref()

// 获取流程定义
const getProcessDefinitionByKey = async (key) => {
  const flow = await getProcessDefinition(undefined, key)
  processDefinition.value = flow
}


// 创建流程函数
const createWorkflow = async (id) => {
  // 加点路劲参数，方便流程表单获取数据
  // 获取当前的 URL
  let currentUrl = new URL(window.location.href);
// 设置新的查询参数
  currentUrl.searchParams.set('assetId', id);
// 使用 pushState 更新 URL
  window.history.pushState({}, '', currentUrl.toString());

  try {
    showProcessDetail.value = true
    await nextTick() // 等待 DOM 更新
    await processDefDetailRef.value.initProcessInfo(processDefinition.value)
  } catch (error) {
    console.error('创建流程失败:', error)
    message.error('创建流程失败，请重试')
    showProcessDetail.value = false
  }
}

// 取消流程
const handleCancelWorkFlow = () => {
  showProcessDetail.value = false
  processDefDetailRef.value = null
  // 删除路劲参数
  // 获取当前 URL
  let currentUrl = new URL(window.location.href)
  // 删除指定的查询参数
  currentUrl.searchParams.delete('workflowId');  // 假设你要删除 'workflowId' 参数
  // 使用 replaceState 替换当前的 URL（不会刷新页面）
  window.history.replaceState({}, '', currentUrl.toString());
}


// 表单相关变量
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  assetCode: undefined,
  assetName: undefined,
  assetTypeId: undefined,
  statusId: undefined,
  isFixed: undefined,
  usageDeptId: undefined,
  usageUserId: undefined,
  location: undefined,
  purchaseDate: undefined,
  originalValue: undefined,
  remark: undefined,
})
const formRules = reactive({
  assetCode: [{required: true, message: '资产编码不能为空', trigger: 'blur'}],
  assetName: [{required: true, message: '资产名称不能为空', trigger: 'blur'}],
  assetTypeId: [{required: true, message: '资产类型不能为空', trigger: 'change'}],
  statusId: [{required: true, message: '资产状态不能为空', trigger: 'change'}],
  isFixed: [{required: true, message: '是否固定资产不能为空', trigger: 'change'}],
  usageDeptId: [{required: true, message: '使用部门不能为空', trigger: 'blur'}],
  usageUserId: [{required: true, message: '使用人不能为空', trigger: 'blur'}],
})
const formRef = ref() // 表单 Ref

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  assetCode: undefined,
  assetName: undefined,
  assetTypeId: undefined,
  statusId: undefined,
  isFixed: undefined,
  usageDeptId: undefined,
  usageUserId: undefined,
  location: undefined,
  purchaseDate: undefined,
  originalValue: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const queryAssetTypeList = async () => {
  const res = await AssetTypeApi.getAssetTypeList({})
  assetTypeList.value = res
}

const queryAssetStatusList = async () => {
  const res = await AssetStatusApi.getAssetStatusList({})
  assetStatusList.value = res
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 先加载类型和状态列表
    await Promise.all([
      queryAssetTypeList(),
      queryAssetStatusList(),
    ])

    // 再加载资产列表
    const res = await AssetApi.getAssetPage(queryParams)
    list.value = res.records
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const openForm = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 加载资产类型和状态列表
  try {
    await Promise.all([
      queryAssetTypeList(),
      queryAssetStatusList()
    ])
  } catch (error) {
    console.error('Failed to load asset type and status lists:', error)
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AssetApi.getAsset(id)
    } finally {
      formLoading.value = false
    }
  }
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AssetVO
    if (formType.value === 'create') {
      await AssetApi.createAsset(data)
      message.success(t('common.createSuccess'))
    } else {
      await AssetApi.updateAsset(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 刷新列表
    await getList()
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    assetCode: undefined,
    assetName: undefined,
    assetTypeId: undefined,
    statusId: undefined,
    isFixed: undefined,
    usageDeptId: undefined,
    usageUserId: undefined,
    location: undefined,
    purchaseDate: undefined,
    originalValue: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AssetApi.deleteAsset(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AssetApi.exportAsset(queryParams)
    download.excel(data, '资产列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


/** 发起资产变更流程 路由到发起流程页面自动选则流程 （这种方式弃用） */
// const handleChange = async (id: number) => {
//   try {
//     await startProcess(router, {
//       processKey: 'Asset_Change', // 资产变更流程的 key
//       businessKey: id.toString(), // 资产 ID 作为业务标识
//       variables: {
//         assetId: id
//       }
//     })
//   } catch (error: any) {
//     message.error('发起资产变更流程失败：' + error.message)
//   }
// }

/** 初始化 **/
onMounted(async () => {
  getList();
  await getProcessDefinitionByKey('Asset_Change');
})
</script>
