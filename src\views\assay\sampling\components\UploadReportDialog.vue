<template>
  <Dialog v-model="dialogVisible" title="上传报告文件">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="报告编号">
        <el-input v-model="formData.code" disabled />
      </el-form-item>
      <el-form-item label="报告名称">
        <el-input v-model="formData.name" disabled />
      </el-form-item>
      <el-form-item label="报告文件" prop="file">
        <el-upload
          class="upload-area"
          action="#"
          :auto-upload="false"
          :limit="1"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">支持PDF、Word、Excel格式，文件大小不超过10MB</div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="文件描述" prop="fileDescription">
        <el-input v-model="formData.fileDescription" type="textarea" placeholder="请输入文件描述信息" />
      </el-form-item>
      <el-form-item label="上传备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入上传备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">上 传</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules, UploadFile } from 'element-plus'
import { Dialog } from '../../../../components/Dialog'

defineOptions({ name: 'UploadReportDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)

// 表单数据
const formData = ref({
  id: undefined as number | undefined,
  code: '',
  name: '',
  file: undefined as UploadFile | undefined,
  fileDescription: '',
  remark: ''
})

// 表单校验规则
const formRules = reactive<FormRules>({
  file: [{ required: true, message: '请上传报告文件', trigger: 'change' }],
  fileDescription: [{ required: true, message: '请输入文件描述', trigger: 'blur' }]
})

const formRef = ref()
const emit = defineEmits(['success'])

// 文件变更处理
const handleFileChange = (file: UploadFile) => {
  // 检查文件类型
  const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
  if (!allowedTypes.includes(file.raw?.type || '')) {
    ElMessage.error('文件类型不支持，请上传PDF、Word或Excel文件')
    return false
  }
  
  // 检查文件大小
  const maxSize = 10 * 1024 * 1024 // 10MB
  if ((file.raw?.size || 0) > maxSize) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  
  formData.value.file = file
}

// 文件移除处理
const handleFileRemove = () => {
  formData.value.file = undefined
}

// 打开对话框
const open = async (data: any) => {
  if (!data) return
  
  dialogVisible.value = true
  formData.value.id = data.id
  formData.value.code = data.code
  formData.value.name = data.name
  formData.value.file = undefined
  formData.value.fileDescription = ''
  formData.value.remark = ''
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用 - 这里应该是实际上传文件的代码
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('文件上传成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('文件上传失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script>

<style scoped>
.upload-area {
  width: 100%;
}
</style> 