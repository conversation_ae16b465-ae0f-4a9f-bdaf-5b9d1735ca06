/**
 * 点位数据格式转换工具类
 * 处理内部使用的点位数据格式和API格式之间的转换
 */

/**
 * 将内部使用的点位数据转换为符合后端API要求的格式
 * @param {Array} points 内部使用的点位数组
 * @param {number|string} screenId 画面ID，可选参数
 * @returns {Array} 符合API格式的点位数组
 */
export function convertPointsToApiFormat(points, screenId = null) {
  if (!points || !Array.isArray(points)) return []

  return points.map((point) => {
    // 创建基本API点位对象 - 不再包含isText、styleInfo和id字段
    const apiPoint = {
      name: point.name || '',
      showType: point.showType || point.type || 'circle',
      positionX: String(point.x || 0),
      positionY: String(point.y || 0),
      // 移除id字段，保存时不传递id给后端
    }

    // 设置screenId (必需字段)
    if (screenId) {
      apiPoint.screenId = screenId
    }

    // 设置颜色，便于后端直接读取
    if (point.data?.color) {
      apiPoint.color = point.data.color
    } else if (point.color) {
      apiPoint.color = point.color
    }

    // 对于图形点位，设置大小
    if (!point.isText && point.data?.size) {
      apiPoint.pointSize = point.data.size
    }

    // 对于文本点位，设置字体大小和粗细
    if (point.isText) {
      if (point.data?.pointSize) {
        apiPoint.pointSize = point.data.pointSize
      }
      if (point.data?.textThickness) {
        apiPoint.textThickness = point.data.textThickness
      }
    }

    // 设置关联指标代码
    if (point.data?.indicator) {
      apiPoint.indicatorCode = point.data.indicator
    } else if (point.indicatorCode) {
      apiPoint.indicatorCode = point.indicatorCode
    }

    // 传递与文本相关的样式（仅保留showDisplay字段，移除pointSize和textThickness）
    if (point.isText) {
      // 只保留hasBorder/showDisplay属性，移除pointSize和textThickness
      if (point.data?.hasBorder !== undefined) {
        apiPoint.showDisplay = point.data.hasBorder ? 1 : 0
      } else if (point.showDisplay !== undefined) {
        apiPoint.showDisplay = point.showDisplay
      }
    }

    return apiPoint
  })
}

/**
 * 将API格式的点位数据转换为内部使用的格式
 * @param {Array} apiPoints API格式的点位数组
 * @returns {Array} 内部使用的点位数组
 */
export function convertApiPointsToInternalFormat(apiPoints) {
  if (!apiPoints || !Array.isArray(apiPoints)) return []

  return apiPoints.map((point) => {
    // 解析样式信息
    let styleInfo = {}
    try {
      if (point.styleInfo) {
        styleInfo = JSON.parse(point.styleInfo)
      }
    } catch (e) {
      console.error('解析点位样式信息失败:', e, point.styleInfo)
    }

    // 确定是否为文本类型 - 由于API数据中不再包含isText字段，主要依赖showType
    const isText = point.showType === 'text'

    // 基本点位信息
    const internalPoint = {
      id: point.id || `point-${Date.now()}-${Math.floor(Math.random() * 10000)}`, // 使用API返回的ID或生成新ID
      name: point.name || '',
      x: Number(point.positionX) || 0,
      y: Number(point.positionY) || 0,
      showType: point.showType || 'circle',
      type: point.showType || 'circle', // 保持类型一致性
      isText: isText,
      // 数据对象，包含所有样式和元数据
      data: {
        // 基本样式
        color:
          point.color ||
          styleInfo.color ||
          (isText ? '#000000' : getPointColorByType(point.showType || 'circle')),
        size: styleInfo.size || (isText ? 13 : 8)
      }
    }

    // 添加指标信息
    if (point.indicatorCode) {
      internalPoint.data.indicator = point.indicatorCode
    }

    // 处理文本类型特有属性
    if (isText) {
      // 文本样式属性
      internalPoint.data.pointSize = point.pointSize || styleInfo.pointSize || styleInfo.fontSize || 13
      internalPoint.data.textThickness = point.textThickness || styleInfo.textThickness || styleInfo.fontWeight || 'normal'
      internalPoint.data.hasBorder = point.showDisplay === 1 || styleInfo.hasBorder === true
      internalPoint.data.backgroundColor = styleInfo.backgroundColor || 'rgba(255, 255, 255, 0)'
    } else if (point.pointSize) {
      // 如果是图形点位且有pointSize属性，设置为size
      internalPoint.data.size = point.pointSize
    }

    // 添加其他从styleInfo中解析的属性
    Object.keys(styleInfo).forEach((key) => {
      // 跳过已处理的基本属性
      if (
        !['color', 'size', 'pointSize', 'textThickness', 'fontSize', 'fontWeight', 'hasBorder', 'backgroundColor'].includes(key)
      ) {
        internalPoint.data[key] = styleInfo[key]
      }
    })

    return internalPoint
  })
}

/**
 * 创建默认的点位数据
 * @param {string} type 点位类型
 * @param {number} x X坐标
 * @param {number} y Y坐标
 * @param {boolean} isText 是否是文本点位
 * @returns {Object} 新的点位数据对象
 */
export function createDefaultPoint(type, x, y, isText = false) {
  const pointId = `point-${Date.now()}`

  console.log('创建默认点位 - 类型:', type, '是文本:', isText)

  // 确保类型正确设置
  if (isText) {
    type = 'text'
  }

  const defaultData = {
    size: isText ? 13 : 8,
    color: isText ? '#000000' : getPointColorByType(type),
    indicator: '',
    pointSize: 13
  }

  // 为文本点位添加默认样式
  if (isText) {
    defaultData.textThickness = 'normal'
    defaultData.hasBorder = false
    defaultData.hasBackground = false
    defaultData.backgroundColor = 'transparent'
    defaultData.borderColor = 'transparent'
    defaultData.borderWidth = 0
    defaultData.padding = 0
  }

  return {
    id: pointId,
    x,
    y,
    name: isText ? '文本标签' : `监测点${Math.floor(Math.random() * 1000)}`,
    type: type, // 保留原始type
    showType: type, // 显示类型
    data: defaultData,
    isText
  }
}

/**
 * 根据点位类型获取默认颜色
 * @param {string} type 点位类型
 * @returns {string} 颜色代码
 */
export function getPointColorByType(type) {
  const colors = {
    circle: '#F56C6C',
    square: '#E6A23C',
    triangle: '#409EFF',
    default: '#409EFF'
  }
  return colors[type] || colors.default
}
