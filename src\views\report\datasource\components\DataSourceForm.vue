<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="110px"
      v-loading="formLoading"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item label="数据源类型" prop="sourceType">
            <el-select
              v-model="formData.sourceType"
              placeholder="请选择数据源类型"
              @change="handleSourceTypeChange"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.SOURCE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="数据源编码" prop="sourceCode">
            <el-input v-model="formData.sourceCode" placeholder="请输入数据源编码" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="数据源名称" prop="sourceName">
            <el-input v-model="formData.sourceName" placeholder="请输入数据源名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="数据源状态" prop="enableFlag">
            <el-select v-model="formData.enableFlag" placeholder="请选择数据源状态">
              <el-option
                v-for="item in enableFlagOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="数据源描述" prop="sourceDesc">
        <el-input
          v-model="formData.sourceDesc"
          type="textarea"
          placeholder="请输入数据源描述"
          clearable
        />
      </el-form-item>

      <!-- http连接配置 -->
      <template v-if="formData.sourceType === 'http'">
        <el-divider content-position="left">http连接配置</el-divider>
        <el-form ref="httpFormRef" :model="httpConfig" :rules="httpFormRules" label-width="110px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="请求路径" prop="requestPath">
                <el-input v-model="httpConfig.requestPath" placeholder="请输入请求路径" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="请求方式" prop="requestMethod">
                <el-input
                  v-model="httpConfig.requestMethod"
                  placeholder="请输入请求方式"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="请求头" prop="requestHeaders">
                <el-input
                  v-model="httpConfig.requestHeaders"
                  placeholder="请输入请求头"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="请求体" prop="requestBody">
                <el-input v-model="httpConfig.requestBody" placeholder="请输入请求体" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>

      <!-- 其他数据源类型的配置 -->
      <template v-else>
        <el-divider content-position="left">SQL连接配置</el-divider>
        <el-form
          ref="otherFormRef"
          :model="otherConfig"
          :rules="otherFormRules"
          label-width="110px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="驱动类名" prop="driverName">
                <el-input v-model="otherConfig.driverName" placeholder="请输入驱动类名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据库URL" prop="jdbcUrl">
                <el-input v-model="otherConfig.jdbcUrl" placeholder="请输入数据库URL" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户名" prop="username">
                <el-input
                  v-model="otherConfig.username"
                  placeholder="请输入用户名"
                  clearable
                  autocomplete="off"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码" prop="password">
                <el-input
                  v-model="otherConfig.password"
                  placeholder="请输入密码"
                  type="password"
                  show-password
                  clearable
                  autocomplete="off"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </el-form>
    <template #footer>
      <el-button @click="testConnection" type="warning" :disabled="formLoading">测试连接</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DataSourceApi, DataSourceVO, TestDataSourceVO } from '@/api/report/datasource'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { generateUniqueNumericId } from '@/utils'

/** 数据源管理 表单 */
defineOptions({ name: 'DataSourceForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formRef = ref() // 表单 Ref
const httpFormRef = ref() // HTTP配置表单 Ref
const otherFormRef = ref() // 其他数据源配置表单 Ref
const uuid = ref(generateUniqueNumericId())

interface FormData {
  id: string | undefined
  sourceCode: string | undefined
  sourceName: string | undefined
  sourceDesc: string | undefined
  sourceType: string | undefined
  sourceConfig: string | undefined
  enableFlag: number | undefined
  version: number | undefined
}

const enableFlagOptions = ref([
  { label: '已启用', value: 1 },
  { label: '已禁用', value: 0 }
])

const formData = ref<FormData>({
  id: undefined,
  sourceCode: undefined,
  sourceName: undefined,
  sourceDesc: undefined,
  sourceType: undefined,
  sourceConfig: '',
  enableFlag: undefined,
  version: undefined
})

// http配置
const httpConfig = ref({
  requestPath: '',
  requestMethod: '',
  requestHeaders: '',
  requestBody: ''
})

// 其他数据源配置
const otherConfig = ref({
  driverName: '',
  jdbcUrl: '',
  username: '',
  password: ''
})

const formRules = reactive<Record<string, any>>({
  sourceType: [{ required: true, message: '请选择数据源类型', trigger: 'change' }],
  sourceCode: [{ required: true, message: '请输入数据源编码', trigger: 'blur' }],
  sourceName: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
  enableFlag: [{ required: true, message: '请选择数据源状态', trigger: 'change' }]
})

// http表单验证规则
const httpFormRules = reactive<Record<string, any>>({
  requestPath: [{ required: true, message: '请输入请求路径', trigger: 'blur' }],
  requestMethod: [{ required: true, message: '请输入请求方式', trigger: 'blur' }],
  requestHeaders: [{ required: true, message: '请输入请求头', trigger: 'blur' }],
  requestBody: [{ required: true, message: '请输入请求体', trigger: 'blur' }]
})

// 其他数据源表单验证规则
const otherFormRules = reactive<Record<string, any>>({
  driverName: [{ required: true, message: '请输入驱动类名', trigger: 'blur' }],
  jdbcUrl: [{ required: true, message: '请输入数据库URL', trigger: 'blur' }],
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
})

// 处理数据源类型变化
function handleSourceTypeChange(value: string) {
  if (value === 'http') {
    // 如果是http，初始化默认配置
    httpConfig.value = {
      requestPath: '',
      requestMethod: '',
      requestHeaders: '',
      requestBody: ''
    }
  } else {
    // 如果是其他类型，初始化默认配置
    otherConfig.value = {
      driverName: '',
      jdbcUrl: '',
      username: '',
      password: ''
    }
  }
}

/** 打开弹窗 */
async function open(type: string, id?: number) {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DataSourceApi.getDataSource(id)
      // 如果是http数据源，解析sourceConfig
      if (formData.value.sourceType === 'http' && formData.value.sourceConfig) {
        const config = JSON.parse(formData.value.sourceConfig)
        httpConfig.value = {
          requestPath: config.requestPath || '',
          requestMethod: config.requestMethod || '',
          requestHeaders: config.requestHeaders || '',
          requestBody: config.requestBody || ''
        }
      } else if (formData.value.sourceConfig) {
        const config = JSON.parse(formData.value.sourceConfig)
        otherConfig.value = {
          driverName: config.driverName || '',
          jdbcUrl: config.jdbcUrl || '',
          username: config.username || '',
          password: config.password || ''
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}

/** 测试连接 */
async function testConnection() {
  await formRef.value.validate()
  if (formData.value.sourceType === 'http') {
    await httpFormRef.value.validate()
  } else {
    await otherFormRef.value.validate()
  }
  formData.value.sourceConfig = JSON.stringify(
    formData.value.sourceType === 'http' ? httpConfig.value : otherConfig.value
  )
  const data: TestDataSourceVO = {
    sourceType: formData.value.sourceType || '',
    sourceConfig: formData.value.sourceConfig || ''
  }
  await DataSourceApi.testDataSource(data)
  const res = await DataSourceApi.testDataSource(data)
  if (res) {
    message.success('测试连接成功')
  } else {
    message.error('测试连接失败')
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
async function submitForm() {
  // 校验主表单
  await formRef.value.validate()
  // 根据数据源类型校验对应的配置表单
  if (formData.value.sourceType === 'http') {
    await httpFormRef.value.validate()
  } else {
    await otherFormRef.value.validate()
  }
  // 将配置拼接成JSON
  formData.value.sourceConfig = JSON.stringify(
    formData.value.sourceType === 'http' ? httpConfig.value : otherConfig.value
  )
  // 只在创建时生成新的 id
  if (formType.value === 'create') {
    formData.value.id = uuid.value
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DataSourceVO
    if (formType.value === 'create') {
      await DataSourceApi.createDataSource(data)
      message.success(t('common.createSuccess'))
    } else {
      await DataSourceApi.updateDataSource(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
function resetForm() {
  formData.value = {
    id: undefined,
    sourceCode: undefined,
    sourceName: undefined,
    sourceDesc: undefined,
    sourceType: undefined,
    sourceConfig: '',
    enableFlag: undefined,
    version: undefined
  }
  httpConfig.value = {
    requestPath: '',
    requestMethod: '',
    requestHeaders: '',
    requestBody: ''
  }
  otherConfig.value = {
    driverName: '',
    jdbcUrl: '',
    username: '',
    password: ''
  }
  formRef.value?.resetFields()
  httpFormRef.value?.resetFields()
  otherFormRef.value?.resetFields()
}

defineExpose({ open, resetForm }) // 提供 open 和 resetForm 方法
</script>
