<template>
  <el-dialog
    v-model="dialogVisible"
    title="设备详情"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-tabs v-model="activeTab" class="device-detail-tabs">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备编号">{{ deviceInfo.deviceCode }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">
            <el-tag v-if="deviceInfo.deviceType === '流量计'" type="success">{{ deviceInfo.deviceType }}</el-tag>
            <el-tag v-else-if="deviceInfo.deviceType === '压力表'" type="info">{{ deviceInfo.deviceType }}</el-tag>
            <el-tag v-else-if="deviceInfo.deviceType === '防雷设备'" type="warning">{{ deviceInfo.deviceType }}</el-tag>
            <el-tag v-else-if="deviceInfo.deviceType === '起重设备'" type="danger">{{ deviceInfo.deviceType }}</el-tag>
            <el-tag v-else>{{ deviceInfo.deviceType }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ deviceInfo.deviceName }}</el-descriptions-item>
          <el-descriptions-item label="规格型号">{{ deviceInfo.specifications }}</el-descriptions-item>
          <el-descriptions-item label="安装位置" :span="2">{{ deviceInfo.location }}</el-descriptions-item>
          <el-descriptions-item label="制造商">{{ deviceInfo.manufacturer }}</el-descriptions-item>
          <el-descriptions-item label="责任人">{{ deviceInfo.responsiblePerson }}</el-descriptions-item>
          <el-descriptions-item label="安装日期">{{ deviceInfo.installationDate }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="deviceInfo.status === 'normal'" type="success">正常</el-tag>
            <el-tag v-else-if="deviceInfo.status === 'comingDue'" type="warning">即将到期</el-tag>
            <el-tag v-else-if="deviceInfo.status === 'expired'" type="danger">已过期</el-tag>
            <el-tag v-else-if="deviceInfo.status === 'maintenance'" type="info">维护中</el-tag>
            <el-tag v-else>{{ deviceInfo.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            <div class="whitespace-pre-wrap">{{ deviceInfo.remarks || '-' }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 设备特有属性 -->
        <div v-if="deviceSpecificInfo.length > 0" class="mt-20px">
          <h3 class="font-bold text-base mb-10px">{{ deviceInfo.deviceType }}特有属性</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item v-for="(item, index) in deviceSpecificInfo" :key="index" :label="item.label">
              {{ item.value || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-tab-pane>

      <!-- 检测记录 -->
      <el-tab-pane label="检测记录" name="inspections">
        <el-table :data="inspectionRecords" border style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="inspectionDate" label="检测日期" min-width="120" align="center" />
          <el-table-column prop="inspector" label="检测人员" min-width="100" align="center" />
          <el-table-column prop="result" label="检测结果" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.result === 'pass'" type="success">合格</el-tag>
              <el-tag v-else-if="row.result === 'fail'" type="danger">不合格</el-tag>
              <el-tag v-else>{{ row.result }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="nextInspectionDate" label="下次检测日期" min-width="120" align="center" />
          <el-table-column prop="remarks" label="备注" min-width="200" align="center" show-overflow-tooltip />
        </el-table>
        <div v-if="inspectionRecords.length === 0" class="text-center py-50px text-gray-400">
          <el-empty description="暂无检测记录" />
        </div>
      </el-tab-pane>

      <!-- 维修记录 -->
      <el-tab-pane label="维修记录" name="maintenance">
        <el-table :data="maintenanceRecords" border style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="maintenanceDate" label="维修日期" min-width="120" align="center" />
          <el-table-column prop="maintenanceType" label="维修类型" min-width="100" align="center" />
          <el-table-column prop="maintenanceStaff" label="维修人员" min-width="100" align="center" />
          <el-table-column prop="faultDescription" label="故障描述" min-width="200" align="center" show-overflow-tooltip />
          <el-table-column prop="maintenanceResult" label="维修结果" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.maintenanceResult === 'success'" type="success">修复成功</el-tag>
              <el-tag v-else-if="row.maintenanceResult === 'partial'" type="warning">部分修复</el-tag>
              <el-tag v-else-if="row.maintenanceResult === 'failed'" type="danger">修复失败</el-tag>
              <el-tag v-else>{{ row.maintenanceResult }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="cost" label="维修费用" min-width="100" align="center" />
        </el-table>
        <div v-if="maintenanceRecords.length === 0" class="text-center py-50px text-gray-400">
          <el-empty description="暂无维修记录" />
        </div>
      </el-tab-pane>

      <!-- 附件 -->
      <el-tab-pane label="附件资料" name="attachments">
        <el-table :data="attachments" border style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="fileName" label="文件名称" min-width="200" align="center" show-overflow-tooltip />
          <el-table-column prop="fileType" label="文件类型" min-width="100" align="center" />
          <el-table-column prop="fileSize" label="文件大小" min-width="100" align="center" />
          <el-table-column prop="uploadTime" label="上传时间" min-width="150" align="center" />
          <el-table-column prop="uploader" label="上传人" min-width="100" align="center" />
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleDownload(row)">下载</el-button>
              <el-button link type="primary" @click="handlePreview(row)">预览</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="attachments.length === 0" class="text-center py-50px text-gray-400">
          <el-empty description="暂无附件资料" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="handlePrint">打印</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 对话框状态
const dialogVisible = ref(false)
const activeTab = ref('basic')

// 设备信息
const deviceInfo = ref({
  id: 0,
  deviceCode: '',
  deviceName: '',
  deviceType: '',
  specifications: '',
  location: '',
  inspectionCycle: '',
  lastInspectionDate: '',
  nextInspectionDate: '',
  status: '',
  installationDate: '',
  manufacturer: '',
  responsiblePerson: '',
  remarks: '',
  // 特有属性
  diameter: '',
  range: '',
  accuracyClass: '',
  pressureRange: '',
  volume: '',
  designPressure: '',
  vesselCategory: '',
  medium: ''
})

// 设备特有属性
const deviceSpecificInfo = ref([])

// 检测记录
const inspectionRecords = ref([
  // 示例数据，实际应从后端获取
  {
    id: 1,
    inspectionDate: '2024-01-15',
    inspector: '张三',
    result: 'pass',
    nextInspectionDate: '2024-07-15',
    remarks: '设备运行正常，各项指标达标'
  },
  {
    id: 2,
    inspectionDate: '2023-07-10',
    inspector: '李四',
    result: 'pass',
    nextInspectionDate: '2024-01-10',
    remarks: '建议更换密封圈'
  }
])

// 维修记录
const maintenanceRecords = ref([
  // 示例数据，实际应从后端获取
  {
    id: 1,
    maintenanceDate: '2023-08-15',
    maintenanceType: '常规维护',
    maintenanceStaff: '王五',
    faultDescription: '密封圈老化',
    maintenanceResult: 'success',
    cost: '¥500'
  }
])

// 附件列表
const attachments = ref([
  // 示例数据，实际应从后端获取
  {
    id: 1,
    fileName: '设备出厂合格证.pdf',
    fileType: 'PDF',
    fileSize: '2.5MB',
    uploadTime: '2023-05-12 14:30:45',
    uploader: '张三',
    filePath: '/uploads/certificates/123456.pdf'
  },
  {
    id: 2,
    fileName: '设备说明书.pdf',
    fileType: 'PDF',
    fileSize: '5.8MB',
    uploadTime: '2023-05-12 14:35:22',
    uploader: '张三',
    filePath: '/uploads/manuals/123456.pdf'
  }
])

// 打开对话框
const open = (row: any) => {
  if (!row || !row.id) {
    ElMessage.error('数据错误')
    return
  }

  dialogVisible.value = true
  activeTab.value = 'basic'

  // 设置设备信息
  Object.assign(deviceInfo.value, row)

  // 根据设备类型设置特有属性
  setSpecificInfo()

  // 获取检测记录、维修记录和附件（实际项目中应该从后端获取）
  // getInspectionRecords(row.id)
  // getMaintenanceRecords(row.id)
  // getAttachments(row.id)
}

// 设置设备特有属性
const setSpecificInfo = () => {
  deviceSpecificInfo.value = []

  switch (deviceInfo.value.deviceType) {
    case '流量计':
      deviceSpecificInfo.value = [
        { label: '口径', value: deviceInfo.value.diameter },
        { label: '量程', value: deviceInfo.value.range }
      ]
      break
    case '压力表':
      deviceSpecificInfo.value = [
        { label: '精度等级', value: deviceInfo.value.accuracyClass },
        { label: '量程范围', value: deviceInfo.value.pressureRange }
      ]
      break
    case '压力容器':
      deviceSpecificInfo.value = [
        { label: '容积', value: deviceInfo.value.volume },
        { label: '设计压力', value: deviceInfo.value.designPressure },
        { label: '容器类别', value: deviceInfo.value.vesselCategory },
        { label: '介质', value: deviceInfo.value.medium }
      ]
      break
    default:
      break
  }
}

// 下载附件
const handleDownload = (row) => {
  ElMessage.success(`正在下载: ${row.fileName}`)
  // 实际下载逻辑
  // window.open(row.filePath)
}

// 预览附件
const handlePreview = (row) => {
  ElMessage.success(`正在预览: ${row.fileName}`)
  // 实际预览逻辑
}

// 打印设备信息
const handlePrint = () => {
  ElMessage.success('打印功能开发中')
  // 实际打印实现
  // window.print()
}

// 暴露给父组件的方法
defineExpose({ open })
</script>

<style scoped>
:deep(.el-descriptions__label) {
  font-weight: 500;
  background-color: var(--el-fill-color-light);
}

:deep(.device-detail-tabs .el-tabs__header) {
  margin-bottom: 15px;
}
</style> 