<template>
  <div class="equipment-analysis">
    <div class="filter-section">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filterForm.department" placeholder="请选择部门">
            <el-option
              v-for="item in departments"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="data-section">
      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <span>设备设施概览</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="设备总数">{{ overview.totalCount }}</el-descriptions-item>
          <el-descriptions-item label="正常运行">{{ overview.normalCount }}</el-descriptions-item>
          <el-descriptions-item label="待检修">{{ overview.repairCount }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="code" label="设备编号" width="120" />
        <el-table-column prop="name" label="设备名称" width="150" />
        <el-table-column prop="department" label="所属部门" width="120" />
        <el-table-column prop="type" label="设备类型" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '正常' ? 'success' : scope.row.status === '待检修' ? 'warning' : 'danger'"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastInspection" label="上次检查时间" width="150" />
        <el-table-column prop="nextInspection" label="下次检查时间" width="150" />
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <DetailDialog
      v-model:visible="detailDialogVisible"
      :type="'equipment'"
      :detail-data="currentDetailData || {}"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import DetailDialog from '../dialogs/DetailDialog.vue'

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  department: ''
})

// 部门列表
const departments = [
  { label: '生产部', value: 'production' },
  { label: '安全部', value: 'safety' },
  { label: '设备部', value: 'equipment' },
  { label: '质量部', value: 'quality' }
]

// 设备概览数据
const overview = reactive({
  totalCount: 856,
  normalCount: 780,
  repairCount: 76
})

// 表格数据
const tableData = ref([
  {
    code: 'EQ001',
    name: '液压机',
    department: '生产部',
    type: '机械设备',
    status: '正常',
    lastInspection: '2023-09-15',
    nextInspection: '2023-12-15'
  },
  {
    code: 'EQ002',
    name: '空压机',
    department: '设备部',
    type: '动力设备',
    status: '待检修',
    lastInspection: '2023-08-20',
    nextInspection: '2023-11-20'
  },
  {
    code: 'EQ003',
    name: '叉车',
    department: '物流部',
    type: '运输设备',
    status: '正常',
    lastInspection: '2023-10-01',
    nextInspection: '2024-01-01'
  },
  {
    code: 'EQ004',
    name: '电焊机',
    department: '生产部',
    type: '电气设备',
    status: '正常',
    lastInspection: '2023-09-25',
    nextInspection: '2023-12-25'
  },
  {
    code: 'EQ005',
    name: '切割机',
    department: '生产部',
    type: '机械设备',
    status: '故障',
    lastInspection: '2023-10-10',
    nextInspection: '2024-01-10'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 弹窗控制
const detailDialogVisible = ref(false)
const currentDetailData = ref(null)

// 查询方法
const handleSearch = () => {
  // TODO: 实现查询逻辑
}

// 重置方法
const handleReset = () => {
  filterForm.dateRange = []
  filterForm.department = ''
}

// 分页方法
const handleSizeChange = (size: number) => {
  pageSize.value = size
  // TODO: 重新加载数据
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  // TODO: 重新加载数据
}

// 详情方法
const handleDetail = (row: any) => {
  currentDetailData.value = row
  detailDialogVisible.value = true
}

const handleDetailClose = () => {
  detailDialogVisible.value = false
  currentDetailData.value = null
}
</script>

<style lang="scss" scoped>
.equipment-analysis {
  padding: 20px;

  .filter-section {
    margin-bottom: 20px;
  }

  .data-section {
    .mb-20 {
      margin-bottom: 20px;
    }
  }

  .pagination-section {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 