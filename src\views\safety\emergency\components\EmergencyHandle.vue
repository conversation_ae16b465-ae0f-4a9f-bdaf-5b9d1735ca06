<template>
  <div class="emergency-handle">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="事件名称">
          <el-input v-model="searchForm.name" placeholder="请输入事件名称" />
        </el-form-item>
        <el-form-item label="事件类型">
          <el-select v-model="searchForm.type" placeholder="请选择事件类型">
            <el-option label="火灾事故" value="fire" />
            <el-option label="设备故障" value="equipment" />
            <el-option label="人员伤害" value="injury" />
            <el-option label="自然灾害" value="disaster" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="待处理" value="pending" />
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item label="发生时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleAdd">新增事件</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="name" label="事件名称" />
      <el-table-column prop="type" label="事件类型">
        <template #default="scope">
          {{ getEventType(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="level" label="事件等级">
        <template #default="scope">
          <el-tag :type="getLevelType(scope.row.level)">
            {{ getLevelText(scope.row.level) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="location" label="发生地点" />
      <el-table-column prop="occurTime" label="发生时间" />
      <el-table-column prop="status" label="处理状态">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280">
        <template #default="scope">
          <el-button 
            type="primary" 
            link 
            @click="handleEdit(scope.row)"
            v-if="scope.row.status === 'pending'"
          >编辑</el-button>
          <el-button 
            type="success" 
            link 
            @click="handleProcess(scope.row)"
            v-if="scope.row.status !== 'completed'"
          >处理</el-button>
          <el-button 
            type="warning" 
            link 
            @click="handlePlan(scope.row)"
          >关联预案</el-button>
          <el-button 
            type="info" 
            link 
            @click="handleView(scope.row)"
          >查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 弹窗 -->
    <emergency-dialog
      v-model="dialogVisible"
      :dialog-type="dialogType"
      :emergency-data="currentEmergency"
      @submit="handleDialogSubmit"
    />
  </div>
</template>

<script>
import EmergencyDialog from '../dialogs/EmergencyDialog.vue'

export default {
  name: 'EmergencyHandle',
  components: {
    EmergencyDialog
  },
  data() {
    return {
      searchForm: {
        name: '',
        type: '',
        status: '',
        dateRange: []
      },
      tableData: [
        {
          id: 1,
          name: '实验室化学品泄露事故',
          type: 'equipment',
          level: 'medium',
          location: '研发中心实验室',
          occurTime: '2024-03-15 09:30:00',
          status: 'processing',
          description: '实验过程中发生化学品泄露，已进行初步处置',
          solution: '1. 隔离泄露区域\n2. 穿戴防护装备\n3. 使用专业设备处理泄露物',
          measures: '已启动化学品泄露应急预案，正在进行现场处置',
          linkedPlans: [2]
        },
        {
          id: 2,
          name: '配电房设备故障',
          type: 'equipment',
          level: 'high',
          location: '总部大楼配电房',
          occurTime: '2024-03-14 16:45:00',
          status: 'completed',
          description: '配电房主变压器发生故障，导致部分区域断电',
          solution: '1. 切换备用电源\n2. 检修故障设备\n3. 恢复供电',
          measures: '已完成设备维修，供电恢复正常',
          result: '故障已排除，设备运行正常'
        },
        {
          id: 3,
          name: '工厂车间小型火情',
          type: 'fire',
          level: 'low',
          location: '生产车间A区',
          occurTime: '2024-03-16 11:20:00',
          status: 'pending',
          description: '车间设备过热导致局部冒烟',
          linkedPlans: [1]
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3,
      dialogVisible: false,
      dialogType: 'add',
      currentEmergency: {}
    }
  },
  methods: {
    getEventType(type) {
      const types = {
        fire: '火灾事故',
        equipment: '设备故障',
        injury: '人员伤害',
        disaster: '自然灾害'
      }
      return types[type] || type
    },
    getLevelType(level) {
      const types = {
        low: 'info',
        medium: 'warning',
        high: 'danger'
      }
      return types[level] || 'info'
    },
    getLevelText(level) {
      const texts = {
        low: '轻微',
        medium: '一般',
        high: '严重'
      }
      return texts[level] || level
    },
    getStatusType(status) {
      const types = {
        pending: 'info',
        processing: 'warning',
        completed: 'success'
      }
      return types[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成'
      }
      return texts[status] || status
    },
    handleSearch() {
      // 实现搜索逻辑
    },
    resetSearch() {
      this.searchForm = {
        name: '',
        type: '',
        status: '',
        dateRange: []
      }
    },
    handleAdd() {
      this.dialogType = 'add'
      this.currentEmergency = {}
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentEmergency = { ...row }
      this.dialogVisible = true
    },
    handleProcess(row) {
      this.dialogType = 'process'
      this.currentEmergency = { ...row }
      this.dialogVisible = true
    },
    handlePlan(row) {
      this.dialogType = 'plan'
      this.currentEmergency = { ...row }
      this.dialogVisible = true
    },
    handleView(row) {
      this.dialogType = 'view'
      this.currentEmergency = { ...row }
      this.dialogVisible = true
    },
    handleDialogSubmit(formData) {
      // 处理弹窗提交
      console.log('提交数据：', formData)
      if (this.dialogType === 'add') {
        this.$message.success('新增成功')
      } else if (this.dialogType === 'edit') {
        this.$message.success('修改成功')
      } else if (this.dialogType === 'process') {
        this.$message.success('处理方案已更新')
      } else if (this.dialogType === 'plan') {
        this.$message.success('关联预案已更新')
      }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    fetchData() {
      // 获取表格数据
    }
  }
}
</script>

<style lang="scss" scoped>
.emergency-handle {
  .search-area {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 