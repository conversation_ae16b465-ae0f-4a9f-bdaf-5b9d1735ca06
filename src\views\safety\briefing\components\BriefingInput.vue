<template>
  <div class="briefing-input-container">
    <div class="header-actions">
      <el-button type="primary" @click="submitBriefing" :loading="submitLoading">提交审核</el-button>
      <el-button type="success" @click="autoFillData">自动获取填充</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
    
    <el-form
      ref="formRef"
      :model="briefingForm"
      :rules="rules"
      label-width="120px"
      class="briefing-form"
    >
      <el-form-item label="简报标题" prop="title">
        <el-input v-model="briefingForm.title" placeholder="请输入简报标题" />
      </el-form-item>
      
      <el-form-item label="报告日期" prop="reportDate">
        <el-date-picker
          v-model="briefingForm.reportDate"
          type="date"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      
      <el-form-item label="报告人" prop="reporter">
        <el-input v-model="briefingForm.reporter" placeholder="请输入报告人" />
      </el-form-item>
      
      <el-form-item label="所属部门" prop="department">
        <el-select v-model="briefingForm.department" placeholder="请选择部门">
          <el-option
            v-for="item in departmentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-divider content-position="center">工作概况</el-divider>
      
      <el-form-item label="安全检查情况" prop="safetyInspection">
        <el-input
          v-model="briefingForm.safetyInspection"
          type="textarea"
          :rows="3"
          placeholder="请输入安全检查情况"
        />
      </el-form-item>
      
      <el-form-item label="隐患整改情况" prop="hazardRectification">
        <el-input
          v-model="briefingForm.hazardRectification"
          type="textarea"
          :rows="3"
          placeholder="请输入隐患整改情况"
        />
      </el-form-item>
      
      <el-form-item label="安全培训情况" prop="safetyTraining">
        <el-input
          v-model="briefingForm.safetyTraining"
          type="textarea"
          :rows="3"
          placeholder="请输入安全培训情况"
        />
      </el-form-item>
      
      <el-form-item label="事故分析情况" prop="accidentAnalysis">
        <el-input
          v-model="briefingForm.accidentAnalysis"
          type="textarea"
          :rows="3"
          placeholder="请输入事故分析情况"
        />
      </el-form-item>
      
      <el-divider content-position="center">工作计划</el-divider>
      
      <el-form-item label="下期工作计划" prop="workPlan">
        <el-input
          v-model="briefingForm.workPlan"
          type="textarea"
          :rows="3"
          placeholder="请输入下期工作计划"
        />
      </el-form-item>
      
      <el-form-item label="注意事项" prop="notes">
        <el-input
          v-model="briefingForm.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入注意事项"
        />
      </el-form-item>
      
      <el-form-item label="附件" prop="attachments">
        <el-upload
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          multiple
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持上传任意格式文件，单个文件不超过10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules, UploadUserFile } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'

const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const fileList = ref<UploadUserFile[]>([])

// 部门选项
const departmentOptions = [
  { label: '安全管理部', value: 'safety' },
  { label: '生产部', value: 'production' },
  { label: '工程部', value: 'engineering' },
  { label: '质量部', value: 'quality' }
]

// 表单数据
const briefingForm = reactive({
  title: '',
  reportDate: '',
  reporter: '',
  department: '',
  safetyInspection: '',
  hazardRectification: '',
  safetyTraining: '',
  accidentAnalysis: '',
  workPlan: '',
  notes: '',
  attachments: []
})

// 表单验证规则
const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入简报标题', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  reportDate: [
    { required: true, message: '请选择报告日期', trigger: 'change' }
  ],
  reporter: [
    { required: true, message: '请输入报告人', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  safetyInspection: [
    { required: true, message: '请输入安全检查情况', trigger: 'blur' }
  ],
  hazardRectification: [
    { required: true, message: '请输入隐患整改情况', trigger: 'blur' }
  ]
})

// 处理文件变更
const handleFileChange = (uploadFile: UploadUserFile) => {
  fileList.value.push(uploadFile)
}

// 自动填充数据
const autoFillData = () => {
  ElMessageBox.confirm(
    '确定要自动获取并填充数据吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟获取数据
    setTimeout(() => {
      briefingForm.title = `安全生产工作简报(${new Date().toLocaleDateString()})`
      briefingForm.reportDate = new Date().toISOString().split('T')[0]
      briefingForm.reporter = '张安全'
      briefingForm.department = 'safety'
      briefingForm.safetyInspection = '本周共开展安全检查4次，发现安全隐患6项，已整改4项，剩余2项正在整改中。'
      briefingForm.hazardRectification = '上周遗留隐患已全部整改完毕，本周新增隐患主要集中在设备老化和操作流程不规范方面。'
      briefingForm.safetyTraining = '本周组织了一次全厂安全知识培训，参与人数85人，培训内容包括消防安全和应急预案演练。'
      briefingForm.accidentAnalysis = '近期无安全事故发生，同比去年同期减少了2起轻微事故，安全生产态势良好。'
      briefingForm.workPlan = '计划下周对重点区域进行专项检查，并组织一次安全生产演练活动。'
      
      ElMessage.success('数据已自动填充')
    }, 1000)
  }).catch(() => {
    // 取消自动填充
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
    fileList.value = []
  }
}

// 提交简报
const submitBriefing = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true
      
      // 模拟提交请求
      setTimeout(() => {
        submitLoading.value = false
        ElMessage.success('简报提交成功，等待审核')
        resetForm()
      }, 1500)
    } else {
      ElMessage.error('表单填写有误，请检查')
    }
  })
}
</script>

<style lang="scss" scoped>
.briefing-input-container {
  padding: 20px 0;
  
  .header-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }
  
  .briefing-form {
    max-width: 900px;
  }
}
</style> 