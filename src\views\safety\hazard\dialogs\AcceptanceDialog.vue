<template>
  <el-dialog
    title="验收确认"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="acceptance-form"
    >
      <el-form-item label="验收人" prop="inspector">
        <el-input v-model="formData.inspector" placeholder="请输入验收人姓名" />
      </el-form-item>

      <el-form-item label="验收结果" prop="result">
        <el-radio-group v-model="formData.result">
          <el-radio label="pass">通过</el-radio>
          <el-radio label="fail">不通过</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="验收意见" prop="remarks">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入验收意见"
        />
      </el-form-item>

      <el-form-item label="现场照片" prop="photos">
        <el-upload
          class="upload-demo"
          action="#"
          list-type="picture-card"
          :auto-upload="false"
          :on-change="handlePhotoChange"
          :file-list="formData.photos"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { UploadFile } from '../types'

interface AcceptanceFormData {
  inspector: string
  result: '' | 'pass' | 'fail'
  remarks: string
  photos: UploadFile[]
}

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive<AcceptanceFormData>({
  inspector: '',
  result: '',
  remarks: '',
  photos: []
})

const rules: FormRules = {
  inspector: [{ required: true, message: '请输入验收人姓名', trigger: 'blur' }],
  result: [{ required: true, message: '请选择验收结果', trigger: 'change' }],
  remarks: [{ required: true, message: '请输入验收意见', trigger: 'blur' }]
}

const handlePhotoChange = (file: UploadFile, fileList: UploadFile[]) => {
  formData.photos = fileList
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      // TODO: 实现提交逻辑
      ElMessage.success('验收确认提交成功')
      dialogVisible.value = false
      emit('success', {
        ...formData,
        status: formData.result === 'pass' ? '验收通过' : '验收不通过',
        confirmationTime: new Date().toISOString()
      })
    }
  })
}

const handleClosed = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  Object.assign(formData, {
    inspector: '',
    result: '',
    remarks: '',
    photos: []
  })
}

// 暴露方法给父组件
defineExpose({
  open: () => {
    dialogVisible.value = true
  }
})
</script>

<style lang="scss" scoped>
.acceptance-form {
  .el-upload--picture-card {
    --el-upload-picture-card-size: 100px;
  }
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style> 