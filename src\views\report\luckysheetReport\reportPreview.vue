<template>
  <div class="report-page" v-loading="loading" element-loading-text="报表预览数据加载中，请稍候...">
    <div class="test-panel">
      <el-button type="primary" @click="handleTest">测试保护区域</el-button>
    </div>
    <LuckySheetContainer ref="luckysheetRef" />
  </div>
</template>
<script lang="ts" setup>
import LuckySheetContainer from './components/luckysheet.vue'
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import emitter from '@/utils/eventBus'
import { useRoute } from 'vue-router'
import { ReportApi } from '@/api/report/reportInfo/index'
import { validate } from 'uuid'
import { setSheetProtection } from '@/utils/luckysheet'
import { ElMessage } from 'element-plus'

const route = useRoute()
const luckysheetRef = ref()
const reportCode = ref()
const loading = ref(false)
const option = ref({
  container: 'luckysheet',
  lang: 'zh',
  showinfobar: true,
  editMode: false,
  showtoolbar: false,
})

// 测试保护区域
const handleTest = () => {
  if (!luckysheetRef.value) {
    console.error('Luckysheet未初始化')
    return
  }

  // 获取luckysheet实例
  const luckysheet = luckysheetRef.value.getSheet()
  if (!luckysheet) {
    console.error('获取luckysheet实例失败')
    return
  }

  // 设置保护区域
  setSheetProtection(0, [
    {
      name: '测试保护区域',
      password: '123456',
      hintText: '请输入密码查看',
      sqref: '$A$1:$B$10'
    }
  ], {
    selectLockedCells: 0,
    formatCells: 0
  }, () => {
    console.log('保护区域设置完成')
  })
}

const handlePreviewData = async () => {
  // 设置loading状态为true
  loading.value = true
  
  try {
    // 获取预览数据
    const res = (await ReportApi.getReportPreviewByCode(reportCode.value, 2)) as any
    if (res.code === 0 && res.data != null) {
      let data = res.data
      console.log('预览数据:', data)
      option.value = {
        ...option.value,
        title: data.reportName,
        gridKey: data.excelId,
        data: data.sheetConfigList
      }
  //     option.value.data[0].config.authority = {//当前工作表的权限配置
  //   selectLockedCells:1, //选定锁定单元格
  //   selectunLockedCells:1, //选定解除锁定的单元格
  //   formatCells:1, //设置单元格格式
  //   formatColumns:1, //设置列格式
  //   formatRows:1, //设置行格式
  //   insertColumns:1, //插入列
  //   insertRows:1, //插入行
  //   insertHyperlinks:1, //插入超链接
  //   deleteColumns:1, //删除列
  //   deleteRows:1, //删除行
  //   sort:1, //排序
  //   filter:1, //使用自动筛选
  //   usePivotTablereports:1, //使用数据透视表和报表
  //   editObjects:1, //编辑对象
  //   editScenarios:1, //编辑方案    
  //   sheet:1, //如果为1或true，则该工作表受到保护；如果为0或false，则该工作表不受保护。
  //   hintText:"", //弹窗提示的文字
  //   algorithmName:"None",//加密方案：MD2,MD4,MD5,RIPEMD-128,RIPEMD-160,SHA-1,SHA-256,SHA-384,SHA-512,WHIRLPOOL
  //   saltValue:null, //密码解密的盐参数，为一个自己定的随机数值
  //   allowRangeList:[{ //区域保护
  //       name:"area", //名称
  //       password:"1", //密码
  //       hintText:"", //提示文字
  //       algorithmName:"None",//加密方案：MD2,MD4,MD5,RIPEMD-128,RIPEMD-160,SHA-1,SHA-256,SHA-384,SHA-512,WHIRLPOOL
  //       saltValue:null, //密码解密的盐参数，为一个自己定的随机数值
  //       sqref:"$C$1:$D$5" //区域范围}],},
  //   }
  // ]};
      console.log('option.value:', option.value)
    } else {
      console.error('获取预览数据失败:', res)
      ElMessage.error('获取报表预览数据失败：' + (res.msg || '未知错误'))
    }
    
    // 等待组件准备好
    nextTick(() => {
      if (luckysheetRef.value) {
        console.log('初始化luckysheet，参数:', option.value)
        luckysheetRef.value.initLuckysheet(option.value)
      } else {
        console.error('luckysheetRef is not ready')
        ElMessage.error('报表初始化失败，请刷新页面重试')
      }
      // 完成加载，设置loading为false
      loading.value = false
    })
  } catch (error) {
    console.error('加载预览数据失败:', error)
    ElMessage.error('加载报表预览数据失败，请稍后重试')
    loading.value = false
  }
}

onMounted(() => {
  // 获取路由参数 -- 报表编码
  reportCode.value = route.query.reportCode
  handlePreviewData()
  // 监听预览数据事件
  // emitter.on('preview-data', handlePreviewData)
})

onUnmounted(() => {
  // 组件卸载时移除事件监听
  emitter.off('preview-data', handlePreviewData)
})
</script>
<style scoped lang="scss">
.report-page {
  width: 100%;
  height: calc(100vh - 170px);
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .test-panel {
    padding: 10px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }
}
</style>
