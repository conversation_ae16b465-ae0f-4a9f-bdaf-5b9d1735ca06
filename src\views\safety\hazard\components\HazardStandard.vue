<template>
  <div class="hazard-standard">
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">新增标准</el-button>
      <el-button type="success" @click="handleImport">导入</el-button>
      <el-button type="warning" @click="handleExport">导出</el-button>
      <el-input
        v-model="searchKeyword"
        placeholder="请输入关键字搜索"
        style="width: 200px; margin-left: 16px"
        clearable
      />
    </div>
    
    <el-table
      v-loading="loading"
      :data="standardList"
      border
      style="width: 100%"
    >
      <el-table-column prop="code" label="标准编号" width="120" />
      <el-table-column prop="name" label="标准名称" min-width="200" />
      <el-table-column prop="categoryId" label="所属分类" width="150">
        <template #default="{ row }">
          {{ getCategoryName(row.categoryId) }}
        </template>
      </el-table-column>
      <el-table-column prop="version" label="版本" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '生效' : '失效' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button link type="primary" @click="handleView(row)">查看</el-button>
          <el-button 
            link 
            :type="row.status === 'active' ? 'danger' : 'success'"
            @click="handleToggleStatus(row)"
          >
            {{ row.status === 'active' ? '停用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <standard-dialog
      v-model="dialogVisible"
      :type="dialogType"
      :data="currentStandard"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { StandardData } from '../types'
import StandardDialog from '../dialogs/StandardDialog.vue'

const loading = ref(false)
const standardList = ref<StandardData[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit' | 'view'>('add')
const currentStandard = ref<StandardData | null>(null)
const searchKeyword = ref('')

// 获取分类名称
const getCategoryName = (categoryId: number) => {
  // TODO: 实现分类名称获取逻辑
  return `分类${categoryId}`
}

// 获取标准列表
const getStandardList = async () => {
  loading.value = true
  try {
    // TODO: 实现获取标准列表的接口调用
    // const { data } = await api.getStandardList({
    //   page: currentPage.value,
    //   pageSize: pageSize.value
    // })
    // standardList.value = data.list
    // total.value = data.total
  } catch (error) {
    console.error(error)
    ElMessage.error('获取标准列表失败')
  } finally {
    loading.value = false
  }
}

// 新增标准
const handleAdd = () => {
  dialogType.value = 'add'
  currentStandard.value = null
  dialogVisible.value = true
}

// 编辑标准
const handleEdit = (row: StandardData) => {
  dialogType.value = 'edit'
  currentStandard.value = { ...row }
  dialogVisible.value = true
}

// 查看标准
const handleView = (row: StandardData) => {
  dialogType.value = 'view'
  currentStandard.value = { ...row }
  dialogVisible.value = true
}

// 切换标准状态
const handleToggleStatus = async (row: StandardData) => {
  try {
    await ElMessageBox.confirm(
      `确定要${row.status === 'active' ? '停用' : '启用'}该标准吗？`,
      '提示',
      {
        type: 'warning'
      }
    )
    // TODO: 实现切换状态的接口调用
    // await api.toggleStandardStatus(row.id)
    ElMessage.success('操作成功')
    getStandardList()
  } catch (error) {
    console.error(error)
  }
}

// 导入标准
const handleImport = () => {
  // TODO: 实现导入功能
}

// 导出标准
const handleExport = () => {
  // TODO: 实现导出功能
}

// 操作成功回调
const handleSuccess = () => {
  dialogVisible.value = false
  getStandardList()
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  getStandardList()
}

// 当前页改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getStandardList()
}

onMounted(() => {
  getStandardList()
})
</script>

<style lang="scss" scoped>
.hazard-standard {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .operation-bar {
    margin-bottom: 16px;
  }
  
  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 