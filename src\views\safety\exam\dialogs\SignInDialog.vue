<template>
  <el-dialog
    v-model="visible"
    title="培训签到"
    width="500px"
    destroy-on-close
    @closed="handleClose"
  >
    <div class="sign-in-content">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="扫码签到" name="qrcode">
          <div class="qrcode-container">
            <div class="qrcode-wrapper" v-loading="qrcodeLoading">
              <img v-if="qrcodeUrl" :src="qrcodeUrl" alt="签到二维码" />
            </div>
            <p class="tip">请使用手机扫描二维码进行签到</p>
          </div>
        </el-tab-pane>
        <el-tab-pane label="手写签名" name="signature">
          <div class="signature-container">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="form.name" placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="部门" prop="department">
                <el-select v-model="form.department" placeholder="请选择部门" style="width: 100%">
                  <el-option
                    v-for="item in departmentOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="签名" prop="signature">
                <div class="signature-pad-container">
                  <div class="signature-pad" ref="signaturePad"></div>
                  <el-button type="primary" link @click="handleClear">清除签名</el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="activeTab === 'signature'">确认签到</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'SignInDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    planId: {
      type: [String, Number],
      required: true
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      activeTab: 'qrcode',
      qrcodeLoading: false,
      qrcodeUrl: '',
      form: {
        name: '',
        department: '',
        signature: ''
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        department: [{ required: true, message: '请选择部门', trigger: 'change' }],
        signature: [{ required: true, message: '请签名', trigger: 'change' }]
      },
      departmentOptions: [
        { label: '生产部', value: 'production' },
        { label: '安全部', value: 'safety' },
        { label: '质量部', value: 'quality' }
      ],
      signaturePad: null
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.initQRCode()
        this.$nextTick(() => {
          if (this.activeTab === 'signature') {
            this.initSignaturePad()
          }
        })
      }
    },
    activeTab(val) {
      if (val === 'signature') {
        this.$nextTick(() => {
          this.initSignaturePad()
        })
      }
    }
  },
  methods: {
    initQRCode() {
      this.qrcodeLoading = true
      // TODO: 调用API获取签到二维码
      setTimeout(() => {
        this.qrcodeUrl = 'https://example.com/qrcode.png'
        this.qrcodeLoading = false
      }, 1000)
    },
    initSignaturePad() {
      // TODO: 初始化签名板
    },
    handleClear() {
      // TODO: 清除签名
    },
    handleSubmit() {
      if (this.activeTab === 'signature') {
        this.$refs.formRef.validate(async valid => {
          if (valid) {
            // TODO: 调用API保存签到数据
            this.$emit('success')
            this.visible = false
          }
        })
      }
    },
    handleClose() {
      this.activeTab = 'qrcode'
      this.$refs.formRef?.resetFields()
      if (this.signaturePad) {
        // TODO: 清除签名板
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sign-in-content {
  .qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;

    .qrcode-wrapper {
      width: 200px;
      height: 200px;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .tip {
      margin-top: 12px;
      color: #909399;
      font-size: 14px;
    }
  }

  .signature-container {
    .signature-pad-container {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      overflow: hidden;

      .signature-pad {
        width: 100%;
        height: 200px;
        background-color: #fff;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 