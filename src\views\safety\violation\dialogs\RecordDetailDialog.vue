<template>
  <el-dialog
    :title="recordData ? '违章记录详情' : '新增违章记录'"
    v-model="dialogVisible"
    width="700px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      :disabled="!!recordData"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="违章人员" prop="violatorName">
            <el-input v-model="form.violatorName" placeholder="请输入违章人员姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属部门" prop="department">
            <el-select v-model="form.department" placeholder="请选择部门" style="width: 100%">
              <el-option
                v-for="item in departmentOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="违章类型" prop="violationType">
            <el-select
              v-model="form.violationType"
              placeholder="请选择违章类型"
              @change="handleViolationTypeChange"
              style="width: 100%"
            >
              <el-option
                v-for="item in violationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="违章标准" prop="standardId">
            <el-select
              v-model="form.standardId"
              placeholder="请选择违章标准"
              @change="handleStandardChange"
              style="width: 100%"
            >
              <el-option
                v-for="item in standardOptions"
                :key="item.id"
                :label="item.description"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="违章时间" prop="violationTime">
            <el-date-picker
              v-model="form.violationTime"
              type="datetime"
              placeholder="请选择违章时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="违章地点" prop="location">
            <el-input v-model="form.location" placeholder="请输入违章地点" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="违章描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入违章描述"
        />
      </el-form-item>

      <el-form-item label="处罚结果" prop="punishment">
        <el-input v-model="form.punishment" disabled />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ recordData ? '关闭' : '取消' }}</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="!recordData">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
// 模拟违章标准数据
const mockStandards = {
  safety: [
    {
      id: 'std001',
      description: '未按规定佩戴安全帽',
      punishment: '警告+罚款100元'
    },
    {
      id: 'std002',
      description: '未系安全带作业',
      punishment: '警告+罚款200元+扣分2分'
    }
  ],
  equipment: [
    {
      id: 'std003',
      description: '违规操作起重机',
      punishment: '警告+罚款500元+扣分3分+安全教育'
    },
    {
      id: 'std004',
      description: '设备未经检查擅自使用',
      punishment: '警告+罚款300元+扣分2分'
    }
  ],
  environment: [
    {
      id: 'std005',
      description: '建筑垃圾未按规定处置',
      punishment: '警告+罚款300元'
    },
    {
      id: 'std006',
      description: '施工噪音超标',
      punishment: '警告+罚款200元'
    }
  ]
}

export default {
  name: 'RecordDetailDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    recordData: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      form: {
        violatorName: '',
        department: '',
        violationType: '',
        standardId: '',
        violationTime: '',
        location: '',
        description: '',
        punishment: ''
      },
      departmentOptions: [
        { value: 'dept1', label: '施工一部' },
        { value: 'dept2', label: '施工二部' },
        { value: 'dept3', label: '安全部' }
      ],
      violationTypeOptions: [
        { value: 'safety', label: '安全操作' },
        { value: 'equipment', label: '设备使用' },
        { value: 'environment', label: '环境保护' }
      ],
      standardOptions: [],
      rules: {
        violatorName: [
          { required: true, message: '请输入违章人员姓名', trigger: 'blur' }
        ],
        department: [
          { required: true, message: '请选择所属部门', trigger: 'change' }
        ],
        violationType: [
          { required: true, message: '请选择违章类型', trigger: 'change' }
        ],
        standardId: [
          { required: true, message: '请选择违章标准', trigger: 'change' }
        ],
        violationTime: [
          { required: true, message: '请选择违章时间', trigger: 'change' }
        ],
        location: [
          { required: true, message: '请输入违章地点', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入违章描述', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    modelValue(val) {
      if (val && this.recordData) {
        // 查看详情时，需要转换部门和违章类型的值
        const department = this.departmentOptions.find(opt => opt.label === this.recordData.department)
        const violationType = this.violationTypeOptions.find(opt => opt.label === this.recordData.violationType)
        
        this.form = {
          ...this.recordData,
          department: department?.value || '',
          violationType: violationType?.value || ''
        }

        // 加载对应的违章标准列表
        if (violationType) {
          this.standardOptions = mockStandards[violationType.value] || []
        }
      }
    }
  },
  methods: {
    handleViolationTypeChange() {
      this.form.standardId = ''
      this.form.punishment = ''
      // 根据违章类型获取对应的标准列表
      this.standardOptions = mockStandards[this.form.violationType] || []
    },
    handleStandardChange() {
      const standard = this.standardOptions.find(item => item.id === this.form.standardId)
      if (standard) {
        this.form.description = standard.description
        this.form.punishment = standard.punishment
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.form = {
        violatorName: '',
        department: '',
        violationType: '',
        standardId: '',
        violationTime: '',
        location: '',
        description: '',
        punishment: ''
      }
      this.standardOptions = []
    },
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 转换部门和违章类型的显示文本
          const department = this.departmentOptions.find(opt => opt.value === this.form.department)
          const violationType = this.violationTypeOptions.find(opt => opt.value === this.form.violationType)
          
          const data = {
            ...this.form,
            recordId: `R${new Date().getTime()}`,
            department: department.label,
            violationType: violationType.label
          }
          
          this.$message.success('保存成功')
          this.$emit('success', data)
          this.handleClose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 