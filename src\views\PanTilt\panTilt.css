/**
  云台样式
 */
 .ws-pan-tilt-control{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    border: 1px solid #e9ebee;
    padding-top: 10px;
    position: relative;
  }
  
  .ws-pan-tilt-circle{
    width: 180px;
    height: 180px;
    background-color: #f5f6f9;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
  }
  
  .ws-pan-tilt-direction-item{
    position: absolute;
    width: 90px;
    height: 90px;
    transform-origin: 100% 100%;
    transition: border 0.3s ease;
    cursor: pointer;
  }
  
  .ws-pan-tilt-direction-item:nth-child(1){
    transform: rotate(66deg) skew(45deg);
  }
  .ws-pan-tilt-direction-item:nth-child(1) img{
    width: 16px;
    position: absolute;
    transform: skew(-45deg) rotate(-66deg) translate(-1px, -8px);
    top: 55px;
    left: 52px;
  }
  .ws-pan-tilt-direction-item:nth-child(2){
    transform: rotate(111deg) skew(45deg);
  }
  .ws-pan-tilt-direction-item:nth-child(2) img{
    width: 16px;
    position: absolute;
    transform: skew(-45deg) rotate(-111deg) translate(1px, 2px);
    top: 55px;
    left: 44px;
  }
  .ws-pan-tilt-direction-item:nth-child(3){
    transform: rotate(156deg) skew(45deg);
  }
  .ws-pan-tilt-direction-item:nth-child(3) img{
    width: 16px;
    position: absolute;
    transform: skew(-45deg) rotate(-156deg) translate(0px, 3px);
    top: 55px;
    left: 44px;
  }
  .ws-pan-tilt-direction-item:nth-child(4){
    transform: rotate(-159deg) skew(45deg);
  }
  .ws-pan-tilt-direction-item:nth-child(4) img{
    width: 16px;
    position: absolute;
    transform: skew(-45deg) rotate(159deg) translate(0px, 1px);
    top: 55px;
    left: 46px;
  }
  .ws-pan-tilt-direction-item:nth-child(5){
    transform: rotate(-114deg) skew(45deg);
  }
  .ws-pan-tilt-direction-item:nth-child(5) img{
    width: 16px;
    position: absolute;
    transform: skew(-45deg) rotate(114deg) translate(2px, -2px);
    top: 51px;
    left: 48px;
  }
  .ws-pan-tilt-direction-item:nth-child(6){
    transform: rotate(-69deg) skew(45deg);
  }
  .ws-pan-tilt-direction-item:nth-child(6) img{
    width: 16px;
    position: absolute;
    transform: skew(-45deg) rotate(69deg) translate(2px, 2px);
    top: 52px;
    left: 50px;
  }
  .ws-pan-tilt-direction-item:nth-child(7){
    transform: rotate(-24deg) skew(45deg);
  }
  .ws-pan-tilt-direction-item:nth-child(7) img{
    width: 16px;
    position: absolute;
    transform: skew(-45deg) rotate(24deg) translate(2px, 2px);
    top: 50px;
    left: 48px;
  }
  .ws-pan-tilt-direction-item:nth-child(8){
    transform: rotate(21deg) skew(45deg);
  }
  .ws-pan-tilt-direction-item:nth-child(8) img{
    width: 16px;
    position: absolute;
    transform: skew(-45deg) rotate(-21deg) translate(0px, 2px);
    top: 52px;
    left: 49px;
  }
  .ws-pan-tilt-inner-circle{
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: white;
    position: absolute;
    top: 28%;
    left: 28%;
  }
  
  .cloud-control-wrapper{
    display: flex;
    width: 100%;
    height: 42px;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #e9ebee;
    margin-top: 10px;
  }
  
  .cloud-control-wrapper .ws-pan-tilt-control-item img{
    width: 20px;
    height: 20px;
    cursor: pointer;
    margin: 0 8px;
  }
  
  .cloud-control-wrapper .cloud-control-separate{
    border-left: 1px solid #e9ebee;
    height: 20px;
  }
  .ws-pan-tilt-mask {
    position: absolute;
    z-index: 100;
    cursor: not-allowed;
    background-color: #ccc;
    opacity: 0.2;
  }
  .ws-pan-tilt-mask-direction {
    top: 0;
    width: 100%;
    height: calc(100% - 42px);
  }
  .ws-pan-tilt-mask-zoom {
    left: 0;
    bottom: 0;
    height: 42px;
    width: calc(50% + 36px);
  }
  .ws-pan-tilt-mask-aperture {
    right: 0;
    bottom: 0;
    height: 42px;
    width: calc(50% - 36px);
  }
  