<template>
  <div class="waste-statistics">
    <div class="filter-section">
      <el-form :inline="true" :model="queryForm">
        <el-form-item label="统计时间">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>危化品使用量统计</span>
            </div>
          </template>
          <div class="chart-container" ref="usageChartRef"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>使用频率分析</span>
            </div>
          </template>
          <div class="chart-container" ref="frequencyChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-card class="summary-card">
      <template #header>
        <div class="card-header">
          <span>使用情况汇总</span>
        </div>
      </template>
      <el-table :data="summaryData" border style="width: 100%">
        <el-table-column prop="chemicalName" label="危化品名称" />
        <el-table-column prop="totalAmount" label="总使用量" />
        <el-table-column prop="unit" label="单位" />
        <el-table-column prop="frequency" label="使用频次" />
        <el-table-column prop="userCount" label="使用人数" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import * as echarts from 'echarts'

const queryForm = reactive({
  dateRange: []
})

const summaryData = ref([])
const usageChartRef = ref(null)
const frequencyChartRef = ref(null)

let usageChart = null
let frequencyChart = null

onMounted(() => {
  initCharts()
})

const initCharts = () => {
  // 初始化使用量统计图表
  usageChart = echarts.init(usageChartRef.value)
  usageChart.setOption({
    title: {
      text: '危化品使用量趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [150, 230, 224, 218, 135, 147],
      type: 'line'
    }]
  })

  // 初始化使用频率分析图表
  frequencyChart = echarts.init(frequencyChartRef.value)
  frequencyChart.setOption({
    title: {
      text: '使用频率分布'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '50%',
      data: [
        { value: 235, name: '硫酸' },
        { value: 274, name: '盐酸' },
        { value: 310, name: '硝酸' },
        { value: 335, name: '乙醇' },
        { value: 400, name: '其他' }
      ]
    }]
  })
}

const handleSearch = () => {
  // 实现查询逻辑
}

const handleReset = () => {
  queryForm.dateRange = []
}

// 监听窗口大小变化，重绘图表
window.addEventListener('resize', () => {
  usageChart?.resize()
  frequencyChart?.resize()
})
</script>

<style scoped>
.waste-statistics {
  padding: 20px;
}

.filter-section {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-card {
  margin-top: 20px;
}
</style> 