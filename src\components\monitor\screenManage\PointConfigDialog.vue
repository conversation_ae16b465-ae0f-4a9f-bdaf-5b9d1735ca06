<template>
  <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑测点' : '添加测点'" width="600px" destroy-on-close
    @closed="handleClosed">
    <el-form ref="formRef" :model="form" label-width="100px" label-position="left">
      <!-- 基本信息 -->
      <el-divider content-position="left">基本信息</el-divider>
      <el-form-item label="测点名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入测点名称" />
      </el-form-item>

      <el-form-item label="测点类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择测点类型" style="width: 100%">
          <el-option label="指标测点" value="metric" />
          <el-option label="状态测点" value="status" />
        </el-select>
      </el-form-item>

      <template v-if="form.type === 'metric'">
        <el-form-item label="选择指标" prop="metricId">
          <el-cascader v-model="form.metricId" :options="typedMetricOptions" :props="{
            checkStrictly: true,
            label: 'name',
            value: 'id',
            children: 'metrics'
          }" placeholder="请选择指标" clearable style="width: 100%" />
        </el-form-item>
      </template>

      <template v-if="form.type === 'status'">
        <el-form-item label="设备状态" prop="stationId">
          <el-select v-model="form.stationId" placeholder="请选择设备" style="width: 100%">
            <el-option v-for="station in typedStationOptions" :key="station.id" :label="station.name"
              :value="station.id" />
          </el-select>
        </el-form-item>
      </template>

      <!-- 布局模板 -->
      <el-divider content-position="left">布局模板</el-divider>
      <el-form-item label="布局模板">
        <layout-templates v-model="form.layoutTemplate" :layout-options="layoutOptions" />
      </el-form-item>

      <el-form-item>
        <layout-preview :label-position="labelPosition" :number-position="numberPosition"
          :layout-template="form.layoutTemplate" />
      </el-form-item>

      <!-- 位置和样式 -->
      <el-divider content-position="left">位置和样式</el-divider>
      <el-form-item label="标签位置" v-if="form.layoutTemplate !== 'valueOnly'">
        <position-control :modelValue="form.labelPosition || { x: 0, y: 0 }"
          @update:modelValue="val => form.labelPosition = val" label="标签" coordinate-type="label"
          :default-value="getDefaultPositionByLayout('label')" @reset="resetLabelPosition" />
      </el-form-item>

      <el-form-item label="数值位置">
        <position-control :modelValue="form.numberPosition || { x: 0, y: 0 }"
          @update:modelValue="val => form.numberPosition = val" label="数值" coordinate-type="number"
          :default-value="getDefaultPositionByLayout('number')" @reset="resetNumberPosition" />
      </el-form-item>

      <el-form-item label="颜色配置">
        <color-picker v-model="colorConfig.normal" label="正常状态颜色" />
        <color-picker v-model="colorConfig.warning" label="告警状态颜色" />
        <color-picker v-model="colorConfig.error" label="错误状态颜色" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import type { CascaderOption, FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { computed, reactive, ref, watch } from 'vue';
import ColorPicker from './ColorPicker.vue';
import LayoutPreview from './LayoutPreview.vue';
import LayoutTemplates from './LayoutTemplates.vue';
import PositionControl from './PositionControl.vue';
import { LayoutOption, MonitorPoint, PointColorConfig, Position, Station } from './types';

// 布局选项
const layoutOptions: LayoutOption[] = [
  {
    value: 'right',
    label: '右侧布局',
    labelOffset: { x: 15, y: 0 },
    numberOffset: { x: 50, y: 0 }
  },
  {
    value: 'left',
    label: '左侧布局',
    labelOffset: { x: -15, y: 0 },
    numberOffset: { x: -50, y: 0 }
  },
  {
    value: 'top',
    label: '顶部布局',
    labelOffset: { x: 0, y: -15 },
    numberOffset: { x: 0, y: -35 }
  },
  {
    value: 'bottom',
    label: '底部布局',
    labelOffset: { x: 0, y: 15 },
    numberOffset: { x: 0, y: 35 }
  },
  {
    value: 'valueOnly',
    label: '仅数值',
    labelOffset: { x: 0, y: 0 },
    numberOffset: { x: 0, y: 0 }
  }
];

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  pointData: {
    type: Object as () => MonitorPoint | null,
    default: null
  },
  metricOptions: {
    type: Array,
    default: () => []
  },
  stationOptions: {
    type: Array,
    default: () => []
  }
});

// 计算属性 - 类型化站点选项
const typedStationOptions = computed<Station[]>(() => {
  return (props.stationOptions || []) as Station[];
});

// 计算属性 - 类型化指标选项
const typedMetricOptions = computed<CascaderOption[]>(() => {
  return (props.metricOptions || []) as CascaderOption[];
});

// 确保位置对象始终存在
const labelPosition = computed<Position>(() => {
  return form.labelPosition || { x: 0, y: 0 };
});

const numberPosition = computed<Position>(() => {
  return form.numberPosition || { x: 0, y: 0 };
});

// 定义事件
const emit = defineEmits(['update:visible', 'save']);

// 表单引用
const formRef = ref<FormInstance>();

// 对话框状态
const dialogVisible = ref(props.visible);
const submitting = ref(false);

// 是否为编辑模式
const isEdit = computed(() => !!props.pointData?.id);

// 表单数据
const form = reactive<MonitorPoint>({
  id: '',
  name: '',
  type: 'metric',
  metricId: undefined,
  stationId: undefined,
  position: { x: 0, y: 0 },
  labelPosition: { x: 15, y: 0 },
  numberPosition: { x: 50, y: 0 },
  layoutTemplate: 'right',
  color: undefined,
  svgImageData: undefined
});

// 颜色配置
const colorConfig = reactive<PointColorConfig>({
  normal: '#409EFF',
  warning: '#E6A23C',
  error: '#F56C6C'
});

// 根据布局获取默认位置
const getDefaultPositionByLayout = (type: 'label' | 'number') => {
  const layout = layoutOptions.find(option => option.value === form.layoutTemplate);
  if (layout) {
    return type === 'label' ? layout.labelOffset : layout.numberOffset;
  }
  return { x: 0, y: 0 };
};

// 重置标签位置
const resetLabelPosition = () => {
  const defaultPos = getDefaultPositionByLayout('label');
  if (!form.labelPosition) {
    form.labelPosition = { x: 0, y: 0 };
  }
  form.labelPosition.x = defaultPos.x;
  form.labelPosition.y = defaultPos.y;
};

// 重置数值位置
const resetNumberPosition = () => {
  const defaultPos = getDefaultPositionByLayout('number');
  if (!form.numberPosition) {
    form.numberPosition = { x: 0, y: 0 };
  }
  form.numberPosition.x = defaultPos.x;
  form.numberPosition.y = defaultPos.y;
};

// 重置表单
const resetForm = () => {
  // 重置为默认值
  form.id = '';
  form.name = '';
  form.type = 'metric';
  form.metricId = undefined;
  form.stationId = undefined;
  form.position = { x: 0, y: 0 };
  form.labelPosition = { x: 15, y: 0 };
  form.numberPosition = { x: 50, y: 0 };
  form.layoutTemplate = 'right';
  form.svgImageData = undefined;

  // 重置颜色配置
  colorConfig.normal = '#409EFF';
  colorConfig.warning = '#E6A23C';
  colorConfig.error = '#F56C6C';
};

// 监听pointData变化
watch(() => props.pointData, (newVal) => {
  if (newVal) {
    // 深度复制，避免直接修改props
    Object.keys(form).forEach(key => {
      if (key in newVal && newVal[key as keyof MonitorPoint] !== undefined) {
        if (typeof newVal[key as keyof MonitorPoint] === 'object' && newVal[key as keyof MonitorPoint] !== null) {
          (form as any)[key] = JSON.parse(JSON.stringify(newVal[key as keyof MonitorPoint]));
        } else {
          (form as any)[key] = newVal[key as keyof MonitorPoint];
        }
      }
    });

    // 处理颜色配置
    if (newVal.color && typeof newVal.color === 'object') {
      colorConfig.normal = newVal.color.normal || '#409EFF';
      colorConfig.warning = newVal.color.warning || '#E6A23C';
      colorConfig.error = newVal.color.error || '#F56C6C';
    } else if (newVal.color && typeof newVal.color === 'string') {
      colorConfig.normal = newVal.color;
    }
  } else {
    // 重置表单
    resetForm();
  }
}, { immediate: true });

// 监听layoutTemplate变化
watch(() => form.layoutTemplate, (newVal) => {
  // 当布局模板变化时，更新标签和数值的位置
  const layout = layoutOptions.find(option => option.value === newVal);
  if (layout) {
    if (!form.labelPosition) form.labelPosition = { x: 0, y: 0 };
    if (!form.numberPosition) form.numberPosition = { x: 0, y: 0 };

    form.labelPosition.x = layout.labelOffset.x;
    form.labelPosition.y = layout.labelOffset.y;
    form.numberPosition.x = layout.numberOffset.x;
    form.numberPosition.y = layout.numberOffset.y;
  }
});

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
});

// 监听dialogVisible变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
});

// 对话框关闭处理
const handleClosed = () => {
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  submitting.value = true;
  try {
    // 验证表单（可根据需要添加表单验证规则）
    await formRef.value?.validate();

    // 设置颜色配置
    form.color = { ...colorConfig };

    // 提交表单数据
    emit('save', JSON.parse(JSON.stringify(form)));

    // 关闭对话框
    dialogVisible.value = false;

    // 成功提示
    ElMessage.success(isEdit.value ? '测点更新成功' : '测点添加成功');
  } catch (error) {
    console.error('表单验证失败', error);
    ElMessage.error('请检查表单内容是否填写正确');
  } finally {
    submitting.value = false;
  }
};

// 初始化默认位置
watch(() => form, () => {
  if (!form.labelPosition) {
    form.labelPosition = { x: 0, y: 0 };
  }
  if (!form.numberPosition) {
    form.numberPosition = { x: 0, y: 0 };
  }
}, { immediate: true, deep: true });
</script>

<style scoped lang="scss">
.el-divider {
  margin: 16px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>