<template>
  <el-dialog
    :title="editData ? '编辑处罚标准' : '新增处罚标准'"
    v-model="dialogVisible"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="标准编号" prop="code">
        <el-input v-model="form.code" placeholder="请输入标准编号" />
      </el-form-item>
      <el-form-item label="违章类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择违章类型" style="width: 100%">
          <el-option
            v-for="item in violationTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="违章描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入违章描述"
        />
      </el-form-item>
      <el-form-item label="处罚方式" prop="punishment">
        <el-checkbox-group v-model="form.punishmentTypes">
          <el-checkbox label="warning">警告</el-checkbox>
          <el-checkbox label="fine">罚款</el-checkbox>
          <el-checkbox label="score">扣分</el-checkbox>
          <el-checkbox label="education">安全教育</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="扣分" prop="score" v-if="form.punishmentTypes.includes('score')">
        <el-input-number
          v-model="form.score"
          :min="0"
          :max="12"
          :step="1"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="罚款金额" prop="fine" v-if="form.punishmentTypes.includes('fine')">
        <el-input-number
          v-model="form.fine"
          :min="0"
          :max="10000"
          :step="100"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'AddStandardDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    editData: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      form: {
        code: '',
        type: '',
        description: '',
        punishmentTypes: [],
        score: 0,
        fine: 0
      },
      violationTypeOptions: [
        { value: 'safety', label: '安全操作' },
        { value: 'equipment', label: '设备使用' },
        { value: 'environment', label: '环境保护' }
      ],
      rules: {
        code: [
          { required: true, message: '请输入标准编号', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择违章类型', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入违章描述', trigger: 'blur' }
        ],
        punishmentTypes: [
          { required: true, message: '请选择处罚方式', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    modelValue(val) {
      if (val && this.editData) {
        this.form = {
          ...this.editData,
          punishmentTypes: this.getPunishmentTypes(this.editData.punishment)
        }
      }
    }
  },
  methods: {
    getPunishmentTypes(punishment) {
      const types = []
      if (punishment.includes('警告')) types.push('warning')
      if (punishment.includes('罚款')) types.push('fine')
      if (punishment.includes('扣分')) types.push('score')
      if (punishment.includes('安全教育')) types.push('education')
      return types
    },
    formatPunishment() {
      const parts = []
      if (this.form.punishmentTypes.includes('warning')) {
        parts.push('警告')
      }
      if (this.form.punishmentTypes.includes('fine')) {
        parts.push(`罚款${this.form.fine}元`)
      }
      if (this.form.punishmentTypes.includes('score')) {
        parts.push(`扣分${this.form.score}分`)
      }
      if (this.form.punishmentTypes.includes('education')) {
        parts.push('安全教育')
      }
      return parts.join('+')
    },
    handleClose() {
      this.dialogVisible = false
      this.form = {
        code: '',
        type: '',
        description: '',
        punishmentTypes: [],
        score: 0,
        fine: 0
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          const data = {
            ...this.form,
            punishment: this.formatPunishment()
          }
          // TODO: 调用保存接口
          this.$message.success('保存成功')
          this.$emit('success')
          this.handleClose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 