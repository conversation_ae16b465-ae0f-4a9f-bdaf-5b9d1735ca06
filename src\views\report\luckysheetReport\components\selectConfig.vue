<template>
  <div>
    <!-- 筛选条件区域，只在有配置时显示 -->
    <div v-if="hasSelectConfig" class="select-config">
      <el-form :model="form" inline>
        <template v-for="item in selectConfig" :key="item.field">
          <el-form-item :label="item.label">
            <!-- 日期选择器 -->
            <el-date-picker v-if="item.type === 'datePicker'" v-model="form[item.field]" :type="getDatePickerType(item)"
              :format="item.format" :placeholder="item.placeholder" :clearable="item.clearable"
              @change="handleSelectChange(item.field)" />
            <!-- 下拉选择器 -->
            <el-select v-else-if="item.type === 'select'" v-model="form[item.field]" :placeholder="item.placeholder"
              :multiple="item.multiple" :clearable="true" @change="handleSelectChange(item.field)">
              <el-option v-for="option in item.options" :key="option.value" :label="option.label"
                :value="option.value" />
            </el-select>
          </el-form-item>
        </template>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
<!--          <el-button @click="handleReset">重置</el-button>-->
          <el-button @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import request from '@/config/axios'

import dayjs from 'dayjs'

// 数据源接口定义
interface DataSource {
  api: string
  method: string
  valueKey: string
  labelKey: string
  params: Record<string, any>
}

// 选项接口定义
interface SelectOption {
  value: string | number
  label: string
}

// 筛选条件配置接口定义
interface SelectConfigItem {
  field: string // 字段标识
  label: string // 显示标签
  type: 'datePicker' | 'select' // 组件类型
  placeholder?: string // 占位文本
  clearable?: boolean // 是否可清除
  // 日期选择器特有属性
  range?: boolean // 是否为范围选择
  granularity?: 'year' | 'month' | 'day' // 时间粒度
  format?: string // 日期格式
  // 下拉选择器特有属性
  multiple?: boolean // 是否多选
  dataSource?: DataSource // 数据源配置
  options?: SelectOption[] // 选项列表
}

// 组件属性定义
const props = defineProps<{
  config: {
    field: string
    label: string
    type: 'datePicker' | 'select'
    placeholder?: string
    clearable?: boolean
    range?: boolean
    granularity?: 'year' | 'month' | 'day'
    format?: string
    multiple?: boolean
    dataSource?: {
      api: string
      method: string
      valueKey: string
      labelKey: string
      params: Record<string, any>
    }
    options?: Array<{
      value: string | number
      label: string
    }>
  }[]
}>()

// 定义事件
const emit = defineEmits(['search', 'reset', 'export'])

// 响应式数据
const selectConfig = computed(() => props.config || [])
const form = reactive<Record<string, any>>({})

// 计算是否有筛选配置
const hasSelectConfig = computed(() => {
  return selectConfig.value.length > 0
})

// 获取日期选择器的默认值
const getDefaultDateValue = (item: any) => {
  const now = dayjs()
  switch (item.granularity) {
    case 'year':
      form.format = 'yyyy'
      return now.format('YYYY')
    case 'month':
      form.format = 'yyyy-MM'
      return now.format('YYYY-MM')
    case 'day':
      form.format = 'yyyy-MM-dd'
      return now.format('YYYY-MM-DD')
    default:
      form.format = 'yyyy-MM-dd'
      return now.format('YYYY-MM-DD')
  }
}

// 根据配置获取日期选择器类型
const getDatePickerType = (item: any) => {
  if (!item.granularity) return 'date'
  const base = item.granularity === 'day' ? 'date' : item.granularity
  return item.range ? `${base}range` : base
}

// 组件挂载时初始化
onMounted(async () => {
  // 初始化表单默认值
  selectConfig.value.forEach(item => {
    if (item.type === 'datePicker') {
      // 设置日期选择器的默认值
      form[item.field] = getDefaultDateValue(item)
      form.dateTime = form[item.field]
    } else if (item.type === 'select') {
      form[item.field] = item.multiple ? [] : null
      if (item.dataSource) {
        loadSelectOptions(item)
      }
    }
  })
})

// 加载下拉选择器选项
const loadSelectOptions = async (item: SelectConfigItem) => {
  if (!item.dataSource) return;

  const { api, method, valueKey, labelKey, params } = item.dataSource;

  try {
    let data: any = null;
    if (method.toLowerCase() === 'get') {
      data = await request.get({ url: api, params: params });
    } else if (method.toLowerCase() === 'post') {
      data = await request.post({ url: api, data: params });
    }

    if (data && data.length > 0) {
      item.options = data.map((data: any) => ({
        value: data[valueKey],
        label: data[labelKey]
      }));

      // 默认选中第一个选项
      if (!item.multiple) {
        form[item.field] = item.options[0].value;
      } else {
        form[item.field] = [item.options[0].value];
      }

      // 触发搜索事件
      emit('search', form);
    }
  } catch (error) {
    console.error(`加载${item.label}选项失败:`, error);
  }
};


// 处理选择变更
const handleSelectChange = (field: string) => {
  emit('search', form)
}

// 处理查询按钮点击
const handleSearch = () => {
  emit('search', form)
}

// 处理重置按钮点击
const handleReset = () => {
  Object.keys(form).forEach(key => {
    form[key] = Array.isArray(form[key]) ? [] : null
  })
  emit('reset')
}
// 导出操作
const handleExport = () => {
  emit('export')
}

</script>

<style scoped>
.select-config {
  padding: 12px;
  padding-right: 0;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
}

.export-area {
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-start;
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 16px;
  margin-right: 16px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
  color: #606266;
}

:deep(.el-select) {
  width: 200px;
}

:deep(.el-date-picker) {
  width: 200px;
}

/* 按钮样式优化 */
.el-button {
  margin-left: 8px;
}

.el-button:first-child {
  margin-left: 0;
}
</style>
