<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">用途管理</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleAdd">
              <el-icon>
                <Plus />
              </el-icon>新增用途
            </el-button>
          </div>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-270px)] flex flex-col">
        <div class="w-full h-[50px] mb-2 gap-2 flex items-center">
          <el-form :model="searchForm" inline>
            <el-form-item label="用途名称">
              <el-input v-model="searchForm.searchKeyword" placeholder="请输入用途名称搜索" class="search-input" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
              <el-button type="info">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="purposeList" height="100%" v-loading="loading">
                <el-table-column prop="name" label="用途名称" />
                <el-table-column prop="warehouse" label="关联仓库" />
                <el-table-column prop="goodsType" label="关联商品类型" />
                <el-table-column label="入库流程">
                  <template #default="{ row }">
                    <el-tag>{{ row.inboundProcess }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="领用流程">
                  <template #default="{ row }">
                    <el-tag type="success">{{ row.outboundProcess }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="库存限制">
                  <template #default="{ row }">
                    <el-switch v-model="row.stockLimit" :active-text="row.stockLimit ? '需要库存' : '不需要库存'" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button type="primary" link @click="handleEdit(row)"> 编辑 </el-button>
                    <el-button type="danger" link @click="handleDelete(row)"> 删除 </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end mt-2">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>
    <!-- 新增对话框 -->
    <AddPurposeDialog v-model:visible="addDialogVisible" @submit="handleAddSubmit" />

    <!-- 编辑对话框 -->
    <EditPurposeDialog v-model:visible="editDialogVisible" :edit-data="editData" @submit="handleEditSubmit" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import AddPurposeDialog from './components/AddPurposeDialog.vue'
import EditPurposeDialog from './components/EditPurposeDialog.vue'

// 数据相关
const loading = ref(false)
const addDialogVisible = ref(false)
const editDialogVisible = ref(false)
const editData = ref({})
const searchForm = reactive({
  searchKeyword: ''
})

// 模拟数据
const purposeList = ref([
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  },
  {
    id: 1,
    name: '办公用品',
    warehouse: '主仓库',
    goodsType: '文具',
    inboundProcess: '直接入库',
    outboundProcess: '审批领用',
    stockLimit: true
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(3)

const handleAdd = () => {
  addDialogVisible.value = true
}

const handleEdit = (row: any) => {
  editData.value = { ...row }
  editDialogVisible.value = true
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确认删除用途"${row.name}"吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里添加删除逻辑
    ElMessage.success('删除成功')
  })
}

const handleAddSubmit = (formData: any) => {
  console.log('🚀 ~ handleAddSubmit ~ formData:', formData)
  ElMessage.success('添加成功')
}

const handleEditSubmit = (formData: any) => {
  console.log('🚀 ~ handleEditSubmit ~ formData:', formData)
  ElMessage.success('修改成功')
}

const handleSearch = () => {
  console.log('搜索条件：', searchForm)
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}
</script>

<style scoped lang="scss"></style>
