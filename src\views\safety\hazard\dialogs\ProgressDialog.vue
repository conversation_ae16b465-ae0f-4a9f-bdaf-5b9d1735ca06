<template>
  <el-dialog
    title="更新进度"
    v-model="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="progress-form"
    >
      <el-form-item label="当前进度" prop="progress">
        <el-slider
          v-model="formData.progress"
          :step="10"
          :marks="marks"
          show-input
        />
      </el-form-item>

      <el-form-item label="进度说明" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入进度说明"
        />
      </el-form-item>

      <el-form-item label="现场照片" prop="photos">
        <el-upload
          class="upload-demo"
          action="#"
          list-type="picture-card"
          :auto-upload="false"
          :on-change="handlePhotoChange"
          :file-list="formData.photos"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>

      <el-form-item label="是否完成" prop="isCompleted">
        <el-switch
          v-model="formData.isCompleted"
          :disabled="formData.progress < 100"
          :active-text="formData.progress === 100 ? '整改完成' : '进行中'"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { ProgressData, UploadFile } from '../types'

const props = defineProps({
  data: {
    type: Object as () => Partial<ProgressData>,
    default: () => ({})
  }
})

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive<ProgressData>({
  progress: 0,
  description: '',
  photos: [],
  isCompleted: false
})

const marks = {
  0: '0%',
  20: '20%',
  40: '40%',
  60: '60%',
  80: '80%',
  100: '100%'
}

const rules: FormRules = {
  progress: [{ required: true, message: '请设置当前进度', trigger: 'change' }],
  description: [{ required: true, message: '请输入进度说明', trigger: 'blur' }]
}

// 监听进度变化，自动设置完成状态
watch(() => formData.progress, (newValue) => {
  if (newValue === 100) {
    formData.isCompleted = true
  } else {
    formData.isCompleted = false
  }
})

const handlePhotoChange = (file: UploadFile, fileList: UploadFile[]) => {
  formData.photos = fileList
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      // TODO: 实现提交逻辑
      ElMessage.success('进度更新成功')
      dialogVisible.value = false
      emit('success', {
        ...formData,
        status: formData.isCompleted ? '待验收' : '整改中',
        updateTime: new Date().toISOString()
      })
    }
  })
}

const handleClosed = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  Object.assign(formData, {
    progress: 0,
    description: '',
    photos: [],
    isCompleted: false
  })
}

// 暴露方法给父组件
defineExpose({
  open: (data?: any) => {
    dialogVisible.value = true
    if (data) {
      formData.progress = data.progress || 0
    }
  }
})
</script>

<style lang="scss" scoped>
.progress-form {
  .el-slider {
    width: calc(100% - 20px);
    margin-top: 10px;
  }

  .el-upload--picture-card {
    --el-upload-picture-card-size: 100px;
  }
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style> 