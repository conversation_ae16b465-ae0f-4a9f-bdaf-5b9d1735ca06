<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-card class="assessment-card">
        <template #header>
          <div class="card-header">
            <span>评估信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="评估人员" prop="assessor">
              <el-input v-model="form.assessor" placeholder="请输入评估人员姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评估日期" prop="assessmentDate">
              <el-date-picker
                v-model="form.assessmentDate"
                type="date"
                placeholder="请选择评估日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="复核人员" prop="reviewer">
              <el-input v-model="form.reviewer" placeholder="请输入复核人员姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="复核日期" prop="reviewDate">
              <el-date-picker
                v-model="form.reviewDate"
                type="date"
                placeholder="请选择复核日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="assessment-card">
        <template #header>
          <div class="card-header">
            <span>评估过程</span>
          </div>
        </template>
        <el-form-item label="评估方法" prop="method">
          <el-radio-group v-model="form.method">
            <el-radio label="LEC">LEC法</el-radio>
            <el-radio label="LS">LS法</el-radio>
            <el-radio label="other">其他方法</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="危险源辨识" prop="hazardIdentification">
          <el-input
            v-model="form.hazardIdentification"
            type="textarea"
            :rows="3"
            placeholder="请描述作业过程中可能存在的危险源"
          />
        </el-form-item>
        <el-form-item label="评估依据" prop="basis">
          <el-select v-model="form.basis" multiple placeholder="请选择评估依据" style="width: 100%">
            <el-option label="法律法规" value="law" />
            <el-option label="标准规范" value="standard" />
            <el-option label="操作规程" value="procedure" />
            <el-option label="事故案例" value="case" />
            <el-option label="专家意见" value="expert" />
          </el-select>
        </el-form-item>
        <el-form-item label="评估过程描述" prop="processDescription">
          <el-input
            v-model="form.processDescription"
            type="textarea"
            :rows="4"
            placeholder="请详细描述评估过程"
          />
        </el-form-item>
      </el-card>

      <el-card class="assessment-card">
        <template #header>
          <div class="card-header">
            <span>管控建议</span>
          </div>
        </template>
        <el-form-item label="工程控制" prop="engineeringControls">
          <el-input
            v-model="form.engineeringControls"
            type="textarea"
            :rows="2"
            placeholder="请输入工程控制措施"
          />
        </el-form-item>
        <el-form-item label="管理控制" prop="managementControls">
          <el-input
            v-model="form.managementControls"
            type="textarea"
            :rows="2"
            placeholder="请输入管理控制措施"
          />
        </el-form-item>
        <el-form-item label="个人防护" prop="personalProtection">
          <el-input
            v-model="form.personalProtection"
            type="textarea"
            :rows="2"
            placeholder="请输入个人防护措施"
          />
        </el-form-item>
        <el-form-item label="应急措施" prop="emergencyMeasures">
          <el-input
            v-model="form.emergencyMeasures"
            type="textarea"
            :rows="2"
            placeholder="请输入应急处置措施"
          />
        </el-form-item>
      </el-card>

      <el-card class="assessment-card">
        <template #header>
          <div class="card-header">
            <span>附件信息</span>
          </div>
        </template>
        <el-form-item label="附件上传">
          <el-upload
            class="upload-demo"
            action="/api/upload"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            multiple
            :limit="5"
            :on-exceed="handleExceed"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                可上传评估相关的图片、文档等文件，单个文件不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'AssessmentDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    assessmentData: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      form: {
        assessor: '',
        assessmentDate: '',
        reviewer: '',
        reviewDate: '',
        method: 'LEC',
        hazardIdentification: '',
        basis: [],
        processDescription: '',
        engineeringControls: '',
        managementControls: '',
        personalProtection: '',
        emergencyMeasures: '',
        remarks: ''
      },
      rules: {
        assessor: [
          { required: true, message: '请输入评估人员姓名', trigger: 'blur' }
        ],
        assessmentDate: [
          { required: true, message: '请选择评估日期', trigger: 'change' }
        ],
        reviewer: [
          { required: true, message: '请输入复核人员姓名', trigger: 'blur' }
        ],
        reviewDate: [
          { required: true, message: '请选择复核日期', trigger: 'change' }
        ],
        method: [
          { required: true, message: '请选择评估方法', trigger: 'change' }
        ],
        hazardIdentification: [
          { required: true, message: '请描述危险源', trigger: 'blur' }
        ],
        basis: [
          { required: true, message: '请选择评估依据', trigger: 'change' }
        ],
        processDescription: [
          { required: true, message: '请描述评估过程', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    },
    dialogTitle() {
      return this.assessmentData ? '编辑评估记录' : '新增评估记录'
    }
  },
  watch: {
    modelValue(val) {
      if (val && this.assessmentData) {
        this.form = { ...this.assessmentData }
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$refs.formRef?.resetFields()
    },
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // TODO: 调用保存接口
          this.$message.success('保存成功')
          this.$emit('success', this.form)
          this.handleClose()
        }
      })
    },
    handlePreview(file) {
      console.log('预览文件：', file)
    },
    handleRemove(file, fileList) {
      console.log('移除文件：', file, fileList)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传5个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`)
    }
  }
}
</script>

<style lang="scss" scoped>
.assessment-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.el-upload {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 