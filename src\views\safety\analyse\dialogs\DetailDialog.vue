<template>
  <el-dialog v-model="dialogVisible" :title="title" width="800px" :before-close="handleClose">
    <div class="detail-content">
      <el-descriptions :column="2" border>
        <template v-if="type === 'personnel'">
          <el-descriptions-item label="培训项目">{{ detailData.trainingName }}</el-descriptions-item>
          <el-descriptions-item label="培训时间">{{ detailData.trainingTime }}</el-descriptions-item>
          <el-descriptions-item label="培训人数">{{ detailData.trainingCount }}人</el-descriptions-item>
          <el-descriptions-item label="完成人数">{{ detailData.completedCount }}人</el-descriptions-item>
          <el-descriptions-item label="培训方式">{{ detailData.trainingMethod }}</el-descriptions-item>
          <el-descriptions-item label="培训状态">
            <el-tag :type="detailData.status === '已完成' ? 'success' : 'warning'">
              {{ detailData.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="培训内容" :span="2">{{ detailData.content }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ detailData.remark }}</el-descriptions-item>
        </template>

        <template v-if="type === 'environment'">
          <el-descriptions-item label="检测项目">{{ detailData.monitorItem }}</el-descriptions-item>
          <el-descriptions-item label="检测时间">{{ detailData.monitorTime }}</el-descriptions-item>
          <el-descriptions-item label="检测位置">{{ detailData.location }}</el-descriptions-item>
          <el-descriptions-item label="检测结果">
            <el-tag :type="detailData.result === '合格' ? 'success' : 'danger'">
              {{ detailData.result }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标准值">{{ detailData.standardValue }}</el-descriptions-item>
          <el-descriptions-item label="实测值">{{ detailData.actualValue }}</el-descriptions-item>
          <el-descriptions-item label="检测说明" :span="2">{{ detailData.description }}</el-descriptions-item>
        </template>

        <template v-if="type === 'equipment'">
          <el-descriptions-item label="设备名称">{{ detailData.equipmentName }}</el-descriptions-item>
          <el-descriptions-item label="设备编号">{{ detailData.equipmentCode }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ detailData.equipmentType }}</el-descriptions-item>
          <el-descriptions-item label="运行状态">
            <el-tag :type="getStatusType(detailData.status)">
              {{ detailData.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最近检修">{{ detailData.lastMaintenance }}</el-descriptions-item>
          <el-descriptions-item label="下次检修">{{ detailData.nextMaintenance }}</el-descriptions-item>
          <el-descriptions-item label="维护记录" :span="2">{{ detailData.maintenanceRecord }}</el-descriptions-item>
        </template>

        <template v-if="type === 'investment'">
          <el-descriptions-item label="投入项目">{{ detailData.investmentName }}</el-descriptions-item>
          <el-descriptions-item label="投入金额">{{ detailData.amount }}万元</el-descriptions-item>
          <el-descriptions-item label="投入类型">{{ detailData.investmentType }}</el-descriptions-item>
          <el-descriptions-item label="使用状态">
            <el-tag :type="detailData.status === '已使用' ? 'success' : 'warning'">
              {{ detailData.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="投入时间">{{ detailData.investmentTime }}</el-descriptions-item>
          <el-descriptions-item label="负责部门">{{ detailData.department }}</el-descriptions-item>
          <el-descriptions-item label="投入说明" :span="2">{{ detailData.description }}</el-descriptions-item>
        </template>
      </el-descriptions>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出详情</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    required: true,
    validator: (value: string) => {
      return ['personnel', 'environment', 'equipment', 'investment'].includes(value)
    }
  },
  detailData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'close', 'export'])

const dialogVisible = ref(props.visible)

// 标题映射
const titleMap = {
  personnel: '人员安全详情',
  environment: '环境监测详情',
  equipment: '设备设施详情',
  investment: '安全投入详情'
}

const title = computed(() => titleMap[props.type])

// 设备状态类型
const getStatusType = (status: string) => {
  const statusMap = {
    '正常运行': 'success',
    '待维修': 'warning',
    '维修中': 'info',
    '停用': 'danger'
  }
  return statusMap[status] || 'info'
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  emit('update:visible', false)
  emit('close')
}

// 导出详情
const handleExport = () => {
  emit('export', props.detailData)
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})
</script>

<style lang="scss" scoped>
.detail-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>