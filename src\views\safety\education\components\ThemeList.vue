<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="search-form"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="主题名称" prop="themeName">
        <el-input
          v-model="queryParams.themeName"
          placeholder="请输入主题名称"
          clearable
          @keyup.enter="handleQuery"
          class="search-input"
        />
      </el-form-item>
      <el-form-item label="主题类型" prop="themeType">
        <el-select
          v-model="queryParams.themeType"
          placeholder="请选择主题类型"
          clearable
          class="search-input"
        >
          <el-option label="安全月活动" value="safety-month" />
          <el-option label="节假日教育" value="holiday" />
          <el-option label="专项培训" value="special" />
          <el-option label="安全技能" value="skill" />
          <el-option label="应急演练" value="emergency" />
        </el-select>
      </el-form-item>
      <el-form-item label="部门" prop="departmentId">
        <el-tree-select
          v-model="queryParams.departmentId"
          :data="departmentOptions"
          placeholder="请选择部门"
          clearable
          check-strictly
          class="search-input"
        />
      </el-form-item>
      <el-form-item label="活动状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择活动状态"
          clearable
          class="search-input"
        >
          <el-option label="未开始" value="not-started" />
          <el-option label="进行中" value="in-progress" />
          <el-option label="已结束" value="finished" />
          <el-option label="已取消" value="canceled" />
        </el-select>
      </el-form-item>
      <el-form-item label="活动时间" prop="activityTime">
        <el-date-picker
          v-model="queryParams.activityTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="search-input"
        />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="button-icon" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="button-icon" /> 重置
        </el-button>
        <el-button
          type="primary"
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="button-icon" /> 新增主题教育
        </el-button>
        <el-button
          type="success"
          @click="handleExport"
          :loading="exportLoading"
        >
          <Icon icon="ep:download" class="button-icon" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true">
      <el-table-column label="主题名称" align="center" prop="themeName" min-width="180" />
      <el-table-column label="主题类型" align="center" prop="themeType" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.themeType === 'safety-month'" type="success">安全月活动</el-tag>
          <el-tag v-else-if="scope.row.themeType === 'holiday'" type="warning">节假日教育</el-tag>
          <el-tag v-else-if="scope.row.themeType === 'special'" type="danger">专项培训</el-tag>
          <el-tag v-else-if="scope.row.themeType === 'skill'" type="info">安全技能</el-tag>
          <el-tag v-else-if="scope.row.themeType === 'emergency'">应急演练</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="组织部门" align="center" prop="departmentName" width="120" />
      <el-table-column label="负责人" align="center" prop="organizer" width="100" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="160" />
      <el-table-column label="结束时间" align="center" prop="endTime" width="160" />
      <el-table-column label="参与人数" align="center" prop="participantCount" width="100">
        <template #default="scope">
          <el-button link type="primary" @click="handleViewParticipants(scope.row)">
            {{ scope.row.participantCount }}人
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="培训课时" align="center" prop="duration" width="100" />
      <el-table-column label="活动状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 'not-started'" type="info">未开始</el-tag>
          <el-tag v-else-if="scope.row.status === 'in-progress'" type="success">进行中</el-tag>
          <el-tag v-else-if="scope.row.status === 'finished'" type="warning">已结束</el-tag>
          <el-tag v-else-if="scope.row.status === 'canceled'" type="danger">已取消</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="相关资料" align="center" prop="attachments" width="100">
        <template #default="scope">
          <el-button 
            link 
            type="primary" 
            @click="handleViewAttachments(scope.row)"
            v-if="scope.row.attachments && scope.row.attachments.length"
          >
            {{ scope.row.attachments.length }}个
          </el-button>
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >编辑</el-button>
          <el-button
            link
            type="primary"
            @click="handleViewDetail(scope.row)"
          >详情</el-button>
          <el-button
            link
            type="success"
            v-if="scope.row.status === 'not-started' || scope.row.status === 'in-progress'"
            @click="handleSelectParticipants(scope.row)"
          >选择参与人员</el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ThemeEdit ref="formRef" @success="getList" />
  
  <!-- 查看参与人员弹窗 -->
  <Dialog v-model="participantsDialogVisible" title="参与人员列表" width="65%">
    <el-table :data="participantsList" :stripe="true">
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="姓名" prop="name" align="center" />
      <el-table-column label="部门" prop="departmentName" align="center" />
      <el-table-column label="岗位" prop="postName" align="center" />
      <el-table-column label="参与状态" prop="participateStatus" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.participateStatus === 'attended'" type="success">已参加</el-tag>
          <el-tag v-else-if="scope.row.participateStatus === 'not-attended'" type="danger">未参加</el-tag>
          <el-tag v-else-if="scope.row.participateStatus === 'leave'" type="info">请假</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="考核评分" prop="score" align="center">
        <template #default="scope">
          <span v-if="scope.row.score">{{ scope.row.score }}分</span>
          <span v-else>未评分</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleEditParticipant(scope.row)"
          >编辑</el-button>
          <el-button
            link
            type="danger"
            @click="handleRemoveParticipant(scope.row.id)"
          >移除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </Dialog>
  
  <!-- 查看附件弹窗 -->
  <Dialog v-model="attachmentsDialogVisible" title="相关资料查看">
    <el-table :data="attachmentsList" :stripe="true">
      <el-table-column label="资料名称" prop="fileName" />
      <el-table-column label="资料类型" prop="fileType">
        <template #default="scope">
          <el-tag v-if="scope.row.fileType === 'pdf'">PDF文档</el-tag>
          <el-tag v-else-if="scope.row.fileType === 'image'" type="success">图片</el-tag>
          <el-tag v-else-if="scope.row.fileType === 'video'" type="warning">视频</el-tag>
          <el-tag v-else-if="scope.row.fileType === 'doc'" type="danger">Word文档</el-tag>
          <el-tag v-else type="info">其他</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="上传时间" prop="uploadTime" />
      <el-table-column label="上传人" prop="uploader" />
      <el-table-column label="操作" align="center" width="150">
        <template #default="scope">
          <el-button link type="primary" @click="handleViewAttachment(scope.row)">查看</el-button>
          <el-button link type="primary" @click="handleDownloadAttachment(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
  </Dialog>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import ThemeEdit from '../dialogs/ThemeEdit.vue'

/** 在岗员工主题教育记录列表 */
defineOptions({ name: 'ThemeList' })

const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const exportLoading = ref(false) // 导出的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  themeName: undefined,
  themeType: undefined,
  departmentId: undefined,
  status: undefined,
  activityTime: []
})
const queryFormRef = ref() // 搜索的表单

// 部门树形选择数据
const departmentOptions = ref([
  {
    value: 1,
    label: '公司总部',
    children: [
      {
        value: 11,
        label: '安全环保部'
      },
      {
        value: 12,
        label: '行政部'
      }
    ]
  },
  {
    value: 2,
    label: '生产部',
    children: [
      {
        value: 21,
        label: '一号车间'
      },
      {
        value: 22,
        label: '二号车间'
      },
      {
        value: 23,
        label: '三号车间'
      }
    ]
  }
])

// 查看参与人员相关
const participantsDialogVisible = ref(false)
const participantsList = ref<any[]>([])
const currentTheme = ref<any>(null)

// 查看附件相关
const attachmentsDialogVisible = ref(false)
const attachmentsList = ref<any[]>([])

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 模拟接口调用
    // const data = await ThemeApi.getThemeList(queryParams)
    // list.value = data.list
    // total.value = data.total
    
    // 模拟数据
    setTimeout(() => {
      list.value = [
        {
          id: 1,
          themeName: '安全生产月知识培训',
          themeType: 'safety-month',
          departmentId: 11,
          departmentName: '安全环保部',
          organizer: '张经理',
          startTime: '2023-06-01 09:00:00',
          endTime: '2023-06-30 18:00:00',
          participantCount: 45,
          duration: 8,
          status: 'finished',
          attachments: [
            { id: 1, fileName: '安全生产月活动方案.pdf', fileType: 'pdf', uploadTime: '2023-05-25 10:15:30', uploader: '张经理' },
            { id: 2, fileName: '安全培训PPT.pptx', fileType: 'doc', uploadTime: '2023-05-26 14:20:15', uploader: '李安全' }
          ]
        },
        {
          id: 2,
          themeName: '夏季防暑应急演练',
          themeType: 'emergency',
          departmentId: 21,
          departmentName: '一号车间',
          organizer: '王车间',
          startTime: '2023-07-15 14:00:00',
          endTime: '2023-07-15 16:30:00',
          participantCount: 18,
          duration: 2.5,
          status: 'in-progress',
          attachments: [
            { id: 3, fileName: '夏季防暑应急预案.docx', fileType: 'doc', uploadTime: '2023-07-10 09:30:45', uploader: '王车间' }
          ]
        },
        {
          id: 3,
          themeName: '焊接岗位安全技能培训',
          themeType: 'skill',
          departmentId: 23,
          departmentName: '三号车间',
          organizer: '赵师傅',
          startTime: '2023-08-10 08:30:00',
          endTime: '2023-08-12 17:00:00',
          participantCount: 12,
          duration: 16,
          status: 'not-started',
          attachments: []
        }
      ]
      total.value = 3
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('获取主题教育列表失败:', error)
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  exportLoading.value = true
  try {
    // 模拟导出操作
    setTimeout(() => {
      message.success('导出成功')
      exportLoading.value = false
    }, 1000)
  } catch (error) {
    console.error('导出失败:', error)
    exportLoading.value = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查看参与人员操作 */
const handleViewParticipants = (row: any) => {
  currentTheme.value = row
  participantsDialogVisible.value = true
  
  // 模拟获取参与人员数据
  participantsList.value = [
    { id: 1, name: '张三', departmentId: 21, departmentName: '一号车间', postName: '操作工', participateStatus: 'attended', score: 88 },
    { id: 2, name: '李四', departmentId: 21, departmentName: '一号车间', postName: '电工', participateStatus: 'attended', score: 92 },
    { id: 3, name: '王五', departmentId: 22, departmentName: '二号车间', postName: '车间管理', participateStatus: 'not-attended', score: null },
    { id: 4, name: '赵六', departmentId: 23, departmentName: '三号车间', postName: '焊工', participateStatus: 'leave', score: null }
  ]
}

/** 选择参与人员操作 */
const handleSelectParticipants = (row: any) => {
  message.success(`选择 ${row.themeName} 的参与人员`)
  // 实际实现选择参与人员的逻辑
}

/** 编辑参与人员信息 */
const handleEditParticipant = (row: any) => {
  message.success(`编辑参与人员: ${row.name}`)
  // 实际实现编辑参与人员的逻辑
}

/** 移除参与人员 */
const handleRemoveParticipant = (id: number) => {
  try {
    message.confirm('是否确认移除该参与人员?').then(() => {
      // 模拟移除操作
      const index = participantsList.value.findIndex(item => item.id === id)
      if (index !== -1) {
        participantsList.value.splice(index, 1)
        message.success('移除成功')
        
        // 更新当前主题的参与人数
        if (currentTheme.value) {
          currentTheme.value.participantCount = participantsList.value.length
          
          // 更新列表中对应的数据
          const themeIndex = list.value.findIndex(item => item.id === currentTheme.value.id)
          if (themeIndex !== -1) {
            list.value[themeIndex] = { ...currentTheme.value }
          }
        }
      }
    })
  } catch {
    // 取消移除操作
  }
}

/** 查看附件操作 */
const handleViewAttachments = (row: any) => {
  attachmentsDialogVisible.value = true
  attachmentsList.value = row.attachments || []
}

/** 查看单个附件操作 */
const handleViewAttachment = (file: any) => {
  message.success(`查看附件: ${file.fileName}`)
  // 实际实现附件预览逻辑
}

/** 下载单个附件操作 */
const handleDownloadAttachment = (file: any) => {
  message.success(`下载附件: ${file.fileName}`)
  // 实际实现附件下载逻辑
}

/** 查看详情操作 */
const handleViewDetail = (row: any) => {
  message.success(`查看 ${row.themeName} 的详情`)
  // 实际实现查看详情的逻辑
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    await message.confirm('是否确认删除该主题教育记录?')
    // 模拟删除操作
    message.success('删除成功')
    getList()
  } catch {
    // 取消删除操作
  }
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang='scss'>
.search-input {
  width: 220px;
}

.search-form {
  margin-bottom: 16px;
}
</style>