import { viteCommonjs } from '@originjs/vite-plugin-commonjs'
import { resolve } from 'path'
import type { ConfigEnv, UserConfig } from 'vite'
import { loadEnv } from 'vite'
import { createVitePlugins } from './build/vite'
import { exclude, include } from "./build/vite/optimize"
// 当前执行node命令时文件夹的地址(工作目录)
const root = process.cwd()

const now = new Date();
const timestamp = `${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}-${now.getHours().toString().padStart(2,'0')}${now.getMinutes().toString().padStart(2,'0')}`;

console.log(timestamp)
// 路径查找
function pathResolve(dir: string) {
    return resolve(root, '.', dir)
}

// https://vitejs.dev/config/
export default ({command, mode}: ConfigEnv): UserConfig => {
    let env = {} as any
    const isBuild = command === 'build'
    if (!isBuild) {
        env = loadEnv((process.argv[3] === '--mode' ? process.argv[4] : process.argv[3]), root)
    } else {
        env = loadEnv(mode, root)
    }

    const baseOutDir = env.VITE_OUT_DIR || 'dist';
    return {
        base: env.VITE_BASE_PATH,
        root: root,
        // 服务端渲染
        server: {
            port: env.VITE_PORT, // 端口号
            host: "0.0.0.0",
            open: env.VITE_OPEN === 'true' ? '/factory/login' : false,
            proxy: {
                '/factory/admin-api/': {
                    target: env.VITE_PROXY_CORE_API_TARGET,
                    changeOrigin: true,
                    ...(env.VITE_PROXY_CORE_API_REWRITE === 'true'
                        ? { rewrite: path => path.replace(/^\/factory/, '') }
                        : {})
                  },
                  '/factory/process-svg/': {
                    target: env.VITE_PROXY_SVG_TARGET,
                    changeOrigin: true,
                    ...(env.VITE_PROXY_CORE_API_REWRITE === 'true'
                        ? { rewrite: path => path.replace(/^\/factory/, '') }
                        : {})
                  },
                // 前端请求写成 /evo-apigw/... 会被代理到目标地址
                '/factory/evo-apigw': {
                  target: env.VITE_PROXY_APIGW_TARGET, 
                  changeOrigin: true,
                  secure: false, // 如果是自签名证书一定要加
                  rewrite: (path) => path.replace(/^\/evo-apigw/, '/evo-apigw'),
                }
              },
        },
        // 项目使用的vite插件。 单独提取到build/vite/plugin中管理
        plugins: [createVitePlugins(), viteCommonjs()],
        css: {
            preprocessorOptions: {
                scss: {
                    additionalData: '@use "@/styles/variables.scss" as *;',
                    javascriptEnabled: true,
                    silenceDeprecations: ["legacy-js-api"], // 参考自 https://stackoverflow.com/questions/78997907/the-legacy-js-api-is-deprecated-and-will-be-removed-in-dart-sass-2-0-0
                }
            }
        },
        resolve: {
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'],
            alias: [
                {
                    find: 'vue-i18n',
                    replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
                },
                {
                    find: /\@\//,
                    replacement: `${pathResolve('src')}/`
                }
            ]
        },
        build: {
            minify: 'terser',
            outDir: `${baseOutDir}-${timestamp}`,
            sourcemap: env.VITE_SOURCEMAP === 'true' ? 'inline' : false,
            // brotliSize: false,
            terserOptions: {
                compress: {
                    drop_debugger: env.VITE_DROP_DEBUGGER === 'true',
                    drop_console: env.VITE_DROP_CONSOLE === 'true'
                }
            },
            rollupOptions: {
                output: {
                    manualChunks(id) {
                        if (id.includes('node_modules')) {
                            const pkgName = id.toString().split('node_modules/')[1].split('/')[0];
                            return `vendor-${pkgName}`;
                        }
                    },
                    // 添加时间戳到文件名中
                    entryFileNames: `assets/[name]-[hash].js`,
                    chunkFileNames: `assets/[name]-[hash].js`,
                    assetFileNames: `assets/[name]-[hash].[ext]`
                },
            },
        },
        optimizeDeps: {include, exclude}
    }
}
