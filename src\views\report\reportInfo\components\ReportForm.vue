<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="报表名称" prop="reportName">
        <el-input
          v-model="formData.reportName"
          placeholder="请输入名称"
          @input="generateReportCode"
          clearable
        />
      </el-form-item>
      <el-form-item label="报表编码" prop="reportCode">
        <el-input
          v-model="formData.reportCode"
          placeholder="请输入报表编码"
          :disabled="!formData.reportName"
          clearable
        >
          <template #append>
            <el-tooltip content="点击重新生成编码" placement="top">
              <el-button @click="generateReportCode">
                <Icon icon="ep:refresh" />
              </el-button>
            </el-tooltip>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="报表类型" prop="reportType">
        <el-select v-model="formData.reportType" placeholder="请选择报表类型" clearable>
          <el-option :label="'普通报表'" :value="ReportType.NORMAL">
            <template #default>
              <div class="option-item">
                <span>普通报表</span>
                <el-tag size="small" type="info">基础报表</el-tag>
              </div>
            </template>
          </el-option>
          <el-option :label="'填报报表'" :value="ReportType.FILL">
            <template #default>
              <div class="option-item">
                <span>填报报表</span>
                <el-tag size="small" type="warning">支持数据填报</el-tag>
              </div>
            </template>
          </el-option>
          <el-option :label="'协同报表'" :value="ReportType.COLLABORATIVE">
            <template #default>
              <div class="option-item">
                <span>协同报表</span>
                <el-tag size="small" type="success">多人协作</el-tag>
              </div>
            </template>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="formData.reportType === ReportType.FILL" label="开启审核" prop="enableAudit">
        <el-switch
          v-model="formData.enableAudit"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      <el-form-item label="报表描述" prop="reportDesc">
        <el-input
          v-model="formData.reportDesc"
          type="textarea"
          :rows="3"
          placeholder="请输入报表描述"
          show-word-limit
          maxlength="200"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ReportApi, ReportVO } from '@/api/report/reportInfo/index'
import { pinyin } from 'pinyin-pro'
import { generateUniqueNumericId } from '@/utils'

/** 报表信息 表单 */
defineOptions({ name: 'ReportForm' })

// 定义报表类型枚举
enum ReportType {
  NORMAL = 1, // 普通报表
  FILL = 2,   // 填报报表
  COLLABORATIVE = 3 // 协同报表
}

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const uuid = ref(generateUniqueNumericId())

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

// 定义表单数据类型
interface FormData {
  id?: number
  reportName?: string
  reportCode?: string
  reportType?: ReportType
  reportDesc?: string
  enableAudit?: boolean
}

const formData = ref<FormData>({
  id: undefined,
  reportName: '',
  reportCode: '',
  reportType: undefined,
  reportDesc: '',
  enableAudit: false
})

const formRules = reactive({
  reportName: [
    { required: true, message: '请输入报表名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  reportCode: [
    { required: true, message: '请输入报表编码', trigger: 'blur' },
    { pattern: /^[a-z][a-z0-9_]*$/, message: '编码必须以字母开头，只能包含小写字母、数字和下划线', trigger: 'blur' }
  ],
  reportType: [{ required: true, message: '请选择报表类型', trigger: 'change' }],
  reportDesc: [
    { required: true, message: '请输入报表描述', trigger: 'blur' },
    { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
  ]
})

const formRef = ref() // 表单 Ref

/** 自动生成报表编码 */
const generateReportCode = () => {
  if (!formData.value.reportName) return

  try {
    // 使用 pinyin-pro 将中文转换为拼音
    const pinyinResult = pinyin(formData.value.reportName, {
      toneType: 'none', // 不带声调
      type: 'array' // 返回数组格式
    })

    // 将拼音数组转换为字符串
    let code = pinyinResult
      .join('_') // 用下划线连接
      .toLowerCase()
      // 移除特殊字符，只保留字母、数字和下划线
      .replace(/[^a-z0-9_]/g, '_')
      // 移除连续的下划线
      .replace(/_+/g, '_')
      // 移除开头和结尾的下划线
      .replace(/^_+|_+$/g, '')

    // 确保编码以字母开头
    if (!/^[a-z]/.test(code)) {
      code = 'report_' + code
    }

    // 确保编码长度不超过50个字符
    if (code.length > 50) {
      code = code.substring(0, 50)
    }

    formData.value.reportCode = code
  } catch (error) {
    console.error('生成报表编码失败:', error)
    message.error('生成报表编码失败，请手动输入')
  }
}

/** 打开弹窗 */
const open = async (type: string, reportCode?: string) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (reportCode) {
    formLoading.value = true
    try {
      const data = await ReportApi.getReportByReportCode(reportCode)
      // 只保留需要的字段
      formData.value = {
        id: data.id,
        reportName: data.reportName,
        reportCode: data.reportCode,
        reportType: Number(data.reportType) as ReportType,
        reportDesc: data.reportDesc,
        enableAudit: data.enableAudit
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  if (formType.value === 'create') {
    formData.value.id = uuid.value as unknown as number
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ReportVO
    if (formType.value === 'create') {
      await ReportApi.createReport(data)
      message.success(t('common.createSuccess'))
    } else {
      await ReportApi.updateReport(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    reportName: '',
    reportCode: '',
    reportType: undefined,
    reportDesc: '',
    enableAudit: false
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.option-item .el-tag {
  margin-left: 8px;
}
</style>
