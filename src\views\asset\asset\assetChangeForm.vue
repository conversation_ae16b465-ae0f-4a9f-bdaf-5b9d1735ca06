<template>
  <ContentWrap>
    <!-- 原资产信息 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>原资产信息</span>
        </div>
      </template>
      <el-form
        ref="originalFormRef"
        :model="originalData"
        label-width="120px"
        disabled
      >
        <el-form-item label="资产编码">
          <el-input v-model="originalData.assetCode" readonly />
        </el-form-item>
        <el-form-item label="资产名称">
          <el-input v-model="originalData.assetName" readonly />
        </el-form-item>
        <el-form-item label="资产类型">
          <el-select v-model="originalData.assetTypeId" readonly>
            <el-option
              v-for="item in assetTypeList"
              :key="item.id"
              :label="item.typeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="资产状态">
          <el-select v-model="originalData.statusId" readonly>
            <el-option
              v-for="item in assetStatusList"
              :key="item.id"
              :label="item.statusName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="固定资产">
          <el-radio-group v-model="originalData.isFixed" disabled>
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="使用部门">
          <el-input v-model="originalData.usageDeptId" readonly />
        </el-form-item>
        <el-form-item label="使用人">
          <el-input v-model="originalData.usageUserId" readonly />
        </el-form-item>
        <el-form-item label="存放地点">
          <el-input v-model="originalData.location" readonly />
        </el-form-item>
        <el-form-item label="购置日期">
          <el-date-picker
            v-model="originalData.purchaseDate"
            type="date"
            readonly
            disabled
          />
        </el-form-item>
        <el-form-item label="资产原值">
          <el-input v-model="originalData.originalValue" readonly />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 变更信息 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>变更信息</span>
        </div>
      </template>
      <el-form
        ref="changeFormRef"
        :model="changeData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="资产编码" prop="assetCode">
          <el-input v-model="changeData.assetCode" placeholder="请输入资产编码" />
        </el-form-item>
        <el-form-item label="资产名称" prop="assetName">
          <el-input v-model="changeData.assetName" placeholder="请输入资产名称" />
        </el-form-item>
        <el-form-item label="资产类型" prop="assetTypeId">
          <el-select v-model="changeData.assetTypeId" placeholder="请选择资产类型">
            <el-option
              v-for="item in assetTypeList"
              :key="item.id"
              :label="item.typeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="资产状态" prop="statusId">
          <el-select v-model="changeData.statusId" placeholder="请选择资产状态">
            <el-option
              v-for="item in assetStatusList"
              :key="item.id"
              :label="item.statusName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="固定资产" prop="isFixed">
          <el-radio-group v-model="changeData.isFixed">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="使用部门" prop="usageDeptId">
          <el-input v-model="changeData.usageDeptId" placeholder="请选择使用部门" />
        </el-form-item>
        <el-form-item label="使用人" prop="usageUserId">
          <el-input v-model="changeData.usageUserId" placeholder="请选择使用人" />
        </el-form-item>
        <el-form-item label="存放地点" prop="location">
          <el-input v-model="changeData.location" placeholder="请输入存放地点" />
        </el-form-item>
        <el-form-item label="购置日期" prop="purchaseDate">
          <el-date-picker
            v-model="changeData.purchaseDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择购置日期"
          />
        </el-form-item>
        <el-form-item label="资产原值" prop="originalValue">
          <el-input v-model="changeData.originalValue" placeholder="请输入资产原值/采购金额" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="changeData.remark"
            type="textarea"
            placeholder="请输入变更原因及说明"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <div class="flex justify-center">
        <el-button type="primary" @click="submitForm">提交变更</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { AssetApi, AssetVO } from '@/api/asset/asset'
import { AssetTypeApi, AssetTypeVO } from '@/api/asset/type'
import { AssetStatusApi, AssetStatusVO } from '@/api/asset/status'
import { useRoute, useRouter } from 'vue-router'

const message = useMessage() // 消息弹窗
const route = useRoute()
const router = useRouter()

const loading = ref(false)
const assetTypeList = ref<AssetTypeVO[]>([]) // 资产类型列表
const assetStatusList = ref<AssetStatusVO[]>([]) // 资产状态列表

// 原始资产数据
const originalData = ref<Partial<AssetVO>>({})
// 变更后的资产数据
const changeData = ref<Partial<AssetVO>>({})

// 表单校验规则 - 全部设为非必填
const formRules = reactive({
  assetCode: [{ required: false }],
  assetName: [{ required: false }],
  assetTypeId: [{ required: false }],
  statusId: [{ required: false }],
  isFixed: [{ required: false }],
  usageDeptId: [{ required: false }],
  usageUserId: [{ required: false }],
  location: [{ required: false }],
  purchaseDate: [{ required: false }],
  originalValue: [{ required: false }],
  remark: [{ required: false }]
})

const changeFormRef = ref()

// 获取资产类型列表
const getAssetTypeList = async () => {
  const res = await AssetTypeApi.getAssetTypeList({})
  assetTypeList.value = res
}

// 获取资产状态列表
const getAssetStatusList = async () => {
  const res = await AssetStatusApi.getAssetStatusList({})
  assetStatusList.value = res
}

// 获取原始资产信息
const getOriginalAsset = async (id: number) => {
  loading.value = true
  try {
    const res = await AssetApi.getAsset(id)
    originalData.value = res
    // 复制原始数据到变更表单
    changeData.value = { ...res }
  } catch (error) {
    message.error('获取资产信息失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  await changeFormRef.value?.validate()
  try {
    // TODO: 调用资产变更API
    message.success('提交成功')
    goBack()
  } catch (error) {
    message.error('提交失败')
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 初始化
onMounted(async () => {
  const assetId = Number(route.query.assetId)
  if (assetId) {
    await Promise.all([
      getAssetTypeList(),
      getAssetStatusList(),
      getOriginalAsset(assetId)
    ])
  } else {
    message.error('资产ID不能为空')
    goBack()
  }
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>