<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="briefing-form"
      :disabled="isView"
    >
      <el-form-item label="简报标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入简报标题" />
      </el-form-item>
      
      <el-form-item label="报告日期" prop="reportDate">
        <el-date-picker
          v-model="form.reportDate"
          type="date"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      
      <el-form-item label="报告人" prop="reporter">
        <el-input v-model="form.reporter" placeholder="请输入报告人" />
      </el-form-item>
      
      <el-form-item label="所属部门" prop="department">
        <el-select v-model="form.department" placeholder="请选择部门">
          <el-option
            v-for="item in departmentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-divider content-position="center">工作概况</el-divider>
      
      <el-form-item label="安全检查情况" prop="safetyInspection">
        <el-input
          v-model="form.safetyInspection"
          type="textarea"
          :rows="3"
          placeholder="请输入安全检查情况"
        />
      </el-form-item>
      
      <el-form-item label="隐患整改情况" prop="hazardRectification">
        <el-input
          v-model="form.hazardRectification"
          type="textarea"
          :rows="3"
          placeholder="请输入隐患整改情况"
        />
      </el-form-item>
      
      <el-form-item label="安全培训情况" prop="safetyTraining">
        <el-input
          v-model="form.safetyTraining"
          type="textarea"
          :rows="3"
          placeholder="请输入安全培训情况"
        />
      </el-form-item>
      
      <el-form-item label="事故分析情况" prop="accidentAnalysis">
        <el-input
          v-model="form.accidentAnalysis"
          type="textarea"
          :rows="3"
          placeholder="请输入事故分析情况"
        />
      </el-form-item>
      
      <el-divider content-position="center">工作计划</el-divider>
      
      <el-form-item label="下期工作计划" prop="workPlan">
        <el-input
          v-model="form.workPlan"
          type="textarea"
          :rows="3"
          placeholder="请输入下期工作计划"
        />
      </el-form-item>
      
      <el-form-item label="注意事项" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入注意事项"
        />
      </el-form-item>
      
      <el-form-item v-if="!isView" label="附件" prop="attachments">
        <el-upload
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          multiple
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持上传任意格式文件，单个文件不超过10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
      
      <el-form-item v-if="isView && form.status === 'rejected'" label="退回原因">
        <el-input
          v-model="form.rejectReason"
          type="textarea"
          :rows="3"
          disabled
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ isView ? '关闭' : '取消' }}</el-button>
        <template v-if="!isView">
          <el-button type="primary" @click="submitForm">确定</el-button>
        </template>
        <template v-else>
          <el-button v-if="form.status === 'approved'" type="primary" @click="handlePrint">打印</el-button>
          <el-button type="success" @click="handleDownload">下载</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import type { FormInstance, FormRules, UploadUserFile } from 'element-plus'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'add' // add, edit, view
  },
  briefingData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.type) {
    case 'add':
      return '新增简报'
    case 'edit':
      return '编辑简报'
    case 'view':
      return '简报详情'
    default:
      return '简报'
  }
})

// 是否是查看模式
const isView = computed(() => props.type === 'view')

const formRef = ref<FormInstance>()
const fileList = ref<UploadUserFile[]>([])

// 部门选项
const departmentOptions = [
  { label: '安全管理部', value: 'safety' },
  { label: '生产部', value: 'production' },
  { label: '工程部', value: 'engineering' },
  { label: '质量部', value: 'quality' }
]

// 表单数据
const form = reactive({
  id: '',
  title: '',
  reportDate: '',
  reporter: '',
  department: '',
  safetyInspection: '',
  hazardRectification: '',
  safetyTraining: '',
  accidentAnalysis: '',
  workPlan: '',
  notes: '',
  attachments: [] as Array<{name: string, url: string}>,
  status: '',
  rejectReason: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入简报标题', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  reportDate: [
    { required: true, message: '请选择报告日期', trigger: 'change' }
  ],
  reporter: [
    { required: true, message: '请输入报告人', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  safetyInspection: [
    { required: true, message: '请输入安全检查情况', trigger: 'blur' }
  ],
  hazardRectification: [
    { required: true, message: '请输入隐患整改情况', trigger: 'blur' }
  ]
})

// 监听数据变化，初始化表单
watch(() => props.briefingData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(form).forEach(key => {
      if (key in newVal) {
        form[key] = newVal[key]
      }
    })
    
    // 初始化附件列表
    if (newVal.attachments && Array.isArray(newVal.attachments)) {
      fileList.value = newVal.attachments.map((item: any) => ({
        name: item.name,
        url: item.url
      }))
    } else {
      fileList.value = []
    }
  }
}, { deep: true, immediate: true })

// 处理文件变更
const handleFileChange = (uploadFile: UploadUserFile) => {
  fileList.value.push(uploadFile)
}

// 提交表单
const submitForm = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      // 收集附件信息
      form.attachments = fileList.value.map(file => ({
        name: file.name,
        url: file.url || ''
      }))
      
      // 模拟提交请求
      setTimeout(() => {
        ElMessage.success(props.type === 'add' ? '新增成功' : '修改成功')
        dialogVisible.value = false
        emit('success', form)
      }, 500)
    } else {
      ElMessage.error('表单填写有误，请检查')
    }
  })
}

// 处理对话框关闭
const handleClosed = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  fileList.value = []
}

// 打印
const handlePrint = () => {
  ElMessage.success(`正在打印简报：${form.title}`)
  // 实际应用中，这里可以调用打印功能
}

// 下载
const handleDownload = () => {
  ElMessage.success(`正在下载简报：${form.title}`)
  // 实际应用中，这里可以调用文件下载接口
}
</script>

<style lang="scss" scoped>
.briefing-form {
  max-width: 100%;
}
</style> 