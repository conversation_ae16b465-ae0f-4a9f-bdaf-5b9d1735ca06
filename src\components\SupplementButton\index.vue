<template>
  <el-button v-if="showButton" v-hasPermi="['report:prod-quality-data:submit']" type="warning" @click="$emit('click')">
    补录
  </el-button>
</template>

<script setup lang="ts">
import { getConfigByReportType } from '@/api/report/supplementConfig'
import { onMounted, ref } from 'vue'

const props = defineProps({
  reportType: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['click'])
const showButton = ref(false)

const checkSupplementConfig = async () => {
  try {
    // 获取指定报表类型的补录配置
    const config = await getConfigByReportType(props.reportType)

    // 如果配置不存在，不显示按钮
    if (!config) {
      showButton.value = false
      return
    }

    // 检查配置是否启用
    if (!config.enabled) {
      showButton.value = false
      return
    }

    // 检查当前时间是否在允许的操作时间范围内
    const now = new Date().getTime()
    if (config.operationStartTime && config.operationEndTime) {
      showButton.value = now >= config.operationStartTime && now <= config.operationEndTime
    } else {
      showButton.value = false
    }
  } catch (error) {
    console.error('获取补录配置失败:', error)
    showButton.value = false
  }
}

onMounted(() => {
  checkSupplementConfig()
})
</script>