<template>
  <Dialog v-model="dialogVisible" title="数据复检">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="样品编号">{{ originalData.sampleCode }}</el-descriptions-item>
        <el-descriptions-item label="检测项目">{{ originalData.testItem }}</el-descriptions-item>
        <el-descriptions-item label="采样点">{{ originalData.samplingPoint }}</el-descriptions-item>
        <el-descriptions-item label="原检测值">
          <span :class="{ 'text-danger': isOriginalDataExceeded, 'text-warning': isOriginalDataWarning }">
            {{ originalData.testValue }} {{ originalData.unit }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="原检测日期">{{ originalData.testDate }}</el-descriptions-item>
        <el-descriptions-item label="原检测人">{{ originalData.tester }}</el-descriptions-item>
      </el-descriptions>
      
      <div v-if="showStandardInfo" class="standard-info mt-4 mb-4">
        <el-alert
          title="标准限值信息"
          :type="isOriginalDataExceeded ? 'error' : isOriginalDataWarning ? 'warning' : 'info'"
          :description="standardDescription"
          show-icon
          :closable="false"
        />
      </div>
      
      <el-divider content-position="center">复检信息</el-divider>
      
      <el-form-item label="复检原因" prop="retestReason">
        <el-select v-model="formData.retestReason" placeholder="请选择复检原因">
          <el-option label="数据异常" value="dataAbnormal" />
          <el-option label="超标复核" value="exceedVerify" />
          <el-option label="仪器故障" value="instrumentFault" />
          <el-option label="操作失误" value="operationError" />
          <el-option label="质控需要" value="qualityControl" />
          <el-option label="其他原因" value="other" />
        </el-select>
      </el-form-item>
      <el-form-item label="复检值" prop="retestValue">
        <el-input v-model="formData.retestValue" placeholder="请输入复检值">
          <template #append>{{ originalData.unit }}</template>
        </el-input>
      </el-form-item>
      <el-form-item label="复检日期" prop="retestDate">
        <el-date-picker v-model="formData.retestDate" type="date" placeholder="请选择复检日期" />
      </el-form-item>
      <el-form-item label="复检人" prop="retester">
        <el-select v-model="formData.retester" placeholder="请选择复检人">
          <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="检测方法" prop="method">
        <el-select v-model="formData.method" placeholder="请选择检测方法">
          <el-option v-for="method in methodOptions" :key="method.id" :label="method.name" :value="method.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="检测仪器" prop="instrument">
        <el-select v-model="formData.instrument" placeholder="请选择检测仪器">
          <el-option v-for="instrument in instrumentOptions" :key="instrument.id" :label="instrument.name" :value="instrument.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注说明" prop="remarks">
        <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注说明" />
      </el-form-item>
      
      <div v-if="showComparisonResult" class="comparison-result mt-4">
        <el-alert
          :title="comparisonTitle"
          :type="comparisonResultType"
          :description="comparisonDescription"
          show-icon
          :closable="false"
        />
      </div>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">提交复检</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

defineOptions({ name: 'RetestDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)

// 用户选项（模拟数据）
const userOptions = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' },
  { id: 5, name: '钱七' }
]

// 检测方法选项（模拟数据）
const methodOptions = [
  { id: 1, name: '重铬酸钾法' },
  { id: 2, name: '稀释接种法' },
  { id: 3, name: '纳氏试剂比色法' },
  { id: 4, name: '钼酸铵分光光度法' },
  { id: 5, name: '碱性过硫酸钾消解紫外分光光度法' }
]

// 检测仪器选项（模拟数据）
const instrumentOptions = [
  { id: 1, name: '分光光度计A' },
  { id: 2, name: '分光光度计B' },
  { id: 3, name: 'BOD测定仪' },
  { id: 4, name: '紫外分光光度计' },
  { id: 5, name: '原子吸收光谱仪' }
]

// 原始数据
const originalData = ref({
  id: undefined as number | undefined,
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  testValue: '',
  unit: '',
  testDate: '',
  tester: '',
  method: '',
  instrument: '',
  standard: null as { min: number; max: number; unit: string } | null
})

// 表单数据
const formData = ref({
  retestReason: '',
  retestValue: '',
  retestDate: new Date(),
  retester: '',
  method: '',
  instrument: '',
  remarks: ''
})

// 表单校验规则
const formRules = reactive<FormRules>({
  retestReason: [{ required: true, message: '请选择复检原因', trigger: 'change' }],
  retestValue: [
    { required: true, message: '请输入复检值', trigger: 'blur' },
    { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '复检值必须为数字', trigger: 'blur' }
  ],
  retestDate: [{ required: true, message: '请选择复检日期', trigger: 'change' }],
  retester: [{ required: true, message: '请选择复检人', trigger: 'change' }],
  method: [{ required: true, message: '请选择检测方法', trigger: 'change' }],
  instrument: [{ required: true, message: '请选择检测仪器', trigger: 'change' }]
})

const formRef = ref<FormInstance>()
const emit = defineEmits(['success'])

// 计算原始数据是否超标或警告
const isOriginalDataExceeded = computed(() => {
  if (!originalData.value.standard) return false
  return parseFloat(originalData.value.testValue) > originalData.value.standard.max
})

const isOriginalDataWarning = computed(() => {
  if (!originalData.value.standard) return false
  const value = parseFloat(originalData.value.testValue)
  const max = originalData.value.standard.max
  // 警告阈值设为标准值的80%
  return value > max * 0.8 && value <= max
})

// 显示标准信息
const showStandardInfo = computed(() => {
  return originalData.value.standard !== null
})

// 标准信息描述
const standardDescription = computed(() => {
  if (!originalData.value.standard) return ''
  const { min, max, unit } = originalData.value.standard
  return `标准范围：${min} - ${max} ${unit}`
})

// 计算复检值是否超标或警告
const isRetestValueExceeded = computed(() => {
  if (!originalData.value.standard || !formData.value.retestValue) return false
  return parseFloat(formData.value.retestValue) > originalData.value.standard.max
})

const isRetestValueWarning = computed(() => {
  if (!originalData.value.standard || !formData.value.retestValue) return false
  const value = parseFloat(formData.value.retestValue)
  const max = originalData.value.standard.max
  // 警告阈值设为标准值的80%
  return value > max * 0.8 && value <= max
})

// 显示对比结果
const showComparisonResult = computed(() => {
  return formData.value.retestValue !== ''
})

// 对比结果类型
const comparisonResultType = computed(() => {
  if (isRetestValueExceeded.value) {
    return 'error'
  } else if (isRetestValueWarning.value) {
    return 'warning'
  } else {
    return 'success'
  }
})

// 对比结果标题
const comparisonTitle = computed(() => {
  if (!formData.value.retestValue) return ''
  
  const originalValue = parseFloat(originalData.value.testValue)
  const retestValue = parseFloat(formData.value.retestValue)
  const diff = Math.abs(retestValue - originalValue)
  const diffPercent = (diff / originalValue * 100).toFixed(2)
  
  if (retestValue < originalValue) {
    return `复检值较原值降低了${diffPercent}%`
  } else if (retestValue > originalValue) {
    return `复检值较原值升高了${diffPercent}%`
  } else {
    return '复检值与原值相同'
  }
})

// 对比结果描述
const comparisonDescription = computed(() => {
  if (!formData.value.retestValue) return ''
  
  const originalValue = parseFloat(originalData.value.testValue)
  const retestValue = parseFloat(formData.value.retestValue)
  
  if (isOriginalDataExceeded.value && !isRetestValueExceeded.value) {
    return '复检结果正常，原检测数据可能存在问题'
  } else if (!isOriginalDataExceeded.value && isRetestValueExceeded.value) {
    return '复检结果超标，请注意处理'
  } else if (isOriginalDataExceeded.value && isRetestValueExceeded.value) {
    return '原检测和复检均显示超标，请及时处理'
  } else {
    return `原值: ${originalValue} ${originalData.value.unit}, 复检值: ${retestValue} ${originalData.value.unit}`
  }
})

// 获取检测项目的标准范围
const getTestItemStandard = (testItem: string) => {
  // 模拟从配置或API获取标准值
  switch (testItem) {
    case 'COD':
      return { min: 0, max: 50, unit: 'mg/L' }
    case 'BOD5':
      return { min: 0, max: 10, unit: 'mg/L' }
    case '氨氮':
    case 'NH3-N':
      return { min: 0, max: 5, unit: 'mg/L' }
    case '总磷':
    case 'TP':
      return { min: 0, max: 0.5, unit: 'mg/L' }
    case '总氮':
    case 'TN':
      return { min: 0, max: 15, unit: 'mg/L' }
    default:
      return null
  }
}

// 打开对话框
const open = async (data: any) => {
  if (!data) return
  
  dialogVisible.value = true
  
  // 设置原始数据
  originalData.value = {
    id: data.id,
    sampleCode: data.sampleCode,
    testItem: data.testItem,
    samplingPoint: data.samplingPoint,
    testValue: data.testValue,
    unit: data.unit || 'mg/L',
    testDate: data.testDate || '',
    tester: data.tester || '',
    method: data.method || '重铬酸钾法',
    instrument: data.instrument || '分光光度计A',
    standard: getTestItemStandard(data.testItem)
  }
  
  // 重置表单数据
  formData.value = {
    retestReason: data.dataStatus === 'exceed' ? 'exceedVerify' : 'dataAbnormal',
    retestValue: '',
    retestDate: new Date(),
    retester: '',
    method: data.method || '重铬酸钾法',
    instrument: data.instrument || '分光光度计A',
    remarks: ''
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('复检数据提交成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script>

<style scoped>
.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.standard-info,
.comparison-result {
  margin-bottom: 1rem;
}
</style> 