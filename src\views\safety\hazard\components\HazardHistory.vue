<template>
  <div class="hazard-history">
    <el-card class="filter-card">
      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="隐患类型">
          <el-cascader
            v-model="searchForm.category"
            :options="categoryOptions"
            :props="{ checkStrictly: true }"
            clearable
          />
        </el-form-item>
        <el-form-item label="责任部门">
          <el-select v-model="searchForm.department" placeholder="请选择" clearable>
            <el-option label="生产部" value="production" />
            <el-option label="安全部" value="safety" />
            <el-option label="设备部" value="equipment" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="全部" value="" />
            <el-option label="已完成" value="completed" />
            <el-option label="未完成" value="incomplete" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-card">
      <el-table :data="historyList" style="width: 100%">
        <el-table-column type="expand">
          <template #default="{ row }">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in row.activities"
                :key="index"
                :timestamp="activity.time"
                :type="activity.type"
              >
                {{ activity.content }}
              </el-timeline-item>
            </el-timeline>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="隐患编号" width="120" />
        <el-table-column prop="name" label="隐患名称" width="180" />
        <el-table-column prop="category" label="隐患类型" width="150" />
        <el-table-column prop="department" label="责任部门" width="120" />
        <el-table-column prop="discoveryTime" label="发现时间" width="180" />
        <el-table-column prop="completionTime" label="完成时间" width="180" />
        <el-table-column prop="duration" label="处理时长" width="120" />
        <el-table-column prop="status" label="处理状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === '已完成' ? 'success' : 'warning'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看详情</el-button>
            <el-button link type="success" @click="handleExportSingle(row)">导出</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const searchForm = reactive({
  dateRange: [],
  category: [],
  department: '',
  status: ''
})

const categoryOptions = [
  {
    value: 'equipment',
    label: '设备类',
    children: [
      { value: 'mechanical', label: '机械设备' },
      { value: 'electrical', label: '电气设备' },
      { value: 'special', label: '特种设备' }
    ]
  },
  {
    value: 'environment',
    label: '环境类',
    children: [
      { value: 'workplace', label: '作业环境' },
      { value: 'fire', label: '消防安全' },
      { value: 'health', label: '职业卫生' }
    ]
  }
]

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 模拟数据
const historyList = ref([
  {
    code: 'HZD001',
    name: '设备防护装置缺失',
    category: '机械设备',
    department: '生产部',
    discoveryTime: '2024-03-01 10:00:00',
    completionTime: '2024-03-15 15:30:00',
    duration: '14天5小时30分',
    status: '已完成',
    activities: [
      {
        time: '2024-03-01 10:00:00',
        content: '发现隐患并登记',
        type: 'warning'
      },
      {
        time: '2024-03-02 09:00:00',
        content: '制定整改方案',
        type: 'primary'
      },
      {
        time: '2024-03-10 14:00:00',
        content: '整改实施中',
        type: 'primary'
      },
      {
        time: '2024-03-15 15:30:00',
        content: '验收完成',
        type: 'success'
      }
    ]
  }
])

const handleSearch = () => {
  // TODO: 实现搜索逻辑
  console.log('搜索条件：', searchForm)
}

const handleReset = () => {
  Object.assign(searchForm, {
    dateRange: [],
    category: [],
    department: '',
    status: ''
  })
}

const handleExport = () => {
  // TODO: 实现批量导出逻辑
  ElMessage.success('导出成功')
}

const handleView = (row: any) => {
  // TODO: 打开查看详情弹窗
}

const handleExportSingle = (row: any) => {
  // TODO: 导出单条记录
  ElMessage.success(`导出记录：${row.code}`)
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  // TODO: 重新加载数据
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // TODO: 重新加载数据
}
</script>

<style lang="scss" scoped>
.hazard-history {
  .filter-card {
    margin-bottom: 16px;
  }

  .table-card {
    .pagination {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }

  :deep(.el-timeline) {
    padding: 20px;
  }
}
</style> 