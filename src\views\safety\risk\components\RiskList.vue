<template>
  <div class="risk-list">
    <!-- 搜索表单 -->
    <el-form :model="searchForm" inline class="search-form">
      <el-form-item label="作业类型">
        <el-select v-model="searchForm.workType" placeholder="请选择作业类型" clearable>
          <el-option
            v-for="item in workTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="风险等级">
        <el-select v-model="searchForm.riskLevel" placeholder="请选择风险等级" clearable>
          <el-option label="极高风险" value="极高风险" />
          <el-option label="高风险" value="高风险" />
          <el-option label="中度风险" value="中度风险" />
          <el-option label="低风险" value="低风险" />
        </el-select>
      </el-form-item>
      <el-form-item label="作业地点">
        <el-input v-model="searchForm.location" placeholder="请输入作业地点" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 风险清单表格 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="workType" label="作业类型" width="120">
        <template #default="{ row }">
          {{ getWorkTypeLabel(row.workType) }}
        </template>
      </el-table-column>
      <el-table-column prop="location" label="作业地点" width="150" />
      <el-table-column prop="accidentType" label="事故类型" width="120">
        <template #default="{ row }">
          {{ getAccidentTypeLabel(row.accidentType) }}
        </template>
      </el-table-column>
      <el-table-column prop="riskLevel" label="风险等级" width="100">
        <template #default="{ row }">
          <el-tag :type="getRiskLevelType(row.riskLevel)">{{ row.riskLevel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="riskValue" label="风险值" width="100" align="center" />
      <el-table-column prop="description" label="作业内容" min-width="200" show-overflow-tooltip />
      <el-table-column prop="controls" label="管控措施" min-width="200" show-overflow-tooltip />
      <el-table-column prop="assessmentTime" label="评估时间" width="160" />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleDetail(row)">详情</el-button>
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情弹窗 -->
    <risk-detail-dialog
      v-model="showDetailDialog"
      :risk-data="currentRisk"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script>
import RiskDetailDialog from '../dialogs/RiskDetailDialog.vue'

export default {
  name: 'RiskList',
  components: {
    RiskDetailDialog
  },
  data() {
    return {
      searchForm: {
        workType: '',
        riskLevel: '',
        location: ''
      },
      workTypeOptions: [
        { value: 'height', label: '高空作业' },
        { value: 'confined', label: '受限空间作业' },
        { value: 'hot', label: '动火作业' },
        { value: 'lifting', label: '吊装作业' },
        { value: 'electrical', label: '电气作业' }
      ],
      accidentTypeOptions: [
        { value: 'fall', label: '高处坠落' },
        { value: 'collapse', label: '坍塌' },
        { value: 'electric', label: '触电' },
        { value: 'fire', label: '火灾' },
        { value: 'mechanical', label: '机械伤害' }
      ],
      tableData: [
        {
          id: '1',
          workType: 'height',
          location: 'A区施工现场',
          accidentType: 'fall',
          riskLevel: '高风险',
          riskValue: 360,
          description: '外墙清洗作业',
          controls: '1. 正确使用安全带\n2. 设置安全网\n3. 专人监护',
          assessmentTime: '2024-04-21 10:00:00'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 100,
      showDetailDialog: false,
      currentRisk: null
    }
  },
  methods: {
    getWorkTypeLabel(value) {
      const option = this.workTypeOptions.find(item => item.value === value)
      return option ? option.label : value
    },
    getAccidentTypeLabel(value) {
      const option = this.accidentTypeOptions.find(item => item.value === value)
      return option ? option.label : value
    },
    getRiskLevelType(level) {
      const types = {
        '极高风险': 'danger',
        '高风险': 'warning',
        '中度风险': 'warning',
        '低风险': 'success'
      }
      return types[level] || 'info'
    },
    handleSearch() {
      // TODO: 实现搜索逻辑
      console.log('搜索条件：', this.searchForm)
    },
    resetSearch() {
      this.searchForm = {
        workType: '',
        riskLevel: '',
        location: ''
      }
      this.handleSearch()
    },
    handleDetail(row) {
      this.currentRisk = row
      this.showDetailDialog = true
    },
    handleEdit(row) {
      this.currentRisk = row
      this.showDetailDialog = true
    },
    handleDelete(row) {
      this.$confirm('确认删除该风险评估记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用删除接口
        this.$message.success('删除成功')
        this.handleSearch()
      }).catch(() => {})
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.handleSearch()
    },
    handleDialogSuccess() {
      this.showDetailDialog = false
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.risk-list {
  .search-form {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style> 