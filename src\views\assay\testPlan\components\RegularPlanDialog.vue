<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="计划编号" prop="planCode">
        <el-input v-model="formData.planCode" placeholder="请输入计划编号" />
      </el-form-item>

      <el-form-item label="计划名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入计划名称" />
      </el-form-item>

      <el-form-item label="采样频率" prop="frequency">
        <el-select v-model="formData.frequency" placeholder="请选择采样频率">
          <el-option label="每日采样" value="daily" />
          <el-option label="每周采样" value="weekly" />
          <el-option label="每月采样" value="monthly" />
          <el-option label="季度采样" value="quarterly" />
        </el-select>
      </el-form-item>

      <el-form-item label="计划描述" prop="description">
        <el-input v-model="formData.description" type="textarea" placeholder="请输入计划描述" />
      </el-form-item>
      
      <el-form-item label="检测项目" prop="testItem">
        <div class="test-items-container">
          <el-select
            v-model="formData.testItem"
            filterable
            placeholder="请选择检测项目"
            style="width: 100%"
          >
            <el-option v-for="item in testItems" :key="item.id" :label="item.name" :value="item.id">
              <div style="display: flex; justify-content: space-between; align-items: center">
                <span>{{ item.name }}</span>
                <span v-if="item.standardValue" style="color: #999; font-size: 0.8rem">
                  标准值: {{ item.standardValue }}{{ item.unit }}
                </span>
              </div>
            </el-option>
          </el-select>
          <el-button type="primary" link @click="showTestItemDialog">
            从项目库选择
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="采样点" prop="samplingPoint">
        <div class="sampling-points-container">
          <el-select
            v-model="formData.samplingPoint"
            filterable
            placeholder="请选择采样点"
            style="width: 100%"
          >
            <el-option v-for="point in samplingPoints" :key="point.id" :label="point.name" :value="point.id">
              <div style="display: flex; justify-content: space-between; align-items: center">
                <span>{{ point.name }}</span>
                <span v-if="point.location" style="color: #999; font-size: 0.8rem">
                  位置: {{ point.location }}
                </span>
              </div>
            </el-option>
          </el-select>
          <el-button type="primary" link @click="showSamplingPointDialog">
            从采样点库选择
          </el-button>
        </div>
      </el-form-item>
      
      <!-- 执行人员配置 - 分离三个角色 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="采样人员" prop="samplerId">
            <el-select
              v-model="formData.samplerId"
              filterable
              placeholder="请选择采样人员"
              style="width: 100%"
            >
              <el-option
                v-for="person in responsiblePersons"
                :key="person.id"
                :label="person.name"
                :value="person.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检测人员" prop="testerId">
            <el-select
              v-model="formData.testerId"
              filterable
              placeholder="请选择检测人员"
              style="width: 100%"
            >
              <el-option
                v-for="person in responsiblePersons"
                :key="person.id"
                :label="person.name"
                :value="person.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审核人员" prop="reviewerId">
            <el-select
              v-model="formData.reviewerId"
              filterable
              placeholder="请选择审核人员"
              style="width: 100%"
            >
              <el-option
                v-for="person in responsiblePersons"
                :key="person.id"
                :label="person.name"
                :value="person.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-alert
        title="人员角色说明"
        type="info"
        :closable="false"
        show-icon
        class="mb-4"
      >
        <template #default>
          <p><strong>采样人员</strong>：负责现场采样工作</p>
          <p><strong>检测人员</strong>：负责实验室检测工作</p>
          <p><strong>审核人员</strong>：负责检测结果审核工作</p>
        </template>
      </el-alert>
      
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker v-model="formData.startDate" type="date" placeholder="请选择开始日期" />
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker v-model="formData.endDate" type="date" placeholder="请选择结束日期" />
      </el-form-item>
      
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="formData.priority" placeholder="请选择优先级">
          <el-option label="普通" value="normal" />
          <el-option label="高" value="high" />
          <el-option label="紧急" value="urgent" />
        </el-select>
      </el-form-item>

      <el-form-item label="启用状态" prop="isEnabled">
        <el-switch v-model="formData.isEnabled" />
      </el-form-item>
      
      <!-- 样品预期信息 - 确保数据流完整 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <el-icon><Flask /></el-icon>
            <span>样品预期信息</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预期样品数量" prop="expectedSampleQuantity">
              <el-input-number
                v-model="formData.expectedSampleQuantity"
                :min="1"
                :max="10"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预期样品性质" prop="expectedSampleNature">
              <el-select v-model="formData.expectedSampleNature" placeholder="请选择样品性质" style="width: 100%">
                <el-option label="液体" value="liquid" />
                <el-option label="固体" value="solid" />
                <el-option label="半固体" value="semi-solid" />
                <el-option label="气体" value="gas" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预期样品外观" prop="expectedSampleAppearance">
              <el-input v-model="formData.expectedSampleAppearance" placeholder="如：无色透明、微黄等" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预期上清液情况" prop="expectedSupernatant">
              <el-input v-model="formData.expectedSupernatant" placeholder="如：清澈、浑浊、有沉淀等" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="采样说明" prop="samplingInstructions">
          <el-input
            v-model="formData.samplingInstructions"
            type="textarea"
            :rows="2"
            placeholder="请输入采样注意事项和特殊要求"
          />
        </el-form-item>
      </el-card>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="checkConflicts">检查冲突</el-button>
      <el-button :disabled="formLoading" type="success" @click="submitForm">确 定</el-button>
    </template>
    
    <!-- 检测项目选择弹窗 -->
    <el-dialog
      v-model="testItemDialogVisible"
      title="检测项目库"
      width="60%"
      append-to-body
    >
      <div class="search-bar">
        <el-input v-model="testItemSearchKeyword" placeholder="搜索检测项目" clearable>
          <template #suffix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <el-table
        :data="filteredTestItems"
        border
        style="width: 100%"
        class="dialog-table"
        @selection-change="handleTestItemSelectionChange"
      >
        <el-table-column type="selection" min-width="50" />
        <el-table-column prop="id" label="ID" min-width="80" align="center" />
        <el-table-column prop="name" label="检测项目名称" min-width="150" />
        <el-table-column prop="category" label="类别" min-width="100" align="center" />
        <el-table-column prop="standardValue" label="标准值" min-width="140" align="center">
          <template #default="{ row }">
            {{ row.standardValue }} {{ row.unit }}
          </template>
        </el-table-column>
        <el-table-column prop="method" label="检测方法" min-width="200" show-overflow-tooltip />
      </el-table>
      
      <template #footer>
        <el-button @click="testItemDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmTestItems">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 采样点选择弹窗 -->
    <el-dialog
      v-model="samplingPointDialogVisible"
      title="采样点库"
      width="60%"
      append-to-body
    >
      <div class="search-bar">
        <el-input v-model="samplingPointSearchKeyword" placeholder="搜索采样点" clearable>
          <template #suffix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <el-table
        :data="filteredSamplingPoints"
        border
        style="width: 100%"
        class="dialog-table"
        @selection-change="handleSamplingPointSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="name" label="采样点名称" min-width="150" />
        <el-table-column prop="location" label="位置" min-width="180" />
        <el-table-column prop="category" label="类型" width="100" align="center" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
      </el-table>
      
      <template #footer>
        <el-button @click="samplingPointDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSamplingPoints">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 冲突检查结果弹窗 -->
    <el-dialog
      v-model="conflictDialogVisible"
      title="计划冲突检查结果"
      width="50%"
      append-to-body
    >
      <div v-if="conflicts.length > 0">
        <el-alert type="warning" title="检测到以下冲突，请调整计划:" :closable="false" show-icon />
        <el-table :data="conflicts" border style="width: 100%; margin-top: 1rem;">
          <el-table-column prop="type" label="冲突类型" width="8rem" />
          <el-table-column prop="description" label="冲突描述" />
          <el-table-column prop="suggestion" label="建议操作" />
        </el-table>
      </div>
      <div v-else>
        <el-alert type="success" title="未检测到任何冲突，可以安排执行" :closable="false" show-icon />
      </div>
      <template #footer>
        <el-button @click="conflictDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="submitForm" v-if="conflicts.length === 0">确认计划</el-button>
      </template>
    </el-dialog>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

defineOptions({ name: 'RegularPlanDialog' })

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')

// 检测项目弹窗
const testItemDialogVisible = ref(false)
const testItemSearchKeyword = ref('')
const selectedTestItems = ref<number[]>([])

// 采样点弹窗
const samplingPointDialogVisible = ref(false)
const samplingPointSearchKeyword = ref('')
const selectedSamplingPoints = ref<number[]>([])

// 冲突检查弹窗
const conflictDialogVisible = ref(false)
const conflicts = ref<{type: string; description: string; suggestion: string}[]>([])

// 检测项目数据（模拟数据）
const testItems = [
  { id: 1, name: 'COD', category: '常规', standardValue: '≤50', unit: 'mg/L', method: '重铬酸钾法' },
  { id: 2, name: 'BOD5', category: '常规', standardValue: '≤10', unit: 'mg/L', method: '稀释与接种法' },
  { id: 3, name: '氨氮', category: '常规', standardValue: '≤5', unit: 'mg/L', method: '纳氏试剂比色法' },
  { id: 4, name: '总磷', category: '常规', standardValue: '≤0.5', unit: 'mg/L', method: '钼酸铵分光光度法' },
  { id: 5, name: '总氮', category: '常规', standardValue: '≤15', unit: 'mg/L', method: '碱性过硫酸钾消解UV分光光度法' },
  { id: 6, name: '重金属', category: '特殊', standardValue: '≤0.1', unit: 'mg/L', method: '原子吸收分光光度法' },
  { id: 7, name: '挥发性有机物', category: '特殊', standardValue: '≤0.05', unit: 'mg/L', method: '气相色谱-质谱法' }
]

// 采样点数据（模拟数据）
const samplingPoints = [
  { id: 1, name: '进水总口', location: '处理厂进水口', category: '进水', description: '处理厂进水检测点' },
  { id: 2, name: '生化池', location: '生化处理区1号池', category: '处理中', description: '生化处理过程监测点' },
  { id: 3, name: '二沉池出水', location: '二沉池出水渠', category: '处理中', description: '二级处理出水检测点' },
  { id: 4, name: '出水总口', location: '处理厂出水口', category: '出水', description: '处理厂最终出水检测点' },
  { id: 5, name: '污泥池', location: '污泥处理区', category: '污泥', description: '污泥处理检测点' }
]

// 人员分组数据
const staffGroups = [
  {
    role: 'sampler',
    label: '采样员',
    options: [
      { id: 1, name: '张三', role: 'sampler' },
      { id: 2, name: '李四', role: 'sampler' }
    ]
  },
  {
    role: 'tester',
    label: '检测员',
    options: [
      { id: 3, name: '王五', role: 'tester' },
      { id: 4, name: '赵六', role: 'tester' }
    ]
  },
  {
    role: 'auditor',
    label: '审核员',
    options: [
      { id: 5, name: '钱七', role: 'auditor' }
    ]
  }
]

// 表单数据 - v4.0版本字段映射
const formData = ref({
  id: undefined,
  factoryId: 1,     // ✅ 新增：水厂ID，多水厂支持
  name: '',
  samplingFrequency: '',  // ✅ 修改：frequency → samplingFrequency
  description: '',
  testItems: [] as number[],  // ✅ 修复：items → testItems，与接口定义一致
  samplingPoints: [] as number[],
  // ✅ v4.0修改：人员字段改为ID关联
  samplerId: null,      // 采样人员ID
  testerId: null,       // 检测人员ID
  reviewerId: null,     // 审核人员ID
  startDate: '',
  endDate: '',
  priority: 'medium',
  // ❌ 删除：移除系统排程相关字段
  // scheduling: true,
  // schedulingStrategy: 'uniform',
  // avoidWeekends: true,
  // avoidHolidays: true,
  // considerWorkload: true,
  status: 'pending',
  remark: '',

  // ✅ 新增：样品预期信息字段，确保数据流完整
  expectedSampleQuantity: 1,
  expectedSampleNature: '液体',
  expectedSampleAppearance: '',
  expectedSupernatant: '',
  samplingInstructions: '',
  needTest: true
})

// 表单校验规则 - v4.0版本
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '计划名称不能为空', trigger: 'blur' }],
  samplingFrequency: [{ required: true, message: '采样频率不能为空', trigger: 'change' }],  // ✅ 修改：frequency → samplingFrequency
  testItems: [{ required: true, message: '检测项目不能为空', trigger: 'change' }],  // ✅ 修复：items → testItems
  samplingPoints: [{ required: true, message: '采样点不能为空', trigger: 'change' }],
  // ✅ v4.0修改：人员ID字段的验证规则
  samplerId: [{ required: true, message: '采样人员不能为空', trigger: 'change' }],
  testerId: [{ required: true, message: '检测人员不能为空', trigger: 'change' }],
  reviewerId: [{ required: true, message: '审核人员不能为空', trigger: 'change' }],
  startDate: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }],
  endDate: [{ required: true, message: '结束日期不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
})

const formRef = ref()
const emit = defineEmits(['success'])

// 过滤后的检测项目列表
const filteredTestItems = computed(() => {
  if (!testItemSearchKeyword.value) {
    return testItems
  }
  return testItems.filter(item => 
    item.name.toLowerCase().includes(testItemSearchKeyword.value.toLowerCase()) ||
    item.category.toLowerCase().includes(testItemSearchKeyword.value.toLowerCase())
  )
})

// 过滤后的采样点列表
const filteredSamplingPoints = computed(() => {
  if (!samplingPointSearchKeyword.value) {
    return samplingPoints
  }
  return samplingPoints.filter(point => 
    point.name.toLowerCase().includes(samplingPointSearchKeyword.value.toLowerCase()) ||
    point.location.toLowerCase().includes(samplingPointSearchKeyword.value.toLowerCase()) ||
    point.category.toLowerCase().includes(samplingPointSearchKeyword.value.toLowerCase())
  )
})

// 打开对话框
const open = async (type: string, data?: any) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增常规采样计划' : '编辑常规采样计划'
  formType.value = type
  resetForm()
  
  // 如果是编辑模式，设置表单数据
  if (type === 'update' && data) {
    // 深拷贝，避免直接修改原始数据
    formData.value = JSON.parse(JSON.stringify(data))
    // 如果数据中没有某些字段，设置默认值
    if (!formData.value.testItems) formData.value.testItems = []  // ✅ 修复：items → testItems
    if (!formData.value.samplingPoints) formData.value.samplingPoints = []
    // ✅ v4.0修改：处理三个人员ID字段的默认值
    if (!formData.value.samplerId) formData.value.samplerId = null
    if (!formData.value.testerId) formData.value.testerId = null
    if (!formData.value.reviewerId) formData.value.reviewerId = null
    if (!formData.value.samplingFrequency) formData.value.samplingFrequency = ''
    if (!formData.value.factoryId) formData.value.factoryId = 1
    if (formData.value.scheduling === undefined) {
      formData.value.scheduling = true
      formData.value.schedulingStrategy = 'uniform'
      formData.value.avoidWeekends = true
      formData.value.avoidHolidays = true
      formData.value.considerWorkload = true
    }
  }
}
defineExpose({ open })

// 显示检测项目选择弹窗
const showTestItemDialog = () => {
  testItemDialogVisible.value = true
  testItemSearchKeyword.value = ''
  selectedTestItems.value = [...formData.value.testItems]  // ✅ 修复：items → testItems
}

// 处理检测项目选择变更
const handleTestItemSelectionChange = (selection: any[]) => {
  selectedTestItems.value = selection.map(item => item.id)
}

// 确认检测项目选择
const confirmTestItems = () => {
  formData.value.testItems = [...selectedTestItems.value]  // ✅ 修复：items → testItems
  testItemDialogVisible.value = false
}

// 显示采样点选择弹窗
const showSamplingPointDialog = () => {
  samplingPointDialogVisible.value = true
  samplingPointSearchKeyword.value = ''
  selectedSamplingPoints.value = [...formData.value.samplingPoints]
}

// 处理采样点选择变更
const handleSamplingPointSelectionChange = (selection: any[]) => {
  selectedSamplingPoints.value = selection.map(point => point.id)
}

// 确认采样点选择
const confirmSamplingPoints = () => {
  formData.value.samplingPoints = [...selectedSamplingPoints.value]
  samplingPointDialogVisible.value = false
}

// 检查计划冲突
const checkConflicts = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 随机生成冲突，实际项目中应该是通过API获取
    const hasConflict = Math.random() > 0.6
    
    if (hasConflict) {
      conflicts.value = [
        {
          type: '人员冲突',
          description: '执行日期与其他任务重叠，人员安排冲突',
          suggestion: '调整执行时间或更换执行人员'
        },
        {
          type: '设备冲突',
          description: '检测设备已被其他计划预约',
          suggestion: '调整检测时间或协调设备使用'
        }
      ]
    } else {
      conflicts.value = []
    }
    
    conflictDialogVisible.value = true
  } catch (error) {
    console.error('检查冲突失败:', error)
    ElMessage.error('检查冲突失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    factoryId: 1,     // ✅ v4.0新增：水厂ID
    name: '',
    samplingFrequency: '',  // ✅ v4.0修改：frequency → samplingFrequency
    description: '',
    testItems: [],  // ✅ 修复：items → testItems
    samplingPoints: [],
    // ✅ v4.0修改：人员字段改为ID关联
    samplerId: null,
    testerId: null,
    reviewerId: null,
    startDate: '',
    endDate: '',
    priority: 'medium',
    // ❌ 删除：移除系统排程相关字段
    status: 'pending',
    remark: '',

    // ✅ 新增：样品预期信息字段重置
    expectedSampleQuantity: 1,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '',
    expectedSupernatant: '',
    samplingInstructions: '',
    needTest: true
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.regular-plan-form {
  max-width: 100%;
}

.form-card {
  margin-bottom: 1.5rem;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.form-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.test-items-container,
.sampling-points-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-row {
  display: flex;
  gap: 1rem;
}

.date-row .el-form-item {
  flex: 1;
  margin-bottom: 0;
}

.priority-select {
  width: 100%;
}

.priority-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.priority-desc {
  font-size: 0.875rem;
  color: #909399;
}

.scheduling-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.75rem;
  padding: 0.75rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
}

.search-bar {
  margin-bottom: 1rem;
}

.link-button {
  align-self: flex-end;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-card__header) {
  padding: 0.75rem 1.25rem;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 1.25rem;
}

:deep(.el-select .el-select__tags) {
  padding-right: 1.5rem;
}

:deep(.el-dialog__body) {
  padding: 1.25rem;
}

:deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
}

:deep(.el-radio) {
  margin-right: 1.5rem;
}

/* 弹窗表格优化样式 */
.dialog-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dialog-table :deep(.el-table__header) {
  background-color: #f8fafc;
}

.dialog-table :deep(.el-table__header th) {
  background-color: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

.dialog-table :deep(.el-table__body tr:hover) {
  background-color: #f9fafb;
}

.dialog-table :deep(.el-table__body td) {
  padding: 12px 8px;
  border-bottom: 1px solid #f3f4f6;
}
</style> 