<template>
  <div class="violation-record">
    <el-form :model="queryForm" inline class="query-form">
      <el-form-item label="违章时间">
        <el-date-picker
          v-model="queryForm.timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="违章人员">
        <el-input
          v-model="queryForm.violatorName"
          placeholder="请输入违章人员姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="所属部门">
        <el-select
          v-model="queryForm.department"
          placeholder="请选择部门"
          clearable
        >
          <el-option
            v-for="item in departmentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="违章类型">
        <el-select
          v-model="queryForm.violationType"
          placeholder="请选择违章类型"
          clearable
        >
          <el-option
            v-for="item in violationTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleAddRecord">新增记录</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="recordList" border style="width: 100%">
      <el-table-column prop="recordId" label="记录编号" width="120" />
      <el-table-column prop="violatorName" label="违章人员" width="120" />
      <el-table-column prop="department" label="所属部门" width="150" />
      <el-table-column prop="violationType" label="违章类型" width="120" />
      <el-table-column prop="location" label="违章地点" width="150" />
      <el-table-column prop="violationTime" label="违章时间" width="180" />
      <el-table-column prop="description" label="违章描述" />
      <el-table-column prop="punishment" label="处罚结果" width="200" />
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleViewDetail(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <record-detail-dialog
      v-model="showDetailDialog"
      :record-data="currentRecord"
      @success="handleSuccess"
    />
  </div>
</template>

<script>
import RecordDetailDialog from '../dialogs/RecordDetailDialog.vue'

// 模拟数据
const mockData = [
  {
    recordId: 'R202401001',
    violatorName: '张三',
    department: '施工一部',
    violationType: '安全操作',
    location: 'A区施工现场',
    violationTime: '2024-01-15 09:30:00',
    description: '未按规定佩戴安全帽',
    punishment: '警告+罚款100元'
  },
  {
    recordId: 'R202401002',
    violatorName: '李四',
    department: '施工二部',
    violationType: '设备使用',
    location: 'B区机械室',
    violationTime: '2024-01-16 14:20:00',
    description: '违规操作起重机',
    punishment: '警告+罚款500元+扣分3分+安全教育'
  },
  {
    recordId: 'R202401003',
    violatorName: '王五',
    department: '施工一部',
    violationType: '环境保护',
    location: 'C区施工现场',
    violationTime: '2024-01-17 11:15:00',
    description: '建筑垃圾未按规定处置',
    punishment: '警告+罚款300元'
  }
]

export default {
  name: 'ViolationRecord',
  components: {
    RecordDetailDialog
  },
  data() {
    return {
      queryForm: {
        timeRange: [],
        violatorName: '',
        department: '',
        violationType: ''
      },
      departmentOptions: [
        { value: 'dept1', label: '施工一部' },
        { value: 'dept2', label: '施工二部' },
        { value: 'dept3', label: '安全部' }
      ],
      violationTypeOptions: [
        { value: 'safety', label: '安全操作' },
        { value: 'equipment', label: '设备使用' },
        { value: 'environment', label: '环境保护' }
      ],
      recordList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      showDetailDialog: false,
      currentRecord: null
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    handleQuery() {
      // 模拟后端查询
      let results = [...mockData]
      
      const { timeRange, violatorName, department, violationType } = this.queryForm
      
      if (timeRange && timeRange.length === 2) {
        results = results.filter(item => {
          const time = item.violationTime.split(' ')[0]
          return time >= timeRange[0] && time <= timeRange[1]
        })
      }
      
      if (violatorName) {
        results = results.filter(item => 
          item.violatorName.includes(violatorName)
        )
      }
      
      if (department) {
        results = results.filter(item => 
          item.department === this.departmentOptions.find(opt => opt.value === department)?.label
        )
      }
      
      if (violationType) {
        results = results.filter(item =>
          item.violationType === this.violationTypeOptions.find(opt => opt.value === violationType)?.label
        )
      }

      this.recordList = results
      this.total = results.length
    },
    handleReset() {
      this.queryForm = {
        timeRange: [],
        violatorName: '',
        department: '',
        violationType: ''
      }
      this.handleQuery()
    },
    handleAddRecord() {
      this.currentRecord = null
      this.showDetailDialog = true
    },
    handleViewDetail(row) {
      this.currentRecord = { ...row }
      this.showDetailDialog = true
    },
    handleSuccess(data) {
      this.showDetailDialog = false
      // 模拟新增记录
      if (!this.currentRecord) {
        mockData.unshift(data)
      }
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.handleQuery()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.violation-record {
  .query-form {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .el-pagination {
    margin-top: 20px;
    justify-content: flex-end;
  }
}
</style> 