<script lang="ts" setup>
import { computed, onMounted, ref, unref, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import logoImg from '@/assets/imgs/logo.png'
import logoCollapseImg from '@/assets/imgs/logo2.png'

defineOptions({ name: 'Logo' })

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()

const show = ref(true)

const title = computed(() => appStore.getTitle)

const layout = computed(() => appStore.getLayout)

const collapse = computed(() => appStore.getCollapse)

const props = defineProps({
  className: {
    type: String,
    default: ''
  }
})

const isCollapse = computed(() => appStore.getCollapse)

onMounted(() => {
  if (unref(collapse)) show.value = false
})

watch(
  () => collapse.value,
  (collapse: boolean) => {
    if (unref(layout) === 'topLeft' || unref(layout) === 'top' || unref(layout) === 'cutMenu') {
      show.value = true
      return
    }
    if (!collapse) {
      setTimeout(() => {
        show.value = !collapse
      }, 400)
    } else {
      show.value = !collapse
    }
  }
)

watch(
  () => layout.value,
  (layout) => {
    if (layout === 'top' || layout === 'cutMenu') {
      show.value = true
    } else {
      if (unref(collapse)) {
        show.value = false
      } else {
        show.value = true
      }
    }
  }
)
</script>

<template>
  <div
    :class="[
      'flex !h-[var(--logo-height)] items-center cursor-pointer relative decoration-none overflow-hidden',
      className
    ]"
  >
    <img
      :class="[
        'h-[calc(var(--logo-height)-30px)] transition-all duration-300',
        {
          'w-[calc(var(--logo-height)-20px)] pl-[10px] ml-[5px]': isCollapse,
          'w-[calc(var(--logo-height)+70px)] pl-[20px] ml-[ ]': !isCollapse
        }
      ]"
      :src="isCollapse ? logoCollapseImg : logoImg"
    />
    <!-- <div
      v-if="show"
      :class="[
        'ml-5px text-16px font-700',
        {
          'text-[var(--logo-title-text-color)]': layout === 'classic',
          'text-[var(--top-header-text-color)]':
            layout === 'topLeft' || layout === 'top' || layout === 'cutMenu'
        }
      ]"
    >
      {{ title }}
    </div> -->
  </div>
</template>
