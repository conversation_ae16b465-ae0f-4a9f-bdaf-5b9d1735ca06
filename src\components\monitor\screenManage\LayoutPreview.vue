<template>
  <div class="preview-container">
    <div class="preview-area">
      <div class="preview-svg-container">
        <div class="preview-point">
          <!-- 图标 -->
          <div class="preview-icon">
            <el-icon>
              <warning />
            </el-icon>
          </div>

          <!-- 标签和数值 -->
          <div class="preview-label" :style="{
            left: getPreviewPosition('label', 'x') + 'px',
            top: getPreviewPosition('label', 'y') + 'px',
            display: layoutTemplate === 'valueOnly' ? 'none' : 'block'
          }">
            测点名称
          </div>

          <div class="preview-value" :style="{
            left: getPreviewPosition('number', 'x') + 'px',
            top: getPreviewPosition('number', 'y') + 'px'
          }">
            123.45
          </div>
        </div>
      </div>
      <div class="preview-tip">
        <el-icon>
          <info-filled />
        </el-icon>
        预览效果仅供参考，实际效果取决于SVG大小和比例
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { InfoFilled, Warning } from '@element-plus/icons-vue';
import { LayoutTemplateType, Position } from './types';

// 定义属性
const props = defineProps({
  labelPosition: {
    type: Object as () => Position,
    default: () => ({ x: 0, y: -10 })
  },
  numberPosition: {
    type: Object as () => Position,
    default: () => ({ x: 0, y: 10 })
  },
  layoutTemplate: {
    type: String as () => LayoutTemplateType,
    default: 'right'
  }
});

// 获取预览位置
const getPreviewPosition = (type: string, axis: string) => {
  const center = { x: 90, y: 90 }; // 预览区域中心点
  let offset = { x: 0, y: 0 };

  if (type === 'label' && props.labelPosition) {
    offset = {
      x: parseInt(String(props.labelPosition.x)) || 0,
      y: parseInt(String(props.labelPosition.y)) || 0
    };
  } else if (type === 'number' && props.numberPosition) {
    offset = {
      x: parseInt(String(props.numberPosition.x)) || 0,
      y: parseInt(String(props.numberPosition.y)) || 0
    };
  }

  return center[axis as keyof typeof center] + offset[axis as keyof typeof offset];
};
</script>

<style scoped lang="scss">
.preview-container {
  margin: 20px 0;
}

.preview-area {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
  background-color: #f5f7fa;
}

.preview-svg-container {
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: white;
  border-radius: 4px;
  margin-bottom: 10px;
}

.preview-point {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background-color: #f5f7fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);

  :deep(.el-icon) {
    font-size: 18px;
    color: #409eff;
  }
}

.preview-label,
.preview-value {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  transform: translate(-50%, -50%);
}

.preview-label {
  color: #606266;
}

.preview-value {
  color: #409eff;
  font-weight: bold;
}

.preview-tip {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;

  :deep(.el-icon) {
    margin-right: 5px;
  }
}
</style>