<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="60%"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="plan-form"
    >
      <el-form-item label="预案名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入预案名称" />
      </el-form-item>
      
      <el-form-item label="适用范围" prop="scope">
        <el-select v-model="form.scope" placeholder="请选择适用范围" style="width: 100%">
          <el-option label="全公司" value="company" />
          <el-option label="部门" value="department" />
          <el-option label="工作区域" value="workspace" />
        </el-select>
      </el-form-item>

      <el-form-item label="应急流程" prop="process">
        <el-input
          v-model="form.process"
          type="textarea"
          :rows="4"
          placeholder="请输入应急流程"
        />
      </el-form-item>

      <el-form-item label="预案描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入预案描述"
        />
      </el-form-item>

      <el-form-item label="附件上传">
        <el-upload
          class="upload-demo"
          action="/api/upload"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          multiple
          :limit="5"
          :on-exceed="handleExceed"
        >
          <el-button type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持上传应急流程图、联络表等文件，单个文件不超过10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'PlanDialog',
  props: {
    dialogType: {
      type: String,
      default: 'add'
    },
    planData: {
      type: Object,
      default: () => ({})
    },
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'submit'],
  computed: {
    dialogTitle() {
      const titles = {
        add: '新增预案',
        edit: '编辑预案',
        view: '查看预案'
      }
      return titles[this.dialogType] || '预案详情'
    },
    dialogVisible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  data() {
    return {
      form: {
        name: '',
        scope: '',
        process: '',
        description: '',
        attachments: []
      },
      rules: {
        name: [
          { required: true, message: '请输入预案名称', trigger: 'blur' }
        ],
        scope: [
          { required: true, message: '请选择适用范围', trigger: 'change' }
        ],
        process: [
          { required: true, message: '请输入应急流程', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    initForm() {
      if (this.dialogType === 'add') {
        this.form = {
          name: '',
          scope: '',
          process: '',
          description: '',
          attachments: []
        }
      } else {
        this.form = { ...this.planData }
      }
      if (this.dialogType === 'view') {
        this.$nextTick(() => {
          this.$refs.formRef.setDisabled(true)
        })
      }
    },
    handleClosed() {
      this.$refs.formRef?.resetFields()
    },
    handlePreview(file) {
      // 处理文件预览
    },
    handleRemove(file, fileList) {
      // 处理文件移除
    },
    handleExceed(files, fileList) {
      this.$message.warning('最多只能上传5个文件')
    },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()
        // 提交表单逻辑
        this.$emit('submit', this.form)
        this.dialogVisible = false
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.plan-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 20px;
}
</style> 