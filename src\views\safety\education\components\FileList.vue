<template>
  <div class="file-list">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="文件标题">
          <el-input v-model="searchForm.title" placeholder="请输入文件标题" />
        </el-form-item>
        <el-form-item label="文件类型">
          <el-select v-model="searchForm.fileType" placeholder="请选择文件类型">
            <el-option label="文档" value="doc" />
            <el-option label="图片" value="image" />
            <el-option label="视频" value="video" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="发布时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleAdd">上传文件</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="title" label="文件标题" min-width="200" show-overflow-tooltip />
      <el-table-column prop="file_type" label="文件类型" width="100">
        <template #default="scope">
          <el-tag :type="getFileTypeTag(scope.row.file_type)">
            {{ getFileTypeText(scope.row.file_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="publish_date" label="发布时间" width="120" />
      <el-table-column prop="description" label="文件描述" min-width="250" show-overflow-tooltip />
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="scope">
          <div class="operation-buttons">
            <el-button type="primary" link @click="handleAddAttachment(scope.row)">添加附件</el-button>
            <el-button type="warning" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="success" link @click="handleDownload(scope.row)">下载</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 上传文件弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '上传文件' : '编辑文件'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="文件标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入文件标题" />
        </el-form-item>
        <el-form-item label="所属分类" prop="category_id">
          <el-select v-model="form.category_id" placeholder="请选择所属分类" style="width: 100%">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文件描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入文件描述"
          />
        </el-form-item>
        <el-form-item label="文件上传" prop="file" v-if="dialogType === 'add'">
          <el-upload
            class="upload-demo"
            action="/api/upload"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            :limit="1"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持任意格式文件，单个文件不超过50MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加附件弹窗 -->
    <el-dialog
      v-model="attachmentDialogVisible"
      title="添加附件"
      width="500px"
    >
      <el-form
        ref="attachmentFormRef"
        :model="attachmentForm"
        :rules="attachmentRules"
        label-width="80px"
      >
        <el-form-item label="附件名称" prop="title">
          <el-input v-model="attachmentForm.title" placeholder="请输入附件名称" />
        </el-form-item>
        <el-form-item label="附件类型" prop="type">
          <el-select v-model="attachmentForm.type" placeholder="请选择附件类型" style="width: 100%">
            <el-option label="补充说明" value="supplement" />
            <el-option label="参考资料" value="reference" />
            <el-option label="证明材料" value="evidence" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="attachmentForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item label="附件上传" prop="file">
          <el-upload
            class="upload-demo"
            action="/api/upload"
            :on-success="handleAttachmentUploadSuccess"
            :before-upload="beforeUpload"
            :limit="1"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持任意格式文件，单个文件不超过50MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="attachmentDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAttachmentSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑文件弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑文件信息"
      width="500px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="文件标题" prop="title">
          <el-input v-model="editForm.title" placeholder="请输入文件标题" />
        </el-form-item>
        <el-form-item label="所属分类" prop="category_id">
          <el-select v-model="editForm.category_id" placeholder="请选择所属分类" style="width: 100%">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文件描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入文件描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleEditSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'FileList',
  data() {
    return {
      searchForm: {
        title: '',
        fileType: '',
        dateRange: []
      },
      tableData: [
        {
          id: 1,
          category_id: 1,
          title: '安全生产操作规程.pdf',
          file_url: '/files/safety/operation-manual.pdf',
          file_type: 'doc',
          publish_date: '2024-03-15',
          description: '详细介绍各岗位安全生产操作规程和注意事项'
        },
        {
          id: 2,
          category_id: 2,
          title: '消防安全培训视频.mp4',
          file_url: '/files/safety/fire-training.mp4',
          file_type: 'video',
          publish_date: '2024-03-14',
          description: '消防安全知识培训及消防设施使用演示'
        },
        {
          id: 3,
          category_id: 1,
          title: '安全警示标识图集.jpg',
          file_url: '/files/safety/warning-signs.jpg',
          file_type: 'image',
          publish_date: '2024-03-13',
          description: '企业安全警示标识规范及使用说明'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3,
      dialogVisible: false,
      dialogType: 'add',
      form: {
        title: '',
        category_id: '',
        description: '',
        file_url: '',
        file_type: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入文件标题', trigger: 'blur' }
        ],
        category_id: [
          { required: true, message: '请选择所属分类', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入文件描述', trigger: 'blur' }
        ]
      },
      categoryOptions: [
        { value: 1, label: '规章制度' },
        { value: 2, label: '培训资料' },
        { value: 3, label: '安全标准' }
      ],
      attachmentDialogVisible: false,
      currentFileId: null,
      attachmentForm: {
        title: '',
        type: '',
        remark: '',
        file_url: ''
      },
      attachmentRules: {
        title: [
          { required: true, message: '请输入附件名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择附件类型', trigger: 'change' }
        ]
      },
      editDialogVisible: false,
      editForm: {
        id: null,
        title: '',
        category_id: '',
        description: ''
      }
    }
  },
  methods: {
    getFileTypeTag(type) {
      const tags = {
        doc: 'primary',
        image: 'success',
        video: 'warning',
        other: 'info'
      }
      return tags[type] || 'info'
    },
    getFileTypeText(type) {
      const texts = {
        doc: '文档',
        image: '图片',
        video: '视频',
        other: '其他'
      }
      return texts[type] || '其他'
    },
    handleSearch() {
      // 实现搜索逻辑
      console.log('搜索条件：', this.searchForm)
      this.fetchData()
    },
    resetSearch() {
      this.searchForm = {
        title: '',
        fileType: '',
        dateRange: []
      }
    },
    handleAdd() {
      this.dialogType = 'add'
      this.form = {
        title: '',
        category_id: '',
        description: '',
        file_url: '',
        file_type: ''
      }
      this.dialogVisible = true
    },
    handleDownload(row) {
      // 实现文件下载逻辑
      const link = document.createElement('a')
      link.href = row.file_url
      link.download = row.title
      link.click()
    },
    handleDelete(row) {
      this.$confirm('确定要删除该文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实现删除逻辑
        console.log('删除文件：', row)
        this.$message.success('删除成功')
        this.fetchData()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    handleUploadSuccess(response, file) {
      this.form.file_url = response.url
      this.form.file_type = this.getFileType(file.name)
      this.$message.success('文件上传成功')
    },
    beforeUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('文件大小不能超过 50MB!')
      }
      return isLt50M
    },
    getFileType(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      const docTypes = ['doc', 'docx', 'pdf', 'txt', 'xls', 'xlsx']
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp']
      const videoTypes = ['mp4', 'avi', 'mov', 'wmv']
      
      if (docTypes.includes(extension)) return 'doc'
      if (imageTypes.includes(extension)) return 'image'
      if (videoTypes.includes(extension)) return 'video'
      return 'other'
    },
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()
        // 实现提交逻辑
        console.log('提交数据：', this.form)
        this.$message.success(this.dialogType === 'add' ? '上传成功' : '修改成功')
        this.dialogVisible = false
        this.fetchData()
      } catch (error) {
        console.error('表单验证失败', error)
      }
    },
    fetchData() {
      // 实现获取表格数据的逻辑
      console.log('获取数据，页码：', this.currentPage, '每页条数：', this.pageSize)
    },
    handleAddAttachment(row) {
      this.currentFileId = row.id;
      this.attachmentForm = {
        title: '',
        type: '',
        remark: '',
        file_url: ''
      };
      this.attachmentDialogVisible = true;
    },
    handleAttachmentUploadSuccess(response, file) {
      this.attachmentForm.file_url = response.url;
      this.$message.success('附件上传成功');
    },
    async handleAttachmentSubmit() {
      try {
        await this.$refs.attachmentFormRef.validate();
        // 实现提交附件逻辑
        console.log('提交附件数据：', {
          fileId: this.currentFileId,
          ...this.attachmentForm
        });
        this.$message.success('附件添加成功');
        this.attachmentDialogVisible = false;
      } catch (error) {
        console.error('表单验证失败', error);
      }
    },
    handleEdit(row) {
      this.editForm = {
        id: row.id,
        title: row.title,
        category_id: row.category_id,
        description: row.description
      }
      this.editDialogVisible = true
    },
    async handleEditSubmit() {
      try {
        await this.$refs.editFormRef.validate()
        // 实现编辑提交逻辑
        console.log('提交编辑数据：', this.editForm)
        this.$message.success('文件信息修改成功')
        this.editDialogVisible = false
        this.fetchData()
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.file-list {
  .search-area {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .operation-buttons {
    display: flex;
    justify-content: space-between;
    width: 100%;
    
    .el-button {
      margin-left: 0;
      padding: 4px 8px;
    }
  }
}
</style>