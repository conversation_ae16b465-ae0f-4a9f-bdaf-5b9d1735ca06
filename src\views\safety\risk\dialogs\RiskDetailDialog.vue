<template>
  <el-dialog
    :title="isEdit ? '编辑风险评估' : '风险评估详情'"
    v-model="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="!isEdit"
    >
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="作业地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入作业地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="作业类型" prop="workType">
              <el-select v-model="form.workType" placeholder="请选择作业类型" style="width: 100%">
                <el-option
                  v-for="item in workTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="作业内容描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请详细描述作业内容"
          />
        </el-form-item>
      </el-card>

      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>危险性评估</span>
          </div>
        </template>
        <el-form-item label="事故类型" prop="accidentType">
          <el-select v-model="form.accidentType" placeholder="请选择可能发生的事故类型" style="width: 100%">
            <el-option
              v-for="item in accidentTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="发生频率(L)" prop="frequency">
              <el-select v-model="form.frequency" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in frequencyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    分值：{{ item.value }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="暴露程度(E)" prop="exposure">
              <el-select v-model="form.exposure" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in exposureOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    分值：{{ item.value }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="后果严重性(C)" prop="consequence">
              <el-select v-model="form.consequence" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in consequenceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    分值：{{ item.value }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>评估结果</span>
          </div>
        </template>
        <div class="result-info">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="result-item">
                <span class="label">风险值(D)：</span>
                <span class="value">{{ form.riskValue }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="result-item">
                <span class="label">风险等级：</span>
                <el-tag :type="getRiskLevelType(form.riskLevel)">{{ form.riskLevel }}</el-tag>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>管控措施</span>
          </div>
        </template>
        <el-form-item label="管控措施" prop="controls">
          <el-input
            v-model="form.controls"
            type="textarea"
            :rows="4"
            placeholder="请输入针对性的管控措施"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ isEdit ? '取消' : '关闭' }}</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="isEdit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'RiskDetailDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    riskData: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      isEdit: false,
      form: {
        location: '',
        workType: '',
        description: '',
        accidentType: '',
        frequency: '',
        exposure: '',
        consequence: '',
        riskValue: 0,
        riskLevel: '',
        controls: ''
      },
      workTypeOptions: [
        { value: 'height', label: '高空作业' },
        { value: 'confined', label: '受限空间作业' },
        { value: 'hot', label: '动火作业' },
        { value: 'lifting', label: '吊装作业' },
        { value: 'electrical', label: '电气作业' }
      ],
      accidentTypeOptions: [
        { value: 'fall', label: '高处坠落' },
        { value: 'collapse', label: '坍塌' },
        { value: 'electric', label: '触电' },
        { value: 'fire', label: '火灾' },
        { value: 'mechanical', label: '机械伤害' }
      ],
      frequencyOptions: [
        { value: 10, label: '经常发生' },
        { value: 6, label: '偶尔发生' },
        { value: 3, label: '极少发生' },
        { value: 1, label: '几乎不可能' }
      ],
      exposureOptions: [
        { value: 10, label: '连续暴露' },
        { value: 6, label: '每天工作时暴露' },
        { value: 3, label: '每周一次' },
        { value: 1, label: '极少暴露' }
      ],
      consequenceOptions: [
        { value: 10, label: '死亡' },
        { value: 6, label: '严重伤害' },
        { value: 3, label: '轻微伤害' },
        { value: 1, label: '轻微影响' }
      ],
      rules: {
        location: [
          { required: true, message: '请输入作业地点', trigger: 'blur' }
        ],
        workType: [
          { required: true, message: '请选择作业类型', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入作业内容描述', trigger: 'blur' }
        ],
        accidentType: [
          { required: true, message: '请选择事故类型', trigger: 'change' }
        ],
        frequency: [
          { required: true, message: '请选择发生频率', trigger: 'change' }
        ],
        exposure: [
          { required: true, message: '请选择暴露程度', trigger: 'change' }
        ],
        consequence: [
          { required: true, message: '请选择后果严重性', trigger: 'change' }
        ],
        controls: [
          { required: true, message: '请输入管控措施', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    modelValue(val) {
      if (val && this.riskData) {
        this.isEdit = false
        this.form = { ...this.riskData }
      }
    }
  },
  methods: {
    getRiskLevelType(level) {
      const types = {
        '极高风险': 'danger',
        '高风险': 'warning',
        '中度风险': 'warning',
        '低风险': 'success'
      }
      return types[level] || 'info'
    },
    calculateRisk() {
      const { frequency, exposure, consequence } = this.form
      this.form.riskValue = frequency * exposure * consequence

      if (this.form.riskValue >= 400) {
        this.form.riskLevel = '极高风险'
      } else if (this.form.riskValue >= 200) {
        this.form.riskLevel = '高风险'
      } else if (this.form.riskValue >= 70) {
        this.form.riskLevel = '中度风险'
      } else {
        this.form.riskLevel = '低风险'
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.form = {
        location: '',
        workType: '',
        description: '',
        accidentType: '',
        frequency: '',
        exposure: '',
        consequence: '',
        riskValue: 0,
        riskLevel: '',
        controls: ''
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.calculateRisk()
          // TODO: 调用保存接口
          this.$message.success('保存成功')
          this.$emit('success', this.form)
          this.handleClose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.result-info {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .result-item {
    display: flex;
    align-items: center;
    gap: 12px;

    .label {
      font-weight: bold;
    }

    .value {
      font-size: 24px;
      color: #409eff;
    }
  }
}
</style> 