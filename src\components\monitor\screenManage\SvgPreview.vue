<template>
  <div class="svg-preview-container">
    <div class="svg-preview-header">
      <span>SVG预览</span>
      <div class="svg-preview-actions">
        <el-button-group>
          <el-tooltip content="放大" placement="top">
            <el-button size="small" @click="zoomIn" :disabled="zoomLevel >= 2.0">
              <el-icon><zoom-in /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="缩小" placement="top">
            <el-button size="small" @click="zoomOut" :disabled="zoomLevel <= 0.5">
              <el-icon><zoom-out /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="重置缩放" placement="top">
            <el-button size="small" @click="resetZoom">
              <el-icon>
                <refresh />
              </el-icon>
            </el-button>
          </el-tooltip>
        </el-button-group>
        <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
      </div>
    </div>
    <div class="svg-preview-content" ref="svgContainer" @wheel="handleWheel">
      <!-- 内容容器 -->
      <div class="content-wrapper" :style="{ transform: `scale(${zoomLevel})` }">
        <!-- SVG内容 -->
        <div class="svg-wrapper">
          <div v-if="!svgContent" class="empty-svg">
            <el-icon>
              <picture />
            </el-icon>
            <p>请上传SVG图片</p>
          </div>
          <div v-else v-html="svgContent" class="svg-content"></div>
        </div>

        <!-- 测点显示层 -->
        <div v-if="svgContent && points.length > 0" class="points-layer">
          <div v-for="point in points" :key="point.id" class="monitor-point"
            :style="{ left: `${point.position.x}px`, top: `${point.position.y}px` }">
            <div class="point-icon" :class="[point.type, point.status]">
              <el-icon>
                <component :is="getIconByType(point.type)" />
              </el-icon>
            </div>

            <div v-if="point.showDisplay !== false" class="point-value" :style="getValueStyle(point)">
              <div v-if="point.layoutTemplate !== 'valueOnly'" class="value-label" :style="getLabelStyle(point)">
                {{ point.displayName || point.name }}
              </div>
              <div class="value-number" :style="getNumberStyle(point)">
                {{ formatValue(point.value) }} {{ point.unit || '' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Refresh, ZoomIn, ZoomOut } from '@element-plus/icons-vue';
import { defineProps, onMounted, onUnmounted, ref } from 'vue';
import { MonitorPoint } from './types';

// 定义属性
const props = defineProps({
  svgContent: {
    type: String,
    default: ''
  },
  points: {
    type: Array as () => MonitorPoint[],
    default: () => []
  }
});

// SVG容器引用
const svgContainer = ref<HTMLElement | null>(null);

// 缩放级别
const zoomLevel = ref(1.0);

// 获取测点类型对应的图标
const getIconByType = (type: string) => {
  const iconMap: Record<string, string> = {
    'flow': 'WaterMeter',
    'level': 'Odometer',
    'ph': 'Cpu',
    'pump': 'SwitchButton',
    'gate': 'Switch'
  };

  return iconMap[type] || 'Monitor';
};

// 格式化数值
const formatValue = (value?: number) => {
  if (value === undefined || value === null) return '--';
  return value.toFixed(2);
};

// 获取数值样式
const getValueStyle = (point: MonitorPoint) => {
  const basePosition = point.displayPosition || { x: 0, y: 0 };

  return {
    left: `${basePosition.x}px`,
    top: `${basePosition.y}px`,
    backgroundColor: point.displayBgColor || 'rgba(255, 255, 255, 0.7)',
    color: point.displayColor || '#303133'
  };
};

// 获取标签样式
const getLabelStyle = (point: MonitorPoint) => {
  const labelPos = point.labelPosition || { x: 0, y: 0 };

  return {
    left: `${labelPos.x}px`,
    top: `${labelPos.y}px`
  };
};

// 获取数值样式
const getNumberStyle = (point: MonitorPoint) => {
  const numberPos = point.numberPosition || { x: 0, y: 0 };

  return {
    left: `${numberPos.x}px`,
    top: `${numberPos.y}px`
  };
};

// 放大
const zoomIn = () => {
  zoomLevel.value = Math.min(2.0, zoomLevel.value + 0.1);
};

// 缩小
const zoomOut = () => {
  zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1);
};

// 重置缩放
const resetZoom = () => {
  zoomLevel.value = 1.0;
};

// 鼠标滚轮缩放
const handleWheel = (event: WheelEvent) => {
  if (event.ctrlKey) {
    event.preventDefault();
    event.stopPropagation();

    const delta = event.deltaY || event.detail || (event as any).wheelDelta;
    if (delta < 0) {
      // 向上滚动，放大
      zoomLevel.value = Math.min(2.0, zoomLevel.value + 0.1);
    } else {
      // 向下滚动，缩小
      zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1);
    }
  }
};

// 组件挂载时
onMounted(() => {
  // 添加鼠标滚轮事件，防止在滚动时触发页面缩放
  if (svgContainer.value) {
    svgContainer.value.addEventListener('wheel', handleWheel, { passive: false });
    svgContainer.value.addEventListener('mousewheel', handleWheel as any, { passive: false });
    svgContainer.value.addEventListener('DOMMouseScroll', handleWheel as any, { passive: false });
  }
});

// 组件卸载时
onUnmounted(() => {
  if (svgContainer.value) {
    svgContainer.value.removeEventListener('wheel', handleWheel);
    svgContainer.value.removeEventListener('mousewheel', handleWheel as any);
    svgContainer.value.removeEventListener('DOMMouseScroll', handleWheel as any);
  }
});
</script>

<style scoped lang="scss">
.svg-preview-container {
  border: 1px solid #dcdfe6;
  border-radius: 0;
  background-color: #f5f7fa;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.svg-preview-header {
  padding: 2px 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  height: 32px;

  span {
    font-weight: bold;
    color: #303133;
    font-size: 14px;
  }

  .svg-preview-actions {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .zoom-level {
    font-size: 12px;
    color: #606266;
    width: 40px;
    text-align: center;
  }
}

.svg-preview-content {
  position: relative;
  flex: 1;
  overflow: auto;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.content-wrapper {
  position: relative;
  transform-origin: top left;
  transition: transform 0.2s ease;
  display: inline-block;
}

.svg-wrapper {
  position: relative;
  background-color: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.empty-svg {
  width: 100%;
  min-width: 300px;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;

  :deep(.el-icon) {
    font-size: 48px;
    margin-bottom: 10px;
  }

  p {
    margin: 0;
  }
}

.svg-content {
  :deep(svg) {
    display: block;
    max-width: 100%;
    height: auto;
  }
}

.points-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.monitor-point {
  position: absolute;
  transform: translate(-50%, -50%);
}

.point-icon {
  width: 28px;
  height: 28px;
  background-color: #f5f7fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);

  &.flow {
    color: #409EFF;
  }

  &.level {
    color: #67C23A;
  }

  &.ph {
    color: #E6A23C;
  }

  &.pump,
  &.gate {
    color: #F56C6C;
  }

  &.running {
    background-color: #67C23A;
    color: white;
  }

  &.stopped {
    background-color: #909399;
    color: white;
  }

  &.alarm {
    background-color: #F56C6C;
    color: white;
  }

  :deep(.el-icon) {
    font-size: 16px;
  }
}

.point-value {
  position: absolute;
  min-width: 80px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transform: translate(-50%, -50%);
}

.value-label {
  position: relative;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.value-number {
  position: relative;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}
</style>