<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">领用申请</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleAdd">
              <el-icon>
                <Plus />
              </el-icon>新增申请
            </el-button>
            <el-upload class="upload-btn" action="/api/stock/usage/import" :show-file-list="false"
              :before-upload="beforeUpload" :on-success="handleImportSuccess" :on-error="handleImportError">
              <el-button type="primary">
                <el-icon>
                  <Upload />
                </el-icon>批量导入
              </el-button>
            </el-upload>
            <el-button @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>导出
            </el-button>
          </div>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-270px)] flex flex-col">
        <div class="w-full h-[50px] mb-2 gap-2 flex items-center">
          <el-form :model="searchForm" inline>
            <el-form-item label="申请单号：">
              <el-input v-model="searchForm.requestNo" placeholder="请输入申请单号" />
            </el-form-item>
            <el-form-item label="申请人：">
              <el-input v-model="searchForm.applicant" placeholder="请输入申请人" />
            </el-form-item>
            <el-form-item label="申请状态：">
              <el-select v-model="searchForm.status" placeholder="请选择状态" style="width: 120px">
                <el-option label="待审核" value="0" />
                <el-option label="已通过" value="1" />
                <el-option label="已驳回" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="申请时间：">
              <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border height="100%">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="requestNo" label="申请单号" align="center" min-width="120" />
                <el-table-column prop="applicant" label="申请人" align="center" width="120" />
                <el-table-column prop="department" label="申请部门" align="center" width="120" />
                <el-table-column prop="purpose" label="用途" align="center" min-width="120" />
                <el-table-column prop="requestTime" label="申请时间" align="center" width="160" sortable />
                <el-table-column prop="status" label="状态" align="center" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="200" fixed="right">
                  <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">
                      详情
                    </el-button>
                    <el-button v-if="scope.row.status === '0'" type="primary" link @click="handleEdit(scope.row)">
                      编辑
                    </el-button>
                    <el-button v-if="scope.row.status === '0'" type="danger" link @click="handleDelete(scope.row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end mt-2">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增领用申请' : '编辑领用申请'" width="800px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="申请部门" prop="department">
          <el-input v-model="form.department" placeholder="请输入申请部门" />
        </el-form-item>
        <el-form-item label="用途" prop="purpose">
          <el-input v-model="form.purpose" type="textarea" :rows="2" placeholder="请输入用途说明" />
        </el-form-item>
        <el-form-item label="领用物品">
          <div class="flex justify-between items-center mb-2">
            <span class="font-bold">物品清单</span>
            <el-button type="primary" link @click="handleAddItem">
              <el-icon>
                <Plus />
              </el-icon>添加物品
            </el-button>
          </div>
          <el-table :data="form.items" border width="100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column label="物品名称" min-width="150" align="center">
              <template #default="{ row }">
                <el-select v-model="row.productId" placeholder="请选择物品" filterable remote
                  :remote-method="handleSearchProduct" :loading="productLoading">
                  <el-option v-for="item in productOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="数量" min-width="150" align="center">
              <template #default="{ row }">
                <el-input-number v-model="row.quantity" :min="1" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="{ $index }">
                <el-button type="danger" link @click="handleRemoveItem($index)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Plus, Upload, Download } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// 类型定义
interface SearchForm {
  requestNo: string
  applicant: string
  status: string
  dateRange: string[]
}

interface TableItem {
  id: string
  requestNo: string
  applicant: string
  department: string
  purpose: string
  requestTime: string
  status: string
}

interface FormItem {
  productId: string
  quantity: number
}

interface FormData {
  department: string
  purpose: string
  items: FormItem[]
}

// 搜索表单数据
const searchForm = reactive<SearchForm>({
  requestNo: '',
  applicant: '',
  status: '',
  dateRange: []
})

// 表格数据
const tableData = ref<TableItem[]>([
  {
    id: '1',
    requestNo: 'LY-20241030-001',
    applicant: '张三',
    department: '运维部',
    purpose: '设备维修',
    requestTime: '2024-10-30 09:37:56',
    status: '0'
  },
  {
    id: '2',
    requestNo: 'LY-20241029-002',
    applicant: '李四',
    department: '工程部',
    purpose: '项目建设',
    requestTime: '2024-10-29 14:20:30',
    status: '1'
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(2)

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const formRef = ref()
const form = reactive<FormData>({
  department: '',
  purpose: '',
  items: []
})

// 表单校验规则
const rules = {
  department: [{ required: true, message: '请输入申请部门', trigger: 'blur' }],
  purpose: [{ required: true, message: '请输入用途说明', trigger: 'blur' }],
  items: [{ required: true, message: '请至少添加一个物品', trigger: 'change' }]
}

// 物品选择相关
const productLoading = ref(false)
const productOptions = ref<Array<{ id: string; name: string }>>([])

// 获取状态样式
const getStatusType = (status: string): 'success' | 'warning' | 'info' | 'danger' => {
  const map: Record<string, 'success' | 'warning' | 'info' | 'danger'> = {
    '0': 'warning',
    '1': 'success',
    '2': 'danger'
  }
  return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    '0': '待审核',
    '1': '已通过',
    '2': '已驳回'
  }
  return map[status] || '未知'
}

// 搜索方法
const handleSearch = () => {
  console.log('搜索条件：', searchForm)
  // TODO: 调用后端API获取数据
}

// 重置方法
const handleReset = () => {
  searchForm.requestNo = ''
  searchForm.applicant = ''
  searchForm.status = ''
  searchForm.dateRange = []
  handleSearch()
}

// 新增方法
const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
  form.department = ''
  form.purpose = ''
  form.items = []
}

// 编辑方法
const handleEdit = (row: TableItem) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  // TODO: 获取详情数据
  form.department = row.department
  form.purpose = row.purpose
  form.items = []
}

// 删除方法
const handleDelete = async (row: TableItem) => {
  console.log('🚀 ~ handleDelete ~ row:', row)
  try {
    await ElMessageBox.confirm('确认要删除该领用申请吗？', '提示', {
      type: 'warning'
    })
    // TODO: 调用删除API
    ElMessage.success('删除成功')
    handleSearch()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 详情方法
const handleDetail = (row: TableItem) => {
  router.push(`/stock/usage-request/detail/${row.id}`)
}

// 导出方法
const handleExport = () => {
  console.log('导出数据')
  // TODO: 调用导出API
}

// 批量导入相关方法
const beforeUpload = (file: File) => {
  const isExcel =
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件！')
    return false
  }
  return true
}

const handleImportSuccess = (response: any) => {
  console.log('🚀 ~ handleImportSuccess ~ response:', response)
  ElMessage.success('导入成功')
  handleSearch()
}

const handleImportError = () => {
  ElMessage.error('导入失败')
}

// 物品选择相关方法
const handleSearchProduct = async (query: string) => {
  if (query) {
    productLoading.value = true
    try {
      // TODO: 调用搜索物品API
      productOptions.value = []
    } finally {
      productLoading.value = false
    }
  }
}

// 添加物品
const handleAddItem = () => {
  form.items.push({
    productId: '',
    quantity: 1
  })
}

// 删除物品
const handleRemoveItem = (index: number) => {
  form.items.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (form.items.length === 0) {
        ElMessage.warning('请至少添加一个物品')
        return
      }
      // 验证物品是否都已选择
      const hasEmptyProduct = form.items.some((item) => !item.productId)
      if (hasEmptyProduct) {
        ElMessage.warning('请选择所有物品')
        return
      }
      // TODO: 调用提交API
      ElMessage.success(dialogType.value === 'add' ? '新增成功' : '编辑成功')
      dialogVisible.value = false
      handleSearch()
    }
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}
</script>

<style scoped lang="scss">
.usage-request {
  padding: 16px;

  .search-bar {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;

    .search-input {
      width: 280px;
    }
  }

  .operation-bar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  :deep(.el-table) {
    margin-top: 20px;

    .el-button {
      padding: 0 5px;
      height: auto;
    }
  }

  .upload-btn {
    display: inline-block;
  }
}
</style>
