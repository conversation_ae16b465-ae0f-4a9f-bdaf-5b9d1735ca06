<template>
  <div class="screen-header">
    <div class="screen-info">
      <!-- 画面名称编辑区域 -->
      <div class="screen-name-container">
        <div v-if="isEditing" class="editing-container">
          <el-input v-model="editName" size="small" placeholder="请输入画面名称" ref="nameInput" class="name-input"
            @keyup.enter="saveScreenName" />
          <div class="editing-actions">
            <el-button type="primary" size="small" @click="saveScreenName" class="save-btn">
              <el-icon>
                <Check />
              </el-icon>
            </el-button>
            <el-button type="default" size="small" @click="cancelEditing" class="cancel-btn">
              <el-icon>
                <Close />
              </el-icon>
            </el-button>
          </div>
        </div>
        <el-tooltip content="点击编辑画面名称" placement="right" effect="light" :visible="showEditTip" :disabled="isEditing">
          <div v-if="!isEditing" class="screen-name-wrapper" @mouseenter="showEditTip = true"
            @mouseleave="showEditTip = false">
            <div class="screen-name" @click="startEditing">
              <span>{{ screen?.name || '未命名画面' }}</span>
              <el-button type="text" class="edit-button" @click.stop="startEditing">
                <el-icon class="edit-icon">
                  <Edit />
                </el-icon>
                <span class="edit-text">编辑名称</span>
              </el-button>
            </div>
            <div class="name-tip" v-if="!screen?.name">
              <el-icon>
                <Warning />
              </el-icon>
              <span>点击设置画面名称</span>
            </div>
          </div>
        </el-tooltip>
      </div>

      <!-- 编辑状态提示 -->
      <div class="screen-status" v-if="screen?.isModified">
        <el-tooltip content="已修改内容尚未保存到服务器，请点击右侧的保存按钮保存修改" placement="bottom" effect="light">
          <span class="status-text">
            <el-icon class="status-icon">
              <Warning />
            </el-icon>
            未保存
          </span>
        </el-tooltip>
      </div>
    </div>

    <div class="screen-actions">
      <!-- SVG上传按钮 -->
      <el-button type="primary" size="small" plain @click="handleSvgUpload">
        <el-icon>
          <Upload />
        </el-icon>
        {{ screen?.svgContent || screen?.svgUrl ? '更换SVG' : '上传SVG' }}
      </el-button>

      <!-- 导出SVG按钮 -->
      <el-button type="primary" size="small" plain @click="handleExportSvg">
        <el-icon>
          <Download />
        </el-icon>
        导出SVG
      </el-button>

      <!-- 保存按钮 -->
      <el-button type="primary" size="small" :disabled="!screen?.isModified" @click="handleSave">
        <el-icon>
          <Check />
        </el-icon>
        保存
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
// 导入Element Plus图标
import { Check, Close, Edit, Download, Upload, Warning } from '@element-plus/icons-vue'

// 定义接口
interface Screen {
  id: string | number;
  name: string;
  svgContent?: string;
  svgUrl?: string;  // 添加svgUrl属性
  isModified?: boolean;
}

// 定义props
const props = defineProps({
  screen: {
    type: Object as () => Screen,
    default: () => ({
      id: '',
      name: '未命名画面',
      svgContent: '',
      isModified: false
    })
  }
})

// 定义emit
const emit = defineEmits(['update-name', 'upload-svg', 'export-svg', 'save'])

// 编辑状态
const isEditing = ref(false)
const editName = ref('')
const nameInput = ref<HTMLInputElement | null>(null)
const showEditTip = ref(false)

// 开始编辑画面名称
const startEditing = () => {
  if (!props.screen) return

  editName.value = props.screen.name
  isEditing.value = true

  // 自动聚焦输入框
  nextTick(() => {
    nameInput.value?.focus()
  })
}

// 保存画面名称
const saveScreenName = () => {
  if (editName.value.trim() === '') {
    ElMessage.warning('画面名称不能为空')
    editName.value = props.screen?.name || '未命名画面'
  } else {
    // 如果名称已更改，才发送事件
    if (editName.value !== props.screen?.name) {
      emit('update-name', editName.value)
      // 不在这里显示消息，让父组件处理
    }
  }

  isEditing.value = false
}

// 监听screen变化，重置编辑状态
watch(() => props.screen, (newScreen) => {
  isEditing.value = false
  if (newScreen) {
    editName.value = newScreen.name
  }
}, { deep: true })

// 处理SVG文件上传
const handleSvgUpload = () => {
  // 直接触发upload-svg事件，不传递内容
  // 父组件会自行打开文件选择对话框
  emit('upload-svg')
}

// 导出SVG
const handleExportSvg = () => {
  emit('export-svg')
}

// 保存画面
const handleSave = () => {
  if (!props.screen?.isModified) {
    ElMessage.info('画面没有修改，无需保存')
    return
  }

  emit('save')
}

// 取消编辑
const cancelEditing = () => {
  isEditing.value = false
}
</script>

<style scoped>
.screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  height: 56px;
  box-sizing: border-box;
}

.screen-info {
  display: flex;
  align-items: center;
}

.screen-name-container {
  max-width: 350px;
}

.screen-name-wrapper {
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  padding: 4px 8px;
  transition: all 0.3s;
}

.screen-name-wrapper:hover {
  background-color: #eef1f6;
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.screen-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.edit-button {
  padding: 2px 8px;
  margin-left: 8px;
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 3px;
  display: flex;
  align-items: center;
  font-size: 12px;
  transition: all 0.3s;
}

.edit-button:hover {
  background-color: rgba(64, 158, 255, 0.2);
  color: #409eff;
}

.edit-icon {
  font-size: 14px;
  margin-right: 4px;
  vertical-align: middle;
}

.el-icon {
  vertical-align: middle;
}

.el-button .el-icon {
  margin-right: 4px;
}

.el-button :deep(.el-icon) {
  vertical-align: middle;
}

.edit-text {
  font-size: 12px;
}

.screen-status {
  margin-left: 12px;
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 12px;
  color: #e6a23c;
  background-color: #fdf6ec;
  padding: 4px 8px;
  border-radius: 3px;
  font-weight: 500;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(230, 162, 60, 0.2);
  animation: pulse 2s infinite;
  cursor: help;
}

.status-icon {
  margin-right: 4px;
  color: #e6a23c;
  font-size: 14px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0.4);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(230, 162, 60, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0);
  }
}

.screen-actions {
  display: flex;
  gap: 8px;
}

.editing-container {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border: 1px solid #409eff;
  border-radius: 4px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: highlight-edit 0.5s ease-in-out;
}

@keyframes highlight-edit {
  0% {
    background-color: rgba(64, 158, 255, 0.2);
  }

  50% {
    background-color: rgba(64, 158, 255, 0.1);
  }

  100% {
    background-color: #f5f7fa;
  }
}

.name-input {
  margin-right: 8px;
  flex: 1;
}

.name-input :deep(.el-input__inner) {
  border-color: #409eff;
  background-color: #fff;
  font-weight: 500;
}

.editing-actions {
  display: flex;
  gap: 4px;
}

.save-btn,
.cancel-btn {
  padding: 2px 8px;
  height: 32px;
  min-height: 32px;
}

.save-btn {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.cancel-btn {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #606266;
}

.name-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.name-tip .el-icon {
  margin-right: 4px;
  color: #e6a23c;
}
</style>