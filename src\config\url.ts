/**
 * URL配置文件
 * 用于集中管理项目中使用的URL
 */

// 基础URL，用于API请求和资源加载
export const BASE_URL = import.meta.env.VITE_BASE_URL || '';

// API URL路径
export const API_URL = import.meta.env.VITE_API_URL || '/api';

// 资源路径前缀，用于图片、SVG等静态资源
export const ASSETS_URL = BASE_URL;

// SVG资源路径前缀，用于SVG图片
export const SVG_URL = BASE_URL;

// 监控ICC请求 前缀(上下文)
// nginx会将 /factory/evo-apigw/ 代理到 https://172.16.198.81/evo-apigw/
export const MONITOR_URL_PREFIX = 'factory';


// 监控静态资源路径前缀
export const MONITOR_STATIC_URL_PREFIX = '../../factory/lib';