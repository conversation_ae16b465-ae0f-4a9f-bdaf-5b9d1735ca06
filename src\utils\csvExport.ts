/**
 * CSV导出工具函数
 * 用于化验管理模块的各种单据导出
 */

export interface CSVColumn {
  key: string
  label: string
  width?: number
  formatter?: (value: any, row: any) => string
}

export interface CSVExportOptions {
  filename: string
  columns: CSVColumn[]
  data: any[]
  title?: string
  subtitle?: string
  footer?: string[]
}

/**
 * 将数据导出为CSV格式
 */
export function exportToCSV(options: CSVExportOptions) {
  const { filename, columns, data, title, subtitle, footer } = options
  
  let csvContent = ''
  
  // 添加BOM以支持中文
  csvContent = '\uFEFF'
  
  // 添加标题
  if (title) {
    csvContent += `"${title}"\n\n`
  }
  
  // 添加副标题
  if (subtitle) {
    csvContent += `"${subtitle}"\n\n`
  }
  
  // 添加表头
  const headers = columns.map(col => `"${col.label}"`).join(',')
  csvContent += headers + '\n'
  
  // 添加数据行
  data.forEach(row => {
    const values = columns.map(col => {
      let value = row[col.key]
      
      // 使用自定义格式化函数
      if (col.formatter) {
        value = col.formatter(value, row)
      }
      
      // 处理空值
      if (value === null || value === undefined) {
        value = ''
      }
      
      // 转换为字符串并处理特殊字符
      value = String(value)
      
      // 如果包含逗号、引号或换行符，需要用引号包围
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        value = `"${value.replace(/"/g, '""')}"`
      } else {
        value = `"${value}"`
      }
      
      return value
    })
    
    csvContent += values.join(',') + '\n'
  })
  
  // 添加页脚
  if (footer && footer.length > 0) {
    csvContent += '\n'
    footer.forEach(line => {
      csvContent += `"${line}"\n`
    })
  }
  
  // 创建下载链接
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

/**
 * 采样单CSV导出
 */
export function exportSamplingForm(samplingTask: any) {
  const columns: CSVColumn[] = [
    { key: 'label', label: '项目', width: 120 },
    { key: 'value', label: '内容', width: 200 }
  ]
  
  const data = [
    { label: '采样单编号', value: samplingTask.sampleCode || `ST${samplingTask.id.toString().padStart(6, '0')}` },
    { label: '计划名称', value: samplingTask.planName },
    { label: '采样点', value: samplingTask.samplingPoint },
    { label: '采样日期', value: samplingTask.planDate },
    { label: '采样人员', value: samplingTask.executor },
    { label: '样品数量', value: samplingTask.sampleQuantity },
    { label: '样品性质', value: samplingTask.sampleNature },
    { label: '预期外观', value: samplingTask.expectedAppearance },
    { label: '预期上清液', value: samplingTask.expectedSupernatant },
    { label: '采样说明', value: samplingTask.samplingInstructions },
    { label: '检测项目', value: samplingTask.testItems?.join(', ') || '' },
    { label: '采样位置', value: samplingTask.samplingLocation || '' },
    { label: '采样条件', value: samplingTask.samplingCondition || '' },
    { label: '实际外观', value: samplingTask.sampleAppearance || '' },
    { label: '实际上清液', value: samplingTask.supernatant || '' },
    { label: '实际数量', value: samplingTask.actualSampleQuantity || '' },
    { label: '采样时间', value: samplingTask.actualSamplingTime || '' },
    { label: '状态', value: getStatusText(samplingTask.status) }
  ]
  
  exportToCSV({
    filename: `采样单_${samplingTask.planName}_${new Date().toISOString().slice(0, 10)}`,
    columns,
    data,
    title: '采样单',
    subtitle: `采样单编号：${samplingTask.sampleCode || `ST${samplingTask.id.toString().padStart(6, '0')}`}`,
    footer: [
      `导出时间：${new Date().toLocaleString()}`,
      '注：此单据为系统自动生成，请妥善保管'
    ]
  })
}

/**
 * 送检单CSV导出
 */
export function exportInspectionForm(samplingTask: any) {
  const columns: CSVColumn[] = [
    { key: 'label', label: '项目', width: 120 },
    { key: 'value', label: '内容', width: 200 }
  ]
  
  const data = [
    { label: '送检单编号', value: `IF${samplingTask.id.toString().padStart(6, '0')}` },
    { label: '样品编号', value: samplingTask.sampleCode || `SM${samplingTask.id.toString().padStart(6, '0')}` },
    { label: '采样点', value: samplingTask.samplingPoint },
    { label: '采样日期', value: samplingTask.actualSamplingTime || samplingTask.planDate },
    { label: '采样人员', value: samplingTask.sampler || samplingTask.executor },
    { label: '送检人员', value: samplingTask.executor },
    { label: '样品数量', value: samplingTask.actualSampleQuantity || samplingTask.sampleQuantity },
    { label: '样品性质', value: samplingTask.sampleNature },
    { label: '样品外观', value: samplingTask.sampleAppearance || samplingTask.expectedAppearance },
    { label: '上清液情况', value: samplingTask.supernatant || samplingTask.expectedSupernatant },
    { label: '检测项目', value: samplingTask.testItems?.join(', ') || '' },
    { label: '检测人员', value: samplingTask.tester || '' },
    { label: '预计完成时间', value: getEstimatedCompletionTime(samplingTask.planDate) },
    { label: '特殊要求', value: samplingTask.samplingInstructions || '' },
    { label: '送检时间', value: new Date().toLocaleString() },
    { label: '状态', value: '待检测' }
  ]
  
  exportToCSV({
    filename: `送检单_${samplingTask.samplingPoint}_${new Date().toISOString().slice(0, 10)}`,
    columns,
    data,
    title: '送检单',
    subtitle: `送检单编号：IF${samplingTask.id.toString().padStart(6, '0')}`,
    footer: [
      `送检时间：${new Date().toLocaleString()}`,
      '注：请检测人员按要求完成检测并及时反馈结果'
    ]
  })
}

/**
 * 检验单CSV导出
 */
export function exportTestForm(testData: any) {
  const columns: CSVColumn[] = [
    { key: 'label', label: '项目', width: 120 },
    { key: 'value', label: '内容', width: 200 }
  ]
  
  const data = [
    { label: '检验单编号', value: `TF${testData.id.toString().padStart(6, '0')}` },
    { label: '样品编号', value: testData.sampleCode },
    { label: '采样点', value: testData.samplingPoint },
    { label: '采样日期', value: testData.samplingDate },
    { label: '检测项目', value: testData.testItem },
    { label: '检测方法', value: testData.testMethod },
    { label: '检测仪器', value: testData.testInstrument },
    { label: '检测值', value: testData.testValue },
    { label: '单位', value: testData.unit },
    { label: '标准值', value: testData.standardValue },
    { label: '检测人员', value: testData.tester },
    { label: '检测日期', value: testData.testDate },
    { label: '审核人员', value: testData.reviewer || '' },
    { label: '审核日期', value: testData.reviewDate || '' },
    { label: '检测结果', value: getTestResult(testData.testValue, testData.standardValue) },
    { label: '备注', value: testData.remark || '' }
  ]
  
  exportToCSV({
    filename: `检验单_${testData.testItem}_${new Date().toISOString().slice(0, 10)}`,
    columns,
    data,
    title: '检验单',
    subtitle: `检验单编号：TF${testData.id.toString().padStart(6, '0')}`,
    footer: [
      `检测完成时间：${testData.testDate || new Date().toLocaleString()}`,
      '注：检测结果仅对送检样品负责'
    ]
  })
}

// 辅助函数
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    pending: '待采样',
    sampling: '采样中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

function getEstimatedCompletionTime(planDate: string): string {
  const date = new Date(planDate)
  date.setDate(date.getDate() + 3) // 预计3天完成
  return date.toLocaleDateString()
}

function getTestResult(testValue: string, standardValue: string): string {
  if (!testValue || !standardValue) return '待判定'
  
  const value = parseFloat(testValue)
  const standard = parseFloat(standardValue)
  
  if (isNaN(value) || isNaN(standard)) return '待判定'
  
  return value <= standard ? '合格' : '不合格'
}
