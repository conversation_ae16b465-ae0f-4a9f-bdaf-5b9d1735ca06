<template>
  <div v-if="username === 'admin' || username === '谈正鑫'" class="dashboard-container">
    <!-- 时间周期选择器 -->
    <div class="time-period-selector">
      <div class="left-controls">
        <el-radio-group v-model="timePeriod" @change="handlePeriodChange">
          <el-radio-button label="day">日</el-radio-button>
          <el-radio-button label="week">周</el-radio-button>
          <el-radio-button label="month">月</el-radio-button>
          <el-radio-button label="quarter">季</el-radio-button>
          <el-radio-button label="year">年</el-radio-button>
        </el-radio-group>

        <!-- <el-date-picker v-model="dateRange" :type="datePickerType" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :shortcuts="dateShortcuts" @change="handleDateChange" /> -->
      </div>

      <div class="view-selector">
        <span class="view-label">视图模式:</span>
        <el-select v-model="viewMode" placeholder="请选择视图模式" @change="handleViewModeChange">
          <el-option label="全局视图" value="global" />
          <el-option label="单厂视图" value="single" />
        </el-select>

        <el-select v-if="viewMode === 'single'" v-model="selectedPlantId" placeholder="请选择水厂"
          @change="handleSinglePlantChange" style="margin-left: 10px; width: 200px;">
          <template v-for="plant in plants" :key="plant.id">
            <!-- 父级水厂 -->
            <el-option-group v-if="plant.children && plant.children.length" :label="plant.name">
              <el-option v-for="child in plant.children" :key="child.id" :label="child.name" :value="child.id" />
            </el-option-group>
            <!-- 没有子级的水厂 -->
            <el-option v-else :label="plant.name" :value="plant.id" />
          </template>
        </el-select>
      </div>
    </div>

    <!-- A. 运行总览 + 异常速览区 -->
    <el-card shadow="hover" class="section-card overview-section">
      <template #header>
        <div class="card-header">
          <span>{{ viewMode === 'global' ? '集团运行总览' : getSinglePlantName() + ' 运行总览' }}</span>
          <el-button type="primary" size="small" @click="exportReport">导出报表</el-button>
        </div>
      </template>

      <el-skeleton :loading="loading" animated>
        <el-row :gutter="20">
          <!-- 1. 指标卡片 -->
          <el-col :span="16">
            <el-row :gutter="16">
              <el-col :span="6" v-for="(card, index) in overviewCards" :key="index">
                <div class="stat-card">
                  <div class="stat-card-header">
                    <span class="stat-title">{{ card.title }}</span>
                    <el-tooltip :content="card.tooltip" placement="top">
                      <el-icon>
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                  <div class="stat-value">
                    {{ viewMode === 'global' ? card.value : getCardSingleValue(card.key) }}
                    <span class="stat-unit">{{ card.unit }}</span>
                  </div>
                  <div class="stat-compare" :class="card.trend > 0 ? 'up' : 'down'">
                    环比 {{ card.trend > 0 ? '+' : '' }}{{ card.trend }}%
                    <el-icon v-if="card.trend > 0">
                      <ArrowUp />
                    </el-icon>
                    <el-icon v-else>
                      <ArrowDown />
                    </el-icon>
                  </div>
                  <div v-if="viewMode === 'single'" class="stat-mode-indicator">
                    <el-tag size="small" type="info">单厂数据</el-tag>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-col>

          <!-- 2. 当前异常列表 + 告警饼图 -->
          <el-col :span="8">
            <div class="alert-container">
              <div class="alert-chart">
                <Echart :options="alertChartOptions" :height="180" />
              </div>
              <div class="alert-list">
                <div class="alert-list-header">
                  <span>
                    {{ viewMode === 'global' ? '最新告警' : getSinglePlantName() + ' 告警' }}
                    ({{ filteredAlerts.length }})
                    <el-tag v-if="viewMode === 'single'" size="small" type="info">单厂数据</el-tag>
                  </span>
                  <el-link type="primary" @click="navigateToAlertPage">查看全部</el-link>
                </div>
                <el-scrollbar height="150px">
                  <div v-for="(alert, index) in filteredAlerts" :key="index" class="alert-item"
                    :class="{ 'severe': alert.level === 'severe' }" @click="showAlertDetail(alert)">
                    <el-tag :type="getAlertTypeTag(alert.type)" size="small">{{ alert.type }}</el-tag>
                    <span class="alert-content">{{ alert.content }}</span>
                    <span class="alert-time">{{ alert.time }}</span>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-skeleton>
    </el-card>

    <!-- 主体内容区 -->
    <el-row :gutter="16" class="main-content">
      <!-- B & D. 左侧：运行趋势分析、单位能耗趋势 -->
      <el-col :span="16">
        <!-- B. 运行趋势图分析 -->
        <el-card shadow="hover" class="section-card">
          <template #header>
            <div class="card-header">
              <span>
                {{ viewMode === 'global' ? '运行趋势分析' : getSinglePlantName() + ' 运行趋势' }}
                <el-tag v-if="viewMode === 'single'" size="small" type="info" style="margin-left: 5px;">单厂数据</el-tag>
              </span>
              <div class="header-actions">
                <el-radio-group v-model="trendMetric" size="small" @change="changeTrendMetric">
                  <el-radio-button label="processVolume">处理量</el-radio-button>
                  <el-radio-button label="passRate">达标率</el-radio-button>
                </el-radio-group>
                <el-button size="small" @click="exportTrendData">导出</el-button>
              </div>
            </div>
          </template>

          <el-skeleton :loading="loading" animated>
            <Echart :options="trendChartOptions" :height="300" />
          </el-skeleton>
        </el-card>

        <!-- D. 能耗与药耗分析 -->
        <el-card shadow="hover" class="section-card">
          <template #header>
            <div class="card-header">
              <span>
                {{ viewMode === 'global' ? '能耗与药耗分析' : getSinglePlantName() + ' 能耗分析' }}
                <el-tag v-if="viewMode === 'single'" size="small" type="info" style="margin-left: 5px;">单厂数据</el-tag>
              </span>
              <div class="header-actions">
                <el-radio-group v-model="consumptionType" size="small" @change="changeConsumptionType">
                  <el-radio-button label="electricity">电耗</el-radio-button>
                  <el-radio-button label="chemical">药耗</el-radio-button>
                  <el-radio-button label="hourly">分时能耗</el-radio-button>
                </el-radio-group>
                <el-button size="small" type="primary" @click="navigateToAnalysis">详细分析</el-button>
              </div>
            </div>
          </template>

          <el-skeleton :loading="loading" animated>
            <Echart v-if="consumptionType !== 'hourly'" :options="consumptionChartOptions" :height="300" />
            <div v-else class="hourly-consumption">
              <Echart :options="hourlyConsumptionOptions" :height="300" />
            </div>
          </el-skeleton>
        </el-card>
      </el-col>

      <!-- C & E. 右侧：绩效对比、资源排行、运维保障 -->
      <el-col :span="8">
        <!-- C. 水厂KPI绩效分析区 -->
        <el-card shadow="hover" class="section-card">
          <template #header>
            <div class="card-header">
              <span>
                {{ viewMode === 'global' ? '水厂绩效分析' : getSinglePlantName() + ' 绩效分析' }}
                <el-tag v-if="viewMode === 'single'" size="small" type="info" style="margin-left: 5px;">单厂数据</el-tag>
              </span>
              <div class="header-actions">
                <el-radio-group v-model="kpiDisplayMode" size="small" @change="changeKpiDisplayMode"
                  v-if="viewMode === 'global'">
                  <el-radio-button label="table">表格</el-radio-button>
                  <el-radio-button label="radar">雷达图</el-radio-button>
                </el-radio-group>
                <el-radio-group v-model="kpiTimeRange" size="small" v-if="viewMode === 'single'">
                  <el-radio-button label="month">本月</el-radio-button>
                  <el-radio-button label="quarter">本季</el-radio-button>
                  <el-radio-button label="year">本年</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>

          <el-skeleton :loading="loading" animated>
            <!-- 全局视图：KPI 表格或雷达图 -->
            <div v-if="viewMode === 'global'">
              <!-- KPI 表格视图 -->
              <div v-if="kpiDisplayMode === 'table'" class="kpi-table">
                <el-table :data="kpiTableData" :max-height="350" size="small" border stripe
                  :default-sort="{ prop: 'kpiScore', order: 'descending' }">
                  <el-table-column prop="name" label="水厂" width="90" fixed />
                  <el-table-column v-for="col in kpiColumns" :key="col.prop" :prop="col.prop" :label="col.label"
                    :sortable="col.sortable" :formatter="col.formatter" :width="col.width" />
                </el-table>
              </div>

              <!-- KPI 雷达图视图 -->
              <div v-else class="kpi-radar">
                <div class="plant-selector">
                  <span>对比水厂：</span>
                  <el-select v-model="selectedPlants" multiple collapse-tags collapse-tags-tooltip placeholder="请选择水厂"
                    size="small" @change="updateRadarChart">
                    <el-option v-for="plant in plants" :key="plant.id" :label="plant.name" :value="plant.id" />
                  </el-select>
                </div>
                <Echart :options="kpiRadarOptions" :height="300" />
              </div>
            </div>

            <!-- 单厂视图：本年度KPI情况 -->
            <div v-else class="single-plant-kpi">
              <!-- KPI指标卡片 -->
              <div class="kpi-metrics">
                <el-row :gutter="20">
                  <el-col :span="8" v-for="(item, index) in [
                    { name: '达标率', value: getCardSingleValue('passRate'), unit: '%', trend: 1.2, up: true, color: 'blue' },
                    { name: '负荷率', value: getSingleKpiValue('loadRate'), unit: '%', trend: 3.5, up: true, color: 'green' },
                    { name: 'KPI评分', value: getSingleKpiValue('kpiScore'), unit: '', trend: 2.1, up: true, color: 'purple' },
                    { name: '单位电耗', value: getCardSingleValue('electricity'), unit: 'kWh/m³', trend: 2.3, up: false, color: 'orange' },
                    { name: '单位药耗', value: getSingleKpiValue('chemical'), unit: '元/m³', trend: 1.8, up: false, color: 'cyan' },
                    { name: '巡检率', value: getSingleKpiValue('inspectionRate'), unit: '%', trend: 1.5, up: true, color: 'magenta' }
                  ]" :key="index" class="kpi-col">
                    <div class="kpi-metric-card" :class="'kpi-color-' + item.color">
                      <div class="kpi-metric-header">{{ item.name }}</div>
                      <div class="kpi-metric-value">{{ item.value }}<span class="unit">{{ item.unit }}</span></div>
                      <div class="kpi-metric-trend" :class="item.up ? 'up' : 'down'">
                        {{ item.up ? '+' : '-' }}{{ item.trend }}%
                        <el-icon v-if="item.up">
                          <ArrowUp />
                        </el-icon>
                        <el-icon v-else>
                          <ArrowDown />
                        </el-icon>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 年度KPI趋势图 -->
              <div class="kpi-trend-chart">
                <Echart :options="singlePlantKpiOptions" :height="220" />
              </div>
            </div>
          </el-skeleton>
        </el-card>

        <!-- E. 运维保障与数据质量监测 -->
        <el-card shadow="hover" class="section-card">
          <template #header>
            <div class="card-header">
              <span>运维保障与数据质量</span>
              <el-dropdown @command="handleMaintenanceCommand">
                <span class="el-dropdown-link">
                  <el-button size="small">更多<el-icon>
                      <ArrowDown />
                    </el-icon></el-button>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="device">设备详情</el-dropdown-item>
                    <el-dropdown-item command="inspection">巡检任务</el-dropdown-item>
                    <el-dropdown-item command="dataQuality">数据质量</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>

          <el-skeleton :loading="loading" animated>
            <el-row :gutter="16">
              <!-- 设备运行状态 -->
              <el-col :span="12">
                <div class="maintenance-card device-status">
                  <div class="maintenance-header">设备运行状态</div>
                  <div class="device-counts">
                    <div class="device-count-item">
                      <div class="count-value">{{ deviceStatus.total }}</div>
                      <div class="count-label">总数</div>
                    </div>
                    <div class="device-count-item online">
                      <div class="count-value">{{ deviceStatus.online }}</div>
                      <div class="count-label">在线</div>
                    </div>
                    <div class="device-count-item offline">
                      <div class="count-value">{{ deviceStatus.offline }}</div>
                      <div class="count-label">离线</div>
                    </div>
                    <div class="device-count-item fault">
                      <div class="count-value">{{ deviceStatus.fault }}</div>
                      <div class="count-label">故障</div>
                    </div>
                  </div>
                  <div class="device-trend">
                    <div class="trend-header">
                      <span>故障率趋势</span>
                      <span class="trend-value">{{ deviceStatus.faultRate }}%</span>
                    </div>
                    <Echart :options="deviceTrendOptions" :height="80" />
                  </div>
                </div>
              </el-col>

              <!-- 巡检任务完成率 -->
              <el-col :span="12">
                <div class="maintenance-card inspection-rate">
                  <div class="maintenance-header">巡检任务完成率</div>
                  <div class="inspection-chart">
                    <Echart :options="inspectionChartOptions" :height="120" />
                  </div>
                  <div class="inspection-stats">
                    <div class="inspection-stat-item">
                      <span class="label">计划巡检：</span>
                      <span class="value">{{ inspectionData.planned }}</span>
                    </div>
                    <div class="inspection-stat-item">
                      <span class="label">已完成：</span>
                      <span class="value">{{ inspectionData.completed }}</span>
                    </div>
                    <div class="inspection-stat-item">
                      <span class="label">环比：</span>
                      <span class="value" :class="inspectionData.monthOnMonth > 0 ? 'up' : 'down'">
                        {{ inspectionData.monthOnMonth > 0 ? '+' : '' }}{{ inspectionData.monthOnMonth }}%
                      </span>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>

            <!-- 数据异常监测 -->
            <!-- <div class="data-quality">
              <div class="quality-header">数据异常监测</div>
              <el-row :gutter="10">
                <el-col :span="12" v-for="(item, index) in dataQualityIssues" :key="index">
                  <div class="quality-item">
                    <el-icon :color="item.color">
                      <Warning />
                    </el-icon>
                    <div class="quality-content">
                      <div class="quality-name">{{ item.name }}</div>
                      <div class="quality-value">{{ item.value }}{{ item.unit }}</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div> -->
          </el-skeleton>
        </el-card>
      </el-col>
    </el-row>

    <!-- F. 地图与空间分布（GIS模块） -->
    <!-- <el-card shadow="hover" class="section-card map-section">
      <template #header>
        <div class="card-header">
          <span>
            {{ viewMode === 'global' ? '地理空间分布' : getSinglePlantName() + ' 地理位置' }}
            <el-tag v-if="viewMode === 'single'" size="small" type="info" style="margin-left: 5px;">单厂数据</el-tag>
          </span>
          <div class="header-actions">
            <el-checkbox-group v-model="mapLayers" size="small" @change="updateMapLayers">
              <el-checkbox-button label="water">水厂</el-checkbox-button>
              <el-checkbox-button label="quality">水质指标</el-checkbox-button>
              <el-checkbox-button label="rainfall">雨量</el-checkbox-button>
              <el-checkbox-button label="alerts">告警热力</el-checkbox-button>
            </el-checkbox-group>
          </div>
        </div>
      </template>

      <el-skeleton :loading="loading" animated>
        <div class="map-container" id="gisMap"></div>
      </el-skeleton>
    </el-card> -->

    <!-- 弹窗组件 -->
    <el-dialog v-model="alertDetailVisible" title="告警详情" width="600px">
      <div v-if="currentAlert" class="alert-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="告警类型">{{ currentAlert.type }}</el-descriptions-item>
          <el-descriptions-item label="告警级别">
            <el-tag :type="getAlertTypeTag(currentAlert.level)">{{ currentAlert.level }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="告警时间">{{ currentAlert.time }}</el-descriptions-item>
          <el-descriptions-item label="水厂">{{ currentAlert.plant }}</el-descriptions-item>
          <el-descriptions-item label="位置">{{ currentAlert.location }}</el-descriptions-item>
          <el-descriptions-item label="内容">{{ currentAlert.content }}</el-descriptions-item>
          <el-descriptions-item label="建议措施">{{ currentAlert.suggestion }}</el-descriptions-item>
        </el-descriptions>
        <div class="dialog-footer">
          <el-button @click="alertDetailVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleAlertAction">处理</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog v-model="plantDetailVisible" title="水厂详情" width="800px">
      <div v-if="selectedPlantDetail" class="plant-detail">
        <el-tabs>
          <el-tab-pane label="基本信息">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="水厂名称">{{ selectedPlantDetail.name }}</el-descriptions-item>
              <el-descriptions-item label="所属区域">{{ selectedPlantDetail.region }}</el-descriptions-item>
              <el-descriptions-item label="处理能力">{{ selectedPlantDetail.capacity }} 万吨/日</el-descriptions-item>
              <el-descriptions-item label="今日处理量">{{ selectedPlantDetail.todayVolume }} 万吨</el-descriptions-item>
              <el-descriptions-item label="投产时间">{{ selectedPlantDetail.startDate }}</el-descriptions-item>
              <el-descriptions-item label="建设类型">{{ selectedPlantDetail.constructionType }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="运行指标">
            <el-row :gutter="20">
              <el-col :span="12">
                <Echart :options="plantPerformanceOptions" :height="200" />
              </el-col>
              <el-col :span="12">
                <div class="indicator-list">
                  <div v-for="(item, index) in selectedPlantDetail.indicators" :key="index" class="indicator-item">
                    <span class="indicator-name">{{ item.name }}</span>
                    <el-progress :percentage="item.value" :color="getProgressColor(item.value)"
                      :format="(percentage) => percentage + '%'" />
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="告警记录">
            <el-table :data="selectedPlantDetail.alerts" size="small">
              <el-table-column prop="time" label="时间" width="150" />
              <el-table-column prop="type" label="类型" width="100">
                <template #default="scope">
                  <el-tag :type="getAlertTypeTag(scope.row.type)" size="small">{{ scope.row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="content" label="内容" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '已处理' ? 'success' : 'warning'" size="small">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <div class="dialog-footer">
          <el-button @click="plantDetailVisible = false">关闭</el-button>
          <el-button type="primary" @click="navigateToPlantDetail">查看完整报告</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
  <div v-else>
    <el-card shadow="never">
      <el-skeleton :loading="loading" animated>
        <el-row :gutter="16" justify="space-between">
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <div class="flex items-center">
              <div>
                <div class="text-20px">
                  {{ t('workplace.welcome') }} {{ username }} {{ t('workplace.happyDay') }}
                </div>
                <div class="mt-10px text-14px text-gray-500">
                  {{ t('workplace.toady') }}，20℃ - 32℃！
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-skeleton>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'
import {
  ArrowDown,
  ArrowUp,
  QuestionFilled
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import type { EChartsOption } from 'echarts'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'


// 定义告警类型
interface Alert {
  id: number;
  type: string;
  level: string;
  content: string;
  time: string;
  plant: string;
  location: string;
  suggestion: string;
}

// 定义水厂详情类型
interface PlantDetail {
  id: number;
  name: string;
  capacity: number;
  region: string;
  lat: number;
  lng: number;
  todayVolume?: number;
  startDate?: string;
  constructionType?: string;
  indicators?: Array<{ name: string, value: number }>;
  alerts?: Array<{ time: string, type: string, content: string, status: string }>;
  children?: PlantDetail[];
  parentId?: number;
}

defineOptions({ name: 'Home' })

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const loading = ref(true)
const username = computed(() => userStore.getUser?.nickname || '管理员')

// 时间周期选择
const timePeriod = ref('day')
const dateRange = ref([
  dayjs().subtract(7, 'day').toDate(),
  dayjs().toDate()
])
const datePickerType = computed(() => {
  switch (timePeriod.value) {
    case 'day': return 'daterange'
    case 'week': return 'weekrange'
    case 'month': return 'monthrange'
    case 'quarter': return 'quarterrange'
    case 'year': return 'yearrange'
    default: return 'daterange'
  }
})
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      return [
        dayjs().subtract(7, 'day').toDate(),
        dayjs().toDate()
      ]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      return [
        dayjs().subtract(1, 'month').toDate(),
        dayjs().toDate()
      ]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      return [
        dayjs().subtract(3, 'month').toDate(),
        dayjs().toDate()
      ]
    }
  }
]

// 生成随机数(模拟数据用)
const randomBetween = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1) + min)
}

// 生成随机浮点数
const randomFloat = (min: number, max: number, decimals = 2): number => {
  const val = Math.random() * (max - min) + min
  return parseFloat(val.toFixed(decimals))
}

// 生成区间内随机递增的数列
const generateRandomSeries = (count: number, min: number, max: number, increasing = true, deviation = 0.2): number[] => {
  const result: number[] = []
  let lastValue = min + Math.random() * (max - min) / 2

  for (let i = 0; i < count; i++) {
    result.push(lastValue)

    if (increasing) {
      // 基础递增趋势
      lastValue += randomFloat(0, deviation * (max - min))
      // 保证不超过最大值
      lastValue = Math.min(lastValue, max)
    } else {
      // 随机波动
      const change = randomFloat(-deviation * (max - min), deviation * (max - min))
      lastValue += change
      // 保证在范围内
      lastValue = Math.max(Math.min(lastValue, max), min)
    }
  }

  return result
}

// 水厂列表 
const plants = reactive<PlantDetail[]>([
  {
    id: 1,
    name: '经开净水厂',
    capacity: 50,
    region: '经开区',
    lat: 31.83,
    lng: 117.28,
    children: [
      { id: 101, name: '经开净水厂一二期', capacity: 25, region: '经开区', parentId: 1, lat: 31.83, lng: 117.28 },
      { id: 102, name: '经开净水厂三期', capacity: 15, region: '经开区', parentId: 1, lat: 31.83, lng: 117.28 },
      { id: 103, name: '经开净水厂四期', capacity: 10, region: '经开区', parentId: 1, lat: 31.83, lng: 117.28 }
    ]
  },
  { id: 2, name: '望塘净水厂', capacity: 35, region: '望塘区', lat: 31.82, lng: 117.22 },
  { id: 3, name: '塘西河净水厂', capacity: 45, region: '西河区', lat: 31.85, lng: 117.32 },
  { id: 4, name: '北涝圩净水厂', capacity: 30, region: '北区', lat: 31.90, lng: 117.24 },
  {
    id: 5,
    name: '长岗净水厂',
    capacity: 60,
    region: '长岗区',
    lat: 31.78,
    lng: 117.30,
    children: [
      { id: 501, name: '长岗净水厂三期', capacity: 20, region: '长岗区', parentId: 5, lat: 31.78, lng: 117.30 },
      { id: 502, name: '旺兴塘净水站', capacity: 8, region: '长岗区', parentId: 5, lat: 31.77, lng: 117.31 },
      { id: 503, name: '孙老堰河净水站', capacity: 7, region: '长岗区', parentId: 5, lat: 31.79, lng: 117.29 },
      { id: 504, name: '小庙净水站', capacity: 5, region: '长岗区', parentId: 5, lat: 31.76, lng: 117.30 },
      { id: 505, name: '枣林净水站', capacity: 6, region: '长岗区', parentId: 5, lat: 31.80, lng: 117.28 }
    ]
  },
  { id: 6, name: '西部组团净水一厂', capacity: 40, region: '西部区', lat: 31.72, lng: 117.20 },
  { id: 7, name: '西部组团净水二厂', capacity: 35, region: '西部区', lat: 31.74, lng: 117.18 },
  {
    id: 8,
    name: '巢湖净水中心',
    capacity: 55,
    region: '巢湖区',
    lat: 31.67,
    lng: 117.86,
    children: [
      { id: 801, name: '岗岭净水厂', capacity: 15, region: '巢湖区', parentId: 8, lat: 31.67, lng: 117.86 },
      { id: 802, name: '城北净水厂', capacity: 12, region: '巢湖区', parentId: 8, lat: 31.69, lng: 117.87 },
      { id: 803, name: '南岸（三胜）净水站', capacity: 8, region: '巢湖区', parentId: 8, lat: 31.65, lng: 117.85 },
      { id: 804, name: '散兵镇净水站', capacity: 5, region: '巢湖区', parentId: 8, lat: 31.70, lng: 117.84 },
      { id: 805, name: '银屏镇净水站', capacity: 4, region: '巢湖区', parentId: 8, lat: 31.68, lng: 117.82 },
      { id: 806, name: '烔炀镇净水站', capacity: 5, region: '巢湖区', parentId: 8, lat: 31.66, lng: 117.88 },
      { id: 807, name: '黄麓镇净水站', capacity: 4, region: '巢湖区', parentId: 8, lat: 31.63, lng: 117.85 },
      { id: 808, name: '黄麓师范学校净水站', capacity: 3, region: '巢湖区', parentId: 8, lat: 31.64, lng: 117.84 },
      { id: 809, name: '夏阁镇净水站', capacity: 4, region: '巢湖区', parentId: 8, lat: 31.62, lng: 117.87 },
      { id: 810, name: '中垾镇净水站', capacity: 3, region: '巢湖区', parentId: 8, lat: 31.71, lng: 117.89 },
      { id: 811, name: '柘皋镇净水站', capacity: 3, region: '巢湖区', parentId: 8, lat: 31.66, lng: 117.90 },
      { id: 812, name: '槐林镇净水站', capacity: 3, region: '巢湖区', parentId: 8, lat: 31.65, lng: 117.91 },
      { id: 813, name: '沐集社区净水站', capacity: 2, region: '巢湖区', parentId: 8, lat: 31.64, lng: 117.88 },
      { id: 814, name: '坝镇净水站', capacity: 2, region: '巢湖区', parentId: 8, lat: 31.67, lng: 117.92 }
    ]
  },
  {
    id: 9,
    name: '肥东净水中心',
    capacity: 50,
    region: '肥东区',
    lat: 31.88,
    lng: 117.47,
    children: [
      { id: 901, name: '肥东净水厂四期', capacity: 15, region: '肥东区', parentId: 9, lat: 31.88, lng: 117.47 },
      { id: 902, name: '长临河镇净水站', capacity: 6, region: '肥东区', parentId: 9, lat: 31.87, lng: 117.48 },
      { id: 903, name: '桥头集镇净水站', capacity: 5, region: '肥东区', parentId: 9, lat: 31.89, lng: 117.46 },
      { id: 904, name: '复兴净水站', capacity: 4, region: '肥东区', parentId: 9, lat: 31.90, lng: 117.45 },
      { id: 905, name: '长乐净水站', capacity: 5, region: '肥东区', parentId: 9, lat: 31.86, lng: 117.49 },
      { id: 906, name: '梁园镇净水站', capacity: 4, region: '肥东区', parentId: 9, lat: 31.91, lng: 117.44 },
      { id: 907, name: '石塘镇净水站', capacity: 5, region: '肥东区', parentId: 9, lat: 31.85, lng: 117.50 },
      { id: 908, name: '元疃镇净水站', capacity: 4, region: '肥东区', parentId: 9, lat: 31.92, lng: 117.43 },
      { id: 909, name: '陈集镇净水站', capacity: 3, region: '肥东区', parentId: 9, lat: 31.84, lng: 117.51 },
      { id: 910, name: '庐江净水中心', capacity: 10, region: '肥东区', parentId: 9, lat: 31.83, lng: 117.52 }
    ]
  },
  {
    id: 10,
    name: '同大镇净水站',
    capacity: 45,
    region: '庐江区',
    lat: 31.25,
    lng: 117.29,
    children: [
      { id: 1001, name: '白山镇净水站', capacity: 4, region: '庐江区', parentId: 10, lat: 31.26, lng: 117.28 },
      { id: 1002, name: '石头镇净水站', capacity: 3, region: '庐江区', parentId: 10, lat: 31.24, lng: 117.30 },
      { id: 1003, name: '盛桥镇净水站', capacity: 3, region: '庐江区', parentId: 10, lat: 31.27, lng: 117.27 },
      { id: 1004, name: '郭河镇净水站', capacity: 4, region: '庐江区', parentId: 10, lat: 31.23, lng: 117.31 },
      { id: 1005, name: '许桥集净水站', capacity: 3, region: '庐江区', parentId: 10, lat: 31.28, lng: 117.26 },
      { id: 1006, name: '龙桥镇净水站', capacity: 4, region: '庐江区', parentId: 10, lat: 31.22, lng: 117.32 },
      { id: 1007, name: '白湖镇净水站', capacity: 4, region: '庐江区', parentId: 10, lat: 31.29, lng: 117.25 },
      { id: 1008, name: '泥河镇净水站', capacity: 3, region: '庐江区', parentId: 10, lat: 31.21, lng: 117.33 },
      { id: 1009, name: '矾山镇净水站', capacity: 3, region: '庐江区', parentId: 10, lat: 31.30, lng: 117.24 },
      { id: 1010, name: '汤池镇净水站', capacity: 4, region: '庐江区', parentId: 10, lat: 31.20, lng: 117.34 },
      { id: 1011, name: '万山镇净水站', capacity: 3, region: '庐江区', parentId: 10, lat: 31.19, lng: 117.35 },
      { id: 1012, name: '金牛镇净水站', capacity: 3, region: '庐江区', parentId: 10, lat: 31.18, lng: 117.36 }
    ]
  }
])

// 获取所有水厂（扁平化结构，包含子级）
const allPlants = computed<PlantDetail[]>(() => {
  const result: PlantDetail[] = []

  // 添加父级水厂
  plants.forEach(plant => {
    result.push(plant)

    // 添加子级水厂
    if (plant.children && plant.children.length) {
      plant.children.forEach(child => {
        result.push(child)
      })
    }
  })

  return result
})

// 获取水厂详情（支持子级水厂）
const getPlantById = (id: number): PlantDetail | null => {
  // 首先检查顶级水厂
  const parentPlant = plants.find(p => p.id === id)
  if (parentPlant) return parentPlant

  // 如果不是顶级水厂，则查找子级
  for (const plant of plants) {
    if (plant.children && plant.children.length) {
      const childPlant = plant.children.find(c => c.id === id)
      if (childPlant) return childPlant
    }
  }

  return null
}

// 获取水厂的父级（如果有）
const getParentPlant = (id: number): PlantDetail | null => {
  for (const plant of plants) {
    if (plant.children && plant.children.length) {
      const isChild = plant.children.some(c => c.id === id)
      if (isChild) return plant
    }
  }

  return null
}

// A. 运行总览数据
const overviewCards = reactive([
  {
    title: '今日处理总量',
    value: '32.56',
    unit: '万m³',
    trend: 2.8,
    tooltip: '当前各厂处理水量总和，每小时更新',
    key: 'processVolume'
  },
  {
    title: '出水达标率',
    value: '98.7',
    unit: '%',
    trend: 0.5,
    tooltip: '出水各指标达标样本比例',
    key: 'passRate'
  },
  {
    title: '单位电耗均值',
    value: '0.42',
    unit: 'kWh/m³',
    trend: -1.3,
    tooltip: '各厂平均每立方米处理水量的电力消耗',
    key: 'electricity'
  },
  {
    title: '单位药耗均值',
    value: '0.19',
    unit: '元/m³',
    trend: -0.8,
    tooltip: '各厂平均每立方米处理水量的药剂费用',
    key: 'chemical'
  },
  {
    title: '当前在线设备率',
    value: '97.2',
    unit: '%',
    trend: 1.1,
    tooltip: '在线运行设备占总设备的比例',
    key: 'deviceOnlineRate'
  },
  {
    title: '今日告警数',
    value: '7',
    unit: '条',
    trend: -15.0,
    tooltip: '今日产生的所有级别告警总数',
    key: 'alertCount'
  }
])

// 告警数据
const alertTypeData = reactive([
  { value: 35, name: '设备类' },
  { value: 25, name: '水质类' },
  { value: 15, name: '通讯类' },
  { value: 10, name: '其他' }
])

const alertChartOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    right: 10,
    top: 'center',
    data: alertTypeData.map(item => item.name)
  },
  series: [
    {
      name: '告警类型',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 4,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: alertTypeData
    }
  ]
}))

// 最近告警列表
const recentAlerts = reactive<Alert[]>([
  {
    id: 1,
    type: '设备类',
    level: 'severe',
    content: '经开净水厂三期曝气池溶解氧低于阈值',
    time: '10分钟前',
    plant: '经开净水厂三期',
    location: '曝气池#2',
    suggestion: '检查曝气设备是否正常运行，调整鼓风量'
  },
  {
    id: 2,
    type: '水质类',
    level: 'warning',
    content: '塘西河净水厂进水氨氮超标',
    time: '25分钟前',
    plant: '塘西河净水厂',
    location: '进水口',
    suggestion: '增加处理药剂投加量，适当延长水力停留时间'
  },
  {
    id: 3,
    type: '通讯类',
    level: 'info',
    content: '望塘净水厂监测点数据传输中断',
    time: '42分钟前',
    plant: '望塘净水厂',
    location: '监测点#3',
    suggestion: '检查网络连接和传感器供电情况'
  },
  {
    id: 4,
    type: '设备类',
    level: 'warning',
    content: '长岗净水厂回流泵电流波动',
    time: '1小时前',
    plant: '长岗净水厂',
    location: '回流泵站',
    suggestion: '检查泵轴承是否磨损，电机是否过热'
  },
  {
    id: 5,
    type: '水质类',
    level: 'severe',
    content: '北涝圩净水厂出水浊度异常',
    time: '2小时前',
    plant: '北涝圩净水厂',
    location: '出水口',
    suggestion: '检查混凝剂投加系统，增加沉淀时间'
  }
])

// 当前告警详情
const currentAlert = ref<Alert | null>(null)
const alertDetailVisible = ref(false)
const showAlertDetail = (alert: Alert) => {
  currentAlert.value = alert
  alertDetailVisible.value = true
}

// 处理告警
const handleAlertAction = () => {
  // 这里添加告警处理逻辑
  ElMessage.success('告警已处理')
  alertDetailVisible.value = false
}

// 获取告警类型对应的Tag类型
const getAlertTypeTag = (type: string): string => {
  switch (type) {
    case '设备类':
      return 'danger'
    case '水质类':
      return 'warning'
    case '通讯类':
      return 'info'
    case 'severe':
      return 'danger'
    case 'warning':
      return 'warning'
    case 'info':
      return 'info'
    default:
      return ''
  }
}

// 导航至告警页面
const navigateToAlertPage = () => {
  // 跳转逻辑
  console.log('导航至告警页面')
}

const getAllApi = async () => {
  await Promise.all([
    // getCount()
    // getProject()
    // getNotice()
    // getShortcut()
    // getUserAccessSource()
    // getWeeklyUserActivity()
  ])
  loading.value = false
}

getAllApi()

// B. 运行趋势分析数据
const trendMetric = ref('processVolume')
const trendChartData = reactive({
  processVolume: {
    xAxisData: ['6/1', '6/2', '6/3', '6/4', '6/5', '6/6', '6/7'],
    series: [
      {
        name: '经开净水厂',
        data: [5.2, 4.9, 5.1, 5.4, 5.2, 5.5, 5.3],
        type: 'bar',
        stack: 'total',
        barMaxWidth: 35
      },
      {
        name: '望塘净水厂',
        data: [3.1, 3.3, 3.2, 3.0, 3.4, 3.1, 3.2],
        type: 'bar',
        stack: 'total',
        barMaxWidth: 35
      },
      {
        name: '塘西河净水厂',
        data: [4.3, 4.2, 4.4, 4.1, 4.5, 4.3, 4.2],
        type: 'bar',
        stack: 'total',
        barMaxWidth: 35
      },
      {
        name: '北涝圩净水厂',
        data: [2.8, 2.9, 2.7, 3.0, 2.8, 2.9, 3.1],
        type: 'bar',
        stack: 'total',
        barMaxWidth: 35
      },
      {
        name: '长岗净水厂',
        data: [5.5, 5.3, 5.6, 5.8, 5.4, 5.9, 5.7],
        type: 'bar',
        stack: 'total',
        barMaxWidth: 35
      }
    ]
  },
  passRate: {
    xAxisData: ['6/1', '6/2', '6/3', '6/4', '6/5', '6/6', '6/7'],
    series: [
      {
        name: '全集团平均',
        data: [97.5, 97.8, 98.1, 98.5, 98.7, 98.4, 98.7],
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          shadowColor: 'rgba(0,0,0,0.2)',
          shadowBlur: 10
        },
        itemStyle: {
          borderWidth: 3
        }
      },
      {
        name: '经开净水厂',
        data: [98.1, 98.4, 98.7, 99.0, 98.9, 98.8, 99.1],
        type: 'line',
        smooth: true
      },
      {
        name: '望塘净水厂',
        data: [96.8, 97.2, 97.5, 98.0, 98.4, 97.9, 98.2],
        type: 'line',
        smooth: true
      },
      {
        name: '塘西河净水厂',
        data: [97.5, 97.8, 98.0, 98.2, 98.5, 98.3, 98.6],
        type: 'line',
        smooth: true
      }
    ]
  }
})

const trendChartOptions = computed<EChartsOption>(() => {
  const isProcessVolume = trendMetric.value === 'processVolume'

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: isProcessVolume ? 'shadow' : 'line'
      }
    },
    legend: {
      data: trendChartData[trendMetric.value].series.map(item => item.name),
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '60px',
      top: '30px',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: trendChartData[trendMetric.value].xAxisData
    },
    yAxis: {
      type: 'value',
      name: isProcessVolume ? '处理量(万m³)' : '达标率(%)',
      min: isProcessVolume ? 0 : 95
    },
    series: trendChartData[trendMetric.value].series
  }
})

// 切换趋势指标
const changeTrendMetric = (metric: string) => {
  trendMetric.value = metric
}

// 导出趋势数据
const exportTrendData = () => {
  ElMessage.success('趋势数据导出成功')
}

// D. 能耗与药耗分析数据
const consumptionType = ref('electricity')

// 电耗排行数据
const electricityConsumptionData = reactive({
  names: ['经开净水厂', '望塘净水厂', '塘西河净水厂', '北涝圩净水厂', '长岗净水厂', '西部组团净水一厂', '西部组团净水二厂', '巢湖净水中心', '肥东净水中心', '同大镇净水站'],
  values: [0.37, 0.39, 0.41, 0.42, 0.43, 0.44, 0.45, 0.46, 0.47, 0.48],
  changes: [-3.2, -2.1, -1.5, -0.8, 0, 0.5, 1.2, 1.8, 2.3, 3.1]
})

// 药耗排行数据
const chemicalConsumptionData = reactive({
  names: ['巢湖净水中心', '经开净水厂', '望塘净水厂', '塘西河净水厂', '长岗净水厂', '北涝圩净水厂', '西部组团净水一厂', '肥东净水中心', '西部组团净水二厂', '同大镇净水站'],
  values: [0.15, 0.16, 0.17, 0.18, 0.19, 0.21, 0.22, 0.23, 0.24, 0.26],
  changes: [-5.1, -3.8, -2.5, -1.2, 0, 0.7, 1.5, 2.2, 3.4, 4.1]
})

// 分时能耗数据
const hourlyConsumptionData = reactive({
  hours: Array.from({ length: 24 }, (_, i) => i),
  electricityValues: [
    0.45, 0.43, 0.41, 0.40, 0.42, 0.47, 0.53, 0.59, 0.55, 0.52, 0.50, 0.51,
    0.54, 0.56, 0.53, 0.52, 0.54, 0.57, 0.61, 0.59, 0.56, 0.53, 0.49, 0.46
  ],
  loadRate: [
    68, 65, 63, 62, 64, 72, 82, 91, 87, 84, 81, 83,
    85, 87, 84, 83, 85, 89, 92, 90, 87, 83, 78, 73
  ]
})

const consumptionChartOptions = computed<EChartsOption>(() => {
  const isElectricity = consumptionType.value === 'electricity'
  const data = isElectricity ? electricityConsumptionData : chemicalConsumptionData

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        const index = params[0].dataIndex
        return `${data.names[index]}<br/>
                ${isElectricity ? '电耗' : '药耗'}: ${data.values[index]} ${isElectricity ? 'kWh/m³' : '元/m³'}<br/>
                环比: ${data.changes[index] > 0 ? '+' : ''}${data.changes[index]}%`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '30px',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: isElectricity ? 'kWh/m³' : '元/m³'
    },
    yAxis: {
      type: 'category',
      data: data.names,
      inverse: true,
      axisLabel: {
        fontSize: 12
      }
    },
    visualMap: {
      orient: 'horizontal',
      left: 'center',
      min: -5,
      max: 5,
      text: ['环比增加', '环比减少'],
      dimension: 0,
      inRange: {
        color: ['#52c41a', '#faad14', '#f5222d']
      },
      show: false
    },
    series: [
      {
        name: isElectricity ? '电耗' : '药耗',
        type: 'bar',
        data: data.values.map((value, index) => ({
          value,
          itemStyle: {
            color: data.changes[index] <= 0 ? '#52c41a' : data.changes[index] < 2 ? '#faad14' : '#f5222d'
          }
        })),
        label: {
          show: true,
          position: 'right',
          formatter: function (params) {
            const index = params.dataIndex
            return `${params.value} ${data.changes[index] > 0 ? '+' : ''}${data.changes[index]}%`
          }
        }
      }
    ]
  }
})

const hourlyConsumptionOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend: {
    data: ['电耗', '负荷率'],
    bottom: 0
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '60px',
    top: '30px',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: hourlyConsumptionData.hours.map(h => `${h}:00`),
    axisPointer: {
      type: 'shadow'
    }
  },
  yAxis: [
    {
      type: 'value',
      name: '电耗(kWh/m³)',
      min: 0.3,
      max: 0.7,
      interval: 0.1
    },
    {
      type: 'value',
      name: '负荷率(%)',
      min: 50,
      max: 100,
      interval: 10
    }
  ],
  series: [
    {
      name: '电耗',
      type: 'bar',
      yAxisIndex: 0,
      data: hourlyConsumptionData.electricityValues,
      barMaxWidth: 30
    },
    {
      name: '负荷率',
      type: 'line',
      yAxisIndex: 1,
      data: hourlyConsumptionData.loadRate,
      smooth: true,
      lineStyle: {
        width: 3
      },
      itemStyle: {
        borderWidth: 2
      }
    }
  ]
}))

// 切换能耗类型
const changeConsumptionType = (type: string) => {
  consumptionType.value = type
}

// 跳转到详细分析页面
const navigateToAnalysis = () => {
  if (consumptionType.value === 'electricity') {
    router.push('/energy/electricityConsumptionAnalysis')
  } else if (consumptionType.value === 'chemical') {
    router.push('/energy/drugConsumptionAnalysis')
  } else {
    router.push('/energy/electricityConsumptionAnalysis')
  }
}

// C. 水厂KPI绩效分析
const kpiDisplayMode = ref('table')

// KPI表格数据
const kpiTableData = reactive([
  { name: '经开净水厂', passRate: 98.9, loadRate: 85, electricity: 0.39, chemical: 0.17, reportRate: 100, inspectionRate: 93, faultRate: 0.8, kpiScore: 95.3 },
  { name: '望塘净水厂', passRate: 97.9, loadRate: 78, electricity: 0.44, chemical: 0.19, reportRate: 100, inspectionRate: 88, faultRate: 1.5, kpiScore: 91.2 },
  { name: '塘西河净水厂', passRate: 98.6, loadRate: 82, electricity: 0.42, chemical: 0.22, reportRate: 98, inspectionRate: 90, faultRate: 1.1, kpiScore: 92.8 },
  { name: '北涝圩净水厂', passRate: 98.2, loadRate: 75, electricity: 0.45, chemical: 0.21, reportRate: 100, inspectionRate: 91, faultRate: 0.9, kpiScore: 93.5 },
  { name: '长岗净水厂', passRate: 99.1, loadRate: 88, electricity: 0.37, chemical: 0.16, reportRate: 100, inspectionRate: 95, faultRate: 0.5, kpiScore: 96.7 },
  { name: '西部组团净水一厂', passRate: 97.5, loadRate: 70, electricity: 0.48, chemical: 0.24, reportRate: 96, inspectionRate: 87, faultRate: 1.8, kpiScore: 88.5 },
  { name: '西部组团净水二厂', passRate: 98.4, loadRate: 80, electricity: 0.43, chemical: 0.18, reportRate: 98, inspectionRate: 89, faultRate: 1.3, kpiScore: 92.1 },
  { name: '巢湖净水中心', passRate: 98.8, loadRate: 84, electricity: 0.41, chemical: 0.15, reportRate: 100, inspectionRate: 94, faultRate: 0.7, kpiScore: 94.8 },
  { name: '肥东净水中心', passRate: 97.8, loadRate: 72, electricity: 0.47, chemical: 0.23, reportRate: 95, inspectionRate: 85, faultRate: 1.7, kpiScore: 89.3 },
  { name: '同大镇净水站', passRate: 98.0, loadRate: 76, electricity: 0.46, chemical: 0.26, reportRate: 97, inspectionRate: 86, faultRate: 1.6, kpiScore: 90.2 }
])

// KPI表格列定义
const kpiColumns = [
  { prop: 'passRate', label: '达标率(%)', sortable: true, width: 100, formatter: (row: any) => row.passRate.toFixed(1) + '%' },
  { prop: 'loadRate', label: '负荷率(%)', sortable: true, width: 100, formatter: (row: any) => row.loadRate.toFixed(0) + '%' },
  { prop: 'electricity', label: '电耗', sortable: true, width: 90, formatter: (row: any) => row.electricity.toFixed(2) },
  { prop: 'chemical', label: '药耗', sortable: true, width: 90, formatter: (row: any) => row.chemical.toFixed(2) },
  { prop: 'reportRate', label: '报表率(%)', sortable: true, width: 100, formatter: (row: any) => row.reportRate.toFixed(0) + '%' },
  { prop: 'inspectionRate', label: '巡检率(%)', sortable: true, width: 100, formatter: (row: any) => row.inspectionRate.toFixed(0) + '%' },
  { prop: 'faultRate', label: '故障率(%)', sortable: true, width: 100, formatter: (row: any) => row.faultRate.toFixed(1) + '%' },
  { prop: 'kpiScore', label: 'KPI评分', sortable: true, width: 100, formatter: (row: any) => row.kpiScore.toFixed(1) }
]

// 雷达图比较
const selectedPlants = ref([1, 5]) // 默认选中经开净水厂和长岗净水厂

// KPI维度定义
const kpiDimensions = [
  { name: '达标率', max: 100 },
  { name: '负荷率', max: 100 },
  { name: '电耗效率', max: 100 },
  { name: '药耗效率', max: 100 },
  { name: '报表率', max: 100 },
  { name: '巡检率', max: 100 },
  { name: '设备完好率', max: 100 }
]

// KPI雷达图配置
const kpiRadarOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    data: selectedPlants.value.map(id => {
      const plant = allPlants.value.find(p => p.id === id)
      return plant ? plant.name : ''
    }),
    bottom: 0
  },
  radar: {
    indicator: kpiDimensions,
    radius: '60%',
    center: ['50%', '55%'],
    axisName: {
      color: '#333',
      fontSize: 12
    }
  },
  series: [
    {
      type: 'radar',
      data: kpiRadarData.value,
      areaStyle: {
        opacity: 0.1
      },
      lineStyle: {
        width: 2
      }
    }
  ]
}))

// KPI雷达图数据
const kpiRadarData = computed(() => {
  const data: any[] = []

  selectedPlants.value.forEach(plantId => {
    const plant = allPlants.value.find(p => p.id === plantId)
    if (plant) {
      const kpiData = kpiTableData.find(k => k.name === plant.name)
      if (kpiData) {
        data.push({
          value: [
            kpiData.passRate,
            kpiData.loadRate,
            100 - (kpiData.electricity / 0.5) * 100, // 电耗效率转换
            100 - (kpiData.chemical / 0.3) * 100, // 药耗效率转换
            kpiData.reportRate,
            kpiData.inspectionRate,
            100 - kpiData.faultRate * 10 // 设备完好率转换
          ],
          name: plant.name
        })
      }
    }
  })

  return data
})

// 切换KPI显示模式
const changeKpiDisplayMode = (mode: string) => {
  kpiDisplayMode.value = mode
}

// 更新雷达图
const updateRadarChart = () => {
  // 雷达图自动更新，这里可以添加其他逻辑
  if (selectedPlants.value.length > 4) {
    selectedPlants.value = selectedPlants.value.slice(0, 4)
    ElMessage.warning('最多只能选择4个水厂进行对比')
  }
}

// E. 运维保障与数据质量监测数据
// 设备运行状态
const deviceStatus = reactive({
  total: 524,
  online: 509,
  offline: 11,
  fault: 4,
  faultRate: 0.76,
  trendData: [0.9, 0.85, 0.78, 0.82, 0.8, 0.76, 0.74]
})

// 设备故障率趋势图配置
const deviceTrendOptions = computed<EChartsOption>(() => ({
  grid: {
    top: 5,
    right: 5,
    bottom: 5,
    left: 5,
    containLabel: false
  },
  xAxis: {
    type: 'category',
    show: false,
    data: ['5/31', '6/1', '6/2', '6/3', '6/4', '6/5', '6/6']
  },
  yAxis: {
    type: 'value',
    show: false,
    min: 0,
    max: 2
  },
  series: [
    {
      data: deviceStatus.trendData,
      type: 'line',
      smooth: true,
      symbol: 'none',
      lineStyle: {
        width: 2,
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(24,144,255,0.2)' },
            { offset: 1, color: 'rgba(24,144,255,0)' }
          ]
        }
      }
    }
  ]
}))

// 巡检任务完成率数据
const inspectionData = reactive({
  planned: 432,
  completed: 412,
  rate: 95.4,
  monthOnMonth: 2.1
})

// 巡检任务环形图配置
const inspectionChartOptions = computed<EChartsOption>(() => ({
  tooltip: {
    formatter: '{b}: {c} ({d}%)'
  },
  series: [
    {
      name: '巡检任务',
      type: 'pie',
      radius: ['60%', '80%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'center',
        formatter: `{b|${inspectionData.rate}%}\n{c|完成率}`,
        rich: {
          b: {
            fontSize: 20,
            fontWeight: 'bold',
            color: '#52c41a',
            padding: [0, 0, 5, 0]
          },
          c: {
            fontSize: 14,
            color: '#999'
          }
        }
      },
      data: [
        {
          value: inspectionData.completed,
          name: '已完成',
          itemStyle: { color: '#52c41a' }
        },
        {
          value: inspectionData.planned - inspectionData.completed,
          name: '未完成',
          itemStyle: { color: '#f0f0f0' },
          label: { show: false }
        }
      ]
    }
  ]
}))

// 数据异常监测
const dataQualityIssues = reactive([
  { name: '异常采集数', value: 37, unit: '条', color: '#faad14' },
  { name: '数据延迟', value: 15, unit: '分钟', color: '#52c41a' },
  { name: '超限值', value: 8, unit: '条', color: '#f5222d' },
  { name: '空值率', value: 0.5, unit: '%', color: '#52c41a' }
])

// 运维模块命令处理
const handleMaintenanceCommand = (command: string) => {
  switch (command) {
    case 'device':
      ElMessage.info('跳转到设备详情页')
      break
    case 'inspection':
      ElMessage.info('跳转到巡检任务页')
      break
    case 'dataQuality':
      ElMessage.info('跳转到数据质量监测页')
      break
  }
}

// F. 地图与空间分布数据
const mapLayers = ref(['water'])
const mapInstance = ref(null)

// 更新地图图层
const updateMapLayers = () => {
  // 在这里更新地图图层显示，集成实际地图时实现
  console.log('图层更新:', mapLayers.value)
}

// 水厂状态计算
const getPlantStatus = (plantId: number) => {
  // 示例：根据告警数量和设备状态计算水厂整体状态
  const alertCount = recentAlerts.filter(a => a.plant === getPlantById(plantId)?.name).length
  if (alertCount > 1) return 'warning' // 黄色
  if (alertCount > 0) return 'severe' // 红色
  return 'normal' // 绿色
}

// 水厂详情
const selectedPlantDetail = ref<PlantDetail | null>(null)
const plantDetailVisible = ref(false)

// 显示水厂详情
const showPlantDetail = (plantId: number) => {
  const plant = getPlantById(plantId)
  if (plant) {
    selectedPlantDetail.value = {
      ...plant,
      todayVolume: randomFloat(plant.capacity * 0.7, plant.capacity * 0.9, 2),
      startDate: '2018-05-15',
      constructionType: '改扩建',
      indicators: [
        { name: '达标率', value: randomFloat(95, 99.5, 1) },
        { name: '负荷率', value: randomFloat(70, 90) },
        { name: '能耗指数', value: randomFloat(75, 95) },
        { name: '药耗指数', value: randomFloat(80, 95) },
        { name: '设备完好率', value: randomFloat(90, 99) }
      ],
      alerts: Array.from({ length: 5 }, (_, i) => ({
        time: `2023-06-${String(6 - i).padStart(2, '0')} ${randomBetween(8, 20)}:${String(randomBetween(0, 59)).padStart(2, '0')}`,
        type: ['设备类', '水质类', '通讯类'][randomBetween(0, 2)],
        content: `${plant.name}${['进水流量异常', '水泵振动过大', '曝气溶解氧不足', 'PH值超限', '浊度传感器故障'][randomBetween(0, 4)]}`,
        status: i > 2 ? '已处理' : '处理中'
      }))
    }
    plantDetailVisible.value = true
  }
}

// 水厂性能图表配置
const plantPerformanceOptions = computed<EChartsOption>(() => {
  if (!selectedPlantDetail.value) return {}

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '30px',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: 100
    },
    yAxis: {
      type: 'category',
      data: selectedPlantDetail.value.indicators?.map(item => item.name) || [],
      inverse: true
    },
    series: [
      {
        name: '指标表现',
        type: 'bar',
        data: selectedPlantDetail.value.indicators?.map(item => ({
          value: item.value,
          itemStyle: {
            color: getProgressColor(item.value)
          }
        })) || [],
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%'
        }
      }
    ]
  }
})

// 获取进度条颜色
const getProgressColor = (value: number) => {
  if (value >= 95) return '#52c41a' // 绿色
  if (value >= 85) return '#1890ff' // 蓝色
  if (value >= 75) return '#faad14' // 黄色
  return '#f5222d' // 红色
}

// 跳转至水厂详情页
const navigateToPlantDetail = () => {
  if (selectedPlantDetail.value) {
    // 跳转逻辑
    ElMessage.info(`跳转到${selectedPlantDetail.value.name}详情页`)
    plantDetailVisible.value = false
  }
}

// 视图模式控制
const viewMode = ref('global') // 'global' 或 'single'
const selectedPlantId = ref<number | null>(null)

// 单厂数据
const singlePlantData = reactive({
  processVolume: 5.35, // 万m³
  passRate: 99.1, // %
  loadRate: 78, // %
  electricity: 0.39, // kWh/m³
  chemical: 0.17, // 元/m³
  deviceOnlineRate: 97.8, // %
  inspectionRate: 89, // %
  kpiScore: 93.5, // 评分
  alertCount: 2 // 条
})

// 处理视图模式变化
const handleViewModeChange = (mode: string) => {
  viewMode.value = mode
  if (mode === 'global') {
    // 切换到全局视图
    selectedPlantId.value = null
  } else if (mode === 'single' && !selectedPlantId.value) {
    // 默认选择第一个水厂
    selectedPlantId.value = plants[0].id
    handleSinglePlantChange(selectedPlantId.value)
  }
  loadData()
}

// 处理单厂切换
const handleSinglePlantChange = (plantId: number) => {
  selectedPlantId.value = plantId
  const plant = getPlantById(plantId)
  if (plant) {
    // 从KPI表格数据中获取选中厂的数据
    const kpiData = kpiTableData.find(k => k.name === plant.name)
    if (kpiData) {
      singlePlantData.passRate = kpiData.passRate
      singlePlantData.loadRate = kpiData.loadRate
      singlePlantData.electricity = kpiData.electricity
      singlePlantData.chemical = kpiData.chemical
      singlePlantData.deviceOnlineRate = 100 - kpiData.faultRate * 10
      singlePlantData.inspectionRate = kpiData.inspectionRate
      singlePlantData.kpiScore = kpiData.kpiScore
    } else {
      // 如果没有精确匹配的KPI数据，使用默认值或父级水厂的数据
      const parentPlant = getParentPlant(plantId)
      if (parentPlant) {
        const parentKpiData = kpiTableData.find(k => k.name === parentPlant.name)
        if (parentKpiData) {
          singlePlantData.passRate = parentKpiData.passRate
          singlePlantData.loadRate = parentKpiData.loadRate
          singlePlantData.electricity = parentKpiData.electricity
          singlePlantData.chemical = parentKpiData.chemical
          singlePlantData.deviceOnlineRate = 100 - parentKpiData.faultRate * 10
          singlePlantData.inspectionRate = parentKpiData.inspectionRate
          singlePlantData.kpiScore = parentKpiData.kpiScore
        }
      }
    }

    // 生成随机处理量（实际项目中应从API获取）
    singlePlantData.processVolume = randomFloat(plant.capacity * 0.7, plant.capacity * 0.9, 2)

    // 获取告警数量
    singlePlantData.alertCount = recentAlerts.filter(a => a.plant === plant.name).length
  }

  // 更新图表数据等...
  loadData()
}

// 获取单厂卡片值
const getCardSingleValue = (key: string) => {
  if (!key || viewMode.value !== 'single') return '0'

  // 根据key返回对应的单厂数据
  switch (key) {
    case 'processVolume':
      return singlePlantData.processVolume.toFixed(2)
    case 'passRate':
      return singlePlantData.passRate.toFixed(1)
    case 'electricity':
      return singlePlantData.electricity.toFixed(2)
    case 'chemical':
      return singlePlantData.chemical.toFixed(2)
    case 'deviceOnlineRate':
      return singlePlantData.deviceOnlineRate.toFixed(1)
    case 'alertCount':
      return singlePlantData.alertCount.toString()
    default:
      return '0'
  }
}

// 获取单个水厂名称
const getSinglePlantName = () => {
  if (viewMode.value !== 'single' || !selectedPlantId.value) return ''
  const plant = getPlantById(selectedPlantId.value)
  return plant ? plant.name : ''
}

// 在script部分添加 filteredAlerts 计算属性
// 根据视图模式过滤告警数据
const filteredAlerts = computed(() => {
  if (viewMode.value === 'single' && selectedPlantId.value) {
    const plant = getPlantById(selectedPlantId.value)
    if (plant) {
      return recentAlerts.filter(alert => alert.plant === plant.name)
    }
  }
  return recentAlerts
})

// 模拟加载数据
const loadData = () => {
  loading.value = true

  // 模拟异步请求
  setTimeout(() => {
    // 根据视图模式调整数据
    if (viewMode.value === 'single') {
      // 单厂视图下调整图表数据
      if (selectedPlantId.value) {
        const plant = getPlantById(selectedPlantId.value)
        if (plant) {
          // 更新单厂处理量趋势
          if (trendChartData.processVolume.series.length > 0) {
            // 找到选中厂的数据
            const plantSeries = trendChartData.processVolume.series.find(s => s.name === plant.name)
            if (plantSeries) {
              // 创建新的趋势数据，仅保留选中厂
              trendChartData.processVolume.series = [
                {
                  ...plantSeries,
                  stack: undefined as any, // 单厂不需要堆叠
                  barWidth: 40 as any, // 单厂柱宽度可以增大
                  itemStyle: {
                    color: '#1890ff' // 统一颜色
                  }
                }
              ]
            }
          }

          // 更新单厂达标率趋势
          if (trendChartData.passRate.series.length > 0) {
            // 找到选中厂的数据
            const plantSeries = trendChartData.passRate.series.find(s => s.name === plant.name)
            if (plantSeries) {
              // 创建新的趋势数据，仅保留选中厂
              trendChartData.passRate.series = [
                {
                  ...plantSeries,
                  lineStyle: {
                    width: 3,
                    shadowColor: 'rgba(0,0,0,0.2)',
                    shadowBlur: 10,
                    color: '#1890ff' as any
                  },
                  itemStyle: {
                    borderWidth: 3,
                    color: '#1890ff' as any
                  }
                }
              ]
            }
          }

          // 更新告警饼图
          // 这里模拟更新操作，实际项目中应该从API获取数据
          const plantAlerts = filteredAlerts.value
          const typeCount: Record<string, number> = {}

          plantAlerts.forEach(alert => {
            if (!typeCount[alert.type]) {
              typeCount[alert.type] = 0
            }
            typeCount[alert.type]++
          })

          // 创建新的告警类型数据
          const newAlertTypeData: any[] = []
          for (const type in typeCount) {
            newAlertTypeData.push({
              name: type,
              value: typeCount[type]
            })
          }

          // 如果没有告警，添加一个空数据
          if (newAlertTypeData.length === 0) {
            newAlertTypeData.push({
              name: '无告警',
              value: 1
            })
          }

          // 更新告警类型数据
          alertTypeData.length = 0
          newAlertTypeData.forEach(item => alertTypeData.push(item))
        }
      }
    } else {
      // 全局视图 - 重置数据到默认状态
      // 这里可以重新加载全局数据
      // 实际项目中应该从API获取数据

      // 示例：重置趋势图到多厂模式
      // 这里可以调用API重新加载数据
    }

    loading.value = false
  }, 800)
}

// 页面加载完成后初始化
const onMounted = () => {
  // 默认选择全局视图
  viewMode.value = 'global'
  loadData()

  // 延迟一点初始化地图，确保DOM已渲染
  nextTick(() => {
    // initMap()
  })
}

onMounted()

// 处理时间周期变化
const handlePeriodChange = (period: string) => {
  timePeriod.value = period
  // 根据选择的周期调整日期选择器类型和默认值
  switch (period) {
    case 'day':
      dateRange.value = [
        dayjs().subtract(7, 'day').toDate(),
        dayjs().toDate()
      ]
      break
    case 'week':
      dateRange.value = [
        dayjs().subtract(4, 'week').startOf('week').toDate(),
        dayjs().endOf('week').toDate()
      ]
      break
    case 'month':
      dateRange.value = [
        dayjs().subtract(6, 'month').startOf('month').toDate(),
        dayjs().endOf('month').toDate()
      ]
      break
    case 'quarter':
      dateRange.value = [
        dayjs().subtract(4, 'quarter' as any).startOf('quarter' as any).toDate(),
        dayjs().endOf('quarter' as any).toDate()
      ]
      break
    case 'year':
      dateRange.value = [
        dayjs().subtract(2, 'year').startOf('year').toDate(),
        dayjs().endOf('year').toDate()
      ]
      break
  }
  loadData()
}

// 处理日期范围变化
const handleDateChange = (value: any) => {
  if (value) {
    dateRange.value = value
    loadData()
  }
}

// 导出报表
const exportReport = () => {
  ElMessage.success('报表已导出')
}

// 获取单厂KPI值
const getSingleKpiValue = (key: string) => {
  switch (key) {
    case 'passRate':
      return singlePlantData.passRate.toFixed(1)
    case 'loadRate':
      return singlePlantData.loadRate.toFixed(0)
    case 'electricity':
      return singlePlantData.electricity.toFixed(2)
    case 'chemical':
      return singlePlantData.chemical.toFixed(2)
    case 'inspectionRate':
      return singlePlantData.inspectionRate.toFixed(0)
    case 'kpiScore':
      return singlePlantData.kpiScore.toFixed(1)
    default:
      return '0'
  }
}

// 单厂KPI趋势图配置
const singlePlantKpiOptions = computed<EChartsOption>(() => {
  // 生成近7个月的数据
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
  // 生成KPI评分趋势数据
  const kpiScoreData = generateRandomSeries(7, 85, 95, true, 0.05).map(v => parseFloat(v.toFixed(1)))

  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['KPI评分', '达标率', '负荷率'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '50px',
      top: '30px',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: [
      {
        type: 'value',
        name: '评分/百分比(%)',
        min: 70,
        max: 100
      },
      {
        type: 'value',
        name: '耗能指标',
        min: 0,
        max: 1,
        position: 'right',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: 'KPI评分',
        type: 'line',
        data: kpiScoreData,
        lineStyle: {
          width: 3
        }
      },
      {
        name: '达标率',
        type: 'line',
        data: generateRandomSeries(7, 95, 100, true, 0.02).map(v => parseFloat(v.toFixed(1))),
        lineStyle: {
          width: 2
        }
      },
      {
        name: '负荷率',
        type: 'line',
        data: generateRandomSeries(7, 70, 90, false, 0.1).map(v => parseFloat(v.toFixed(0))),
        lineStyle: {
          width: 2
        }
      },
      {
        name: '电耗',
        type: 'bar',
        yAxisIndex: 1,
        data: generateRandomSeries(7, 0.35, 0.45, false, 0.1).map(v => parseFloat(v.toFixed(2))),
        barMaxWidth: 10
      },
      {
        name: '药耗',
        type: 'bar',
        yAxisIndex: 1,
        data: generateRandomSeries(7, 0.15, 0.25, false, 0.1).map(v => parseFloat(v.toFixed(2))),
        barMaxWidth: 10
      }
    ]
  }
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;

  .time-period-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left-controls {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .view-selector {
      display: flex;
      align-items: center;
      gap: 10px;

      .view-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .section-card {
    margin-bottom: 20px;
    border-radius: 4px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 16px;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
  }

  .overview-section {
    .stat-card {
      background-color: #f9f9f9;
      border-radius: 4px;
      padding: 15px;
      height: 120px;
      position: relative;
      margin-bottom: 16px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .stat-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .stat-title {
          font-size: 14px;
          color: #606266;
        }
      }

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin: 10px 0;

        .stat-unit {
          font-size: 14px;
          font-weight: normal;
          color: #909399;
          margin-left: 5px;
        }
      }

      .stat-compare {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 5px;

        &.up {
          color: #f56c6c;
        }

        &.down {
          color: #67c23a;
        }
      }

      .stat-mode-indicator {
        position: absolute;
        top: 10px;
        right: 10px;
      }
    }

    .alert-container {
      height: 100%;
      display: flex;
      flex-direction: column;

      .alert-chart {
        flex: 0 0 auto;
      }

      .alert-list {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .alert-list-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          padding: 0 10px;
        }

        .alert-item {
          padding: 8px 10px;
          border-bottom: 1px solid #ebeef5;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: background-color 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }

          &.severe {
            background-color: #fef0f0;

            &:hover {
              background-color: #fde2e2;
            }
          }

          .alert-content {
            flex: 1;
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .alert-time {
            color: #909399;
            font-size: 12px;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .main-content {
    margin-bottom: 20px;
  }

  .kpi-table {
    max-height: 350px;
    overflow: auto;
  }

  .kpi-radar {
    .plant-selector {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .maintenance-card {
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;

    .maintenance-header {
      font-weight: bold;
      margin-bottom: 10px;
    }

    .device-counts {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;

      .device-count-item {
        text-align: center;
        flex: 1;

        .count-value {
          font-size: 20px;
          font-weight: bold;
        }

        .count-label {
          font-size: 12px;
          color: #909399;
        }

        &.online {
          color: #67c23a;
        }

        &.offline {
          color: #e6a23c;
        }

        &.fault {
          color: #f56c6c;
        }
      }
    }

    .device-trend {
      .trend-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        font-size: 13px;

        .trend-value {
          font-weight: bold;
        }
      }
    }
  }

  .inspection-stats {
    margin-top: 10px;

    .inspection-stat-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      font-size: 13px;

      .value {
        font-weight: bold;

        &.up {
          color: #f56c6c;
        }

        &.down {
          color: #67c23a;
        }
      }
    }
  }

  .data-quality {
    margin-top: 15px;

    .quality-header {
      font-weight: bold;
      margin-bottom: 10px;
    }

    .quality-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      margin-bottom: 10px;

      .quality-content {
        .quality-name {
          font-size: 13px;
          margin-bottom: 5px;
        }

        .quality-value {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }

  .map-section {
    .map-container {
      height: 400px;
      background-color: #f5f7fa;
    }
  }

  .plant-detail {
    .indicator-list {
      .indicator-item {
        margin-bottom: 15px;

        .indicator-name {
          display: block;
          margin-bottom: 5px;
        }
      }
    }
  }
}

.single-plant-kpi {
  .kpi-metrics {
    margin-bottom: 20px;

    .kpi-col {
      margin-bottom: 16px;
    }

    .kpi-metric-card {
      background-color: #f9fbfd;
      border-radius: 8px;
      padding: 16px;
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      border: 1px solid #ebeef5;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.09);
        transform: translateY(-2px);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
      }

      &.kpi-color-blue::before {
        background: linear-gradient(to bottom, #409EFF, #79bbff);
      }

      &.kpi-color-green::before {
        background: linear-gradient(to bottom, #67C23A, #95d475);
      }

      &.kpi-color-purple::before {
        background: linear-gradient(to bottom, #8e44ad, #9b59b6);
      }

      &.kpi-color-orange::before {
        background: linear-gradient(to bottom, #E6A23C, #f3d19e);
      }

      &.kpi-color-cyan::before {
        background: linear-gradient(to bottom, #00c1de, #7fdbda);
      }

      &.kpi-color-magenta::before {
        background: linear-gradient(to bottom, #eb2f96, #ff85c0);
      }

      .kpi-metric-header {
        font-size: 15px;
        color: #606266;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .kpi-metric-value {
        font-size: 26px;
        font-weight: bold;
        color: #303133;
        margin: 8px 0;

        .unit {
          font-size: 14px;
          color: #909399;
          margin-left: 4px;
          font-weight: normal;
        }
      }

      .kpi-metric-trend {
        display: flex;
        align-items: center;
        font-size: 13px;
        font-weight: 500;
        border-radius: 12px;
        padding: 4px 8px;
        width: fit-content;
        margin-top: 8px;

        &.up {
          color: #f56c6c;
          background-color: rgba(245, 108, 108, 0.1);
        }

        &.down {
          color: #67c23a;
          background-color: rgba(103, 194, 58, 0.1);
        }

        .el-icon {
          margin-left: 4px;
        }
      }
    }
  }

  .kpi-trend-chart {
    margin-top: 16px;
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #ebeef5;
  }
}
</style>
