<template>
  <el-dialog
    v-model="visible"
    title="检测数据对比分析"
    width="1000px"
    :close-on-click-modal="false"
  >
    <div class="compare-container">
      <!-- 对比配置 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <span>对比配置</span>
        </template>
        <el-form :model="compareForm" :inline="true">
          <el-form-item label="对比维度">
            <el-select v-model="compareForm.dimension" placeholder="请选择对比维度">
              <el-option label="时间对比" value="time" />
              <el-option label="采样点对比" value="point" />
              <el-option label="检测项目对比" value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="图表类型">
            <el-select v-model="compareForm.chartType" placeholder="请选择图表类型">
              <el-option label="柱状图" value="bar" />
              <el-option label="折线图" value="line" />
              <el-option label="雷达图" value="radar" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="generateCompare">
              <el-icon><TrendCharts /></el-icon>生成对比
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 对比图表 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <div class="flex justify-between items-center">
            <span>对比图表</span>
            <div>
              <el-button size="small" @click="exportChart">导出图表</el-button>
              <el-button size="small" @click="switchChartType">切换图表</el-button>
            </div>
          </div>
        </template>
        <div ref="compareChartRef" class="chart-container"></div>
      </el-card>

      <!-- 对比数据表格 -->
      <el-card shadow="never">
        <template #header>
          <div class="flex justify-between items-center">
            <span>对比数据详情</span>
            <el-button size="small" @click="exportData">导出数据</el-button>
          </div>
        </template>
        <el-table :data="compareData" border style="width: 100%" max-height="300">
          <el-table-column prop="category" label="对比项" width="150" />
          <el-table-column prop="value1" label="数值1" width="100">
            <template #default="{ row }">
              <span :class="getValueClass(row.status1)">
                {{ row.value1 }} {{ row.unit }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="value2" label="数值2" width="100">
            <template #default="{ row }">
              <span :class="getValueClass(row.status2)">
                {{ row.value2 }} {{ row.unit }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="difference" label="差值" width="100">
            <template #default="{ row }">
              <span :class="getDifferenceClass(row.difference)">
                {{ row.difference > 0 ? '+' : '' }}{{ row.difference }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="changeRate" label="变化率" width="100">
            <template #default="{ row }">
              <span :class="getDifferenceClass(row.changeRate)">
                {{ row.changeRate > 0 ? '+' : '' }}{{ row.changeRate }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="analysis" label="分析结论" min-width="200" />
        </el-table>
      </el-card>

      <!-- 对比总结 -->
      <el-card shadow="never" class="mt-4">
        <template #header>
          <span>对比分析总结</span>
        </template>
        <div class="summary-content">
          <el-alert
            v-for="(summary, index) in compareSummary"
            :key="index"
            :title="summary.title"
            :type="summary.type"
            :description="summary.description"
            show-icon
            :closable="false"
            class="mb-2"
          />
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="generateReport">
          <el-icon><Document /></el-icon>生成对比报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

defineOptions({ name: 'DataCompareDialog' })

// 对话框状态
const visible = ref(false)
const compareChartRef = ref<HTMLDivElement>()
let compareChart: echarts.ECharts | null = null

// 对比表单
const compareForm = reactive({
  dimension: 'time',
  chartType: 'bar'
})

// 对比数据
const compareData = ref([])
const selectedData = ref([])

// 对比总结
const compareSummary = ref([])

// 打开对话框
const open = (data: any[] = []) => {
  visible.value = true
  selectedData.value = data
  
  // 延迟生成对比
  setTimeout(() => {
    generateCompare()
  }, 100)
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  if (compareChart) {
    compareChart.dispose()
    compareChart = null
  }
}

// 生成对比
const generateCompare = () => {
  // 模拟对比数据
  const mockData = [
    {
      category: '2023-07-15',
      value1: 45,
      value2: 52,
      unit: 'mg/L',
      status1: 'normal',
      status2: 'exceed',
      difference: 7,
      changeRate: 15.6,
      analysis: '数值上升，需要关注'
    },
    {
      category: '2023-07-16',
      value1: 38,
      value2: 42,
      unit: 'mg/L',
      status1: 'normal',
      status2: 'normal',
      difference: 4,
      changeRate: 10.5,
      analysis: '数值略有上升，在正常范围内'
    },
    {
      category: '2023-07-17',
      value1: 48,
      value2: 35,
      unit: 'mg/L',
      status1: 'warning',
      status2: 'normal',
      difference: -13,
      changeRate: -27.1,
      analysis: '数值明显下降，处理效果良好'
    }
  ]
  
  compareData.value = mockData
  
  // 生成对比总结
  compareSummary.value = [
    {
      title: '总体趋势',
      type: 'info',
      description: '对比期间内，检测数值整体呈现波动趋势，平均变化率为-0.3%'
    },
    {
      title: '异常发现',
      type: 'warning',
      description: '发现1次超标情况，建议加强相关时段的处理工艺监控'
    },
    {
      title: '改善建议',
      type: 'success',
      description: '最新数据显示处理效果有明显改善，建议保持当前工艺参数'
    }
  ]
  
  // 渲染图表
  renderCompareChart(mockData)
  
  ElMessage.success('对比分析生成完成')
}

// 渲染对比图表
const renderCompareChart = (data: any[]) => {
  if (!compareChartRef.value) return
  
  if (compareChart) {
    compareChart.dispose()
  }
  
  compareChart = echarts.init(compareChartRef.value)
  
  let option: any = {}
  
  if (compareForm.chartType === 'bar') {
    option = {
      title: {
        text: '数据对比分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['对比值1', '对比值2'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.category)
      },
      yAxis: {
        type: 'value',
        name: '浓度 (mg/L)'
      },
      series: [
        {
          name: '对比值1',
          type: 'bar',
          data: data.map(item => item.value1),
          itemStyle: { color: '#409eff' }
        },
        {
          name: '对比值2',
          type: 'bar',
          data: data.map(item => item.value2),
          itemStyle: { color: '#67c23a' }
        }
      ]
    }
  } else if (compareForm.chartType === 'line') {
    option = {
      title: {
        text: '数据对比趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['对比值1', '对比值2'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.category)
      },
      yAxis: {
        type: 'value',
        name: '浓度 (mg/L)'
      },
      series: [
        {
          name: '对比值1',
          type: 'line',
          data: data.map(item => item.value1),
          itemStyle: { color: '#409eff' }
        },
        {
          name: '对比值2',
          type: 'line',
          data: data.map(item => item.value2),
          itemStyle: { color: '#67c23a' }
        }
      ]
    }
  }
  
  compareChart.setOption(option)
}

// 切换图表类型
const switchChartType = () => {
  const types = ['bar', 'line', 'radar']
  const currentIndex = types.indexOf(compareForm.chartType)
  compareForm.chartType = types[(currentIndex + 1) % types.length]
  renderCompareChart(compareData.value)
}

// 获取数值样式类
const getValueClass = (status: string) => {
  return {
    'text-green-600': status === 'normal',
    'text-yellow-600': status === 'warning',
    'text-red-600': status === 'exceed'
  }
}

// 获取差值样式类
const getDifferenceClass = (value: number) => {
  return {
    'text-green-600': value < 0,
    'text-red-600': value > 0,
    'text-gray-600': value === 0
  }
}

// 导出图表
const exportChart = () => {
  if (compareChart) {
    const url = compareChart.getDataURL({
      type: 'png',
      backgroundColor: '#fff'
    })
    const link = document.createElement('a')
    link.download = '数据对比图表.png'
    link.href = url
    link.click()
    ElMessage.success('图表导出成功')
  }
}

// 导出数据
const exportData = () => {
  ElMessage.success('对比数据导出成功')
}

// 生成报告
const generateReport = () => {
  ElMessage.success('对比分析报告生成成功')
}

// 组件卸载时清理图表
onUnmounted(() => {
  if (compareChart) {
    compareChart.dispose()
    compareChart = null
  }
})

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.compare-container {
  max-height: 700px;
  overflow-y: auto;
}

.chart-container {
  height: 400px;
  width: 100%;
}

.summary-content {
  max-height: 200px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}
</style>
