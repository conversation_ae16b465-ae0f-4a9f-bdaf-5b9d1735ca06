<template>
  <div class="waste-container">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="使用记录" name="usage">
        <WasteUsageRecord @showDialog="showAddDialog = true" />
      </el-tab-pane>
      <el-tab-pane label="使用统计" name="statistics">
        <div class="statistics-container">
          <el-form :inline="true" :model="statisticsForm" class="statistics-form">
            <el-form-item label="统计时间">
              <el-date-picker
                v-model="statisticsForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleStatisticsSearch">查询</el-button>
              <el-button @click="handleStatisticsReset">重置</el-button>
            </el-form-item>
          </el-form>

          <el-table :data="statisticsData" border style="width: 100%">
            <el-table-column prop="chemicalName" label="危化品名称" />
            <el-table-column prop="totalAmount" label="总使用量" />
            <el-table-column prop="unit" label="单位" />
            <el-table-column prop="useCount" label="使用次数" />
            <el-table-column prop="userCount" label="使用人数" />
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <AddUsageDialog
      v-model="showAddDialog"
      @submit="handleAddSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import WasteUsageRecord from './components/WasteUsageRecord.vue'
import AddUsageDialog from './dialogs/AddUsageDialog.vue'

const activeTab = ref('usage')
const showAddDialog = ref(false)

const statisticsForm = reactive({
  dateRange: []
})

// 模拟统计数据
const statisticsData = ref([
  {
    chemicalName: '硫酸',
    totalAmount: 500,
    unit: 'ml',
    useCount: 5,
    userCount: 3
  },
  {
    chemicalName: '盐酸',
    totalAmount: 300,
    unit: 'ml',
    useCount: 3,
    userCount: 2
  }
])

const handleAddSubmit = (formData) => {
  console.log('提交的表单数据：', formData)
  // 这里实现数据提交逻辑
}

const handleStatisticsSearch = () => {
  console.log('统计查询条件：', statisticsForm)
}

const handleStatisticsReset = () => {
  statisticsForm.dateRange = []
}
</script>

<style scoped>
.waste-container {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
}

.statistics-container {
  padding: 20px;
}

.statistics-form {
  margin-bottom: 20px;
}
</style>
