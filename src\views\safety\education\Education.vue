<template>
  <div class="safety-education">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="文件管理" name="file">
        <FileList />
      </el-tab-pane>
      <el-tab-pane label="入职教育" name="entry">
        <EntryList />
      </el-tab-pane>
      <el-tab-pane label="主题教育" name="theme">
        <ThemeList />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FileList from './components/FileList.vue'
import EntryList from './components/EntryList.vue'
import ThemeList from './components/ThemeList.vue'

defineOptions({ name: 'SafetyEducation' })

const activeTab = ref('file')
</script>

<style lang="scss" scoped>
.safety-education {
  padding: 20px;
  background-color: #fff;
  
  :deep(.el-tabs__content) {
    padding: 20px;
  }
}
</style>
