<script lang="ts" setup>
import { ref, computed, reactive, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { OnlineMonitorAPI, ProcessTreeDTO, DeviceDTO } from '@/api/monitor/onlineMonitor'
import * as ElementPlusIcons from '@element-plus/icons-vue'

// 定义需要使用的图标组件
const ElIconFolderOpened = ElementPlusIcons.FolderOpened
const ElIconDocument = ElementPlusIcons.Document
const ElIconEdit = ElementPlusIcons.Edit
const ElIconDelete = ElementPlusIcons.Delete
const ElIconPlus = ElementPlusIcons.Plus
const ElIconMonitor = ElementPlusIcons.Monitor

// 定义props
const props = defineProps({
  treeData: {
    type: Array,
    required: true
  },
  currentFactory: {
    type: Object,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['update:treeData', 'panel-closed'])

// 定义数据结构
interface TreeNode {
  id: string
  name: string
  isProcess: boolean
  children?: TreeNode[]
  deviceCode?: string
  deviceName?: string
  hasChildren?: boolean
}

interface ProcessItem {
  processId: number
  processCode: string
  processName: string
  factoryCode: string
}

interface DeviceItem {
  deviceId: number
  deviceCode: string
  deviceName: string
  processCode: string
}

interface IndicatorItem {
  id: number
  indicatorCode: string
  indicatorName: string
  unit: string
  deviceCode: string
}

// 指标DTO类型
interface PointDTO {
  indicatorId: string // 对应前端的indicatorCode
  pointId?: string // 可选字段
  pointCode?: string // 可选字段
  pointName: string // 对应前端的indicatorName
  unit: string
  pointType?: string // 可选字段
}

// 设备指标分组DTO
interface DevicePointGroupDTO {
  deviceId: string
  deviceName: string
  points: PointDTO[]
}

// 指标表单DTO接口定义
interface IndicatorSaveReqVO {
  id?: number // 创建时可选，更新时必须
  indicatorCode: string
  indicatorName: string
  unit?: string // 单位可选
  deviceCode: string
}

// API响应类型
interface ApiResponse<T> {
  code: number
  data: T
  message: string
}

// 对话框可见性
const dialogVisible = ref(false)
const processDialogVisible = ref(false)
const deviceDialogVisible = ref(false)
const indicatorDialogVisible = ref(false)

// 对话框类型（新增/编辑）
const processDialogType = ref<'add' | 'edit'>('add')
const deviceDialogType = ref<'add' | 'edit'>('add')
const indicatorDialogType = ref<'add' | 'edit'>('add')

// 表单引用
const processFormRef = ref<FormInstance>()
const deviceFormRef = ref<FormInstance>()
const indicatorFormRef = ref<FormInstance>()

// 树形配置
const defaultProps = {
  children: 'children',
  label: 'name'
}

// 数据副本（用于编辑）
const treeDataCopy = ref<TreeNode[]>([])

// 当前选中的节点
const selectedProcess = ref<TreeNode | null>(null)
const selectedDevice = ref<TreeNode | null>(null)

// 设备和指标列表
const deviceList = ref<TreeNode[]>([])
const indicatorList = ref<IndicatorItem[]>([])

// 可选的工艺、设备和指标
const availableProcesses = ref<ProcessItem[]>([])
const availableDevices = ref<DeviceItem[]>([])
const availableIndicators = ref<PointDTO[]>([])
const devicePointGroups = ref<DevicePointGroupDTO[]>([]) // 添加设备指标分组数据
const processDeviceTree = ref<ProcessTreeDTO[]>([]) // 添加工艺设备树形数据

// 树的展开节点
const expandedKeys = ref<string[]>([])

// 指标树形选择器相关
interface IndicatorTreeNode {
  id: string
  name: string
  code?: string
  unit?: string
  isDevice: boolean
  disabled?: boolean
  children?: IndicatorTreeNode[]
}

const indicatorTreeData = ref<IndicatorTreeNode[]>([])
const indicatorTreeProps = {
  children: 'children',
  label: 'name'
}

// 工艺表单
const processForm = reactive({
  processId: 0, // 用于前端，将会映射到id字段
  processCode: '',
  processName: '',
  factoryCode: computed(() => props.currentFactory.code || '')
})

// 设备表单
const deviceForm = reactive({
  deviceId: 0,
  deviceCode: '',
  deviceName: '',
  processCode: '',
})

// 指标表单
const indicatorForm = reactive({
  id: 0,
  indicatorCode: '',
  indicatorName: '',
  unit: '',
  deviceCode: ''
})

// 定义缓存对象
const dataCache = reactive({
  processesLoaded: false,
  indicatorsLoaded: false,
  lastFactoryId: '', // 记录上次加载数据的水厂ID
  lastDeviceCodeForIndicators: '' // 记录上次加载指标的设备编码
})

// 加载状态
const loading = reactive({
  processes: false,
  devices: false,
  indicators: false,
  processForm: false,
  deviceForm: false,
  indicatorForm: false
})

// 监听树数据变化
watch(() => props.treeData, (newVal) => {
  // 深拷贝树数据
  treeDataCopy.value = JSON.parse(JSON.stringify(newVal))
}, { immediate: true, deep: true })

// 构建指标树形数据
const buildIndicatorTreeData = () => {
  const treeData: IndicatorTreeNode[] = []

  devicePointGroups.value.forEach(group => {
    const deviceNode: IndicatorTreeNode = {
      id: `device-${group.deviceId}`,
      name: group.deviceName,
      isDevice: true,
      disabled: true, // 设备节点不可选择
      children: []
    }

    group.points.forEach(point => {
      const isDisabled = indicatorList.value.some(i => i.indicatorCode === point.indicatorId)
      deviceNode.children!.push({
        id: point.indicatorId,
        name: point.pointName,
        code: point.indicatorId,
        unit: point.unit || '/',
        isDevice: false,
        disabled: isDisabled
      })
    })

    if (deviceNode.children!.length > 0) {
      treeData.push(deviceNode)
    }
  })

  indicatorTreeData.value = treeData
}

// 处理树形选择器节点点击
const handleIndicatorTreeSelect = (data: any) => {
  // 只允许选择指标节点（非设备节点且未禁用）
  if (!data.isDevice && !data.disabled) {
    // 更新表单数据
    indicatorForm.indicatorCode = data.code
    indicatorForm.indicatorName = data.name
    indicatorForm.unit = data.unit || '/'

    console.log('选择指标:', data)
  }
}

// 过滤树形节点
const filterIndicatorNode = (value: string, data: any) => {
  if (!value) return true

  // 设备节点：按设备名称过滤
  if (data.isDevice) {
    return data.name.toLowerCase().includes(value.toLowerCase())
  }

  // 指标节点：按编码或名称过滤
  return (
    data.code?.toLowerCase().includes(value.toLowerCase()) ||
    data.name?.toLowerCase().includes(value.toLowerCase())
  )
}

// 监听指标编码选择变化，自动填充指标名称和单位
watch(() => indicatorForm.indicatorCode, (newVal) => {
  if (newVal && indicatorDialogType.value === 'add') {
    const selectedIndicator = availableIndicators.value.find(i => i.indicatorId === newVal);
    if (selectedIndicator) {
      indicatorForm.indicatorName = selectedIndicator.pointName;
      indicatorForm.unit = selectedIndicator.unit || '/';
      // 移除将pointId赋值给id的操作，id由后端自增
      indicatorForm.id = 0; // 新增时id设置为0
    }
  }
});

// 表单验证规则
const processRules = {
  processName: [
    { required: true, message: '请输入工艺名称', trigger: 'blur' }
  ],
  processCode: [
    { required: true, message: '请选择工艺编码', trigger: 'change' }
  ]
}

// 设备表单验证规则，使用计算属性动态生成
const deviceRules = computed(() => {
  // 定义带有正确类型的基本规则对象
  const baseRules: Record<string, any> = {
    deviceName: [
      { required: true, message: '请输入设备名称', trigger: 'blur' }
    ],
    processCode: [
      { required: true, message: '请选择所属工艺', trigger: 'change' }
    ],
    deviceCode: [
      { required: true, message: '请输入设备编码', trigger: 'blur' }
    ]
  };

  return baseRules;
});

// 指标表单验证规则
const indicatorRules = computed(() => {
  const baseRules: Record<string, any> = {
    indicatorName: [
      { required: true, message: '请输入指标名称', trigger: 'blur' }
    ],
    deviceCode: [
      { required: true, message: '请选择所属设备', trigger: 'change' }
    ],
    unit: [
      { required: true, message: '请输入单位', trigger: 'blur' }
    ]
  };

  // 根据对话框类型设置指标编码验证规则
  if (indicatorDialogType.value === 'edit') {
    baseRules.indicatorCode = [
      { required: true, message: '请输入指标编码', trigger: 'blur' }
    ];
  } else {
    baseRules.indicatorCode = [
      { required: true, message: '请选择指标编码', trigger: 'change' }
    ];
  }

  return baseRules;
});

// 检查设备是否已经在当前工艺段中
const isDeviceAlreadyInProcess = (deviceCode: string): boolean => {
  // 检查当前设备列表中是否已存在该设备编码
  return deviceList.value.some(d => d.id === deviceCode);
}

// 处理节点点击
const handleNodeClick = async (data: TreeNode) => {
  console.log('节点点击:', data);

  if (data.isProcess) {
    // 先清空设备列表和指标列表，确保UI立即响应
    deviceList.value = []
    indicatorList.value = []

    // 更新选中状态
    selectedProcess.value = data
    selectedDevice.value = null

    // 将当前工艺添加到展开节点列表中
    if (!expandedKeys.value.includes(data.id)) {
      expandedKeys.value.push(data.id);
    }

    // 从缓存的树形数据中获取设备列表
    try {
      console.log('从缓存加载工艺下的设备列表:', data.id);

      // 查找工艺对应的设备列表
      const process = processDeviceTree.value.find(p => p.processCode === data.id);
      if (process && process.children) {
        // 转换设备数据为TreeNode格式
        deviceList.value = process.children.map(device => ({
          id: device.deviceCode,
          name: device.deviceName,
          isProcess: false,
          deviceCode: device.deviceCode,
          deviceName: device.deviceName,
          hasChildren: false
        }))

        // 同时更新tree中的children数据
        const processNode = treeDataCopy.value.find(p => p.id === data.id)
        if (processNode) {
          processNode.children = [...deviceList.value]

          // 立即更新父组件的树数据，确保视图同步
          console.log('更新父组件树数据，工艺:', data.name, '设备数量:', deviceList.value.length);
          emit('update:treeData', JSON.parse(JSON.stringify(treeDataCopy.value)))
        }

        console.log('设备列表加载完成，数量:', deviceList.value.length);
      } else {
        // 如果在缓存中找不到，尝试重新加载整个树
        console.log(`在缓存中未找到工艺 ${data.id} 的设备列表，尝试重新加载`);
        await loadAllProcesses();

        // 再次尝试从缓存中获取
        const refreshedProcess = processDeviceTree.value.find(p => p.processCode === data.id);
        if (refreshedProcess && refreshedProcess.children) {
          deviceList.value = refreshedProcess.children.map(device => ({
            id: device.deviceCode,
            name: device.deviceName,
            isProcess: false,
            deviceCode: device.deviceCode,
            deviceName: device.deviceName,
            hasChildren: false
          }));

          // 同时更新tree中的children数据
          const processNode = treeDataCopy.value.find(p => p.id === data.id)
          if (processNode) {
            processNode.children = [...deviceList.value]

            // 立即更新父组件的树数据，确保视图同步
            console.log('更新父组件树数据，工艺:', data.name, '设备数量:', deviceList.value.length);
            emit('update:treeData', JSON.parse(JSON.stringify(treeDataCopy.value)))
          }

          console.log('设备列表重新加载完成，数量:', deviceList.value.length);
        } else {
          deviceList.value = []
          console.warn(`即使重新加载后仍未找到工艺 ${data.id} 的设备列表`);
        }
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
      ElMessage.error('获取设备列表失败')
      deviceList.value = []
    }
  } else {
    // 先清空指标列表，确保UI立即响应
    indicatorList.value = []

    // 查找并更新所属工艺状态 - 这里是关键修改，需要确保找到正确的工艺
    const parentProcess = findParentProcess(data);
    if (parentProcess) {
      // 确保工艺节点被展开
      if (!expandedKeys.value.includes(parentProcess.id)) {
        expandedKeys.value.push(parentProcess.id);
        console.log('自动展开工艺节点:', parentProcess.id);
      }

      // 如果设备不属于当前工艺，先切换到正确的工艺
      if (!selectedProcess.value || selectedProcess.value.id !== parentProcess.id) {
        // 先强制刷新工艺树确保数据最新
        await loadAllProcesses(true);

        // 更新选中的工艺节点
        selectedProcess.value = parentProcess;

        // 确保该工艺下的设备列表已加载
        const process = processDeviceTree.value.find(p => p.processCode === parentProcess.id);
        if (process && process.children) {
          // 更新设备列表
          deviceList.value = process.children.map(device => ({
            id: device.deviceCode,
            name: device.deviceName,
            isProcess: false,
            deviceCode: device.deviceCode,
            deviceName: device.deviceName,
            hasChildren: false
          }));

          // 同时更新工艺节点的设备子节点
          const processNode = treeDataCopy.value.find(p => p.id === parentProcess.id);
          if (processNode) {
            processNode.children = [...deviceList.value];

            // 立即更新父组件的树数据
            console.log('更新父组件树数据，切换到工艺:', parentProcess.name);
            emit('update:treeData', JSON.parse(JSON.stringify(treeDataCopy.value)));
          }
        }
      }
    } else {
      console.warn(`未找到设备 ${data.id} 的所属工艺`);
      // 可以考虑在这里添加错误处理或重新加载整个树
    }

    // 更新选中状态
    selectedDevice.value = data;

    // 加载该设备下的指标列表
    console.log('加载设备下的指标列表:', data.id);
    await loadIndicatorsByDevice(data.id)
    console.log('指标列表加载完成，数量:', indicatorList.value.length);
  }
}

// 查找设备所属的工艺
const findParentProcess = (device: TreeNode): TreeNode | null => {
  // 先在当前树数据中查找
  for (const process of treeDataCopy.value) {
    if (process.children?.some(d => d.id === device.id)) {
      return process;
    }
  }

  // 如果在当前树数据中找不到，尝试在完整的工艺树中查找
  for (const process of processDeviceTree.value) {
    if (process.children?.some(d => d.deviceCode === device.id)) {
      // 找到后转换为TreeNode格式
      const processNode = treeDataCopy.value.find(p => p.id === process.processCode);
      if (processNode) {
        return processNode;
      }

      // 如果在treeDataCopy中也找不到，则创建一个新的TreeNode
      return {
        id: process.processCode,
        name: process.processName,
        isProcess: true,
        children: [],
        hasChildren: true
      };
    }
  }

  return null;
}

// 加载设备下的指标列表
const loadIndicatorsByDevice = async (deviceCode: string) => {
  try {
    // 设置加载状态
    loading.indicators = true

    const res = await OnlineMonitorAPI.queryIndicatorListByDevice(deviceCode) as unknown as ApiResponse<IndicatorItem[]>
    if (res.code === 0 && res.data) {
      // 确保指标列表中的每个项都有正确的ID
      indicatorList.value = res.data.map(indicator => ({
        ...indicator,
        // 确保 id 字段存在，如果后端返回了id，就使用后端的id
        id: indicator.id || 0
      }))
      console.log('加载到指标列表:', indicatorList.value)
    } else {
      indicatorList.value = []
    }
  } catch (error) {
    console.error('获取指标列表失败:', error)
    ElMessage.error('获取指标列表失败')
    indicatorList.value = []
  } finally {
    // 清除加载状态
    loading.indicators = false
  }
}

// 获取所有可用的工艺段，优化加载逻辑
const loadAllProcesses = async (forceRefresh = false) => {
  try {
    if (!props.currentFactory.id) {
      console.warn('未选择水厂或水厂ID为空');
      availableProcesses.value = []
      processDeviceTree.value = []
      return []
    }

    // 移除缓存判断，每次都重新加载数据

    // 设置加载状态
    loading.processes = true

    // 保存当前展开的节点和选中的节点
    const currentExpandedKeys = [...expandedKeys.value];
    const currentSelectedProcessId = selectedProcess.value?.id;
    const currentSelectedDeviceId = selectedDevice.value?.id;

    console.log('加载工艺和设备树形数据，水厂ID:', props.currentFactory.id);
    console.log('当前展开的节点:', currentExpandedKeys);

    try {
      const res = await OnlineMonitorAPI.queryProcessDeviceTree(props.currentFactory.code) as unknown as ApiResponse<ProcessTreeDTO[]>
      if (res.code === 0) {
        if (res.data && res.data.length > 0) {
          // 保存树形数据
          processDeviceTree.value = res.data

          // 提取工艺数据到平铺列表，用于兼容现有代码
          availableProcesses.value = res.data.map(process => ({
            processId: process.processId,
            processCode: process.processCode,
            processName: process.processName,
            factoryCode: process.factoryCode
          }))

          // 提取所有设备数据到平铺列表，用于兼容现有代码
          availableDevices.value = res.data.flatMap(process =>
            process.children.map(device => ({
              deviceId: device.deviceId,
              deviceCode: device.deviceCode,
              deviceName: device.deviceName,
              processCode: device.processCode
            }))
          )

          // 更新树数据副本，保留当前展开状态
          treeDataCopy.value = res.data.map(process => ({
            id: process.processCode,
            name: process.processName,
            isProcess: true,
            children: process.children.map(device => ({
              id: device.deviceCode,
              name: device.deviceName,
              isProcess: false,
              deviceCode: device.deviceCode,
              deviceName: device.deviceName,
              hasChildren: false
            })),
            hasChildren: process.children.length > 0
          }));

          // 恢复展开状态
          if (currentExpandedKeys.length > 0) {
            expandedKeys.value = currentExpandedKeys;
            console.log('恢复展开节点:', expandedKeys.value);
          }

          // 恢复选中状态
          if (currentSelectedProcessId) {
            const process = treeDataCopy.value.find(p => p.id === currentSelectedProcessId);
            if (process) {
              selectedProcess.value = process;

              // 如果之前有选中设备，尝试恢复设备选中状态
              if (currentSelectedDeviceId && process.children) {
                const device = process.children.find(d => d.id === currentSelectedDeviceId);
                if (device) {
                  selectedDevice.value = device;
                }
              }
            }
          }

          // 更新缓存状态以保持接口一致性
          dataCache.processesLoaded = true
          dataCache.lastFactoryId = props.currentFactory.id

          console.log('成功加载工艺和设备树形数据，工艺数量:', res.data.length, '设备总数:', availableDevices.value.length);
          return availableProcesses.value
        } else {
          console.log('当前水厂暂无工艺数据');
          processDeviceTree.value = []
          availableProcesses.value = []
          treeDataCopy.value = []

          // 更新缓存状态
          dataCache.processesLoaded = true
          dataCache.lastFactoryId = props.currentFactory.id

          return []
        }
      } else {
        console.warn('获取工艺和设备树形数据接口返回错误:', res);
        processDeviceTree.value = []
        availableProcesses.value = []
        return []
      }
    } catch (error) {
      console.error('获取工艺和设备树形数据失败:', error)
      ElMessage.error('获取工艺和设备树形数据失败，请检查网络连接')
      processDeviceTree.value = []
      availableProcesses.value = []
      return []
    } finally {
      // 清除加载状态
      loading.processes = false
    }
  } catch (error) {
    console.error('获取工艺和设备树形数据过程中出现未预期错误:', error)
    loading.processes = false
    processDeviceTree.value = []
    availableProcesses.value = []
    return []
  }
}

// 获取所有可用的设备
const loadAllDevices = async (processCode: string) => {
  try {
    // 设置加载状态
    loading.devices = true

    // 强制重新加载整个工艺树，确保数据最新
    await loadAllProcesses(true);

    // 从更新后的树形数据中获取设备列表
    const process = processDeviceTree.value.find(p => p.processCode === processCode);
    if (process && process.children) {
      // 将设备数据转换为DeviceItem格式
      availableDevices.value = process.children.map(device => ({
        deviceId: device.deviceId,
        deviceCode: device.deviceCode,
        deviceName: device.deviceName,
        processCode: device.processCode
      }));
      console.log(`加载工艺 ${processCode} 的设备列表，数量:`, availableDevices.value.length);
      loading.devices = false
      return availableDevices.value;
    } else {
      console.warn(`未找到工艺 ${processCode} 的设备列表`);
      availableDevices.value = [];
      loading.devices = false
      return [];
    }
  } catch (error) {
    console.error('获取设备列表失败:', error);
    ElMessage.error('获取设备列表失败');
    availableDevices.value = [];
    loading.devices = false
    return [];
  }
}

// 优化获取所有可用的指标
const loadAllIndicators = async (deviceCode: string, forceRefresh = false) => {
  try {
    if (!props.currentFactory.id) {
      console.warn('未选择水厂或水厂ID为空');
      devicePointGroups.value = []
      availableIndicators.value = []
      return
    }

    // 移除缓存判断逻辑，每次都重新加载数据
    // 设置加载状态
    loading.indicators = true

    // 使用新的接口URL和参数，将参数从factoryCode改为factoryId
    console.log('加载指标列表，水厂ID:', props.currentFactory.id);

    try {
      const res = await OnlineMonitorAPI.getIndicatorsByFactory(props.currentFactory.id) as unknown as ApiResponse<DevicePointGroupDTO[]>
      if (res.code === 0) {
        if (res.data && res.data.length > 0) {
          console.log('获取到的设备指标分组数据:', res.data);

          // 保存设备指标分组数据
          devicePointGroups.value = res.data;

          // 提取所有指标到平铺列表，用于兼容现有代码
          availableIndicators.value = res.data.flatMap(group =>
            group.points.map(point => ({
              indicatorId: point.indicatorId,
              pointId: point.pointId,
              pointCode: point.pointCode,
              pointName: point.pointName,
              unit: point.unit || '',
              pointType: point.pointType
            }))
          );

          // 更新缓存状态以保持接口一致性
          dataCache.indicatorsLoaded = true
          dataCache.lastFactoryId = props.currentFactory.id
          dataCache.lastDeviceCodeForIndicators = deviceCode

          console.log('转换后的指标数据:', availableIndicators.value);
        } else {
          console.log('当前水厂暂无指标数据');
          devicePointGroups.value = []
          availableIndicators.value = []

          // 更新缓存状态
          dataCache.indicatorsLoaded = true
          dataCache.lastFactoryId = props.currentFactory.id
          dataCache.lastDeviceCodeForIndicators = deviceCode
        }
      } else {
        console.warn('获取指标列表接口返回错误:', res);
        devicePointGroups.value = []
        availableIndicators.value = []
      }
    } catch (error) {
      console.error('获取所有指标失败:', error)
      ElMessage.error('获取指标列表失败，请检查网络连接')
      devicePointGroups.value = []
      availableIndicators.value = []
    } finally {
      // 清除加载状态
      loading.indicators = false
    }
  } catch (error) {
    console.error('获取所有指标过程中出现未预期错误:', error)
    loading.indicators = false
    devicePointGroups.value = []
    availableIndicators.value = []
  }
}

// 新增一个获取所有设备的方法
const loadAllGlobalDevices = async () => {
  try {
    console.log('获取全局所有设备列表');
    const res = await OnlineMonitorAPI.listDevices() as unknown as ApiResponse<DeviceItem[]>
    if (res.code === 0 && res.data) {
      // 更新全局设备列表
      availableDevices.value = res.data;
      console.log('获取到全局设备数量:', res.data.length);
    } else {
      availableDevices.value = []
      console.warn('获取全局设备列表失败:', res);
    }
  } catch (error) {
    console.error('获取全局设备列表失败:', error)
    ElMessage.error('获取全局设备列表失败')
    availableDevices.value = []
  }
}

// 打开对话框
const open = async () => {
  dialogVisible.value = true

  // 重置选中状态
  selectedProcess.value = null
  selectedDevice.value = null
  deviceList.value = []
  indicatorList.value = []
  expandedKeys.value = [] // 重置展开节点

  try {
    // 加载所有可用的工艺段
    await loadAllProcesses(true) // 强制刷新数据

    // 预加载指标列表，即使没有选择设备
    if (props.currentFactory.id) {
      await loadAllIndicators('', true) // 强制刷新数据
    }

    // 将工艺段数据同步到树形结构
    treeDataCopy.value = JSON.parse(JSON.stringify(props.treeData))

    // 如果有工艺数据，尝试加载第一个工艺的设备
    if (treeDataCopy.value.length > 0) {
      const firstProcess = treeDataCopy.value[0]
      console.log('打开对话框，选中第一个工艺:', firstProcess.name);

      // 只展开第一个工艺节点
      expandedKeys.value = [firstProcess.id];
      console.log('设置展开节点:', expandedKeys.value);

      // 首次打开时，选择第一个工艺
      setTimeout(() => {
        handleNodeClick(firstProcess)
      }, 200)
    } else {
      // 当前水厂没有工艺数据，显示友好提示
      ElMessage({
        message: '当前水厂暂无数据，您可以点击"添加工艺"按钮来创建数据',
        type: 'info',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('打开配置面板时发生错误:', error)
    ElMessage({
      message: '获取数据失败，请检查网络连接或联系系统管理员',
      type: 'warning',
      duration: 3000
    })
  }
}

// 处理添加工艺
const handleAddProcess = async () => {
  processDialogType.value = 'add'
  processForm.processId = 0 // 新增时ID设置为0
  processForm.processCode = ''
  processForm.processName = ''

  // 加载所有可用的工艺段
  await loadAllProcesses()

  processDialogVisible.value = true
}

// 处理编辑工艺
const handleEditProcess = async (process: TreeNode) => {
  processDialogType.value = 'edit'

  // 先重新加载工艺列表，确保数据最新
  await loadAllProcesses();

  // 查找完整的工艺信息，以获取正确的ID
  const processInfo = availableProcesses.value.find(p => p.processCode === process.id);
  if (!processInfo) {
    console.error('未找到工艺完整信息，工艺ID:', process.id, '可用工艺列表:', availableProcesses.value);
    ElMessage.error('未找到工艺完整信息，请尝试刷新页面');
    return;
  }

  processForm.processId = processInfo.processId
  processForm.processCode = process.id
  processForm.processName = process.name
  processDialogVisible.value = true
}

// 处理添加设备
const handleAddDevice = async (processData?: TreeNode) => {
  // 如果传入了工艺数据，则使用传入的工艺；否则使用当前选中的工艺
  const targetProcess = processData || selectedProcess.value;

  if (!targetProcess) {
    ElMessage.warning('请先选择工艺')
    return
  }

  deviceDialogType.value = 'add'
  deviceForm.deviceId = 0
  deviceForm.deviceCode = ''
  deviceForm.deviceName = ''
  deviceForm.processCode = targetProcess.id

  deviceDialogVisible.value = true
}

// 处理编辑设备
const handleEditDevice = async (device: TreeNode) => {
  deviceDialogType.value = 'edit'

  // 获取当前设备所属的工艺
  const process = findParentProcess(device);
  if (!process) {
    ElMessage.error('未找到设备所属工艺');
    return;
  }

  // 确保设备所属的工艺节点被展开
  if (!expandedKeys.value.includes(process.id)) {
    expandedKeys.value.push(process.id);
    console.log('自动展开工艺节点:', process.id);
  }

  // 先重新加载整个工艺树，确保有最新数据
  await loadAllProcesses(true);

  // 然后加载该工艺下的设备列表
  await loadAllDevices(process.id);

  // 查找完整的设备信息
  const deviceInfo = availableDevices.value.find(d => d.deviceCode === device.id);
  if (!deviceInfo) {
    console.error('未找到设备完整信息，设备ID:', device.id, '可用设备列表:', availableDevices.value);
    ElMessage.error('未找到设备完整信息，请尝试刷新页面');
    return;
  }

  deviceForm.deviceId = deviceInfo.deviceId
  deviceForm.deviceCode = device.id
  deviceForm.deviceName = device.name
  deviceForm.processCode = process.id
  deviceDialogVisible.value = true
}

// 处理添加指标
const handleAddIndicator = async () => {
  if (!selectedDevice.value) {
    ElMessage.warning('请先选择设备')
    return
  }

  indicatorDialogType.value = 'add'
  indicatorForm.id = 0 // 新增时ID设置为0
  indicatorForm.indicatorCode = ''
  indicatorForm.indicatorName = ''
  indicatorForm.unit = ''
  indicatorForm.deviceCode = selectedDevice.value.id

  // 加载所有可用的指标
  await loadAllIndicators(selectedDevice.value.id)

  // 构建树形数据
  buildIndicatorTreeData()

  indicatorDialogVisible.value = true
}

// 获取设备列表并转换为TreeNode格式
const getDeviceListAsTreeNodes = async (processCode: string): Promise<TreeNode[]> => {
  try {
    // 先强制重新加载整个工艺树，确保数据最新
    await loadAllProcesses(true);

    // 从更新后的树形数据中获取设备列表
    const process = processDeviceTree.value.find(p => p.processCode === processCode);
    if (process && process.children) {
      // 将设备数据转换为TreeNode格式
      const devices = process.children.map(device => ({
        id: device.deviceCode,
        name: device.deviceName,
        isProcess: false,
        deviceCode: device.deviceCode,
        deviceName: device.deviceName,
        hasChildren: false
      }));
      console.log(`获取工艺 ${processCode} 的设备列表并转换为TreeNode格式，数量:`, devices.length);
      return devices;
    } else {
      console.warn(`未找到工艺 ${processCode} 的设备列表`);
      return [];
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败')
    return []
  }
}

// 提交工艺表单
const submitProcessForm = async () => {
  if (!processFormRef.value) return

  await processFormRef.value.validate(async (valid) => {
    if (valid) {
      // 设置加载状态
      loading.processForm = true

      try {
        console.log('准备提交工艺表单数据:', processForm);

        // 准备提交的数据，符合ProcessSaveReqVO类型
        const processData = {
          id: processForm.processId, // 直接使用processId作为id
          processCode: processForm.processCode,
          processName: processForm.processName,
          factoryCode: processForm.factoryCode,
          devices: [] // 添加空设备数组
        }

        console.log('格式化后的提交数据:', processData);

        // 区分新增和编辑调用不同的API
        let res;
        if (processDialogType.value === 'add') {
          console.log('调用创建工艺API');
          res = await OnlineMonitorAPI.createProcess(processData);
        } else {
          console.log('调用更新工艺API');
          res = await OnlineMonitorAPI.updateProcess(processData);
        }

        console.log('API响应结果:', res);

        if (res && res.code === 0) {
          // 关闭对话框
          processDialogVisible.value = false

          // 重新加载工艺列表到缓存中
          await loadAllProcesses(true) // 强制刷新

          // 记录当前树的展开状态
          const currentExpandedKeys = [...expandedKeys.value];
          console.log('当前展开的节点:', currentExpandedKeys);

          if (processDialogType.value === 'add') {
            // 添加成功的消息提示
            ElMessage.success('添加成功')

            try {
              // 仅获取工艺列表，不获取每个工艺的设备
              console.log('获取最新工艺列表');
              const allProcessRes = await OnlineMonitorAPI.queryProcessScreenByFactory(props.currentFactory.code) as unknown as ApiResponse<ProcessItem[]>;
              if (allProcessRes.code === 0 && allProcessRes.data) {
                // 将工艺列表转换为树形结构，但不加载设备
                const newTreeData: TreeNode[] = allProcessRes.data.map(process => {
                  // 检查是否已存在于当前树中
                  const existingProcess = treeDataCopy.value.find(p => p.id === process.processCode);
                  return {
                    id: process.processCode,
                    name: process.processName,
                    isProcess: true,
                    // 如果已存在，保留其子节点；否则设为空数组
                    children: existingProcess ? existingProcess.children : [],
                    hasChildren: true
                  };
                });

                // 更新树数据
                treeDataCopy.value = newTreeData;

                // 立即更新父组件的树数据
                console.log('更新树数据，工艺数量:', newTreeData.length);
                emit('update:treeData', JSON.parse(JSON.stringify(newTreeData)));

                // 对于新增的工艺，添加到展开节点列表中
                const newProcessId = processForm.processCode;
                if (!currentExpandedKeys.includes(newProcessId)) {
                  expandedKeys.value = [...currentExpandedKeys, newProcessId];
                  console.log('更新展开节点列表，添加新工艺:', expandedKeys.value);
                } else {
                  expandedKeys.value = currentExpandedKeys;
                }

                // 找到新增的工艺并选中
                const newProcess = treeDataCopy.value.find(p => p.id === processForm.processCode);
                if (newProcess) {
                  console.log('选中新增的工艺:', newProcess.name);

                  // 使用nextTick和setTimeout确保DOM更新后再执行选中操作
                  nextTick(() => {
                    setTimeout(() => {
                      // 先清空当前选中状态
                      selectedProcess.value = null;
                      selectedDevice.value = null;
                      deviceList.value = [];
                      indicatorList.value = [];

                      // 更新选中状态
                      selectedProcess.value = newProcess;

                      // 手动触发节点点击事件，加载设备列表
                      handleNodeClick(newProcess);
                    }, 300);
                  });
                }
              }
            } catch (error) {
              console.error('获取工艺列表失败:', error);

              // 降级方案：如果获取工艺列表失败，则使用本地添加的方式
              const processInfo = availableProcesses.value.find(p => p.processCode === processForm.processCode);
              if (processInfo) {
                const newProcess: TreeNode = {
                  id: processInfo.processCode,
                  name: processInfo.processName,
                  isProcess: true,
                  children: [],
                  hasChildren: true
                };

                // 检查是否已存在
                const existingIndex = treeDataCopy.value.findIndex(p => p.id === newProcess.id);
                if (existingIndex === -1) {
                  treeDataCopy.value.push(newProcess);
                  emit('update:treeData', JSON.parse(JSON.stringify(treeDataCopy.value)));

                  // 添加到展开节点列表
                  expandedKeys.value = [...currentExpandedKeys, newProcess.id];
                  console.log('更新展开节点列表，添加新工艺(降级方案):', expandedKeys.value);

                  // 选中新增的工艺
                  nextTick(() => {
                    setTimeout(() => {
                      handleNodeClick(newProcess);
                    }, 300);
                  });
                }
              }
            }
          } else {
            // 更新成功的消息提示
            ElMessage.success('更新成功')

            // 更新树中的工艺信息
            const processInfo = availableProcesses.value.find(p => p.processCode === processForm.processCode)
            if (processInfo) {
              const process = treeDataCopy.value.find(p => p.id === processInfo.processCode)
              if (process) {
                // 保存原有的children和展开状态
                const originalChildren = process.children || [];

                // 更新工艺名称
                process.name = processInfo.processName

                // 保留原有的设备列表
                process.children = originalChildren;

                // 立即更新父组件的树数据
                console.log('更新树数据，更新工艺:', process.name);
                emit('update:treeData', JSON.parse(JSON.stringify(treeDataCopy.value)))

                // 保持原有的展开状态
                expandedKeys.value = currentExpandedKeys;

                // 如果当前选中的是这个工艺，重新加载其设备列表
                if (selectedProcess.value && selectedProcess.value.id === process.id) {
                  // 使用nextTick确保DOM更新后再执行
                  nextTick(() => {
                    setTimeout(() => {
                      // 更新选中状态
                      selectedProcess.value = {...process};

                      // 重新加载该工艺下的设备列表
                      handleNodeClick(process);
                    }, 200);
                  });
                }
              }
            }
          }
        } else if (res) {
          console.error('API返回错误:', res);
          ElMessage.error(res.message || '操作失败，服务器返回了非预期的响应格式')
        } else {
          console.error('API返回为空');
          ElMessage.error('操作失败，未收到服务器响应')
        }
      } catch (error) {
        console.error('保存工艺过程中出现异常:', error)
        ElMessage.error('操作失败，请检查网络连接或联系系统管理员')
      } finally {
        // 清除加载状态
        loading.processForm = false
      }
    }
  })
}

// 提交设备表单
const submitDeviceForm = async () => {
  if (!deviceFormRef.value) return

  await deviceFormRef.value.validate(async (valid) => {
    if (valid) {
      // 设置加载状态
      loading.deviceForm = true

      try {
        console.log('准备提交设备表单数据:', deviceForm);

        // 准备提交的数据，符合DeviceSaveReqVO接口
        const deviceData = {
          id: deviceForm.deviceId, // 直接使用deviceId作为id
          deviceCode: deviceForm.deviceCode,
          deviceName: deviceForm.deviceName,
          processCode: deviceForm.processCode,
          indicators: [] // 添加空指标数组
        }

        console.log('格式化后的提交数据:', deviceData);

        // 区分新增和编辑，调用不同的API
        let res;
        if (deviceDialogType.value === 'add') {
          console.log('调用创建设备API');
          res = await OnlineMonitorAPI.createDevice(deviceData);
        } else {
          console.log('调用更新设备API');
          res = await OnlineMonitorAPI.updateDevice(deviceData);
        }

        console.log('API响应结果:', res);

        if (res && res.code === 0) {
          // 关闭对话框
          deviceDialogVisible.value = false

          // 记住当前操作的工艺和设备信息
          const currentProcessCode = deviceForm.processCode;
          const currentDeviceCode = deviceForm.deviceCode;

          // 保存当前展开的节点状态
          const currentExpandedKeys = [...expandedKeys.value];

          // 确保当前工艺节点在展开状态
          if (!currentExpandedKeys.includes(currentProcessCode)) {
            currentExpandedKeys.push(currentProcessCode);
          }

          // 重新获取完整的工艺树数据，确保与主页面保持同步
          await loadAllProcesses(true); // 强制刷新工艺树数据

          // 查找所属工艺
          const process = treeDataCopy.value.find(p => p.id === currentProcessCode);
          if (!process) {
            ElMessage.error('所属工艺不存在');
            return;
          }

          // 设置工艺节点为选中状态
          selectedProcess.value = process;

          // 恢复展开节点状态，确保包含当前工艺
          expandedKeys.value = [...new Set([...currentExpandedKeys, currentProcessCode])];
          console.log('设置展开节点:', expandedKeys.value);

          // 重新加载该工艺下的设备数据
          const devices = await getDeviceListAsTreeNodes(currentProcessCode);

          // 更新工艺节点的设备列表
          if (process && devices.length > 0) {
            process.children = devices;

            // 更新设备列表视图
            deviceList.value = devices;

            // 立即更新父组件的树数据
            emit('update:treeData', JSON.parse(JSON.stringify(treeDataCopy.value)));
          }

          if (deviceDialogType.value === 'add') {
            // 添加成功的消息提示
            ElMessage.success('添加成功');

            // 自动选择新添加的设备
            const newDevice = devices.find(d => d.deviceCode === currentDeviceCode);
            if (newDevice) {
              nextTick(() => {
                setTimeout(() => {
                  // 设置新设备为选中状态
                  selectedDevice.value = newDevice;

                  // 重新处理节点点击，确保加载相关数据
                  handleNodeClick(newDevice);
                }, 300);
              });
            }
          } else {
            // 更新成功的消息提示
            ElMessage.success('更新成功');

            // 如果当前选中的设备被更新了，需要更新选中状态
            if (selectedDevice.value && selectedDevice.value.id === currentDeviceCode) {
              // 找到更新后的设备信息
              const updatedDevice = devices.find(d => d.id === currentDeviceCode);
              if (updatedDevice) {
                nextTick(() => {
                  setTimeout(() => {
                    // 更新选中状态并重新处理节点点击，确保加载相关数据
                    selectedDevice.value = updatedDevice;
                    handleNodeClick(updatedDevice);
                  }, 300);
                });
              }
            }
          }
        } else if (res) {
          console.error('API返回错误:', res);
          ElMessage.error(res.message || '操作失败，服务器返回了非预期的响应格式');
        } else {
          console.error('API返回为空');
          ElMessage.error('操作失败，未收到服务器响应');
        }
      } catch (error) {
        console.error('保存设备过程中出现异常:', error);
        ElMessage.error('操作失败，请检查网络连接或联系系统管理员');
      } finally {
        // 清除加载状态
        loading.deviceForm = false;
      }
    }
  });
}

// 提交指标表单
const submitIndicatorForm = async () => {
  if (!indicatorFormRef.value) return

  await indicatorFormRef.value.validate(async (valid) => {
    if (valid) {
      // 设置加载状态
      loading.indicatorForm = true

      try {
        console.log('准备提交指标表单数据:', indicatorForm);

        // 准备提交的数据，符合IndicatorSaveReqVO接口
        const indicatorData: IndicatorSaveReqVO = {
          indicatorCode: indicatorForm.indicatorCode,
          indicatorName: indicatorForm.indicatorName,
          unit: indicatorForm.unit === '/' ? undefined : indicatorForm.unit,
          deviceCode: indicatorForm.deviceCode
        }

        // 只有在编辑模式下才添加id字段
        if (indicatorDialogType.value === 'edit' && indicatorForm.id) {
          indicatorData.id = indicatorForm.id;
        }

        console.log('格式化后的提交数据:', indicatorData);

        // 区分新增和编辑，调用不同的API
        let res;
        if (indicatorDialogType.value === 'add') {
          console.log('调用创建指标API');
          res = await OnlineMonitorAPI.createIndicator(indicatorData);
        } else {
          console.log('调用更新指标API');
          res = await OnlineMonitorAPI.updateIndicator(indicatorData);
        }

        console.log('API响应结果:', res);

        if (res && res.code === 0) {
          // 关闭对话框
          indicatorDialogVisible.value = false

          // 重新加载该设备下的指标列表
          if (selectedDevice.value) {
            await loadIndicatorsByDevice(selectedDevice.value.id)
          }

          if (indicatorDialogType.value === 'add') {
            ElMessage.success('添加成功')
            // 自动滚动到新增的指标
            setTimeout(() => {
              const indicatorTable = document.querySelector('.el-table__body');
              if (indicatorTable && indicatorList.value.length > 0) {
                indicatorTable.scrollTop = indicatorTable.scrollHeight;
              }
            }, 100);
          } else {
            ElMessage.success('更新成功')
          }
        } else if (res) {
          console.error('API返回错误:', res);
          ElMessage.error(res.message || '操作失败，服务器返回了非预期的响应格式')
        } else {
          console.error('API返回为空');
          ElMessage.error('操作失败，未收到服务器响应')
        }
      } catch (error) {
        console.error('保存指标过程中出现异常:', error)
        ElMessage.error('操作失败，请检查网络连接或联系系统管理员')
      } finally {
        // 清除加载状态
        loading.indicatorForm = false
      }
    }
  })
}

// 处理删除节点
const handleDeleteNode = (node: any, data: TreeNode) => {
  ElMessageBox.confirm(
    `确定要删除${data.isProcess ? '工艺' : '设备'} "${data.name}" 吗？${data.isProcess && data.children?.length ? '这将同时删除其下所有设备！' : ''}`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      let res;

      if (data.isProcess) {
        // 查找工艺对应的完整信息以获取数字ID
        const processInfo = availableProcesses.value.find(p => p.processCode === data.id);
        if (!processInfo) {
          ElMessage.error('未找到工艺信息');
          return;
        }

        // 使用工艺的数字ID进行删除
        const processId = processInfo.processId;
        console.log(`准备删除工艺，ID: ${processId}, 名称: ${data.name}`);
        res = await OnlineMonitorAPI.deleteProcess(processId);
        console.log('删除工艺API响应:', res);

        if (res && res.code === 0) {
          // 保存当前展开节点
          const currentExpandedKeys = [...expandedKeys.value];

          // 从展开节点列表中移除被删除的工艺
          expandedKeys.value = currentExpandedKeys.filter(key => key !== data.id);
          console.log('更新展开节点列表，移除已删除工艺:', expandedKeys.value);

          // 删除成功后重新加载工艺列表
          await loadAllProcesses(true); // 强制刷新工艺树

          // 更新UI
          const index = treeDataCopy.value.findIndex(p => p.id === data.id)
          if (index !== -1) {
            treeDataCopy.value.splice(index, 1)

            // 立即更新父组件的树数据
            emit('update:treeData', JSON.parse(JSON.stringify(treeDataCopy.value)));
          }

          // 清空选中状态
          if (selectedProcess.value?.id === data.id) {
            selectedProcess.value = null;
            selectedDevice.value = null;
            deviceList.value = [];
            indicatorList.value = [];
          }

          ElMessage.success('删除成功');
        } else {
          console.error('删除工艺API返回错误:', res);
          ElMessage.error(res && res.message ? res.message : '删除失败，服务器返回了非预期的响应格式');
        }
      } else {
        // 获取设备所属的工艺
        const process = findParentProcess(data);
        if (!process) {
          ElMessage.error('未找到设备所属工艺');
          return;
        }

        // 记住当前选中的工艺编码
        const currentProcessId = process.id;

        // 保存当前展开节点状态
        const currentExpandedKeys = [...expandedKeys.value];

        // 先加载当前工艺下的设备列表，确保能找到正确的设备信息
        await loadAllDevices(process.id);

        // 查找设备对应的完整信息以获取数字ID
        const deviceInfo = availableDevices.value.find(d => d.deviceCode === data.id);
        if (!deviceInfo) {
          console.error('未找到设备完整信息，设备ID:', data.id, '可用设备列表:', availableDevices.value);
          ElMessage.error('未找到设备信息');
          return;
        }

        // 使用设备的数字ID进行删除
        const deviceId = deviceInfo.deviceId;
        console.log(`准备删除设备，ID: ${deviceId}, 名称: ${data.name}`);
        res = await OnlineMonitorAPI.deleteDevice(deviceId);
        console.log('删除设备API响应:', res);

        if (res && res.code === 0) {
          // 记录当前选中的工艺
          const currentProcess = selectedProcess.value;

          // 重新获取完整的工艺树数据，确保与主页面保持同步
          await loadAllProcesses(true); // 强制刷新工艺树

          // 恢复展开节点状态
          expandedKeys.value = currentExpandedKeys;

          // 找到设备所属的工艺
          const process = treeDataCopy.value.find(p => p.id === currentProcessId);
          if (process) {
            // 删除成功后重新加载该工艺下的设备列表
            const devices = await getDeviceListAsTreeNodes(process.id);
            process.children = devices;

            // 如果当前选中的是这个工艺，更新设备列表
            if (currentProcess && currentProcess.id === process.id) {
              deviceList.value = devices;

              // 将处理后的工艺节点更新为选中的工艺节点
              selectedProcess.value = process;
            }

            // 立即更新父组件的树数据
            emit('update:treeData', JSON.parse(JSON.stringify(treeDataCopy.value)));
          }

          // 清空选中状态
          if (selectedDevice.value?.id === data.id) {
            selectedDevice.value = null;
            indicatorList.value = [];
          }

          ElMessage.success('删除成功');
        } else {
          console.error('删除设备API返回错误:', res);
          ElMessage.error(res && res.message ? res.message : '删除失败，服务器返回了非预期的响应格式');
        }
      }
    } catch (error) {
      console.error('删除操作过程中出现异常:', error);
      ElMessage.error('删除操作失败，请检查网络连接或联系系统管理员');
    }
  }).catch(() => {
    // 取消删除
  });
}

// 处理删除指标
const handleDeleteIndicator = (indicator: IndicatorItem) => {
  ElMessageBox.confirm(
    `确定要删除指标 "${indicator.indicatorName}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      console.log(`准备删除指标，ID: ${indicator.id}, 名称: ${indicator.indicatorName}`);
      const res = await OnlineMonitorAPI.deleteIndicator(indicator.id) as unknown as ApiResponse<any>;
      console.log('删除指标API响应:', res);

      if (res && res.code === 0) {
        // 删除成功后重新加载该设备下的指标列表
        if (selectedDevice.value) {
          await loadIndicatorsByDevice(selectedDevice.value.id)
        }

        ElMessage.success('删除成功')
      } else {
        console.error('删除指标API返回错误:', res);
        ElMessage.error(res && res.message ? res.message : '删除失败，服务器返回了非预期的响应格式')
      }
    } catch (error) {
      console.error('删除指标过程中出现异常:', error)
      ElMessage.error('删除操作失败，请检查网络连接或联系系统管理员')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 保存所有变更
const handleSave = () => {
  ElMessageBox.confirm(
    '确定要保存所有配置修改吗？',
    '保存确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    // 重新获取完整的工艺树数据，确保与后端数据最新状态同步
    await loadAllProcesses();

    // 将修改后的树数据同步回父组件
    emit('update:treeData', JSON.parse(JSON.stringify(treeDataCopy.value)))
    ElMessage.success('配置已保存')
    dialogVisible.value = false
  }).catch(() => {
    // 取消保存
  })
}

// 组件挂载时的处理
onMounted(() => {
  // 可以在这里添加初始化逻辑
})

// 从树节点添加设备
const handleAddDeviceForProcess = (process: TreeNode) => {
  // 设置当前选中的工艺段
  selectedProcess.value = process;

  // 确保工艺节点被展开
  if (!expandedKeys.value.includes(process.id)) {
    expandedKeys.value.push(process.id);
    console.log('自动展开工艺节点:', process.id);
  }

  // 调用添加设备的函数
  handleAddDevice();
}

// 处理编辑指标
const handleEditIndicator = async (indicator: IndicatorItem) => {
  indicatorDialogType.value = 'edit'

  // 如果没有选中设备，无法编辑指标
  if (!selectedDevice.value) {
    ElMessage.error('未找到指标所属设备');
    return;
  }

  // 先重新加载该设备下的指标列表，确保数据最新
  await loadIndicatorsByDevice(selectedDevice.value.id);

  // 查找完整的指标信息
  const indicatorInfo = indicatorList.value.find(i => i.indicatorCode === indicator.indicatorCode);
  if (!indicatorInfo) {
    console.error('未找到指标完整信息，指标编码:', indicator.indicatorCode, '当前指标列表:', indicatorList.value);
    ElMessage.error('未找到指标完整信息，请尝试刷新页面');
    return;
  }

  // 设置表单数据
  indicatorForm.id = indicatorInfo.id // 使用原指标的ID
  indicatorForm.indicatorCode = indicatorInfo.indicatorCode
  indicatorForm.indicatorName = indicatorInfo.indicatorName
  indicatorForm.unit = indicatorInfo.unit || '/'
  indicatorForm.deviceCode = indicatorInfo.deviceCode

  indicatorDialogVisible.value = true
}

// 导出方法
defineExpose({
  open
})
</script>

<template>
      <!-- 主配置面板对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="配置面板"
    width="80%"
    destroy-on-close
    :top="'5vh'"
    @closed="emit('panel-closed')"
    v-loading="loading.processes"
    element-loading-text="加载数据中..."
  >
    <div class="flex h-[70vh] overflow-hidden">
      <!-- 左侧树形结构 -->
      <div class="w-1/4 border-r pr-4 overflow-auto">
        <div class="flex justify-between items-center mb-4 sticky top-0 bg-white z-10 py-2">
          <h3 class="font-bold">工艺设备树</h3>
          <el-button type="primary" size="small" @click="handleAddProcess">
            <el-icon><ElIconPlus /></el-icon> 添加工艺
          </el-button>
        </div>
        <el-tree
          :data="treeDataCopy"
          :props="defaultProps"
          @node-click="handleNodeClick"
          :highlight-current="true"
          node-key="id"
          :default-expanded-keys="expandedKeys"
          class="overflow-auto"
          v-loading="loading.processes"
          element-loading-text="加载中..."
        >
          <template #default="{ node, data }">
            <div class="flex justify-between items-center w-full">
              <div class="flex items-center">
                <el-icon v-if="data.isProcess">
                  <ElIconFolderOpened />
                </el-icon>
                <el-icon v-else>
                  <ElIconDocument />
                </el-icon>
                <span class="ml-2">{{ node.label }}</span>
              </div>
              <div class="flex">
                <el-button
                  v-if="data.isProcess"
                  type="primary"
                  link
                  size="small"
                  @click.stop="handleAddDeviceForProcess(data)"
                >
                  <el-icon><ElIconPlus /></el-icon> 添加设备
                </el-button>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click.stop="data.isProcess ? handleEditProcess(data) : handleEditDevice(data)"
                >
                  <el-icon><ElIconEdit /></el-icon>
                </el-button>
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click.stop="handleDeleteNode(node, data)"
                >
                  <el-icon><ElIconDelete /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 右侧详情和指标列表 -->
      <div class="flex-1 pl-4 overflow-auto">
        <div v-if="selectedDevice" class="mt-4">
          <div class="flex justify-between items-center sticky top-0 bg-white z-10 py-2">
            <h3 class="font-bold">{{ selectedDevice.name }} 的指标列表</h3>
            <el-button type="primary" size="small" @click="handleAddIndicator">
              <el-icon><ElIconPlus /></el-icon> 添加指标
            </el-button>
          </div>
          <el-table
            :data="indicatorList"
            border
            style="width: 100%; margin-top: 10px;"
            max-height="350"
            v-loading="loading.indicators"
            element-loading-text="加载指标中..."
          >
            <el-table-column prop="indicatorName" label="指标名称" />
            <el-table-column prop="indicatorCode" label="指标编码" />
            <el-table-column label="单位">
              <template #default="scope">
                {{ scope.row.unit || '/' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="handleEditIndicator(scope.row)">
                  <el-icon><ElIconEdit /></el-icon> 编辑
                </el-button>
                <el-button type="danger" link @click="handleDeleteIndicator(scope.row)">
                  <el-icon><ElIconDelete /></el-icon> 删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-if="!selectedDevice" class="flex items-center justify-center h-full text-gray-400">
          <p>请选择一个设备查看其指标列表</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 工艺编辑对话框 -->
  <el-dialog
    v-model="processDialogVisible"
    :title="processDialogType === 'add' ? '新增工艺' : '编辑工艺'"
    width="500px"
    append-to-body
    v-loading="loading.processForm"
    element-loading-text="提交中..."
  >
    <div class="max-h-[60vh] overflow-auto pr-2">
      <el-form :model="processForm" label-width="80px" :rules="processRules" ref="processFormRef">
        <el-form-item label="工艺名称" prop="processName">
          <el-input v-model="processForm.processName" placeholder="请输入工艺名称" />
        </el-form-item>
        <el-form-item label="工艺编码" prop="processCode">
          <el-input v-model="processForm.processCode" placeholder="请输入工艺编码" :disabled="processDialogType === 'edit'"/>
          <div class="text-gray-400 text-xs mt-1" v-if="processDialogType === 'add'">工艺编码确定后不可修改</div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex justify-end">
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitProcessForm">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 设备编辑对话框 -->
  <el-dialog
    v-model="deviceDialogVisible"
    :title="deviceDialogType === 'add' ? '新增设备' : '编辑设备'"
    width="500px"
    append-to-body
    v-loading="loading.deviceForm"
    element-loading-text="提交中..."
  >
    <div class="max-h-[60vh] overflow-auto pr-2">
      <el-form :model="deviceForm" label-width="80px" :rules="deviceRules" ref="deviceFormRef">
        <!-- 设备编码，简化为只支持新设备 -->
        <el-form-item label="设备编码" prop="deviceCode">
          <el-input
            v-model="deviceForm.deviceCode"
            placeholder="请输入设备编码"
            :disabled="deviceDialogType === 'edit'"
          />
          <div class="text-gray-400 text-xs mt-1" v-if="deviceDialogType === 'add'">设备编码确定后不可更改</div>
        </el-form-item>

        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="deviceForm.deviceName" placeholder="请输入设备名称" />
        </el-form-item>

        <el-form-item label="所属工艺" prop="processCode">
          <el-select v-model="deviceForm.processCode" placeholder="请选择所属工艺" disabled>
            <el-option
              v-for="item in treeDataCopy"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex justify-end">
        <el-button @click="deviceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitDeviceForm">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 指标编辑对话框 -->
  <el-dialog
    v-model="indicatorDialogVisible"
    :title="indicatorDialogType === 'add' ? '新增指标' : '编辑指标'"
    width="600px"
    append-to-body
    v-loading="loading.indicatorForm"
    element-loading-text="提交中..."
  >
    <div class="max-h-[60vh] overflow-auto pr-2">
      <el-form :model="indicatorForm" label-width="80px" :rules="indicatorRules" ref="indicatorFormRef">
        <!-- 指标编码 -->
        <el-form-item label="指标编码" prop="indicatorCode">
          <el-input
            v-if="indicatorDialogType === 'edit'"
            v-model="indicatorForm.indicatorCode"
            placeholder="请输入指标编码"
            disabled
          />
          <el-tree-select
            v-else
            v-model="indicatorForm.indicatorCode"
            :data="indicatorTreeData"
            :props="indicatorTreeProps"
            node-key="id"
            :check-strictly="true"
            placeholder="请选择指标编码"
            style="width: 100%;"
            filterable
            :filter-node-method="filterIndicatorNode"
            @node-click="handleIndicatorTreeSelect"
          >
            <template #default="{ data }">
              <div class="flex items-center justify-between w-full">
                <div class="flex items-center">
                  <el-icon v-if="data.isDevice" class="text-blue-500 mr-2" :size="14">
                    <ElIconMonitor />
                  </el-icon>
                  <el-icon v-else class="text-green-500 mr-2" :size="12">
                    <ElIconDocument />
                  </el-icon>
                  <span
                    class="text-sm"
                    :class="data.isDevice ? 'font-medium text-gray-800' : 'text-gray-600'"
                  >
                    {{ data.isDevice ? data.name : `${data.code} - ${data.name}` }}
                  </span>
                </div>
                <div v-if="!data.isDevice && data.disabled" class="text-xs text-gray-400 ml-2">
                  已添加
                </div>
              </div>
            </template>
          </el-tree-select>
          <div class="text-gray-400 text-xs mt-1" v-if="indicatorDialogType === 'add'">指标编码确定后不可更改</div>
        </el-form-item>

        <el-form-item label="指标名称" prop="indicatorName">
          <el-input v-model="indicatorForm.indicatorName" placeholder="请输入指标名称" />
        </el-form-item>

        <el-form-item label="单位" prop="unit">
          <el-input v-model="indicatorForm.unit" placeholder="请输入单位" />
        </el-form-item>

        <el-form-item label="所属设备" prop="deviceCode">
          <el-select v-model="indicatorForm.deviceCode" placeholder="请选择所属设备" disabled>
            <el-option
              v-for="device in deviceList"
              :key="device.id"
              :label="device.name"
              :value="device.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="flex justify-end">
        <el-button @click="indicatorDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitIndicatorForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* 添加滚动条样式 */
:deep(.el-dialog__body) {
  padding: 10px 20px;
  overflow: hidden;
}

:deep(.el-tree) {
  height: calc(100% - 50px);
  overflow: auto;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
}

/* 设置标题区域的固定样式 */
.sticky {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
  padding: 5px 0;
}

/* 原有样式 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

/* 指标树形选择器样式 */
:deep(.el-tree-select) {
  .el-tree-select__popper {
    .el-tree {
      .el-tree-node__content {
        height: 36px;
        padding: 0 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        margin-bottom: 1px;
      }

      .el-tree-node__content:hover {
        background-color: #f0f9ff;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #e0f2fe;
      }

      .el-tree-node__expand-icon {
        padding: 4px;
        color: #6b7280;
      }

      .el-tree-node__label {
        font-size: 13px;
        color: #374151;
      }

      .el-tree-node__children {
        padding-left: 16px;
      }

      /* 设备节点样式 */
      .el-tree-node[data-is-device="true"] > .el-tree-node__content {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid #e2e8f0;
        font-weight: 500;
        cursor: default;
      }

      .el-tree-node[data-is-device="true"] > .el-tree-node__content:hover {
        background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
        border-color: #0ea5e9;
      }

      /* 指标节点样式 */
      .el-tree-node[data-is-device="false"] > .el-tree-node__content {
        background: #ffffff;
        border: 1px solid transparent;
        margin-left: 4px;
        cursor: pointer;
      }

      .el-tree-node[data-is-device="false"] > .el-tree-node__content:hover {
        background: #f0fdf4;
        border-color: #22c55e;
      }

      /* 禁用的指标节点 */
      .el-tree-node[data-disabled="true"] > .el-tree-node__content {
        background: #f9fafb;
        color: #9ca3af;
        cursor: not-allowed;
      }

      .el-tree-node[data-disabled="true"] > .el-tree-node__content:hover {
        background: #f9fafb;
        border-color: transparent;
      }
    }
  }
}
</style>
