<template>
  <div class="accident-query">
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <span>查询条件</span>
          <div>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="primary" @click="handleQuery">查询</el-button>
          </div>
        </div>
      </template>
      
      <el-form :model="queryParams" ref="queryFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="事故编号" prop="accidentCode">
              <el-input v-model="queryParams.accidentCode" placeholder="请输入事故编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="事故名称" prop="accidentName">
              <el-input v-model="queryParams.accidentName" placeholder="请输入事故名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="事故分类" prop="classificationType">
              <el-select v-model="queryParams.classificationType" placeholder="请选择事故分类" clearable style="width: 100%">
                <el-option label="轻微事故" value="minor" />
                <el-option label="一般事故" value="normal" />
                <el-option label="重大事故" value="serious" />
                <el-option label="特大事故" value="critical" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="涉事部门" prop="department">
              <el-select v-model="queryParams.department" placeholder="请选择涉事部门" clearable style="width: 100%">
                <el-option label="生产部" value="production" />
                <el-option label="工程部" value="engineering" />
                <el-option label="安全部" value="safety" />
                <el-option label="行政部" value="administration" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="发生日期" prop="occurDate">
              <el-date-picker
                v-model="queryParams.occurDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="影响程度" prop="impactLevel">
              <el-select v-model="queryParams.impactLevel" placeholder="请选择影响程度" clearable style="width: 100%">
                <el-option label="轻微" value="slight" />
                <el-option label="一般" value="moderate" />
                <el-option label="严重" value="severe" />
                <el-option label="灾难性" value="catastrophic" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="关键词" prop="keyword">
              <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="伤亡人数" prop="casualties">
              <el-input-number v-model="queryParams.casualties" :min="0" style="width: 100%" placeholder="最少伤亡人数" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <div class="expand-btn" @click="toggleAdvanced">
              {{ showAdvanced ? '收起' : '展开' }}高级搜索
              <el-icon :class="[showAdvanced ? 'expand-icon--active' : 'expand-icon']">
                <el-icon-arrow-up />
              </el-icon>
            </div>
          </el-col>
        </el-row>
        
        <div v-if="showAdvanced">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="地点" prop="location">
                <el-input v-model="queryParams.location" placeholder="请输入发生地点" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="经济损失" prop="economicLoss">
                <el-input-number 
                  v-model="queryParams.economicLoss" 
                  :min="0" 
                  :step="1000" 
                  style="width: 100%" 
                  placeholder="最小损失金额" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="处理状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择处理状态" clearable style="width: 100%">
                  <el-option label="未处理" value="pending" />
                  <el-option label="处理中" value="processing" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已归档" value="archived" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="是否有报告" prop="hasReport">
                <el-select v-model="queryParams.hasReport" placeholder="是否生成报告" clearable style="width: 100%">
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>
    
    <el-card class="result-card">
      <template #header>
        <div class="card-header">
          <span>查询结果</span>
          <div>
            <el-button type="primary" @click="exportData">导出数据</el-button>
            <el-button type="success" @click="viewDetails">查看详情</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="accidentCode" label="事故编号" min-width="120" />
        <el-table-column prop="accidentName" label="事故名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="classificationType" label="事故分类" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getClassificationType(row.classificationType)">
              {{ getClassificationLabel(row.classificationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="occurTime" label="发生时间" min-width="160" />
        <el-table-column prop="location" label="发生地点" min-width="120" show-overflow-tooltip />
        <el-table-column prop="department" label="涉事部门" min-width="100">
          <template #default="{ row }">
            {{ getDepartmentLabel(row.department) }}
          </template>
        </el-table-column>
        <el-table-column prop="casualties" label="伤亡人数" min-width="100" align="center" />
        <el-table-column prop="economicLoss" label="经济损失" min-width="120" align="right">
          <template #default="{ row }">
            {{ formatCurrency(row.economicLoss) }}
          </template>
        </el-table-column>
        <el-table-column prop="impactLevel" label="影响程度" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getImpactLevelType(row.impactLevel)">
              {{ getImpactLevelLabel(row.impactLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="处理状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click.stop="handleView(row)">查看</el-button>
            <el-button type="success" link @click.stop="handleReport(row)">报告</el-button>
            <el-button type="danger" link @click.stop="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 控制高级搜索显示
const showAdvanced = ref(false)
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 查询参数
const queryFormRef = ref<FormInstance>()
const queryParams = reactive({
  accidentCode: '',
  accidentName: '',
  classificationType: '',
  department: '',
  occurDate: [],
  impactLevel: '',
  keyword: '',
  casualties: null,
  location: '',
  economicLoss: null,
  status: '',
  hasReport: null
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const tableRef = ref()
const selectedRows = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const currentRow = ref(null)

// 模拟数据
const mockData = [
  {
    id: '1',
    accidentCode: 'ACC-20230315',
    accidentName: '车间临时电线短路引发小型火灾',
    classificationType: 'minor',
    occurTime: '2023-03-15 09:45:22',
    location: '1号厂房车间A区',
    department: 'production',
    casualties: 0,
    economicLoss: 15000,
    impactLevel: 'slight',
    status: 'completed',
    hasReport: true
  },
  {
    id: '2',
    accidentCode: 'ACC-20230422',
    accidentName: '叉车操作不当导致物料倒塌',
    classificationType: 'normal',
    occurTime: '2023-04-22 14:30:15',
    location: '物流仓储中心',
    department: 'logistics',
    casualties: 1,
    economicLoss: 35000,
    impactLevel: 'moderate',
    status: 'archived',
    hasReport: true
  },
  {
    id: '3',
    accidentCode: 'ACC-20230518',
    accidentName: '高压设备漏电引发员工触电',
    classificationType: 'serious',
    occurTime: '2023-05-18 11:20:37',
    location: '电气控制室',
    department: 'engineering',
    casualties: 2,
    economicLoss: 120000,
    impactLevel: 'severe',
    status: 'processing',
    hasReport: false
  },
  {
    id: '4',
    accidentCode: 'ACC-20230629',
    accidentName: '化学品泄漏导致多人中毒',
    classificationType: 'critical',
    occurTime: '2023-06-29 15:45:22',
    location: '化学品储存区',
    department: 'production',
    casualties: 5,
    economicLoss: 450000,
    impactLevel: 'catastrophic',
    status: 'pending',
    hasReport: false
  }
]

// 获取事故分类标签类型
const getClassificationType = (type) => {
  const typeMap = {
    minor: 'info',
    normal: 'warning',
    serious: 'danger',
    critical: 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取事故分类显示文本
const getClassificationLabel = (type) => {
  const labelMap = {
    minor: '轻微事故',
    normal: '一般事故',
    serious: '重大事故',
    critical: '特大事故'
  }
  return labelMap[type] || '未知'
}

// 获取部门显示文本
const getDepartmentLabel = (dept) => {
  const deptMap = {
    production: '生产部',
    engineering: '工程部',
    safety: '安全部',
    administration: '行政部',
    logistics: '物流部'
  }
  return deptMap[dept] || dept
}

// 获取影响程度标签类型
const getImpactLevelType = (level) => {
  const typeMap = {
    slight: 'info',
    moderate: 'warning',
    severe: 'danger',
    catastrophic: 'danger'
  }
  return typeMap[level] || 'info'
}

// 获取影响程度显示文本
const getImpactLevelLabel = (level) => {
  const labelMap = {
    slight: '轻微',
    moderate: '一般',
    severe: '严重',
    catastrophic: '灾难性'
  }
  return labelMap[level] || '未知'
}

// 获取状态标签类型
const getStatusType = (status) => {
  const typeMap = {
    pending: 'danger',
    processing: 'warning',
    completed: 'success',
    archived: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态显示文本
const getStatusLabel = (status) => {
  const labelMap = {
    pending: '未处理',
    processing: '处理中',
    completed: '已完成',
    archived: '已归档'
  }
  return labelMap[status] || '未知'
}

// 格式化货币
const formatCurrency = (value) => {
  if (!value && value !== 0) return ''
  return '¥ ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 获取数据
const fetchData = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    // 这里应该是根据queryParams进行过滤的逻辑
    tableData.value = mockData
    total.value = mockData.length
    loading.value = false
  }, 500)
}

// 查询
const handleQuery = () => {
  currentPage.value = 1
  fetchData()
}

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

// 处理表格选择变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

// 处理行点击
const handleRowClick = (row) => {
  currentRow.value = row
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

// 查看详情
const handleView = (row) => {
  ElMessage.info(`查看事故详情: ${row.accidentName}`)
}

// 查看报告
const handleReport = (row) => {
  if (row.hasReport) {
    ElMessage.info(`查看事故报告: ${row.accidentName}`)
  } else {
    ElMessage.warning(`该事故尚未生成报告`)
  }
}

// 编辑事故
const handleEdit = (row) => {
  ElMessage.info(`编辑事故信息: ${row.accidentName}`)
}

// 导出数据
const exportData = () => {
  if (selectedRows.value.length === 0) {
    ElMessageBox.confirm('未选择任何记录，是否导出全部查询结果?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.success('开始导出全部数据')
    }).catch(() => {
      ElMessage.info('已取消导出')
    })
  } else {
    ElMessage.success(`开始导出 ${selectedRows.value.length} 条记录`)
  }
}

// 查看详情（通过按钮）
const viewDetails = () => {
  if (!currentRow.value && selectedRows.value.length === 0) {
    ElMessage.warning('请选择要查看的记录')
    return
  }
  
  const targetRow = currentRow.value || selectedRows.value[0]
  handleView(targetRow)
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.accident-query {
  .query-card, .result-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .expand-btn {
    color: #409eff;
    cursor: pointer;
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .expand-icon {
      margin-left: 5px;
      transition: transform 0.3s;
    }
    
    .expand-icon--active {
      margin-left: 5px;
      transform: rotate(180deg);
      transition: transform 0.3s;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 