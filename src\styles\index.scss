@use './var.css';
@use './FormCreate/index.scss';
@use './theme.scss';
@use 'element-plus/theme-chalk/dark/css-vars.css';
@use './element-rem.css';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

/*垂直滚动条的样式*/
::-webkit-scrollbar {
  /*滚动条的宽度*/
  width: 1px;
  /*滚动条的高度*/
  height: 8px;
}
::-webkit-scrollbar-thumb:vertical {
  height: 100px;
  background-color: #0251bf;
  -webkit-border-radius: 5px;
  outline-offset: -2px;
  border: 1px solid #0290bf;
}

/* 自定义el-tab 组件样式 */
.my_el-tabs.el-tabs {
  .el-tabs__content {
    flex: 1;
  }

  .el-tab-pane {
    height: 100%;
    padding-bottom: 20px;
  }

  .el-tabs__header {
    margin: 0 0 10px;
  }

  .el-tabs__item {
    color: #abdae9;
    font-size: 16px;
    font-family: DINBold;
    padding: 0 10px;

    &:hover {
      color: #fff;
    }

    &.is-active {
      color: #fff;
    }
  }

  .el-tabs__nav-wrap {
    display: flex;
    justify-content: center;
    padding-bottom: 0px;
  }
}

.my_el-tabs.el-tabs--card > .el-tabs__header {
  border-bottom: none;
  .el-tabs__item:first-child {
    border: 2px solid #5b7ec0;
  }

  .el-tabs__item.is-active {
    border-bottom-color: #ffffff00;
  }

  .el-tabs__nav {
    border: none;
  }

  .el-tabs__item {
    border: 2px solid #5b7ec0;
    border-radius: 2px;
  }

  .el-tabs__item.is-active {
    border: 2px solid #b2eaec;
  }
}
