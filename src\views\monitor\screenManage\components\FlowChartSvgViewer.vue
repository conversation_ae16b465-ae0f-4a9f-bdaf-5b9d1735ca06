<template>
  <div class="svg-container" ref="svgContainerRef" @click="handleSvgClick" @wheel="handleWheel"
    :class="{ 'adding-element': addingElementType }">
    <!-- 加载状态 -->
    <div class="loading-overlay" v-if="isLoading">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载工艺流程图...</div>
    </div>

    <!-- 缩放提示 -->
    <div class="zoom-tip">
      <div class="zoom-percentage">{{ Math.round(zoom * 100) }}%</div>
      <div class="zoom-help">使用鼠标滚轮放大/缩小</div>
    </div>
    <!-- SVG容器 -->
    <div class="svg-outer-wrapper" @mousedown="startSvgDrag" @mousemove="onSvgDrag" @mouseup="endSvgDrag"
      @mouseleave="endSvgDrag">
      <!-- SVG内容 -->
      <div class="svg-wrapper" :style="{
        transform: `scale(${zoom * baseFitScale})`,
        transformOrigin: 'center center',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: '100%',
        opacity: isLoading ? 0 : 1
      }">
        <!-- SVG内容容器 -->
        <div class="svg-content" ref="svgElement"
          style="margin: auto; display: flex; justify-content: center; align-items: center;"></div>
      </div>
    </div>
  </div>

  <!-- 不再使用临时点位预览，直接添加到SVG中 -->
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { sanitizeSvgContent, isSvgUrl, convertClientToSvgCoordinates, fetchSvgContent, processSvgContent, getElementFromSvg } from '../utils/SvgUtils'
import { createDefaultPoint, getPointColorByType } from '../utils/PointsDataConverter'

// 定义位点接口
interface Point {
  id: string;
  x: number;
  y: number;
  name: string;
  type?: string;
  showType: string;
  data?: any;
  isText: boolean;
}

const props = defineProps({
  svgContent: {
    type: String,
    default: ''
  },
  zoom: {
    type: Number,
    default: 1
  },
  points: {
    type: Array as () => Point[],
    default: () => []
  },
  addingElementType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits([
  'update:points',
  'select-point',
  'edit-point',
  'update:zoom',
  'drag-end'  // 添加拖拽结束事件
])

// 引用和状态
const svgContainerRef = ref<HTMLElement | null>(null)
const svgElement = ref<HTMLElement | null>(null)
const draggingPoint = ref<Point | null>(null)
const selectedPoint = ref<Point | null>(null)
const isLoading = ref(false) // 新增加载状态标志
const ctrlKeyPressed = ref(false) // 跟踪Ctrl键状态

// 点位拖拽相关状态
const dragStartX = ref(0)
const dragStartY = ref(0)
const dragOriginalX = ref(0)
const dragOriginalY = ref(0)

// SVG拖拽相关状态
const svgIsDragging = ref(false)
const svgTranslateX = ref(0)
const svgTranslateY = ref(0)
const svgDragStartX = ref(0)
const svgDragStartY = ref(0)

// 新增函数：计算合适的缩放比例使SVG完整显示
const calculateFitToViewScale = (svgEl) => {
  // 如果没有SVG元素或容器，则不处理
  if (!svgEl || !svgContainerRef.value) return;

  // 获取容器尺寸
  const containerRect = svgContainerRef.value.getBoundingClientRect();
  const containerWidth = containerRect.width;
  const containerHeight = containerRect.height;

  // 获取SVG尺寸（优先从viewBox获取，因为这是SVG的原始坐标系统）
  let svgWidth, svgHeight;
  const viewBox = svgEl.viewBox?.baseVal;

  if (viewBox) {
    svgWidth = viewBox.width;
    svgHeight = viewBox.height;
  } else {
    // 如果没有viewBox，尝试从width和height属性获取
    svgWidth = parseFloat(svgEl.getAttribute('width') || '800');
    svgHeight = parseFloat(svgEl.getAttribute('height') || '600');
  }

  // 计算合适的缩放比例
  const scaleX = containerWidth / svgWidth;
  const scaleY = containerHeight / svgHeight;

  // 使用较小的缩放比例确保SVG完全可见
  const fitScale = Math.min(scaleX, scaleY) * 0.95; // 乘以0.95留出一点边距

  console.log('适合视图的缩放比例:', fitScale, '容器尺寸:', containerWidth, 'x', containerHeight, 'SVG尺寸:', svgWidth, 'x', svgHeight);

  // 设置该缩放比例为基准(100%)，将基准值保存起来
  baseFitScale.value = fitScale;

  // 如果当前缩放是默认的1.0，则应用合适的缩放
  if (props.zoom === 1) {
    nextTick(() => {
      emit('update:zoom', 1); // 1.0现在代表完全适应视图的比例
    });
  }
}

// 添加一个ref用于存储基准缩放比例
const baseFitScale = ref(1);

// 清理SVG内容，确保安全和正确显示
const sanitizedSvgContent = computed(() => {
  return sanitizeSvgContent(props.svgContent);
})

// 处理SVG点击事件
const handleSvgClick = (event: MouseEvent) => {
  console.log("SVG点击事件------------", {
    addingElementType: props.addingElementType,
    zoom: props.zoom,
    baseFitScale: baseFitScale.value,
    svgTranslate: { x: svgTranslateX.value, y: svgTranslateY.value }
  });

  // 如果正在添加元素
  if (props.addingElementType) {
    // 获取点击位置相对于SVG的坐标
    const svgElem = svgElement.value;
    if (!svgElem) return;

    // 获取SVG元素
    const svgEl = svgElem.querySelector('svg') as SVGSVGElement;
    if (!svgEl) return;

    // 获取SVG的位置和尺寸信息
    const rect = svgEl.getBoundingClientRect();

    // 确保点击在SVG范围内
    if (
      event.clientX < rect.left ||
      event.clientX > rect.right ||
      event.clientY < rect.top ||
      event.clientY > rect.bottom
    ) {
      return; // 点击位置不在SVG内，忽略
    }

    console.log("点击事件信息:", {
      clientX: event.clientX,
      clientY: event.clientY,
      svgRect: rect,
      svgTranslate: { x: svgTranslateX.value, y: svgTranslateY.value }
    });

    // 使用统一的坐标转换函数计算SVG坐标
    const { x: svgX, y: svgY } = convertClientToSvgCoordinates(
      event.clientX,
      event.clientY,
      svgEl,
      baseFitScale.value
    );

    console.log("点击坐标转换结果:", {
      客户端: { x: event.clientX, y: event.clientY },
      SVG: { x: svgX, y: svgY },
      基准缩放: baseFitScale.value,
      当前缩放: props.zoom
    });

    // 创建新点位
    const isText = props.addingElementType === 'text';

    // 确保添加类型是有效的监测点类型（circle, square, triangle）或文本
    const validType = isText ? 'text' :
      ['circle', 'square', 'triangle'].includes(props.addingElementType) ?
        props.addingElementType : 'circle';

    console.log("创建点位类型:", validType, "是否文本:", isText);

    const newPoint = createDefaultPoint(
      validType,
      svgX,
      svgY,
      isText
    );

    console.log("创建的新点位:", newPoint);

    // 获取或创建监测点容器
    let pointsGroup = svgEl.querySelector('#monitoring-points');
    if (!pointsGroup) {
      pointsGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
      pointsGroup.setAttribute("id", "monitoring-points");
      pointsGroup.setAttribute("class", "monitoring-points-layer");
      svgEl.appendChild(pointsGroup);
    }

    // 将新点位添加到数组中
    const newPoints = [...props.points, newPoint];
    emit('update:points', newPoints);

    console.log("添加新点位到SVG", newPoint);

    // 直接在SVG中添加临时点位
    if (isText) {
      console.log("添加文本点位", newPoint);
      addTextElementToSVG(newPoint, pointsGroup as SVGGElement);
      // 检查是否成功添加
      const addedElement = svgEl.querySelector(`#text-${newPoint.id}`);
      console.log("文本点位添加结果:", addedElement);
    } else {
      console.log("添加图形点位", newPoint);
      addPointElementToSVG(newPoint, pointsGroup as SVGGElement);
      // 检查是否成功添加
      const addedElement = svgEl.querySelector(`#${newPoint.id}`);
      console.log("图形点位添加结果:", addedElement);
    }

    console.log("新点位添加完成", document.querySelector(`#${newPoint.id}`));

    // 通知父组件触发编辑
    emit('edit-point', newPoint, true);

    // 消息提示
    ElMessage.success(isText ? '文本点位已添加' : `${props.addingElementType}型监测点已添加`);
  } else {
    // 点击空白区域取消选中
    selectedPoint.value = null;
    emit('select-point', null);
  }
}

// 处理位点拖拽开始
const startDrag = (event: MouseEvent, point: Point) => {
  event.stopPropagation();
  event.preventDefault();

  // 只有按住Ctrl键才能拖拽
  const isCtrlPressed = event.ctrlKey || ctrlKeyPressed.value;

  // 允许拖拽的条件：必须按住Ctrl键
  if (!isCtrlPressed) {
    console.log("拖拽失败：未按住Ctrl键", { Ctrl键: isCtrlPressed });
    return;
  }

  console.log("开始拖拽点位:", point.id, "类型:", point.isText ? "文本" : point.showType);

  try {
    // 记录拖拽起始位置，用于计算拖拽距离
    dragStartX.value = event.clientX;
    dragStartY.value = event.clientY;

    // 创建点位的深拷贝，避免直接修改原始点位
    draggingPoint.value = JSON.parse(JSON.stringify(point));

    // 记录原始位置，用于显示拖拽辅助线
    dragOriginalX.value = point.x;
    dragOriginalY.value = point.y;

    // 选中当前点位
    selectedPoint.value = draggingPoint.value;
    emit('select-point', draggingPoint.value);

    // 添加鼠标移动和松开事件监听
    document.addEventListener('mousemove', handleDrag);
    document.addEventListener('mouseup', stopDrag);

    // 添加鼠标样式，提示用户正在拖拽
    document.body.style.cursor = 'grabbing';

    // 在SVG容器上添加拖拽类
    if (svgContainerRef.value) {
      svgContainerRef.value.classList.add('dragging');
    }

    // 高亮当前拖拽的元素
    highlightDraggingElement(point, true);

    // 创建拖拽辅助线
    createDragGuideLines(point);

    // 显示拖拽提示
    ElMessage.info('正在拖拽点位，释放鼠标完成移动');
  } catch (error) {
    console.error("启动拖拽失败:", error);
    draggingPoint.value = null;
  }
}

// 创建拖拽辅助线
const createDragGuideLines = (point: Point) => {
  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) return;

  try {
    // 创建水平辅助线
    const horizontalLine = document.createElementNS("http://www.w3.org/2000/svg", "line");
    horizontalLine.setAttribute("id", "drag-guide-h");
    horizontalLine.setAttribute("x1", "0");
    horizontalLine.setAttribute("y1", point.y.toString());
    horizontalLine.setAttribute("x2", "10000");
    horizontalLine.setAttribute("y2", point.y.toString());
    horizontalLine.setAttribute("stroke", "#1890ff");
    horizontalLine.setAttribute("stroke-width", "1");
    horizontalLine.setAttribute("stroke-dasharray", "5,5");
    horizontalLine.setAttribute("pointer-events", "none");

    // 创建垂直辅助线
    const verticalLine = document.createElementNS("http://www.w3.org/2000/svg", "line");
    verticalLine.setAttribute("id", "drag-guide-v");
    verticalLine.setAttribute("x1", point.x.toString());
    verticalLine.setAttribute("y1", "0");
    verticalLine.setAttribute("x2", point.x.toString());
    verticalLine.setAttribute("y2", "10000");
    verticalLine.setAttribute("stroke", "#1890ff");
    verticalLine.setAttribute("stroke-width", "1");
    verticalLine.setAttribute("stroke-dasharray", "5,5");
    verticalLine.setAttribute("pointer-events", "none");

    // 添加到SVG
    svgEl.appendChild(horizontalLine);
    svgEl.appendChild(verticalLine);

    // 创建原点位置标记
    const originalPosition = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    originalPosition.setAttribute("id", "drag-original-position");
    originalPosition.setAttribute("cx", point.x.toString());
    originalPosition.setAttribute("cy", point.y.toString());
    originalPosition.setAttribute("r", "4");
    originalPosition.setAttribute("fill", "rgba(24, 144, 255, 0.5)");
    originalPosition.setAttribute("stroke", "#1890ff");
    originalPosition.setAttribute("stroke-width", "1");
    originalPosition.setAttribute("pointer-events", "none");

    // 添加到SVG
    svgEl.appendChild(originalPosition);
  } catch (error) {
    console.error("创建拖拽辅助线失败:", error);
  }
}

// 高亮拖拽中的元素
const highlightDraggingElement = (point: Point, highlight: boolean) => {
  try {
    const svgEl = svgElement.value?.querySelector('svg');
    if (!svgEl) return;

    // 使用getElementFromSvg函数查找元素
    const element = getElementFromSvg(svgEl, point.id);

    if (!element) {
      console.warn("高亮拖拽元素失败: 未找到元素", point.id);
      return;
    }

    if (highlight) {
      element.classList.add('dragging');
      element.setAttribute('data-original-opacity', element.getAttribute('opacity') || '1');
      element.setAttribute('opacity', '0.7');

      // 如果是文本组，特殊处理
      if (element.tagName.toLowerCase() === 'g') {
        const children = element.querySelectorAll('*');
        children.forEach(child => {
          child.classList.add('dragging');
        });
      }
    } else {
      element.classList.remove('dragging');
      const originalOpacity = element.getAttribute('data-original-opacity');
      if (originalOpacity) {
        element.setAttribute('opacity', originalOpacity);
      } else {
        element.removeAttribute('opacity');
      }

      // 如果是文本组，特殊处理
      if (element.tagName.toLowerCase() === 'g') {
        const children = element.querySelectorAll('*');
        children.forEach(child => {
          child.classList.remove('dragging');
        });
      }
    }
  } catch (error) {
    console.error(`${highlight ? '添加' : '移除'}拖拽高亮效果失败:`, error);
  }
}

// 处理位点拖拽过程
const handleDrag = (event: MouseEvent) => {
  // 阻止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();

  if (!draggingPoint.value) {
    console.log("拖拽处理 - 没有正在拖拽的点位");
    return;
  }

  // 只有按住Ctrl键才能继续拖拽
  if (!event.ctrlKey && !ctrlKeyPressed.value) {
    console.log("拖拽处理 - 未按住Ctrl键，停止拖拽");
    stopDrag();
    return;
  }

  try {
    // 获取SVG元素
    const svgElem = svgElement.value;
    if (!svgElem) {
      console.error("拖拽处理 - 找不到SVG容器元素");
      return;
    }

    // 获取SVG元素
    const svgEl = svgElem.querySelector('svg');
    if (!svgEl) {
      console.error("拖拽处理 - 找不到SVG元素");
      return;
    }

    // 使用统一的坐标转换函数计算SVG坐标
    const { x: svgX, y: svgY } = convertClientToSvgCoordinates(
      event.clientX,
      event.clientY,
      svgEl as SVGSVGElement,
      baseFitScale.value
    );

    // 检查坐标是否有效
    if (isNaN(svgX) || isNaN(svgY)) {
      console.error("拖拽处理 - 计算的坐标无效", svgX, svgY);
      return;
    }

    // 更新正在拖拽的点位副本的位置
    draggingPoint.value.x = svgX;
    draggingPoint.value.y = svgY;

    // 记录拖拽位置变化
    console.log(`拖拽点位 ${draggingPoint.value.id} 到位置 (${svgX}, ${svgY})`);

    // 直接更新SVG DOM中的点位位置
    updatePointPositionInDOM(draggingPoint.value);

    // 更新辅助线位置
    updateDragGuideLines(svgX, svgY);

    // 计算拖拽距离
    const dragDistance = Math.sqrt(
      Math.pow(svgX - dragOriginalX.value, 2) +
      Math.pow(svgY - dragOriginalY.value, 2)
    );

    // 如果拖拽距离超过阈值，显示吸附线
    if (dragDistance > 5) {
      showSnapLines(svgX, svgY, svgEl);
    }

    // 实时更新点位数组中的坐标，确保与DOM保持同步
    // 这样在缩放或其他操作时，点位位置也能正确显示
    const updatedPoints = props.points.map(p => {
      if (p.id === draggingPoint.value!.id) {
        // 创建点位的副本并更新坐标
        return { ...p, x: svgX, y: svgY };
      }
      return p;
    });

    // 通知父组件点位已更新
    emit('update:points', updatedPoints);
  } catch (error) {
    console.error("拖拽处理失败:", error);
    ElMessage.error('拖拽点位失败');
    // 停止拖拽
    stopDrag();
  }
}

// 更新拖拽辅助线位置
const updateDragGuideLines = (x: number, y: number) => {
  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) return;

  try {
    // 更新水平辅助线
    const horizontalLine = svgEl.querySelector('#drag-guide-h');
    if (horizontalLine) {
      horizontalLine.setAttribute("y1", y.toString());
      horizontalLine.setAttribute("y2", y.toString());
    }

    // 更新垂直辅助线
    const verticalLine = svgEl.querySelector('#drag-guide-v');
    if (verticalLine) {
      verticalLine.setAttribute("x1", x.toString());
      verticalLine.setAttribute("x2", x.toString());
    }

    // 更新拖拽距离标签
    let distanceLabel = svgEl.querySelector('#drag-distance-label');
    if (!distanceLabel) {
      distanceLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
      distanceLabel.setAttribute("id", "drag-distance-label");
      distanceLabel.setAttribute("font-size", "12");
      distanceLabel.setAttribute("fill", "#1890ff");
      distanceLabel.setAttribute("pointer-events", "none");
      svgEl.appendChild(distanceLabel);
    }

    // 计算拖拽距离和位移
    const dx = x - dragOriginalX.value;
    const dy = y - dragOriginalY.value;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // 更新距离标签
    distanceLabel.setAttribute("x", ((x + dragOriginalX.value) / 2).toString());
    distanceLabel.setAttribute("y", ((y + dragOriginalY.value) / 2 - 10).toString());
    distanceLabel.textContent = `△(${Math.round(dx)}, ${Math.round(dy)}) | ${Math.round(distance)}px`;
  } catch (error) {
    console.error("更新拖拽辅助线失败:", error);
  }
}

// 显示吸附线
const showSnapLines = (x: number, y: number, svgEl: SVGSVGElement) => {
  // 吸附阈值（像素）
  const snapThreshold = 5;

  // 查找其他点位，检查是否需要吸附
  props.points.forEach(p => {
    if (draggingPoint.value && p.id !== draggingPoint.value.id) {
      // 检查X坐标是否接近
      if (Math.abs(p.x - x) < snapThreshold) {
        // 创建或更新X吸附线
        let xSnapLine = svgEl.querySelector('#x-snap-line');
        if (!xSnapLine) {
          xSnapLine = document.createElementNS("http://www.w3.org/2000/svg", "line");
          xSnapLine.setAttribute("id", "x-snap-line");
          xSnapLine.setAttribute("stroke", "#ff4d4f");
          xSnapLine.setAttribute("stroke-width", "1");
          xSnapLine.setAttribute("stroke-dasharray", "3,3");
          xSnapLine.setAttribute("pointer-events", "none");
          svgEl.appendChild(xSnapLine);
        }

        // 设置吸附线位置
        xSnapLine.setAttribute("x1", p.x.toString());
        xSnapLine.setAttribute("y1", "0");
        xSnapLine.setAttribute("x2", p.x.toString());
        xSnapLine.setAttribute("y2", "10000");

        // 如果距离非常接近，直接吸附
        if (Math.abs(p.x - x) < 2 && draggingPoint.value) {
          draggingPoint.value.x = p.x;
          updatePointPositionInDOM(draggingPoint.value);
        }
      }

      // 检查Y坐标是否接近
      if (Math.abs(p.y - y) < snapThreshold) {
        // 创建或更新Y吸附线
        let ySnapLine = svgEl.querySelector('#y-snap-line');
        if (!ySnapLine) {
          ySnapLine = document.createElementNS("http://www.w3.org/2000/svg", "line");
          ySnapLine.setAttribute("id", "y-snap-line");
          ySnapLine.setAttribute("stroke", "#ff4d4f");
          ySnapLine.setAttribute("stroke-width", "1");
          ySnapLine.setAttribute("stroke-dasharray", "3,3");
          ySnapLine.setAttribute("pointer-events", "none");
          svgEl.appendChild(ySnapLine);
        }

        // 设置吸附线位置
        ySnapLine.setAttribute("x1", "0");
        ySnapLine.setAttribute("y1", p.y.toString());
        ySnapLine.setAttribute("x2", "10000");
        ySnapLine.setAttribute("y2", p.y.toString());

        // 如果距离非常接近，直接吸附
        if (Math.abs(p.y - y) < 2 && draggingPoint.value) {
          draggingPoint.value.y = p.y;
          updatePointPositionInDOM(draggingPoint.value);
        }
      }
    }
  });
}

// 更新DOM中点位的位置
const updatePointPositionInDOM = (point: Point) => {
  try {
    const svgEl = svgElement.value?.querySelector('svg');
    if (!svgEl) return;

    // 使用getElementFromSvg函数查找元素
    let element = getElementFromSvg(svgEl, point.id);

    if (!element) {
      console.warn("更新DOM中点位位置失败: 未找到元素", point.id);

      // 尝试更多的选择器查找元素
      if (point.isText) {
        // 尝试查找transform属性包含坐标的组元素
        const allGroups = Array.from(svgEl.querySelectorAll('g[transform]'));
        for (const group of allGroups) {
          // 检查元素的data-id或data-text-id属性
          if (group.getAttribute('data-id') === point.id || group.getAttribute('data-text-id') === point.id) {
            element = group;
            console.log(`找到文本点位组元素: ${point.id}`);
            break;
          }
        }
      }

      if (!element) {
        // 尝试通过ID模式查找
        element = svgEl.getElementById(`text-group-${point.id}`) ||
          svgEl.getElementById(`text-${point.id}`) ||
          svgEl.getElementById(point.id.toString());
      }

      // 如果还是找不到，放弃并返回
      if (!element) {
        console.error("无法找到点位元素，放弃更新位置:", point.id);
        return;
      }
    }

    if (point.isText) {
      // 文本点位 - 更新transform属性
      element.setAttribute('transform', `translate(${point.x}, ${point.y})`);
      // 保存实际坐标到data属性，便于后续读取
      element.setAttribute('data-x', point.x.toString());
      element.setAttribute('data-y', point.y.toString());
      console.log(`更新文本点位${point.id}位置为(${point.x},${point.y})`);
    } else {
      // 根据元素类型更新不同的属性
      const tagName = element.tagName.toLowerCase();
      const size = parseFloat(element.getAttribute('data-size') || '8');

      if (tagName === 'circle') {
        element.setAttribute('cx', point.x.toString());
        element.setAttribute('cy', point.y.toString());
      } else if (tagName === 'rect') {
        element.setAttribute('x', (point.x - size).toString());
        element.setAttribute('y', (point.y - size).toString());
      } else if (tagName === 'polygon') {
        const pointsCoord = `${point.x},${point.y - size} ${point.x + size},${point.y + size} ${point.x - size},${point.y + size}`;
        element.setAttribute('points', pointsCoord);
      }

      // 更新data-x和data-y属性
      element.setAttribute('data-x', point.x.toString());
      element.setAttribute('data-y', point.y.toString());

      console.log(`更新图形点位${point.id}位置为(${point.x},${point.y})`);
    }
  } catch (error) {
    console.error('更新DOM中点位位置失败:', error);
  }
}

// 处理位点拖拽结束
const stopDrag = () => {
  try {
    // 记录最后的点位位置
    if (draggingPoint.value) {
      console.log("结束拖拽点位:", draggingPoint.value.id, "最终位置:", draggingPoint.value.x, draggingPoint.value.y);

      // 计算总移动距离
      const dx = draggingPoint.value.x - dragOriginalX.value;
      const dy = draggingPoint.value.y - dragOriginalY.value;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // 确保更新点位DOM位置
      updatePointPositionInDOM(draggingPoint.value);

      // 如果是文本点位，从DOM中获取最新位置，确保坐标准确
      if (draggingPoint.value.isText) {
        const svgEl = svgElement.value?.querySelector('svg');
        if (svgEl) {
          const element = getElementFromSvg(svgEl, draggingPoint.value.id);
          if (element) {
            // 尝试从元素的transform属性获取位置
            const transform = element.getAttribute('transform');
            if (transform) {
              const match = transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
              if (match && match.length >= 3) {
                const x = parseFloat(match[1]);
                const y = parseFloat(match[2]);
                if (!isNaN(x) && !isNaN(y)) {
                  // 更新拖拽点位的坐标
                  draggingPoint.value.x = x;
                  draggingPoint.value.y = y;
                  console.log(`从transform属性更新拖拽点位坐标为 (${x}, ${y})`);
                }
              }
            }

            // 确保元素上有data-x和data-y属性
            element.setAttribute('data-x', draggingPoint.value.x.toString());
            element.setAttribute('data-y', draggingPoint.value.y.toString());
          }
        }
      }

      // 创建更新后的点位数组
      const updatedPoints = props.points.map(p => {
        if (p.id === draggingPoint.value!.id) {
          // 创建点位的副本并更新坐标
          const updatedPoint = { ...p, x: draggingPoint.value!.x, y: draggingPoint.value!.y };
          console.log("更新点位数据:", p.id, "旧坐标:", p.x, p.y, "新坐标:", updatedPoint.x, updatedPoint.y);
          return updatedPoint;
        }
        return p;
      });

      // 移除高亮样式
      removeHighlightFromDraggingElement(draggingPoint.value);

      // 通知外部组件点位已更新
      emit('update:points', updatedPoints);

      // 如果拖拽距离大于1像素，表示有实际移动，触发修改状态
      if (distance > 1) {
        // 触发拖拽点位修改事件，通知父组件设置未保存状态
        emit('drag-end', {
          point: draggingPoint.value,
          distance: distance,
          dx: dx,
          dy: dy
        });

        ElMessage.success(`点位移动成功: (${Math.round(dx)}, ${Math.round(dy)}), 距离: ${Math.round(distance)}px`);
      } else {
        ElMessage.info('点位位置未发生变化');
      }

      // 提示用户拖拽完成
      ElMessage.info('拖拽完成，松开Ctrl键退出拖拽模式');
    }

    // 清除拖拽状态
    draggingPoint.value = null;

    // 移除事件监听
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', stopDrag);

    // 恢复默认鼠标样式
    document.body.style.cursor = 'default';

    // 移除SVG容器上的拖拽类
    if (svgContainerRef.value) {
      svgContainerRef.value.classList.remove('dragging');
    }

    // 清理辅助线和标记
    clearDragGuideLines();
  } catch (error) {
    console.error("结束拖拽失败:", error);
    // 确保清除状态
    draggingPoint.value = null;
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', stopDrag);
    document.body.style.cursor = 'default';

    // 确保移除SVG容器上的拖拽类
    if (svgContainerRef.value) {
      svgContainerRef.value.classList.remove('dragging');
    }

    // 确保清理辅助线和标记
    clearDragGuideLines();
  }
}

// 清理拖拽辅助线和标记
const clearDragGuideLines = () => {
  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) return;

  try {
    // 清理辅助线 - 使用更完整的选择器
    const guidesToRemove = [
      '#drag-guide-h', 'line[id="drag-guide-h"]', 'line[data-type="drag-guide-h"]',
      '#drag-guide-v', 'line[id="drag-guide-v"]', 'line[data-type="drag-guide-v"]',
      '#drag-original-position', 'circle[id="drag-original-position"]', 'circle[data-type="drag-original-position"]',
      '#drag-distance-label', 'text[id="drag-distance-label"]', 'text[data-type="drag-distance-label"]',
      '#x-snap-line', 'line[id="x-snap-line"]', 'line[data-type="snap-line"]',
      '#y-snap-line', 'line[id="y-snap-line"]', 'line[data-type="snap-line"]'
    ];

    // 通过ID和类型查找并移除辅助线元素
    guidesToRemove.forEach(selector => {
      try {
        const elements = svgEl.querySelectorAll(selector);
        elements.forEach(element => {
          if (element && element.parentNode) {
            element.parentNode.removeChild(element);
          }
        });
      } catch (e) {
        console.warn(`删除元素 ${selector} 失败:`, e);
      }
    });

    // 为确保彻底清理，移除所有带有特定辅助线相关属性的元素
    const dragElements = svgEl.querySelectorAll('[data-drag-guide="true"], [data-snap-line="true"]');
    dragElements.forEach(element => {
      if (element && element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });

    // 最后通过元素类型额外查找
    const additionalElements = svgEl.querySelectorAll('line[stroke="#ff4d4f"], line[stroke-dasharray="3,3"]');
    additionalElements.forEach(element => {
      // 检查是否是辅助线，避免删除用户创建的线
      const isGuide = element.getAttribute('pointer-events') === 'none' ||
        element.getAttribute('data-guide') === 'true' ||
        !element.hasAttribute('data-id');

      if (isGuide && element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });

    console.log("清理拖拽辅助线完成");
  } catch (error) {
    console.error("清理拖拽辅助线失败:", error);
  }
}

// 移除高亮样式
const removeHighlightFromDraggingElement = (point: Point) => {
  // 调用highlightDraggingElement来移除高亮
  highlightDraggingElement(point, false);
}

// 选择点位
const selectPoint = (point: Point) => {
  // 如果正在添加元素，不进行选择操作
  if (props.addingElementType) return;

  // 如果选择的是当前已选中的点位，取消选择
  if (selectedPoint.value?.id === point.id) {
    selectedPoint.value = null;
    emit('select-point', null);
  } else {
    selectedPoint.value = point;
    emit('select-point', point);
    // 提示用户可以双击编辑
    ElMessage.info('点位已选中，双击可编辑点位属性');
  }
}

// 编辑点位
const editPoint = (point: Point) => {
  try {
    // 如果正在添加元素，则不允许编辑
    if (props.addingElementType) {
      console.log("正在添加元素，不允许编辑");
      return;
    }

    console.log("触发编辑点位事件", point.id, point.name, "坐标:", point.x, point.y);

    // 确保点位数据完整
    if (!point.id || !point.showType) {
      console.warn("点位数据不完整", point);
    }

    // 重要修复：确保使用最新的点位坐标
    // 从DOM中获取当前点位的实际位置
    const svgEl = svgElement.value?.querySelector('svg');
    if (svgEl) {
      let element;
      if (point.isText) {
        // 优先查找文本组，使用data-id属性
        element = svgEl.querySelector(`g[data-text-id="${point.id}"], g[data-id="${point.id}"]`);
        if (!element) {
          // 如果找不到组，尝试查找独立的文本元素
          element = svgEl.querySelector(`text[data-id="${point.id}"]`);
        }

        // 对于纯数字ID，使用getElementById
        if (!element && /^\d+$/.test(point.id.toString())) {
          const textGroupId = `text-group-${point.id}`;
          const textId = `text-${point.id}`;
          element = svgEl.getElementById(textGroupId) || svgEl.getElementById(textId);
        }
      } else {
        // 使用data-id属性查找图形点位
        element = svgEl.querySelector(`[data-id="${point.id}"]`);

        // 对于纯数字ID，使用getElementById
        if (!element && /^\d+$/.test(point.id.toString())) {
          element = svgEl.getElementById(point.id.toString());
        }
      }

      // 如果找到了元素，从元素属性中获取最新坐标
      if (element) {
        let x = point.x;
        let y = point.y;

        if (point.isText) {
          if (element.tagName.toLowerCase() === 'text') {
            x = parseFloat(element.getAttribute('x') || point.x.toString());
            y = parseFloat(element.getAttribute('y') || point.y.toString());
          } else if (element.tagName.toLowerCase() === 'g') {
            const transform = element.getAttribute('transform');
            if (transform) {
              const match = transform.match(/translate\(([^,]+),([^)]+)\)/);
              if (match && match.length === 3) {
                x = parseFloat(match[1]);
                y = parseFloat(match[2]);
              }
            }
          }
        } else {
          if (element.tagName.toLowerCase() === 'circle') {
            x = parseFloat(element.getAttribute('cx') || point.x.toString());
            y = parseFloat(element.getAttribute('cy') || point.y.toString());
          } else if (element.tagName.toLowerCase() === 'rect') {
            const width = parseFloat(element.getAttribute('width') || '16');
            const height = parseFloat(element.getAttribute('height') || '16');
            x = parseFloat(element.getAttribute('x') || point.x.toString()) + width / 2;
            y = parseFloat(element.getAttribute('y') || point.y.toString()) + height / 2;
          } else if (element.tagName.toLowerCase() === 'polygon') {
            // 对于多边形，需要解析points属性
            const pointsAttr = element.getAttribute('points');
            if (pointsAttr) {
              const coords = pointsAttr.split(' ')[0].split(',');
              if (coords.length === 2) {
                // 对于三角形，第一个点是顶点，我们需要计算中心点
                const points = pointsAttr.split(' ').map(p => {
                  const [px, py] = p.split(',');
                  return { x: parseFloat(px), y: parseFloat(py) };
                });

                // 计算中心点
                if (points.length >= 3) {
                  x = (points[0].x + points[1].x + points[2].x) / 3;
                  y = (points[0].y + points[1].y + points[2].y) / 3;
                }
              }
            }
          }
        }

        // 更新点位坐标
        if (!isNaN(x) && !isNaN(y)) {
          console.log(`从DOM元素获取到最新坐标: (${x}, ${y})，原坐标: (${point.x}, ${point.y})`);
          point.x = x;
          point.y = y;
        }

        // 重要修复：确保从props.points数组中获取完整的点位数据
        // 这样可以确保所有属性（包括data对象）都被正确传递
        const originalPoint = props.points.find(p => p.id === point.id);
        if (originalPoint) {
          // 使用原始点位的数据，但更新坐标
          const completePoint = {
            ...originalPoint,
            x: point.x,
            y: point.y
          };

          console.log("使用完整点位数据:", completePoint);

          // 发送编辑事件，使用完整的点位数据
          emit('edit-point', completePoint, false);

          // 提示用户编辑事件已触发
          ElMessage.info('正在打开编辑器...');
          return;
        }
      }
    }

    // 如果没有找到完整点位数据，使用传入的点位
    console.log("未找到完整点位数据，使用传入的点位:", point);
    emit('edit-point', point, false);

    // 提示用户编辑事件已触发
    ElMessage.info('正在打开编辑器...');
  } catch (error) {
    console.error("编辑点位失败:", error);
    ElMessage.error('编辑点位失败，请重试');
  }
}

// SVG拖动功能
const startSvgDrag = (event: MouseEvent) => {
  // 如果点击的是点位元素，不启动SVG拖动
  if ((event.target as HTMLElement).closest('.monitoring-points-layer')) {
    return;
  }

  svgIsDragging.value = true;
  svgDragStartX.value = event.clientX - svgTranslateX.value;
  svgDragStartY.value = event.clientY - svgTranslateY.value;
}

const onSvgDrag = (event: MouseEvent) => {
  if (!svgIsDragging.value) return;

  svgTranslateX.value = event.clientX - svgDragStartX.value;
  svgTranslateY.value = event.clientY - svgDragStartY.value;

  applySvgTransform();
}

const endSvgDrag = () => {
  svgIsDragging.value = false;
}

const applySvgTransform = () => {
  const svgContent = svgElement.value;
  if (svgContent) {
    svgContent.style.transform = `translate(${svgTranslateX.value}px, ${svgTranslateY.value}px)`;
  }
}

// 重置SVG位置
const resetPosition = () => {
  console.log("重置SVG位置");

  // 重置拖拽状态
  svgIsDragging.value = false;
  svgTranslateX.value = 0;
  svgTranslateY.value = 0;

  try {
    // 获取SVG容器元素
    const svgElem = svgElement.value;

    if (!svgElem) {
      console.warn("重置位置失败：找不到SVG容器元素");
      return;
    }

    // 安全地获取SVG元素
    let svgEl = null;
    try {
      const el = svgElem.querySelector('svg');
      svgEl = el;
    } catch (err) {
      console.error("查询SVG元素失败:", err);
    }

    if (!svgEl) {
      console.warn("重置位置失败：找不到SVG元素，将稍后重试");

      // 如果SVG元素尚未加载，设置一个延迟调用
      setTimeout(() => {
        try {
          // 再次尝试获取SVG元素
          const retryElem = svgElement.value;
          if (!retryElem) {
            console.warn("延迟重试仍然找不到SVG容器元素");
            return;
          }

          let retrySvgEl = null;
          try {
            retrySvgEl = retryElem.querySelector('svg');
          } catch (retryErr) {
            console.error("延迟重试查询SVG元素失败:", retryErr);
            return;
          }

          if (retrySvgEl) {
            console.log("延迟重试成功找到SVG元素");
            // 重新计算适合的缩放比例
            calculateFitToViewScale(retrySvgEl);

            // 应用正确的变换
            try {
              retrySvgEl.setAttribute('width', '100%');
              retrySvgEl.setAttribute('height', '100%');
              retrySvgEl.setAttribute('style', 'max-width:100%; max-height:100%;');
            } catch (attrErr) {
              console.error("设置SVG属性失败:", attrErr);
            }
          } else {
            console.warn("延迟重试仍然找不到SVG元素");
          }
        } catch (retryError) {
          console.error("延迟重置SVG位置时出错:", retryError);
        }
      }, 300);

      return;
    }

    // 重新计算适合的缩放比例
    calculateFitToViewScale(svgEl);

    // 延迟一点时间让浏览器重新渲染后，再计算位置
    setTimeout(() => {
      try {
        // 再次获取SVG元素，确保是最新的
        let updatedSvgEl = null;
        try {
          if (svgElement.value) {
            updatedSvgEl = svgElement.value.querySelector('svg');
          }
        } catch (innerQueryError) {
          console.error("延迟查询SVG元素时出错:", innerQueryError);
        }

        if (updatedSvgEl) {
          // 应用正确的变换
          try {
            updatedSvgEl.setAttribute('width', '100%');
            updatedSvgEl.setAttribute('height', '100%');
            updatedSvgEl.setAttribute('style', 'max-width:100%; max-height:100%;');
          } catch (attrErr) {
            console.error("设置SVG属性失败:", attrErr);
          }

          // 确保SVG内容清晰可见
          try {
            const svgWrapper = svgElem.closest('.svg-wrapper');
            if (svgWrapper) {
              // 重置缩放过渡效果
              (svgWrapper as HTMLElement).style.transition = 'transform 0.3s ease';
              setTimeout(() => {
                if (svgWrapper) {
                  (svgWrapper as HTMLElement).style.transition = '';
                }
              }, 300);
            }
          } catch (wrapperErr) {
            console.error("设置SVG容器样式失败:", wrapperErr);
          }

          console.log("SVG位置已重置");
        } else {
          console.warn("延迟重置中无法找到SVG元素");
        }
      } catch (innerError) {
        console.error("延迟重置SVG位置时出错:", innerError);
      }
    }, 100);
  } catch (error) {
    console.error("重置SVG位置失败:", error);
  }
}

// 添加或更新SVG中的点位
const addOrUpdatePointInSVG = (point: Point, isNew = false, isPreview = false) => {
  // 确保点位有正确的showType属性
  if (!point.showType && point.type) {
    point.showType = point.type;
    console.log("自动设置showType属性:", point.showType);
  } else if (!point.showType) {
    point.showType = point.isText ? 'text' : 'circle';
    console.log("未找到showType，设置默认值:", point.showType);
  }

  // 增加更详细的日志，特别是预览状态下的属性
  if (isPreview) {
    console.log("预览点位详细信息:", {
      id: point.id,
      类型: point.showType,
      是否文本: point.isText,
      位置: { x: point.x, y: point.y },
      大小: point.data?.size,
      颜色: point.data?.color,
      文本大小: point.data?.pointSize,
      文本颜色: point.data?.color,
      背景色: point.data?.backgroundColor,
      边框色: point.data?.borderColor
    });
  }

  console.log("添加或更新SVG中的点位:",
    isNew ? "新增" : (isPreview ? "预览" : "更新"),
    "点位ID:", point.id,
    "点位类型:", point.showType,
    "是否文本:", point.isText,
    "点位数据:", point.data);

  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) return;

  // 获取或创建监测点容器
  let pointsGroup = svgEl.querySelector('#monitoring-points');
  if (!pointsGroup) {
    pointsGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
    pointsGroup.setAttribute("id", "monitoring-points");
    pointsGroup.setAttribute("class", "monitoring-points-layer");
    svgEl.appendChild(pointsGroup);
  }

  // 如果是新增点位，直接添加
  if (isNew) {
    if (point.isText) {
      addTextElementToSVG(point, pointsGroup as SVGGElement);
    } else {
      addPointElementToSVG(point, pointsGroup as SVGGElement);
    }
    return;
  }

  // 更新现有点位
  let existingElement;
  if (point.isText) {
    // 先尝试查找文本组，优先使用data-id属性进行匹配
    existingElement = svgEl.querySelector(`g[data-text-id="${point.id}"], g[data-id="${point.id}"]`);
    // 对于纯数字ID，使用getElementById
    if (!existingElement && /^\d+$/.test(point.id.toString())) {
      const textGroupId = `text-group-${point.id}`;
      existingElement = svgEl.getElementById(textGroupId);
    }

    // 如果仍然找不到，尝试更多选择器
    if (!existingElement) {
      console.log(`尝试使用更多选择器查找文本点位: ${point.id}`);
      // 通过transform属性查找可能的组元素
      const transformSelector = `g[transform="translate(${point.x}, ${point.y})"]`;
      existingElement = svgEl.querySelector(transformSelector);

      // 如果还是找不到，尝试通过位置附近的元素查找
      if (!existingElement) {
        // 查找transform值接近的元素
        const allGroups = Array.from(svgEl.querySelectorAll('g[transform]'));
        for (const group of allGroups) {
          const transform = group.getAttribute('transform');
          if (transform) {
            const match = transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
            if (match && match.length >= 3) {
              const gx = parseFloat(match[1]);
              const gy = parseFloat(match[2]);
              // 检查坐标是否接近
              if (Math.abs(gx - point.x) < 2 && Math.abs(gy - point.y) < 2) {
                existingElement = group;
                console.log(`找到位置接近的文本组: (${gx}, ${gy})`);
                break;
              }
            }
          }
        }
      }
    }

    if (existingElement) {
      // 找到了文本组，更新其位置和属性
      existingElement.setAttribute("transform", `translate(${point.x}, ${point.y})`);

      // 更新文本内容和属性
      const textElement = existingElement.querySelector(`text[data-id="${point.id}"]`);
      // 对于纯数字ID，使用getElementById
      let textElementById: Element | null = null;
      if ((!textElement) && /^\d+$/.test(point.id.toString())) {
        const textId = `text-${point.id}`;
        textElementById = svgEl.getElementById(textId);
      }

      const textElementToUpdate = textElement || textElementById;
      if (textElementToUpdate) {
        textElementToUpdate.textContent = point.name;
        textElementToUpdate.setAttribute("fill", point.data?.color || "#000000");
        textElementToUpdate.setAttribute("font-size", `${point.data?.pointSize || 14}px`);
        textElementToUpdate.setAttribute("font-weight", point.data?.textThickness || "normal");
      }

      // 处理边框和背景
      const hasBackground = point.data?.hasBackground === true;
      const hasBorder = point.data?.hasBorder === true;
      const backgroundColor = hasBackground ? (point.data?.backgroundColor || 'rgba(255, 255, 255, 0.7)') : 'transparent';
      const borderColor = hasBorder ? (point.data?.borderColor || '#000000') : 'transparent';
      const borderWidth = hasBorder ? (point.data?.borderWidth || 1) : 0;
      const padding = (hasBackground || hasBorder) ? (point.data?.padding || 8) : 5;
      // 添加安全边距
      const safetyMargin = 4;

      // 更新边框和背景属性
      existingElement.setAttribute("data-has-background", hasBackground ? "true" : "false");
      existingElement.setAttribute("data-has-border", hasBorder ? "true" : "false");
      existingElement.setAttribute("data-background-color", backgroundColor);
      existingElement.setAttribute("data-border-color", borderColor);
      existingElement.setAttribute("data-border-width", borderWidth.toString());
      existingElement.setAttribute("data-padding", padding.toString());

      // 检查是否有背景矩形
      let backgroundRect = existingElement.querySelector('rect[data-is-background="true"]');

      // 总是处理背景矩形，无论是否有边框
      try {
        // 使用现有的文本元素直接测量，而不是创建临时元素
        const textElementForMeasure = textElementToUpdate;
        if (textElementForMeasure) {
          // 获取现有文本元素的实际尺寸
          const textBBox = textElementForMeasure.getBBox();
          console.log(`更新背景矩形 - 文本"${textElementForMeasure.textContent}"的实际尺寸:`, textBBox);

          // 计算新的边框尺寸，增加安全边距
          const rectWidth = textBBox.width + (padding * 2) + safetyMargin;
          const rectHeight = textBBox.height + (padding * 2) + safetyMargin;

          if (backgroundRect) {
            // 更新现有背景矩形
            backgroundRect.setAttribute("x", `${-rectWidth / 2}`);
            backgroundRect.setAttribute("y", `${-rectHeight / 2}`);
            backgroundRect.setAttribute("width", `${rectWidth}`);
            backgroundRect.setAttribute("height", `${rectHeight}`);
            backgroundRect.setAttribute("fill", backgroundColor);
            backgroundRect.setAttribute("stroke", borderColor);
            backgroundRect.setAttribute("stroke-width", `${borderWidth}`);
            console.log(`更新现有背景矩形: 宽=${rectWidth}, 高=${rectHeight}, 内边距=${padding}, 安全边距=${safetyMargin}, 背景色=${backgroundColor}, 边框=${hasBorder ? '显示' : '隐藏'}`);
          } else {
            // 创建新的背景矩形
            backgroundRect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
            backgroundRect.setAttribute("x", `${-rectWidth / 2}`);
            backgroundRect.setAttribute("y", `${-rectHeight / 2}`);
            backgroundRect.setAttribute("width", `${rectWidth}`);
            backgroundRect.setAttribute("height", `${rectHeight}`);
            backgroundRect.setAttribute("rx", "3");  // 圆角
            backgroundRect.setAttribute("ry", "3");  // 圆角
            backgroundRect.setAttribute("fill", backgroundColor);
            backgroundRect.setAttribute("stroke", borderColor);
            backgroundRect.setAttribute("stroke-width", `${borderWidth}`);
            backgroundRect.setAttribute("data-is-background", "true");
            backgroundRect.setAttribute("data-id", point.id);
            backgroundRect.setAttribute("style", "pointer-events: all !important;");

            // 插入到组的开头（在文本下方）
            existingElement.insertBefore(backgroundRect, existingElement.firstChild);
            console.log(`创建新的背景矩形: 宽=${rectWidth}, 高=${rectHeight}, 内边距=${padding}, 安全边距=${safetyMargin}, 背景色=${backgroundColor}, 边框=${hasBorder ? '显示' : '隐藏'}`);
          }
        }
      } catch (error) {
        console.error("更新背景矩形失败:", error);
      }

      console.log(`文本点位${point.id}位置更新为(${point.x},${point.y}), 边框状态: ${hasBorder}, 背景色: ${backgroundColor}`);
    } else {
      // 尝试查找旧版本的文本元素 (不在组内)
      const oldTextElement = svgEl.querySelector(`text[data-id="${point.id}"]`);
      // 对于纯数字ID，使用getElementById
      let oldTextElementById: Element | null = null;
      if (!oldTextElement && /^\d+$/.test(point.id.toString())) {
        const textId = `text-${point.id}`;
        oldTextElementById = svgEl.getElementById(textId);
      }

      // 移除旧版本文本元素
      const elementToRemove = oldTextElement || oldTextElementById;
      if (elementToRemove && elementToRemove.parentNode) {
        elementToRemove.parentNode.removeChild(elementToRemove);
      }

      // 添加新的文本组
      addTextElementToSVG(point, pointsGroup as SVGGElement);
    }
  } else {
    // 优先使用data-id属性查找图形点位
    existingElement = svgEl.querySelector(`[data-id="${point.id}"]`);
    // 对于纯数字ID，使用getElementById
    if (!existingElement && /^\d+$/.test(point.id.toString())) {
      existingElement = svgEl.getElementById(point.id.toString());
    }

    // 确保我们没有错过任何元素
    if (!existingElement) {
      console.log(`使用更多选择器尝试查找图形点位: ${point.id}`);
      // 尝试更多选择器
      existingElement = svgEl.querySelector(`[id="${point.id}"], [data-point-id="${point.id}"], circle[cx="${point.x}"][cy="${point.y}"], rect[x="${point.x - (point.data?.size || 8)}"][y="${point.y - (point.data?.size || 8)}"]`);
    }

    if (existingElement) {
      // 如果点位类型变了，需要重新创建
      const currentType = existingElement.getAttribute('data-show-type');
      console.log("比较点位类型:", currentType, "vs", point.showType);
      if (currentType !== point.showType) {
        console.log("点位类型变化，重新创建元素");
        existingElement.parentNode?.removeChild(existingElement);
        addPointElementToSVG(point, pointsGroup as SVGGElement);
      } else {
        // 更新点位属性
        console.log("点位类型未变，更新属性");
        updatePointAttributes(existingElement, point);
      }
    } else {
      // 不存在则添加
      addPointElementToSVG(point, pointsGroup as SVGGElement);
    }
  }
}

// 添加文本元素到SVG
const addTextElementToSVG = (point: Point, container: SVGGElement) => {
  console.log("添加文本元素到SVG - 详细信息:", {
    id: point.id,
    文本内容: point.name,
    位置: `(${point.x}, ${point.y})`,
    字体大小: point.data?.pointSize || 13,
    字体颜色: point.data?.color || "#000000",
    边框: point.data?.hasBorder,
    背景色: point.data?.backgroundColor,
    边框颜色: point.data?.borderColor
  });

  try {
    // 确定是否有背景和边框
    const hasBackground = point.data?.hasBackground === true;
    const hasBorder = point.data?.hasBorder === true;
    // 根据背景设置获取背景颜色
    const backgroundColor = hasBackground ? (point.data?.backgroundColor || 'rgba(255, 255, 255, 0.7)') : 'transparent';
    const borderColor = hasBorder ? (point.data?.borderColor || '#000000') : 'transparent';
    const borderWidth = hasBorder ? (point.data?.borderWidth || 1) : 0;
    // 增加默认内边距，确保文本不会紧贴边框
    const padding = (hasBackground || hasBorder) ? (point.data?.padding || 8) : 5;
    // 额外安全边距，确保文本完全被包含
    const safetyMargin = 4;

    const textElementGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
    textElementGroup.setAttribute("id", `text-group-${point.id}`);
    textElementGroup.setAttribute("data-id", point.id); // 确保设置data-id属性
    textElementGroup.setAttribute("data-text-id", point.id);
    textElementGroup.setAttribute("data-type", "text");
    textElementGroup.setAttribute("data-show-type", "text");
    textElementGroup.setAttribute("transform", `translate(${point.x}, ${point.y})`);
    // 保存坐标到data属性，便于拖拽后获取
    textElementGroup.setAttribute("data-x", point.x.toString());
    textElementGroup.setAttribute("data-y", point.y.toString());
    textElementGroup.setAttribute("style", "pointer-events: all !important; cursor: pointer;");

    // 添加监测指标信息到组
    if (point.data?.indicator) {
      textElementGroup.setAttribute("data-indicator-code", point.data.indicator);
    }

    // 添加所有data对象中的属性作为data-*属性
    if (point.data) {
      Object.entries(point.data).forEach(([key, value]) => {
        if (value !== undefined && value !== null && typeof value !== 'object') {
          textElementGroup.setAttribute(`data-${key}`, value.toString());
        }
      });
    }

    // 创建文本元素(先创建文本元素，用于精确测量尺寸)
    const textElement = document.createElementNS("http://www.w3.org/2000/svg", "text");
    textElement.setAttribute("x", "0");
    textElement.setAttribute("y", "0");
    textElement.setAttribute("id", `text-${point.id}`);
    textElement.setAttribute("data-id", point.id); // 确保设置data-id属性
    textElement.setAttribute("data-type", "text");
    textElement.setAttribute("data-show-type", "text");
    textElement.setAttribute("font-size", `${point.data?.pointSize || 13}px`);
    textElement.setAttribute("font-weight", point.data?.textThickness || "normal");
    textElement.setAttribute("fill", point.data?.color || "#000000");
    textElement.setAttribute("text-anchor", "start");
    textElement.setAttribute("dominant-baseline", "central");
    textElement.setAttribute("style", "pointer-events: all !important; cursor: pointer;");

    // 设置文本内容 - 确保有默认文本
    textElement.textContent = point.name || "文本标签";

    // 将文本添加到组，这样可以在SVG中测量实际尺寸
    textElementGroup.appendChild(textElement);
    container.appendChild(textElementGroup);

    // 总是创建背景矩形，无论是否有边框
    try {
      // 临时添加到SVG中以便准确计算实际渲染后的尺寸
      const textBBox = textElement.getBBox();
      console.log(`文本"${textElement.textContent}"的实际尺寸:`, textBBox);

      // 根据文本宽高和内边距计算背景矩形的尺寸
      // 添加额外的安全边距，确保完全包含
      const rectWidth = textBBox.width + (padding * 2) + safetyMargin;
      const rectHeight = textBBox.height + (padding * 2) + safetyMargin;

      // 创建背景矩形
      const backgroundRect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
      backgroundRect.setAttribute("x", `${-rectWidth / 2}`);
      backgroundRect.setAttribute("y", `${-rectHeight / 2}`);
      backgroundRect.setAttribute("width", `${rectWidth}`);
      backgroundRect.setAttribute("height", `${rectHeight}`);
      backgroundRect.setAttribute("rx", "3");  // 圆角
      backgroundRect.setAttribute("ry", "3");  // 圆角
      backgroundRect.setAttribute("fill", backgroundColor);
      backgroundRect.setAttribute("stroke", borderColor);
      backgroundRect.setAttribute("stroke-width", `${borderWidth}`);
      backgroundRect.setAttribute("data-is-background", "true");
      backgroundRect.setAttribute("data-id", point.id); // 确保设置data-id属性
      backgroundRect.setAttribute("style", "pointer-events: all !important; cursor: move;");

      // 添加背景矩形到组，确保矩形在文本后面
      textElementGroup.insertBefore(backgroundRect, textElement);

      // 为背景矩形添加事件处理
      backgroundRect.addEventListener('click', (e) => {
        e.stopPropagation();
        selectPoint(point);
      });

      backgroundRect.addEventListener('dblclick', (e) => {
        e.stopPropagation();
        editPoint(point);
      });

      // 修改背景矩形的mousedown事件，只有按住Ctrl键才能拖拽
      backgroundRect.addEventListener('mousedown', (e) => {
        e.stopPropagation();
        const mouseEvent = e as MouseEvent;
        // 只有按住Ctrl键才能拖拽
        if (mouseEvent.ctrlKey || ctrlKeyPressed.value) {
          console.log("背景矩形开始拖拽", point.id);
          startDrag(mouseEvent, point);
        }
      });

      console.log(`创建了背景矩形: 宽=${rectWidth}, 高=${rectHeight}, 内边距=${padding}, 安全边距=${safetyMargin}, 背景色=${backgroundColor}, 边框=${hasBorder ? '显示' : '隐藏'}`);
    } catch (error) {
      console.error("创建文本背景矩形失败:", error);
    }

    // 保存边框和背景数据 - 总是保存这些数据
    textElementGroup.setAttribute("data-has-background", hasBackground ? "true" : "false");
    textElementGroup.setAttribute("data-has-border", hasBorder ? "true" : "false");
    textElementGroup.setAttribute("data-background-color", backgroundColor);
    textElementGroup.setAttribute("data-border-color", borderColor);
    textElementGroup.setAttribute("data-border-width", borderWidth.toString());
    textElementGroup.setAttribute("data-padding", padding.toString());

    // 绑定组的事件
    textElementGroup.addEventListener('click', (e) => {
      e.stopPropagation();
      selectPoint(point);
    });

    textElementGroup.addEventListener('dblclick', (e) => {
      e.stopPropagation();
      editPoint(point);
    });

    // 修改mousedown事件，只有按住Ctrl键才能拖拽
    textElementGroup.addEventListener('mousedown', (e) => {
      e.stopPropagation();
      const mouseEvent = e as MouseEvent;
      // 只有按住Ctrl键才能拖拽
      if (mouseEvent.ctrlKey || ctrlKeyPressed.value) {
        console.log("文本组开始拖拽", point.id);
        startDrag(mouseEvent, point);
      }
    });

    // 为文本元素单独添加事件处理
    textElement.addEventListener('mousedown', (e) => {
      e.stopPropagation();
      const mouseEvent = e as MouseEvent;
      // 只有按住Ctrl键才能拖拽
      if (mouseEvent.ctrlKey || ctrlKeyPressed.value) {
        console.log("文本元素开始拖拽", point.id);
        startDrag(mouseEvent, point);
      }
    });

    console.log(`文本元素${point.id}已添加到SVG容器中，位置(${point.x}, ${point.y})，文本内容: "${point.name}"`);

    return textElementGroup;
  } catch (error) {
    console.error("添加文本元素失败:", error);
    return null;
  }
}

// 添加点位元素到SVG
const addPointElementToSVG = (point: Point, container: SVGGElement) => {
  let pointElement;
  const size = point.data?.size || 8;
  const color = point.data?.color || getPointColorByType(point.showType);
  console.log("添加点位元素到SVG - 详细信息:", {
    id: point.id,
    位置: `(${point.x}, ${point.y})`,
    类型: point.showType,
    尺寸: size,
    颜色: color
  });

  // 根据点位类型创建不同的SVG元素
  const showType = point.showType || 'circle'; // 确保有默认类型
  console.log("Creating point element with type:", showType, "original type:", point.type);

  switch (showType) {
    case 'circle':
      pointElement = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      pointElement.setAttribute("cx", point.x.toString());
      pointElement.setAttribute("cy", point.y.toString());
      pointElement.setAttribute("r", size.toString());
      pointElement.setAttribute("fill", color);
      console.log(`创建圆形点位，位置(${point.x}, ${point.y})，半径${size}`);
      break;
    case 'square':
      pointElement = document.createElementNS("http://www.w3.org/2000/svg", "rect");
      pointElement.setAttribute("x", (point.x - size).toString());
      pointElement.setAttribute("y", (point.y - size).toString());
      pointElement.setAttribute("width", (size * 2).toString());
      pointElement.setAttribute("height", (size * 2).toString());
      pointElement.setAttribute("fill", color);
      console.log(`创建方形点位，位置(${point.x}, ${point.y})，左上角(${point.x - size}, ${point.y - size})，尺寸${size * 2}x${size * 2}`);
      break;
    case 'triangle':
      pointElement = document.createElementNS("http://www.w3.org/2000/svg", "polygon");
      const pointsCoord = `${point.x},${point.y - size} ${point.x + size},${point.y + size} ${point.x - size},${point.y + size}`;
      pointElement.setAttribute("points", pointsCoord);
      pointElement.setAttribute("fill", color);
      console.log(`创建三角形点位，中心位置(${point.x}, ${point.y})，顶点坐标: ${pointsCoord}`);
      break;
    default:
      // 默认使用圆形
      console.log("未知点位类型，使用默认圆形:", showType);
      pointElement = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      pointElement.setAttribute("cx", point.x.toString());
      pointElement.setAttribute("cy", point.y.toString());
      pointElement.setAttribute("r", size.toString());
      pointElement.setAttribute("fill", color);
      console.log(`创建默认圆形点位，位置(${point.x}, ${point.y})，半径${size}`);
      break;
  }

  // 添加通用属性
  pointElement.setAttribute("id", point.id);
  pointElement.setAttribute("data-id", point.id); // 确保设置data-id属性
  pointElement.setAttribute("data-name", point.name);
  pointElement.setAttribute("data-show-type", point.showType);
  pointElement.setAttribute("data-point", "true");
  pointElement.setAttribute("data-x", point.x.toString());
  pointElement.setAttribute("data-y", point.y.toString());
  pointElement.setAttribute("data-color", color); // 添加color属性
  pointElement.setAttribute("data-size", size.toString()); // 添加size属性
  pointElement.setAttribute("style", "pointer-events: all !important; cursor: pointer;");

  // 添加监测指标
  if (point.data?.indicator) {
    pointElement.setAttribute("data-indicator-code", point.data.indicator);
  }

  // 添加所有data对象中的属性作为data-*属性
  if (point.data) {
    Object.entries(point.data).forEach(([key, value]) => {
      if (value !== undefined && value !== null && typeof value !== 'object') {
        pointElement.setAttribute(`data-${key}`, value.toString());
      }
    });
  }

  // 绑定事件
  pointElement.addEventListener('click', (e) => {
    e.stopPropagation();
    selectPoint(point);
  });

  pointElement.addEventListener('dblclick', (e) => {
    e.stopPropagation();
    editPoint(point);
  });

  pointElement.addEventListener('mousedown', (e) => {
    e.stopPropagation();
    const mouseEvent = e as MouseEvent;
    if (mouseEvent.ctrlKey || ctrlKeyPressed.value) {
      startDrag(mouseEvent, point);
    }
  });

  // 添加到容器
  container.appendChild(pointElement);
  console.log(`点位元素${point.id}已添加到SVG容器中，位置(${point.x}, ${point.y})`);
}

// 更新点位属性
const updatePointAttributes = (element: Element, point: Point) => {
  // 详细记录传入的点位数据
  console.log("传入updatePointAttributes的数据:", {
    id: point.id,
    type: point.type,
    showType: point.showType,
    data: point.data,
    原始size: point.data?.size,
    原始color: point.data?.color
  });

  // 确保从point.data中获取正确的size和color
  const size = point.data?.size || 8;
  const color = point.data?.color || getPointColorByType(point.showType);
  console.log("更新点位属性:", {
    id: point.id,
    旧位置: {
      x: element.tagName.toLowerCase() === 'circle' ? element.getAttribute("cx") :
        element.tagName.toLowerCase() === 'rect' ? parseFloat(element.getAttribute("x") || "0") + size : point.x,
      y: element.tagName.toLowerCase() === 'circle' ? element.getAttribute("cy") :
        element.tagName.toLowerCase() === 'rect' ? parseFloat(element.getAttribute("y") || "0") + size : point.y
    },
    新位置: { x: point.x, y: point.y },
    类型: point.showType,
    最终尺寸: size,
    最终颜色: color
  });

  try {
    // 更新通用属性
    element.setAttribute("data-name", point.name);
    element.setAttribute("data-x", point.x.toString());
    element.setAttribute("data-y", point.y.toString());
    // 确保保存颜色和大小的data属性
    element.setAttribute("data-color", color);
    element.setAttribute("data-size", size.toString());

    // 记录更新前的属性值
    const oldFill = element.getAttribute("fill");
    const oldSize = element.tagName.toLowerCase() === 'circle'
      ? element.getAttribute("r")
      : element.tagName.toLowerCase() === 'rect'
        ? element.getAttribute("width")
        : null;

    console.log("更新前的属性值:", {
      fill: oldFill,
      size: oldSize
    });

    // 更新监测指标
    if (point.data?.indicator) {
      element.setAttribute("data-indicator-code", point.data.indicator);
    } else {
      element.removeAttribute("data-indicator-code");
    }

    // 根据元素类型更新特有属性，包括位置、大小和颜色
    if (element.tagName.toLowerCase() === 'circle') {
      element.setAttribute("cx", point.x.toString()); // 更新X坐标
      element.setAttribute("cy", point.y.toString()); // 更新Y坐标
      element.setAttribute("r", size.toString());     // 更新大小
      element.setAttribute("fill", color);           // 更新颜色
      console.log(`更新圆形点位位置: (${point.x}, ${point.y}), 半径: ${size}, 颜色: ${color}`);
    } else if (element.tagName.toLowerCase() === 'rect') {
      element.setAttribute("x", (point.x - size).toString()); // 更新X坐标
      element.setAttribute("y", (point.y - size).toString()); // 更新Y坐标
      element.setAttribute("width", (size * 2).toString());    // 更新宽度
      element.setAttribute("height", (size * 2).toString());   // 更新高度
      element.setAttribute("fill", color);                     // 更新颜色
      console.log(`更新方形点位位置: 中心(${point.x}, ${point.y}), 左上角(${point.x - size}, ${point.y - size}), 尺寸: ${size * 2}x${size * 2}, 颜色: ${color}`);
    } else if (element.tagName.toLowerCase() === 'polygon') {
      const pointsCoord = `${point.x},${point.y - size} ${point.x + size},${point.y + size} ${point.x - size},${point.y + size}`;
      element.setAttribute("points", pointsCoord); // 更新所有坐标点
      element.setAttribute("fill", color);        // 更新颜色
      console.log(`更新三角形点位位置: 中心(${point.x}, ${point.y}), 顶点坐标: ${pointsCoord}, 尺寸: ${size}, 颜色: ${color}`);
    } else {
      console.warn(`未知元素类型: ${element.tagName}, 点位ID: ${point.id}`);
    }

    // 记录更新后的属性值，确认是否成功
    const newFill = element.getAttribute("fill");
    const newSize = element.tagName.toLowerCase() === 'circle'
      ? element.getAttribute("r")
      : element.tagName.toLowerCase() === 'rect'
        ? element.getAttribute("width")
        : null;

    console.log("更新后的属性值:", {
      fill: newFill,
      size: newSize,
      更新成功: oldFill !== newFill || oldSize !== newSize
    });

    console.log(`点位${point.id}属性更新成功，新位置: (${point.x}, ${point.y}), 颜色: ${color}, 尺寸: ${size}`);
  } catch (error) {
    console.error(`更新点位${point.id}属性失败:`, error);
  }
}

// 从SVG中删除点位
const removePointFromSVG = (point: Point) => {
  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) return;

  try {
    let elementsRemoved = 0;

    if (point.isText) {
      // 查找并移除所有与该文本点位相关的元素，优先使用data-id属性
      const textSelectors = [
        `g[data-text-id="${point.id}"]`,
        `g[data-id="${point.id}"]`,
        `text[data-id="${point.id}"]`
      ];

      // 使用组合选择器查找所有可能的元素
      const textElements = svgEl.querySelectorAll(textSelectors.join(', '));
      textElements.forEach(element => {
        if (element && element.parentNode) {
          element.parentNode.removeChild(element);
          elementsRemoved++;
        }
      });

      // 对于纯数字ID，还需要使用getElementById查找
      if (/^\d+$/.test(point.id.toString())) {
        const textGroupId = `text-group-${point.id}`;
        const textId = `text-${point.id}`;

        const textGroupElement = svgEl.getElementById(textGroupId);
        if (textGroupElement && textGroupElement.parentNode) {
          textGroupElement.parentNode.removeChild(textGroupElement);
          elementsRemoved++;
        }

        const textElement = svgEl.getElementById(textId);
        if (textElement && textElement.parentNode) {
          textElement.parentNode.removeChild(textElement);
          elementsRemoved++;
        }
      }
    } else {
      // 查找并移除所有与该图形点位相关的元素，优先使用data-id属性
      const pointSelectors = [
        `[data-id="${point.id}"]`,
        `circle[data-id="${point.id}"]`,
        `rect[data-id="${point.id}"]`,
        `polygon[data-id="${point.id}"]`
      ];

      // 使用组合选择器查找所有可能的元素
      const pointElements = svgEl.querySelectorAll(pointSelectors.join(', '));
      pointElements.forEach(element => {
        if (element && element.parentNode) {
          element.parentNode.removeChild(element);
          elementsRemoved++;
        }
      });

      // 对于纯数字ID，还需要使用getElementById查找
      if (/^\d+$/.test(point.id.toString())) {
        const element = svgEl.getElementById(point.id.toString());
        if (element && element.parentNode) {
          element.parentNode.removeChild(element);
          elementsRemoved++;
        }
      }
    }

    if (elementsRemoved > 0) {
      console.log(`删除点位: ${point.id}, 类型: ${point.isText ? '文本' : point.showType}, 移除了${elementsRemoved}个元素`);
    } else {
      console.log(`未找到点位: ${point.id}, 类型: ${point.isText ? '文本' : point.showType}`);
    }
  } catch (error) {
    console.error(`删除点位${point.id}失败:`, error);
  }
}

// 清空所有监测点
const clearAllPoints = () => {
  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) return;

  try {
    let totalRemoved = 0;

    // 查找所有可能的点位元素
    const selectors = [
      // 图形点位
      '[data-point="true"]',
      'circle[data-id]',
      'rect[data-id]',
      'polygon[data-id]',
      // 文本点位
      '[data-text-id]',
      'g[data-text-id]',
      '#text-group-[a-zA-Z0-9-]+',
      'text[data-id]',
      // 通用选择器
      '[data-id][data-type]'
    ];

    // 逐个处理选择器，确保不遗漏任何点位
    selectors.forEach(selector => {
      try {
        const elements = svgEl.querySelectorAll(selector);
        elements.forEach(element => {
          try {
            if (element && element.parentNode) {
              element.parentNode.removeChild(element);
              totalRemoved++;
            }
          } catch (elementError) {
            console.warn(`移除元素失败:`, elementError);
          }
        });
      } catch (selectorError) {
        console.warn(`选择器${selector}查询失败:`, selectorError);
      }
    });

    // 清理辅助线和临时元素
    clearDragGuideLines();

    console.log(`清空了所有监测点: 共移除${totalRemoved}个元素`);
  } catch (error) {
    console.error("清空监测点失败:", error);
  }
}

// 获取SVG的宽高
const getSvgDimensions = () => {
  const svgContainer = svgElement.value;
  if (!svgContainer) {
    return { width: 0, height: 0 };
  }

  // 获取SVG元素
  const svgElem = svgContainer.querySelector('svg');
  if (!svgElem) {
    return { width: 0, height: 0 };
  }

  // 尝试从SVG的width和height属性获取
  let width = svgElem.width?.baseVal?.value;
  let height = svgElem.height?.baseVal?.value;

  // 如果没有设置width和height属性，尝试从viewBox获取
  if (!width || !height) {
    const viewBox = svgElem.viewBox?.baseVal;
    if (viewBox) {
      width = viewBox.width;
      height = viewBox.height;
    }
  }

  // 如果仍然没有宽高信息，使用getBoundingClientRect获取实际渲染尺寸
  if (!width || !height) {
    const rect = svgElem.getBoundingClientRect();
    width = rect.width;
    height = rect.height;
  }

  console.log("获取到SVG尺寸:", width, "x", height);
  return { width: width || 0, height: height || 0 };
}

// 监听SVG内容变化
watch(() => props.svgContent, async (newSvgContent) => {
  console.log("SVG内容变化监听触发:", newSvgContent ? (newSvgContent.length > 50 ? newSvgContent.substring(0, 50) + "..." : newSvgContent) : "空内容");

  // 先清空所有现有点位 - 确保旧SVG的点位不会残留
  clearAllPoints();
  console.log("SVG内容变化，已清空所有点位");

  if (!svgElement.value) {
    console.error("SVG元素引用不存在，等待下一个tick");
    // 等待DOM更新
    await nextTick();

    if (!svgElement.value) {
      console.error("SVG元素引用在nextTick后仍然不存在");
      return;
    }
  }

  if (!newSvgContent) {
    console.warn("SVG内容为空，无法加载");
    // 即使内容为空，也清空当前显示内容
    if (svgElement.value) {
      svgElement.value.innerHTML = '';
    }
    return;
  }

  try {
    console.log("开始加载SVG内容");

    // 设置加载状态
    isLoading.value = true;

    // 清空现有内容
    svgElement.value.innerHTML = '';

    // 检查是否是URL格式的SVG
    if (isSvgUrl(newSvgContent)) {
      console.log("检测到URL格式的SVG:", newSvgContent);

      // 显示加载中的占位SVG
      const loadingSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
        <text x="20" y="300" text-anchor="start" font-size="24" fill="#999">
          正在加载SVG...
        </text>
        <g id="monitoring-points" class="monitoring-points-layer"></g>
      </svg>`;
      svgElement.value.innerHTML = loadingSvg;

      try {
        // 使用fetch获取SVG内容
        const svgContent = await fetchSvgContent(newSvgContent);

        // 确保加载后元素引用仍然存在
        if (!svgElement.value) {
          console.error("加载完成后SVG元素引用不存在");
          return;
        }

        // 检查获取的内容是否有效
        if (!svgContent || !svgContent.includes('<svg')) {
          console.error("获取的SVG内容无效:", svgContent);
          svgElement.value.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
            <text x="20" y="300" text-anchor="start" font-size="24" fill="#999">
              无法加载SVG，获取的内容无效
            </text>
            <g id="monitoring-points" class="monitoring-points-layer"></g>
          </svg>`;
          isLoading.value = false;
          return;
        }

        // 更新SVG内容
        svgElement.value.innerHTML = svgContent;
        console.log("URL格式的SVG加载完成");
      } catch (fetchError: any) {
        console.error("获取SVG内容失败:", fetchError);
        // 显示错误提示
        svgElement.value.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
          <text x="20" y="300" text-anchor="start" font-size="24" fill="#999">
            加载SVG失败: ${fetchError.message || '未知错误'}
          </text>
          <g id="monitoring-points" class="monitoring-points-layer"></g>
        </svg>`;
        isLoading.value = false;
        return;
      }
    } else {
      console.log("加载内联格式的SVG");
      // 直接设置SVG内容
      svgElement.value.innerHTML = sanitizedSvgContent.value;
    }

    // 确保SVG元素样式正确
    const svgEl = svgElement.value.querySelector('svg');
    if (svgEl) {
      svgEl.style.width = '100%';
      svgEl.style.height = '100%';
      svgEl.style.maxWidth = '100%';
      svgEl.style.maxHeight = '100%';
      svgEl.style.display = 'block';

      // 检查SVG中是否已存在监测点
      console.log("检查SVG中是否已存在监测点");
      const existingPoints = svgEl.querySelectorAll('[data-showType]');
      if (existingPoints.length > 0) {
        console.log(`SVG中已存在${existingPoints.length}个监测点，直接使用`);
      }

      // 确保SVG中有监测点容器层
      let pointsLayer = svgEl.querySelector('.monitoring-points-layer');
      if (!pointsLayer) {
        pointsLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        pointsLayer.setAttribute('id', 'monitoring-points');
        pointsLayer.setAttribute('class', 'monitoring-points-layer');
        svgEl.appendChild(pointsLayer);
        console.log("添加监测点容器层");
      }
    } else {
      console.warn("未找到SVG元素，无法应用样式");
    }

    // 在SVG内容加载完成后，调整容器大小
    nextTick(() => {
      console.log("SVG内容加载完成，准备调整大小");
      // 重置缩放比例为1.0，这将在adjustSvgContainerSize中被重新计算
      baseFitScale.value = 1;
      adjustSvgContainerSize();

      // 添加一个小延迟，让SVG完全渲染后再关闭加载状态，使过渡更平滑
      setTimeout(() => {
        isLoading.value = false;
        console.log("SVG加载状态关闭");

        // 强制更新SVG叠加层，确保点位正确渲染
        console.log("强制更新SVG叠加层");
        updateSvgOverlay();

        // 再次重置位置，确保SVG正确居中显示
        nextTick(() => {
          if (typeof resetPosition === 'function') {
            console.log("重置SVG位置");
            resetPosition();
          }
        });

        // 延迟一段时间后再次更新叠加层，确保事件绑定正确
        setTimeout(() => {
          console.log("延迟二次更新SVG叠加层，确保事件绑定正确");
          refreshEventBindings();
        }, 500);
      }, 300);
    });
  } catch (error: any) {
    console.error('加载SVG内容失败:', error);
    ElMessage.error('加载SVG内容失败');
    // 出错时也需要关闭加载状态
    isLoading.value = false;

    // 显示错误信息
    if (svgElement.value) {
      svgElement.value.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
        <text x="20" y="300" text-anchor="start" font-size="24" fill="#999">
          加载SVG失败: ${error.message || '未知错误'}
        </text>
        <g id="monitoring-points" class="monitoring-points-layer"></g>
      </svg>`;
    }
  }
}, { immediate: true });

// 监听点位数据变化
watch(() => props.points, (newPoints, oldPoints) => {
  nextTick(() => {
    console.log("点位数据变化，更新SVG叠加层", newPoints.length, "个点位");
    if (newPoints.length > 0) {
      console.log("最后一个点位:", newPoints[newPoints.length - 1]);
    }

    // 检查是否有位置变化的点位
    let hasPositionChanges = false;
    if (oldPoints) {
      for (let i = 0; i < newPoints.length; i++) {
        const point = newPoints[i];
        const oldPoint = oldPoints.find(p => p.id === point.id);
        if (oldPoint && (oldPoint.x !== point.x || oldPoint.y !== point.y)) {
          console.log(`点位${point.id}位置变化: (${oldPoint.x},${oldPoint.y}) -> (${point.x},${point.y})`);
          hasPositionChanges = true;
        }
      }
    }

    // 先更新DOM中点位的位置
    if (hasPositionChanges) {
      console.log("检测到点位位置变化，先更新DOM中的位置");
      newPoints.forEach(point => {
        updatePointPositionInDOM(point);
      });
    }

    // 然后更新整个SVG叠加层
    updateSvgOverlay();
  });
}, { deep: true, immediate: true });

// 监听缩放比例变化，应用实际的缩放
watch(() => props.zoom, () => {
  // 在nextTick中执行，确保DOM已更新
  nextTick(() => {
    // 缩放比例变化后，确保监测点的位置正确
    updateSvgOverlay();
  });
});

// 更新SVG叠加层
const updateSvgOverlay = () => {
  // 获取SVG元素
  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) {
    console.warn("未找到SVG元素，无法更新叠加层");
    return;
  }

  console.log("更新SVG叠加层，点位数量:", props.points.length);

  try {
    // 首先获取当前所有点位的最新位置信息
    const currentPositions = new Map();
    props.points.forEach(point => {
      const element = getElementFromSvg(svgEl, point.id);
      if (element) {
        let x = point.x;
        let y = point.y;

        // 从DOM元素中读取实际坐标
        const dataX = element.getAttribute('data-x');
        const dataY = element.getAttribute('data-y');

        if (dataX && dataY) {
          x = parseFloat(dataX);
          y = parseFloat(dataY);
          console.log(`从DOM元素获取到点位 ${point.id} 的最新位置: (${x}, ${y})`);
        }

        currentPositions.set(point.id, { x, y });
      }
    });

    // 清除所有现有的点位元素，避免重复
    const selectors = [
      '[data-point="true"]',
      'circle[data-id]',
      'rect[data-id]',
      'polygon[data-id]',
      '[data-text-id]',
      'g[data-text-id]',
      'text[data-id]',
      '[data-id][data-type]'
    ];

    // 查找并移除所有现有点位
    const existingElements = svgEl.querySelectorAll(selectors.join(', '));
    console.log(`清除${existingElements.length}个现有点位元素`);

    existingElements.forEach(element => {
      try {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      } catch (error) {
        console.warn("移除点位元素失败:", error);
      }
    });

    // 添加所有点位，使用保存的最新位置
    props.points.forEach(point => {
      // 使用最新位置（如果有的话）
      const currentPos = currentPositions.get(point.id);
      if (currentPos) {
        point.x = currentPos.x;
        point.y = currentPos.y;
      }

      console.log(`添加点位到SVG: ID=${point.id}, 类型=${point.showType}, 位置=(${point.x}, ${point.y})`);
      if (point.isText) {
        addTextElementToSVG(point, svgEl as unknown as SVGGElement);
      } else {
        addPointElementToSVG(point, svgEl as unknown as SVGGElement);
      }

      // 额外设置data-x和data-y属性，确保导出时使用最新坐标
      const element = getElementFromSvg(svgEl, point.id);
      if (element) {
        element.setAttribute('data-x', point.x.toString());
        element.setAttribute('data-y', point.y.toString());
      }
    });

    // 在更新完叠加层后，确保刷新所有点位的事件绑定
    // 这对于确保拖拽和双击事件正常工作非常重要
    refreshEventBindings();
  } catch (error) {
    console.error("更新SVG叠加层失败:", error);
  }
}

// 刷新事件绑定
const refreshEventBindings = () => {
  try {
    const svgEl = svgElement.value?.querySelector('svg');
    if (!svgEl) return;

    // 查找所有点位元素
    const pointElements = svgEl.querySelectorAll('[data-point="true"], circle[data-id], rect[data-id], polygon[data-id]');
    console.log(`刷新事件绑定：找到${pointElements.length}个点位元素`);

    // 为每个点位元素添加事件处理
    pointElements.forEach(element => {
      try {
        // 获取点位ID
        const pointId = element.getAttribute('data-id') || element.getAttribute('id');

        if (!pointId) {
          console.warn("无法识别点位ID:", element);
          return;
        }

        // 构建点位信息
        const pointType = element.getAttribute('data-show-type') ||
          (element.tagName.toLowerCase() === 'circle' ? 'circle' :
            element.tagName.toLowerCase() === 'rect' ? 'square' :
              element.tagName.toLowerCase() === 'polygon' ? 'triangle' : 'circle');

        let x = 0, y = 0;
        // 根据元素类型获取坐标
        if (element.tagName.toLowerCase() === 'circle') {
          x = parseFloat(element.getAttribute('cx') || '0');
          y = parseFloat(element.getAttribute('cy') || '0');
        } else if (element.tagName.toLowerCase() === 'rect') {
          const width = parseFloat(element.getAttribute('width') || '0');
          const height = parseFloat(element.getAttribute('height') || '0');
          x = parseFloat(element.getAttribute('x') || '0') + width / 2;
          y = parseFloat(element.getAttribute('y') || '0') + height / 2;
        } else if (element.tagName.toLowerCase() === 'polygon') {
          // 对于多边形，取第一个点的坐标作为中心点
          const pointsAttr = element.getAttribute('points') || '';
          const pointsArr = pointsAttr.split(' ');
          if (pointsArr.length > 0) {
            const firstPoint = pointsArr[0].split(',');
            if (firstPoint.length === 2) {
              x = parseFloat(firstPoint[0]);
              y = parseFloat(firstPoint[1]);
            }
          }
        }

        // 从元素中提取更多数据属性
        const dataX = element.getAttribute('data-x');
        const dataY = element.getAttribute('data-y');
        const dataColor = element.getAttribute('data-color');
        const dataSize = element.getAttribute('data-size');
        const dataName = element.getAttribute('data-name');
        const dataIndicator = element.getAttribute('data-indicator-code');

        // 如果有data-x和data-y属性，优先使用
        if (dataX) x = parseFloat(dataX);
        if (dataY) y = parseFloat(dataY);

        // 创建点位对象
        const point = {
          id: pointId,
          x,
          y,
          name: dataName || pointId,
          type: pointType,
          showType: pointType,
          isText: false,
          data: {
            indicator: dataIndicator || '',
            type: pointType,
            color: dataColor || element.getAttribute('fill') || getPointColorByType(pointType),
            size: dataSize ? parseFloat(dataSize) :
              element.tagName.toLowerCase() === 'circle' ? parseFloat(element.getAttribute('r') || '8') :
                element.tagName.toLowerCase() === 'rect' ? parseFloat(element.getAttribute('width') || '16') / 2 : 8
          }
        };

        console.log("绑定事件到点位:", {
          id: point.id,
          type: point.type,
          位置: { x: point.x, y: point.y },
          data: point.data
        });

        // 绑定事件
        bindElementEvents(element, point);
      } catch (error) {
        console.error("绑定点位事件失败:", error);
      }
    });

    // 查找所有文本点位
    const textElements = svgEl.querySelectorAll('text[data-id], g[data-text-id]');
    console.log(`刷新事件绑定：找到${textElements.length}个文本点位元素`);

    // 为每个文本点位添加事件处理
    textElements.forEach(element => {
      try {
        // 获取点位ID
        const textId = element.getAttribute('data-id') || element.getAttribute('data-text-id');

        if (!textId) {
          console.warn("无法识别文本点位ID:", element);
          return;
        }

        // 获取坐标
        let x = 0, y = 0;
        const transformAttr = element.getAttribute('transform');

        if (transformAttr && transformAttr.includes('translate')) {
          // 从transform属性解析坐标
          const match = transformAttr.match(/translate\(([^,]+),\s*([^)]+)\)/);
          if (match && match.length === 3) {
            x = parseFloat(match[1]);
            y = parseFloat(match[2]);
          }
        }

        // 从data-x和data-y属性获取坐标
        const dataX = element.getAttribute('data-x');
        const dataY = element.getAttribute('data-y');

        // 如果有data-x和data-y属性，优先使用
        if (dataX) x = parseFloat(dataX);
        if (dataY) y = parseFloat(dataY);

        // 获取文本内容
        let textContent = '';
        if (element.tagName.toLowerCase() === 'text') {
          textContent = element.textContent || '';
        } else {
          // 如果是g元素，查找其中的text元素
          const textChild = element.querySelector('text');
          if (textChild) {
            textContent = textChild.textContent || '';
          }
        }

        // 构建文本点位对象
        const textPoint = {
          id: textId,
          x,
          y,
          name: textContent || textId,
          type: 'text',
          showType: 'text',
          isText: true,
          data: {
            pointSize: element.getAttribute('font-size') || '14',
            textThickness: element.getAttribute('font-weight') || 'normal',
            color: element.getAttribute('fill') || '#000000',
            backgroundColor: element.getAttribute('data-background-color') || 'transparent',
            borderColor: element.getAttribute('data-border-color') || 'transparent',
            borderWidth: element.getAttribute('data-border-width') || '0',
            hasBackground: element.getAttribute('data-has-background') === 'true',
            hasBorder: element.getAttribute('data-has-border') === 'true',
            padding: element.getAttribute('data-padding') || '5'
          }
        };

        console.log("绑定事件到文本点位:", {
          id: textPoint.id,
          内容: textPoint.name,
          位置: { x: textPoint.x, y: textPoint.y },
          data: textPoint.data
        });

        // 绑定事件
        bindElementEvents(element, textPoint);
      } catch (error) {
        console.error("绑定文本点位事件失败:", error);
      }
    });

    console.log(`事件绑定刷新完成，处理了${pointElements.length}个图形点位和${textElements.length}个文本点位`);
  } catch (error) {
    console.error("刷新事件绑定失败:", error);
  }
}

// 为元素绑定事件
const bindElementEvents = (element: Element, point: any) => {
  // 点击事件 - 选中点位
  element.addEventListener('click', (e) => {
    e.stopPropagation();
    selectPoint(point);
  });

  // 双击事件 - 编辑点位
  element.addEventListener('dblclick', (e) => {
    e.stopPropagation();
    editPoint(point);
  });

  // 鼠标按下事件 - 拖拽点位（需要按住Ctrl键）
  element.addEventListener('mousedown', (e) => {
    e.stopPropagation();
    const mouseEvent = e as MouseEvent;
    if (mouseEvent.ctrlKey || ctrlKeyPressed.value) {
      startDrag(mouseEvent, point);
    }
  });

  // 确保元素可交互
  element.setAttribute("style", element.getAttribute("style") || "" + "; pointer-events: all !important; cursor: pointer;");
}

// 调整SVG容器大小
const adjustSvgContainerSize = () => {
  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) return;

  // 确保SVG元素保持原始尺寸但适应容器
  svgEl.style.width = '100%';
  svgEl.style.height = '100%';
  svgEl.style.maxWidth = '100%';
  svgEl.style.maxHeight = '100%';
  svgEl.style.display = 'block';
  svgEl.style.margin = 'auto'; // 使用margin:auto实现居中

  // 确保viewBox属性存在
  if (!svgEl.hasAttribute('viewBox')) {
    const width = svgEl.getAttribute('width') || '800';
    const height = svgEl.getAttribute('height') || '600';
    svgEl.setAttribute('viewBox', `0 0 ${width} ${height}`);
  }

  // 计算适合的初始缩放比例，使SVG完整显示
  calculateFitToViewScale(svgEl);

  // 确保SVG内容容器适应可视区域
  if (svgElement.value) {
    svgElement.value.style.width = '100%';
    svgElement.value.style.height = '100%';
    svgElement.value.style.maxWidth = '100%';
    svgElement.value.style.maxHeight = '100%';
    svgElement.value.style.display = 'flex';
    svgElement.value.style.justifyContent = 'center';
    svgElement.value.style.alignItems = 'center';
  }

  // 确保外部容器样式正确
  if (svgContainerRef.value) {
    svgContainerRef.value.style.display = 'flex';
    svgContainerRef.value.style.justifyContent = 'center';
    svgContainerRef.value.style.alignItems = 'center';
  }
}

// 鼠标滚轮缩放 - 简化版，直接处理缩放
const handleWheel = (e: WheelEvent) => {
  // 防止页面滚动
  e.preventDefault();
  e.stopPropagation();

  // 严格的缩放逻辑，确保正常工作
  // 缩放因子 - 值越大，每次滚动缩放越明显
  const scaleFactor = 0.2;

  // 当前缩放值
  let currentZoom = props.zoom;
  let newZoom = currentZoom;

  // 根据滚轮方向确定是放大还是缩小
  if (e.deltaY < 0) {
    // 向上滚动 - 放大
    newZoom = currentZoom * (1 + scaleFactor);
  } else {
    // 向下滚动 - 缩小
    newZoom = currentZoom / (1 + scaleFactor);
  }

  // 限制缩放范围
  newZoom = Math.max(0.1, Math.min(newZoom, 5.0));

  // 四舍五入到两位小数
  newZoom = Math.round(newZoom * 100) / 100;

  // 如果缩放值没有变化，不做任何事情
  if (newZoom === currentZoom) {
    return;
  }

  // 打印日志
  console.log("滚轮缩放:", currentZoom, "->", newZoom);

  // 更新缩放值
  emit('update:zoom', newZoom);

  // 显示缩放百分比
  const zoomPercentage = Math.round(newZoom * 100);
  console.log(`缩放比例: ${zoomPercentage}%`);
}

// 键盘控制
const handleKeyDown = (event: KeyboardEvent) => {
  // 记录Ctrl键状态
  if (event.key === 'Control') {
    ctrlKeyPressed.value = true;
    console.log('Ctrl键按下，ctrlKeyPressed =', ctrlKeyPressed.value);

    // 添加鼠标样式提示
    document.body.style.cursor = 'move';

    // 在SVG容器上添加提示类
    if (svgContainerRef.value) {
      svgContainerRef.value.classList.add('ctrl-pressed');
    }
  }

  // 键盘缩放控制
  if (event.ctrlKey || event.metaKey) {
    // Ctrl/Command + +/-/0 操作缩放
    if (event.key === '+' || event.key === '=') {
      // 放大
      event.preventDefault();
      emit('update:zoom', Math.min(props.zoom + 0.1, 4));
    } else if (event.key === '-') {
      // 缩小
      event.preventDefault();
      emit('update:zoom', Math.max(props.zoom - 0.1, 0.2));
    } else if (event.key === '0') {
      // 重置缩放
      event.preventDefault();
      emit('update:zoom', 1);
      // 重置平移
      resetPosition();
    }
  }
};

// 键盘释放
const handleKeyUp = (event: KeyboardEvent) => {
  if (event.key === 'Control') {
    ctrlKeyPressed.value = false;
    console.log('Ctrl键释放，ctrlKeyPressed =', ctrlKeyPressed.value);

    // 恢复默认鼠标样式
    document.body.style.cursor = 'default';

    // 移除SVG容器上的提示类
    if (svgContainerRef.value) {
      svgContainerRef.value.classList.remove('ctrl-pressed');
    }

    // 如果正在拖拽，则停止拖拽
    if (draggingPoint.value) {
      stopDrag();
    }
  }
};

// 导出SVG（包含监测点属性）
const exportSvgWithPoints = async (externalSvgContent?: string) => {
  console.log('导出SVG内容，参数类型:', typeof externalSvgContent);

  // 如果提供了外部SVG URL，首先尝试获取内容
  if (externalSvgContent && (externalSvgContent.startsWith('http') || externalSvgContent.startsWith('/'))) {
    try {
      console.log('检测到SVG URL，尝试获取内容:', externalSvgContent);
      const response = await fetch(externalSvgContent);
      if (response.ok) {
        const content = await response.text();
        console.log('成功从URL获取SVG内容，长度:', content.length);
        // 递归调用自身，但这次传入实际内容而非URL
        return exportSvgWithPoints(content);
      } else {
        console.error('从URL获取SVG内容失败，状态码:', response.status);
      }
    } catch (error) {
      console.error('获取SVG URL内容失败:', error);
    }
  }

  // 如果提供了外部SVG内容，使用它；否则使用props中的内容
  const effectiveSvgContent = externalSvgContent || props.svgContent;

  if (!effectiveSvgContent) {
    console.warn('没有有效的SVG内容，返回空SVG');
    return '<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n<svg xmlns="http://www.w3.org/2000/svg"></svg>';
  }

  // 如果提供了外部SVG内容，需要创建一个临时元素来解析它
  let svgEl;
  if (externalSvgContent) {
    const tempContainer = document.createElement('div');
    tempContainer.innerHTML = effectiveSvgContent;
    svgEl = tempContainer.querySelector('svg');

    // 如果无法解析外部SVG内容，直接返回原始内容
    if (!svgEl) {
      console.warn('无法解析外部SVG内容，返回原始内容');
      return effectiveSvgContent;
    }
  } else {
    // 获取当前SVG元素
    svgEl = svgElement.value?.querySelector('svg');
    if (!svgEl) {
      console.warn('当前DOM中没有SVG元素，返回处理后的内容');
      return sanitizedSvgContent.value;
    }
  }

  try {
    // 创建克隆以避免修改当前显示的SVG
    const clone = svgEl.cloneNode(true) as SVGSVGElement;

    // 移除克隆中可能存在的旧监测点容器
    const oldPointsGroup = clone.querySelector('#monitoring-points');
    if (oldPointsGroup) {
      clone.removeChild(oldPointsGroup);
    }

    // 移除所有现有的点位元素
    const selectors = [
      // 图形点位
      '[data-point="true"]',
      'circle[data-id]',
      'rect[data-id]',
      'polygon[data-id]',
      // 文本点位
      '[data-text-id]',
      'g[data-text-id]',
      'text[data-id]',
      // 通用选择器
      '[data-id][data-type]',
      // 拖拽辅助线等临时元素
      '#drag-guide-h',
      '#drag-guide-v',
      '#drag-original-position',
      '#drag-distance-label',
      '#x-snap-line',
      '#y-snap-line'
    ];

    // 合并所有选择器
    const combinedSelector = selectors.join(', ');
    const existingPoints = clone.querySelectorAll(combinedSelector);

    console.log(`导出SVG，移除${existingPoints.length}个现有点位元素`);

    existingPoints.forEach(element => {
      try {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      } catch (error) {
        console.warn("移除点位元素失败:", error);
      }
    });

    console.log(`导出SVG，准备序列化${props.points.length}个点位`);

    // 直接将点位添加到SVG根元素
    props.points.forEach((point) => {
      // 确保使用最新的点位位置 - 从DOM中获取
      const currentElement = svgEl.querySelector(`[data-id="${point.id}"]`);
      let x = point.x;
      let y = point.y;

      if (currentElement) {
        // 从当前DOM元素中读取实际坐标
        const dataX = currentElement.getAttribute('data-x');
        const dataY = currentElement.getAttribute('data-y');
        if (dataX && dataY) {
          x = parseFloat(dataX);
          y = parseFloat(dataY);
          console.log(`导出SVG: 使用DOM中的点位位置 ${point.id}: (${x}, ${y})`);
        }
      }

      if (point.isText) {
        // 创建一个分组元素以包含文本和背景/边框矩形
        const textGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
        textGroup.setAttribute("id", `text-group-${point.id}`);
        textGroup.setAttribute("data-id", point.id);
        textGroup.setAttribute("data-text-id", point.id);
        textGroup.setAttribute("data-type", "text");
        textGroup.setAttribute("data-show-type", "text");
        textGroup.setAttribute("transform", `translate(${x}, ${y})`);
        textGroup.setAttribute("style", "pointer-events: all !important; cursor: pointer;");

        // 提取文本样式属性
        const fontSize = point.data?.pointSize || 13;
        const fontWeight = point.data?.textThickness || "normal";
        const color = point.data?.color || "#000000";

        // 处理背景和边框属性
        const hasBackground = point.data?.hasBackground === true;
        const hasBorder = point.data?.hasBorder === true;
        const backgroundColor = hasBackground ? (point.data?.backgroundColor || 'rgba(255, 255, 255, 0.7)') : 'transparent';
        const borderColor = hasBorder ? (point.data?.borderColor || '#000000') : 'transparent';
        const borderWidth = hasBorder ? (point.data?.borderWidth || 1) : 0;
        const padding = (hasBackground || hasBorder) ? (point.data?.padding || 8) : 5;
        const safetyMargin = 4;

        // 保存边框和背景数据到分组元素
        textGroup.setAttribute("data-has-background", hasBackground ? "true" : "false");
        textGroup.setAttribute("data-has-border", hasBorder ? "true" : "false");
        textGroup.setAttribute("data-background-color", backgroundColor);
        textGroup.setAttribute("data-border-color", borderColor);
        textGroup.setAttribute("data-border-width", borderWidth.toString());
        textGroup.setAttribute("data-padding", padding.toString());

        // 保存所有额外数据到分组元素
        if (point.data) {
          Object.entries(point.data).forEach(([key, value]) => {
            if (value !== undefined && value !== null && typeof value !== 'object') {
              textGroup.setAttribute(`data-${key}`, value.toString());
            }
          });
        }

        // 保存指标编码
        if (point.data?.indicator) {
          textGroup.setAttribute("data-indicator-code", point.data.indicator);
        }

        // 创建文本元素
        const textElement = document.createElementNS("http://www.w3.org/2000/svg", "text");
        textElement.setAttribute("x", "0");
        textElement.setAttribute("y", "0");
        textElement.setAttribute("id", `text-${point.id}`);
        textElement.setAttribute("data-id", point.id);
        textElement.setAttribute("data-type", "text");
        textElement.setAttribute("data-show-type", "text");
        textElement.setAttribute("font-size", `${fontSize}px`);
        textElement.setAttribute("font-weight", fontWeight);
        textElement.setAttribute("fill", color);
        textElement.setAttribute("text-anchor", "start");
        textElement.setAttribute("dominant-baseline", "central");
        textElement.setAttribute("style", "pointer-events: all !important; cursor: pointer;");

        // 设置文本内容
        textElement.textContent = point.name || "文本标签";

        // 临时添加文本元素到文档以计算尺寸
        clone.appendChild(textElement);
        let textWidth = 100;  // 默认宽度
        let textHeight = 20;  // 默认高度

        try {
          // 计算文本实际尺寸
          const textBBox = textElement.getBBox();
          textWidth = textBBox.width;
          textHeight = textBBox.height;
          // 移除临时元素
          clone.removeChild(textElement);
        } catch (error) {
          console.warn("无法计算文本尺寸，使用默认值:", error);
          if (textElement.parentNode) {
            textElement.parentNode.removeChild(textElement);
          }
        }

        // 创建背景矩形
        const rectWidth = textWidth + (padding * 2) + safetyMargin;
        const rectHeight = textHeight + (padding * 2) + safetyMargin;

        const backgroundRect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
        backgroundRect.setAttribute("x", `${-rectWidth / 2}`);
        backgroundRect.setAttribute("y", `${-rectHeight / 2}`);
        backgroundRect.setAttribute("width", `${rectWidth}`);
        backgroundRect.setAttribute("height", `${rectHeight}`);
        backgroundRect.setAttribute("rx", "3");  // 圆角
        backgroundRect.setAttribute("ry", "3");  // 圆角
        backgroundRect.setAttribute("fill", backgroundColor);
        backgroundRect.setAttribute("stroke", borderColor);
        backgroundRect.setAttribute("stroke-width", `${borderWidth}`);
        backgroundRect.setAttribute("data-is-background", "true");
        backgroundRect.setAttribute("data-id", point.id);
        backgroundRect.setAttribute("style", "pointer-events: all !important; cursor: move;");

        // 先添加背景矩形，再添加文本
        textGroup.appendChild(backgroundRect);
        textGroup.appendChild(textElement);

        // 将完整的分组添加到SVG
        clone.appendChild(textGroup);

        console.log(`已添加文本点位 ${point.id} 到SVG，位置(${x}, ${y})，样式: pointSize=${fontSize}, textThickness=${fontWeight}, color=${color}, 背景=${hasBackground}, 边框=${hasBorder}`);
      } else {
        // 图形点位处理
        let pointElement;
        const size = point.data?.size || 8;
        const color = point.data?.color || getPointColorByType(point.showType);
        const showType = point.showType || 'circle';

        // 根据不同类型创建不同的图形元素
        switch (showType) {
          case 'circle':
            pointElement = document.createElementNS("http://www.w3.org/2000/svg", "circle");
            pointElement.setAttribute("cx", x.toString());
            pointElement.setAttribute("cy", y.toString());
            pointElement.setAttribute("r", size.toString());
            pointElement.setAttribute("fill", color);
            break;
          case 'square':
            pointElement = document.createElementNS("http://www.w3.org/2000/svg", "rect");
            pointElement.setAttribute("x", (x - size).toString());
            pointElement.setAttribute("y", (y - size).toString());
            pointElement.setAttribute("width", (size * 2).toString());
            pointElement.setAttribute("height", (size * 2).toString());
            pointElement.setAttribute("fill", color);
            break;
          case 'triangle':
            pointElement = document.createElementNS("http://www.w3.org/2000/svg", "polygon");
            const pointsCoord = `${x},${y - size} ${x + size},${y + size} ${x - size},${y + size}`;
            pointElement.setAttribute("points", pointsCoord);
            pointElement.setAttribute("fill", color);
            break;
          default:
            pointElement = document.createElementNS("http://www.w3.org/2000/svg", "circle");
            pointElement.setAttribute("cx", x.toString());
            pointElement.setAttribute("cy", y.toString());
            pointElement.setAttribute("r", size.toString());
            pointElement.setAttribute("fill", color);
            break;
        }

        // 通用属性
        pointElement.setAttribute("id", point.id);
        pointElement.setAttribute("data-id", point.id); // 确保设置data-id属性
        pointElement.setAttribute("data-name", point.name || "");
        pointElement.setAttribute("data-type", point.type || showType);
        pointElement.setAttribute("data-show-type", showType);
        pointElement.setAttribute("data-point", "true");
        pointElement.setAttribute("data-size", size.toString());

        // 存储颜色到data-color属性
        if (color) {
          pointElement.setAttribute("data-color", color);
        }

        // 监测指标
        if (point.data?.indicator) {
          pointElement.setAttribute("data-indicator-code", point.data.indicator);
        }

        // 保存所有额外数据属性
        if (point.data) {
          Object.entries(point.data).forEach(([key, value]) => {
            if (value !== undefined && value !== null && key !== 'size' && key !== 'color') {
              pointElement.setAttribute(`data-${key}`, value.toString());
            }
          });
        }

        // 直接添加到SVG根元素
        clone.appendChild(pointElement);

        console.log(`已添加${showType}点位 ${point.id} 到SVG，位置(${point.x}, ${point.y})，尺寸: ${size}, 颜色: ${color}`);
      }
    });

    // 确保包含XML声明
    const serializer = new XMLSerializer();
    let finalSvg = serializer.serializeToString(clone);

    if (!finalSvg.trim().startsWith('<?xml')) {
      finalSvg = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n${finalSvg}`;
    } else {
      // 替换现有的XML声明以确保包含standalone属性
      finalSvg = finalSvg.replace(/^<\?xml[^>]*\?>/, '<?xml version="1.0" encoding="UTF-8" standalone="no"?>');
    }

    console.log(`SVG导出完成，大小: ${finalSvg.length}字节，包含${props.points.length}个点位`);
    return finalSvg;
  } catch (error) {
    console.error("导出SVG失败:", error);
    return sanitizedSvgContent.value;
  }
}

// 组件初始化
onMounted(() => {
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown);
  document.addEventListener('keyup', handleKeyUp);

  // 确保滚轮事件能正确工作，加一个特殊的监听器
  if (svgContainerRef.value) {
    svgContainerRef.value.addEventListener('wheel', handleWheel, { passive: false });
    console.log('已添加滚轮事件监听器');
  }

  // 添加窗口大小变化监听
  window.addEventListener('resize', adjustSvgContainerSize);

  // 初始调整大小
  nextTick(() => {
    adjustSvgContainerSize();

    // 延迟初始化事件绑定
    setTimeout(() => {
      refreshEventBindings();
    }, 1000);
  });

  // 显示使用提示
  nextTick(() => {
    console.log('SVG缩放控制提示:');
    console.log('- 鼠标滚轮: 放大/缩小');
    console.log('- Ctrl/Command + +: 放大');
    console.log('- Ctrl/Command + -: 缩小');
    console.log('- Ctrl/Command + 0: 重置缩放和位置');
  });
});

// 组件销毁前清理
onBeforeUnmount(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown);
  document.removeEventListener('keyup', handleKeyUp);

  // 移除滚轮事件监听
  if (svgContainerRef.value) {
    svgContainerRef.value.removeEventListener('wheel', handleWheel);
  }

  window.removeEventListener('resize', adjustSvgContainerSize);
});

// 暴露给父组件的方法
defineExpose({
  resetPosition,
  addOrUpdatePointInSVG,
  removePointFromSVG,
  exportSvgWithPoints,
  clearAllPoints,
  getSvgDimensions
});
</script>

<style scoped>
.svg-container {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background-color: #f0f2f5;
  user-select: none;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.1s ease;
  /* 添加平滑过渡效果 */
}

/* 加载状态覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(240, 242, 245, 0.8);
  z-index: 10;
  backdrop-filter: blur(2px);
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* 加载旋转图标 */
.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e6e6e6;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.svg-container.adding-element {
  cursor: crosshair;
}

.svg-outer-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  will-change: transform;
  /* 提示浏览器优化变换性能 */
}

.svg-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  transition: transform 0.3s ease, opacity 0.3s ease;
  /* 添加缩放和透明度的平滑过渡 */
  will-change: transform, opacity;
  /* 提示浏览器优化变换性能 */
}

.svg-content {
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
}

.svg-content :deep(svg) {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  display: block;
  transform-origin: center center;
  /* 确保以中心为基准点缩放 */
  opacity: 1;
  transition: opacity 0.3s ease;
}

:deep(.monitoring-points-layer) {
  pointer-events: all !important;
  z-index: 1000 !important;
  cursor: pointer;
}

:deep(text),
:deep(circle),
:deep(rect),
:deep(polygon) {
  cursor: pointer;
  pointer-events: all !important;
}

:deep(g[data-text-id]),
:deep(g[data-id]) {
  pointer-events: all !important;
  cursor: pointer;
}

/* Ctrl按下时的样式 */
.svg-container.ctrl-pressed :deep(circle),
.svg-container.ctrl-pressed :deep(rect),
.svg-container.ctrl-pressed :deep(polygon),
.svg-container.ctrl-pressed :deep(g[data-text-id]),
.svg-container.ctrl-pressed :deep(text[data-id]) {
  cursor: move !important;
  filter: brightness(1.1) drop-shadow(0px 0px 3px rgba(24, 144, 255, 0.8));
}

/* 拖拽时的样式 */
.svg-container.dragging :deep(.dragging-element),
.svg-container.dragging :deep(g[data-dragging="true"]),
.svg-container.dragging :deep(g[data-dragging="true"] *) {
  cursor: grabbing !important;
  filter: drop-shadow(0px 0px 4px rgba(255, 87, 34, 0.8));
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    filter: drop-shadow(0px 0px 4px rgba(255, 87, 34, 0.6));
  }

  50% {
    filter: drop-shadow(0px 0px 8px rgba(255, 87, 34, 0.9));
  }

  100% {
    filter: drop-shadow(0px 0px 4px rgba(255, 87, 34, 0.6));
  }
}

/* 可拖拽元素的悬停样式 */
:deep(circle[data-id]:hover),
:deep(rect[data-id]:hover),
:deep(polygon[data-id]:hover),
:deep(g[data-text-id]:hover),
:deep(text[data-id]:hover),
:deep(rect[data-is-background="true"]:hover) {
  filter: brightness(1.2);
  cursor: default;
  transition: filter 0.2s ease;
}

/* Ctrl键按下时的悬停样式 */
.svg-container.ctrl-pressed :deep(circle[data-id]:hover),
.svg-container.ctrl-pressed :deep(rect[data-id]:hover),
.svg-container.ctrl-pressed :deep(polygon[data-id]:hover),
.svg-container.ctrl-pressed :deep(g[data-text-id]:hover),
.svg-container.ctrl-pressed :deep(text[data-id]:hover),
.svg-container.ctrl-pressed :deep(rect[data-is-background="true"]:hover) {
  cursor: grab !important;
}

/* 拖拽辅助线样式 */
:deep(#drag-guide-h),
:deep(#drag-guide-v) {
  opacity: 0.6;
  animation: dash 15s linear infinite;
}

:deep(#drag-distance-label) {
  font-weight: bold;
  text-shadow: 0 0 3px white;
}

@keyframes dash {
  to {
    stroke-dashoffset: 100;
  }
}

/* 缩放提示样式 */
.zoom-tip {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.3s;
  z-index: 100;
  pointer-events: none;
}

.zoom-tip:hover {
  opacity: 1;
}

.zoom-percentage {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}

.zoom-help {
  font-size: 10px;
  opacity: 0.8;
  margin-top: 2px;
}

/* 拖拽提示样式 */
:deep(.hover-effect) {
  filter: brightness(1.3) drop-shadow(0px 0px 3px rgba(24, 144, 255, 0.8)) !important;
  transition: filter 0.2s ease;
}

.drag-tooltip {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 9999;
  transform: translateX(-50%);
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
</style>