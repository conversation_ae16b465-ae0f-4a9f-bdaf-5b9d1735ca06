<template>
  <div class="value-display" :style="{
    left: `${point.displayPosition?.x || point.position.x + 50}px`,
    top: `${point.displayPosition?.y || point.position.y}px`
  }">
    <!-- 参考框线 - 仅在编辑模式下显示 -->
    <div v-if="isHovered || isDragging" class="reference-lines">
      <!-- 图标到值框的连接线 -->
      <div class="reference-line icon-to-value" :style="{
        left: `${-(point.displayPosition?.x || point.position.x + 50) + point.position.x}px`,
        top: `${-(point.displayPosition?.y || point.position.y) + point.position.y}px`,
      }"></div>
      
      <!-- 值框边界 - 突出显示 -->
      <div class="reference-box"></div>
      
      <!-- 标签参考线 -->
      <div v-if="point.layoutTemplate !== 'valueOnly'" class="reference-line value-to-label" :style="{
        left: `calc(50% + ${point.labelPosition?.x || 0}px)`,
        top: `calc(50% + ${point.labelPosition?.y || -10}px)`
      }"></div>
      
      <!-- 数值参考线 -->
      <div class="reference-line value-to-number" :style="{
        left: `calc(50% + ${point.numberPosition?.x || 0}px)`,
        top: `calc(50% + ${point.numberPosition?.y || 10}px)`
      }"></div>
      
      <!-- 坐标信息 -->
      <div class="coordinate-info">
        <div class="absolute-coord">值框: ({{ Math.round(point.displayPosition?.x || point.position.x + 50) }}, {{ Math.round(point.displayPosition?.y || point.position.y) }})</div>
        <div v-if="point.layoutTemplate !== 'valueOnly'" class="relative-coord">标签: ({{ point.labelPosition?.x || 0 }}, {{ point.labelPosition?.y || -10 }})</div>
        <div class="relative-coord">数值: ({{ point.numberPosition?.x || 0 }}, {{ point.numberPosition?.y || 10 }})</div>
      </div>
    </div>
    
    <div class="value-box" :style="{
      backgroundColor: point.displayBgColor || 'rgba(255, 255, 255, 0.9)',
      borderColor: point.displayBgColor ? 'transparent' : 'rgba(0, 0, 0, 0.1)'
    }" @mousedown.ctrl.prevent.stop="$emit('drag', $event, point, 'value')">
      <div v-if="point.layoutTemplate !== 'valueOnly'" class="value-label" :style="{
        color: point.displayColor || '#333',
        left: `calc(50% + ${point.labelPosition?.x || 0}px)`,
        top: `calc(50% + ${point.labelPosition?.y || -10}px)`
      }" @mousedown.ctrl.prevent.stop="$emit('drag', $event, point, 'label')">
        {{ point.displayName || point.name }}
      </div>
      <div class="value-number" :style="{
        color: point.displayColor || '#333',
        left: `calc(50% + ${point.numberPosition?.x || 0}px)`,
        top: `calc(50% + ${point.numberPosition?.y || 10}px)`
      }" @mousedown.ctrl.prevent.stop="$emit('drag', $event, point, 'number')">
        {{ point.value }} {{ point.unit }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue';

// 定义属性
const props = defineProps({
  point: {
    type: Object,
    required: true
  },
  isHovered: {
    type: Boolean,
    default: false
  },
  isDragging: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['drag']);
</script>

<style scoped lang="scss">
.value-display {
  position: absolute;
  transform: translate(-50%, -50%);
  pointer-events: auto;
  z-index: 5;
}

.value-box {
  position: relative;
  min-width: 80px;
  min-height: 40px;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.value-label {
  position: absolute;
  font-size: 12px;
  white-space: nowrap;
  padding: 2px 4px;
  transform-origin: center;
  transition: transform 0.2s ease;
}

.value-number {
  position: absolute;
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  padding: 2px 4px;
  transform-origin: center;
  transition: transform 0.2s ease;
}

.reference-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 50;
}

.reference-line {
  position: absolute;
  background-color: rgba(255, 0, 0, 0.5);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 51;
}

.icon-to-value {
  background-color: rgba(64, 158, 255, 0.5);
  width: 2px;
  height: 2px;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
}

.value-to-label, .value-to-number {
  background-color: rgba(103, 194, 58, 0.5);
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.3);
}

.reference-box {
  position: absolute;
  width: calc(100% + 6px);
  height: calc(100% + 6px);
  border: 2px dashed rgba(64, 158, 255, 0.7);
  left: -3px;
  top: -3px;
  border-radius: 6px;
  pointer-events: none;
}

.coordinate-info {
  position: absolute;
  top: -45px;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 100;
}

.absolute-coord {
  color: #67C23A;
}

.relative-coord {
  color: #E6A23C;
}
</style> 