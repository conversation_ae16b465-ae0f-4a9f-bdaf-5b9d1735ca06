<template>
  <div class="warehouse-management-container" style="width: 100%; height: calc(100vh - 10.625rem); padding: 0;">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold text-1.125rem">基础设置</span>
          <div class="flex gap-0.5rem">
            <el-button type="primary" @click="handleBatchImport">
              <el-icon>
                <Upload />
              </el-icon>
              批量导入
            </el-button>
            <el-button @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>
              导出配置
            </el-button>
          </div>
        </div>
      </template>

      <div class="w-full h-[calc(100%-4rem)] flex flex-col">
        <!-- Tab 切换 -->
        <el-tabs v-model="activeTab" class="warehouse-tabs" @tab-change="handleTabChange">
          <!-- 仓库管理 -->
          <el-tab-pane label="仓库管理" name="warehouse">
            <div class="tab-content">
              <!-- 搜索区域 -->
              <div class="search-area mb-1rem">
                <el-form :model="warehouseSearchForm" inline>
                  <el-form-item label="仓库编号">
                    <el-input v-model="warehouseSearchForm.warehouseCode" placeholder="请输入仓库编号" style="width: 12.5rem"
                      clearable />
                  </el-form-item>
                  <el-form-item label="仓库名称">
                    <el-input v-model="warehouseSearchForm.warehouseName" placeholder="请输入仓库名称" style="width: 12.5rem"
                      clearable />
                  </el-form-item>
                  <el-form-item label="仓库类型">
                    <el-select v-model="warehouseSearchForm.type" placeholder="请选择类型" style="width: 12.5rem" clearable>
                      <el-option label="主仓库" value="main" />
                      <el-option label="分仓库" value="branch" />
                      <el-option label="临时仓库" value="temp" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="状态">
                    <el-select v-model="warehouseSearchForm.status" placeholder="请选择状态" style="width: 12.5rem"
                      clearable>
                      <el-option label="启用" value="1" />
                      <el-option label="停用" value="0" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleWarehouseSearch">
                      <el-icon>
                        <Search />
                      </el-icon>
                      查询
                    </el-button>
                    <el-button @click="handleWarehouseReset">重置</el-button>
                    <el-button type="primary" @click="handleWarehouseAdd">
                      <el-icon>
                        <Plus />
                      </el-icon>
                      新增仓库
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 表格区域 -->
              <div class="table-area flex-1">
                <el-table :data="warehouseTableData" border height="100%" v-loading="warehouseLoading"
                  style="width: 100%">
                  <el-table-column type="index" label="序号" width="3.75rem" align="center" />
                  <el-table-column prop="warehouseCode" label="仓库编号" min-width="7.5rem" align="center" />
                  <el-table-column prop="warehouseName" label="仓库名称" min-width="7.5rem" align="center" />
                  <el-table-column prop="type" label="仓库类型" width="6.25rem" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getWarehouseTypeTag(row.type)">{{ getWarehouseTypeText(row.type) }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="address" label="仓库地址" min-width="12.5rem" align="center"
                    show-overflow-tooltip />
                  <el-table-column prop="managers" label="库管员" min-width="7.5rem" align="center">
                    <template #default="{ row }">
                      <el-tag v-for="manager in row.managers" :key="manager" size="small" class="mr-0.25rem">
                        {{ manager }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="5rem" align="center">
                    <template #default="{ row }">
                      <el-switch v-model="row.status" :active-value="1" :inactive-value="0"
                        @change="handleWarehouseStatusChange(row)" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间" width="10rem" align="center" />
                  <el-table-column label="操作" width="12.5rem" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button type="primary" link @click="handleWarehouseEdit(row)">编辑</el-button>
                      <el-button type="danger" link @click="handleWarehouseDelete(row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 分页 -->
              <div class="pagination-area mt-1rem">
                <el-pagination v-model:current-page="warehousePagination.currentPage"
                  v-model:page-size="warehousePagination.pageSize" :total="warehousePagination.total"
                  layout="total, sizes, prev, pager, next" @size-change="handleWarehouseSizeChange"
                  @current-change="handleWarehouseCurrentChange" />
              </div>
            </div>
          </el-tab-pane>

          <!-- 用途管理 -->
          <el-tab-pane label="用途管理" name="purpose">
            <div class="tab-content">
              <!-- 搜索区域 -->
              <div class="search-area mb-1rem">
                <el-form :model="purposeSearchForm" inline>
                  <el-form-item label="用途名称">
                    <el-input v-model="purposeSearchForm.purposeName" placeholder="请输入用途名称" style="width: 12.5rem"
                      clearable />
                  </el-form-item>
                  <el-form-item label="关联仓库">
                    <el-select v-model="purposeSearchForm.warehouseId" placeholder="请选择仓库" style="width: 12.5rem"
                      clearable>
                      <el-option v-for="warehouse in warehouseOptions" :key="warehouse.id"
                        :label="warehouse.warehouseName" :value="warehouse.id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handlePurposeSearch">
                      <el-icon>
                        <Search />
                      </el-icon>
                      查询
                    </el-button>
                    <el-button @click="handlePurposeReset">重置</el-button>
                    <el-button type="primary" @click="handlePurposeAdd">
                      <el-icon>
                        <Plus />
                      </el-icon>
                      新增用途
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 表格区域 -->
              <div class="table-area flex-1">
                <el-table :data="purposeTableData" border height="100%" v-loading="purposeLoading" style="width: 100%">
                  <el-table-column type="index" label="序号" width="3.75rem" align="center" />
                  <el-table-column prop="purposeCode" label="用途编码" min-width="7.5rem" align="center" />
                  <el-table-column prop="purposeName" label="用途名称" min-width="7.5rem" align="center" />
                  <el-table-column prop="warehouseName" label="关联仓库" min-width="7.5rem" align="center" />
                  <el-table-column prop="goodsTypes" label="商品类型" min-width="10rem" align="center">
                    <template #default="{ row }">
                      <el-tag v-for="type in row.goodsTypes" :key="type" size="small" class="mr-0.25rem">
                        {{ type }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="allowNoStock" label="无库存领用" width="7.5rem" align="center">
                    <template #default="{ row }">
                      <el-tag :type="row.allowNoStock ? 'success' : 'danger'">
                        {{ row.allowNoStock ? '允许' : '不允许' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="描述" min-width="10rem" align="center"
                    show-overflow-tooltip />
                  <el-table-column label="操作" width="12.5rem" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button type="primary" link @click="handlePurposeEdit(row)">编辑</el-button>
                      <el-button type="danger" link @click="handlePurposeDelete(row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 分页 -->
              <div class="pagination-area mt-1rem">
                <el-pagination v-model:current-page="purposePagination.currentPage"
                  v-model:page-size="purposePagination.pageSize" :total="purposePagination.total"
                  layout="total, sizes, prev, pager, next" @size-change="handlePurposeSizeChange"
                  @current-change="handlePurposeCurrentChange" />
              </div>
            </div>
          </el-tab-pane>

          <!-- 商品类别管理 -->
          <el-tab-pane label="商品类别" name="category">
            <div class="tab-content">
              <!-- 搜索区域 -->
              <div class="search-area mb-1rem">
                <el-form :model="categorySearchForm" inline>
                  <el-form-item label="类别名称">
                    <el-input v-model="categorySearchForm.categoryName" placeholder="请输入类别名称" style="width: 12.5rem"
                      clearable />
                  </el-form-item>
                  <el-form-item label="父级类别">
                    <el-select v-model="categorySearchForm.parentId" placeholder="请选择父级类别" style="width: 12.5rem"
                      clearable>
                      <el-option label="无" value="" />
                      <el-option v-for="category in categoryOptions" :key="category.id" :label="category.categoryName"
                        :value="category.id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleCategorySearch">
                      <el-icon>
                        <Search />
                      </el-icon>
                      查询
                    </el-button>
                    <el-button @click="handleCategoryReset">重置</el-button>
                    <el-button type="primary" @click="handleCategoryAdd">
                      <el-icon>
                        <Plus />
                      </el-icon>
                      新增类别
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 表格区域 -->
              <div class="table-area flex-1">
                <el-table :data="categoryTableData" border height="100%" v-loading="categoryLoading" style="width: 100%"
                  row-key="id" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
                  <el-table-column prop="categoryCode" label="类别编码" min-width="7.5rem" align="center" />
                  <el-table-column prop="categoryName" label="类别名称" min-width="7.5rem" align="left" />
                  <el-table-column prop="parentName" label="父级类别" min-width="7.5rem" align="center" />
                  <el-table-column prop="sort" label="排序" width="5rem" align="center" />
                  <el-table-column prop="description" label="描述" min-width="10rem" align="center"
                    show-overflow-tooltip />
                  <el-table-column prop="createTime" label="创建时间" width="10rem" align="center" />
                  <el-table-column label="操作" width="12.5rem" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button type="primary" link @click="handleCategoryEdit(row)">编辑</el-button>
                      <el-button type="danger" link @click="handleCategoryDelete(row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>

          <!-- 期初库存导入 -->
          <el-tab-pane label="期初库存" name="initialStock">
            <div class="tab-content">
              <!-- 操作区域 -->
              <div class="operation-area mb-1rem">
                <el-alert title="期初库存导入说明" type="info" :closable="false" show-icon class="mb-1rem">
                  <template #default>
                    <div class="text-0.875rem">
                      <p>1. 支持Excel文件批量导入商品的初始数量与金额</p>
                      <p>2. 导入前请确保商品信息已在系统中存在</p>
                      <p>3. 导入文件格式：商品编码、商品名称、仓库编码、初始数量、单价、总金额</p>
                    </div>
                  </template>
                </el-alert>

                <div class="flex gap-0.5rem">
                  <el-button type="primary" @click="handleInitialStockImport">
                    <el-icon>
                      <Upload />
                    </el-icon>
                    导入期初库存
                  </el-button>
                  <el-button @click="handleDownloadTemplate">
                    <el-icon>
                      <Download />
                    </el-icon>
                    下载模板
                  </el-button>
                  <el-button type="success" @click="handleInitialStockSearch">
                    <el-icon>
                      <Search />
                    </el-icon>
                    查看导入记录
                  </el-button>
                </div>
              </div>

              <!-- 导入记录表格 -->
              <div class="table-area flex-1">
                <el-table :data="initialStockTableData" border height="100%" v-loading="initialStockLoading"
                  style="width: 100%">
                  <el-table-column type="index" label="序号" width="3.75rem" align="center" />
                  <el-table-column prop="batchNo" label="批次号" min-width="7.5rem" align="center" />
                  <el-table-column prop="fileName" label="文件名" min-width="10rem" align="center" />
                  <el-table-column prop="totalCount" label="总记录数" width="6.25rem" align="center" />
                  <el-table-column prop="successCount" label="成功数" width="5rem" align="center" />
                  <el-table-column prop="failCount" label="失败数" width="5rem" align="center" />
                  <el-table-column prop="status" label="状态" width="6.25rem" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getImportStatusTag(row.status)">{{ getImportStatusText(row.status) }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="operator" label="操作人" width="6.25rem" align="center" />
                  <el-table-column prop="createTime" label="导入时间" width="10rem" align="center" />
                  <el-table-column label="操作" width="10rem" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button type="primary" link @click="handleViewImportDetail(row)">查看详情</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 分页 -->
              <div class="pagination-area mt-1rem">
                <el-pagination v-model:current-page="initialStockPagination.currentPage"
                  v-model:page-size="initialStockPagination.pageSize" :total="initialStockPagination.total"
                  layout="total, sizes, prev, pager, next" @size-change="handleInitialStockSizeChange"
                  @current-change="handleInitialStockCurrentChange" />
              </div>
            </div>
          </el-tab-pane>

          <!-- 库存预警配置 -->
          <el-tab-pane label="库存预警" name="stockAlert">
            <div class="tab-content">
              <!-- 搜索区域 -->
              <div class="search-area mb-1rem">
                <el-form :model="stockAlertSearchForm" inline>
                  <el-form-item label="商品编码">
                    <el-input v-model="stockAlertSearchForm.goodsCode" placeholder="请输入商品编码" style="width: 12.5rem"
                      clearable />
                  </el-form-item>
                  <el-form-item label="商品名称">
                    <el-input v-model="stockAlertSearchForm.goodsName" placeholder="请输入商品名称" style="width: 12.5rem"
                      clearable />
                  </el-form-item>
                  <el-form-item label="仓库">
                    <el-select v-model="stockAlertSearchForm.warehouseId" placeholder="请选择仓库" style="width: 12.5rem"
                      clearable>
                      <el-option v-for="warehouse in warehouseOptions" :key="warehouse.id"
                        :label="warehouse.warehouseName" :value="warehouse.id" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="预警状态">
                    <el-select v-model="stockAlertSearchForm.alertEnabled" placeholder="请选择状态" style="width: 12.5rem"
                      clearable>
                      <el-option label="启用" value="1" />
                      <el-option label="禁用" value="0" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleStockAlertSearch">
                      <el-icon>
                        <Search />
                      </el-icon>
                      查询
                    </el-button>
                    <el-button @click="handleStockAlertReset">重置</el-button>
                    <el-button type="primary" @click="handleStockAlertAdd">
                      <el-icon>
                        <Plus />
                      </el-icon>
                      新增配置
                    </el-button>
                    <el-button type="success" @click="handleBatchStockAlertImport">
                      <el-icon>
                        <Upload />
                      </el-icon>
                      批量导入
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 表格区域 -->
              <div class="table-area flex-1">
                <el-table :data="stockAlertTableData" border height="100%" v-loading="stockAlertLoading"
                  style="width: 100%">
                  <el-table-column type="index" label="序号" width="3.75rem" align="center" />
                  <el-table-column prop="goodsCode" label="商品编码" min-width="7.5rem" align="center" />
                  <el-table-column prop="goodsName" label="商品名称" min-width="7.5rem" align="center" />
                  <el-table-column prop="warehouseName" label="仓库" min-width="6.25rem" align="center" />
                  <el-table-column prop="unit" label="单位" width="5rem" align="center" />
                  <el-table-column prop="minStock" label="库存下限" width="6.25rem" align="center" />
                  <el-table-column prop="maxStock" label="库存上限" width="6.25rem" align="center" />
                  <el-table-column prop="currentStock" label="当前库存" width="6.25rem" align="center">
                    <template #default="{ row }">
                      <span :class="getStockAlertClass(row)">{{ row.currentStock }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="alertEnabled" label="预警状态" width="6.25rem" align="center">
                    <template #default="{ row }">
                      <el-switch v-model="row.alertEnabled" :active-value="1" :inactive-value="0"
                        @change="handleStockAlertStatusChange(row)" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="updateTime" label="更新时间" width="10rem" align="center" />
                  <el-table-column label="操作" width="12.5rem" align="center" fixed="right">
                    <template #default="{ row }">
                      <el-button type="primary" link @click="handleStockAlertEdit(row)">编辑</el-button>
                      <el-button type="danger" link @click="handleStockAlertDelete(row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 分页 -->
              <div class="pagination-area mt-1rem">
                <el-pagination v-model:current-page="stockAlertPagination.currentPage"
                  v-model:page-size="stockAlertPagination.pageSize" :total="stockAlertPagination.total"
                  layout="total, sizes, prev, pager, next" @size-change="handleStockAlertSizeChange"
                  @current-change="handleStockAlertCurrentChange" />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 对话框组件 -->
    <WarehouseDialog v-model:visible="warehouseDialogVisible" :dialog-type="warehouseDialogType"
      :form-data="warehouseFormData" @success="handleWarehouseDialogSuccess" />

    <PurposeDialog v-model:visible="purposeDialogVisible" :dialog-type="purposeDialogType" :form-data="purposeFormData"
      @success="handlePurposeDialogSuccess" />

    <CategoryDialog v-model:visible="categoryDialogVisible" :dialog-type="categoryDialogType"
      :form-data="categoryFormData" :category-list="categoryTableData" @success="handleCategoryDialogSuccess" />

    <StockAlertDialog v-model:visible="stockAlertDialogVisible" :dialog-type="stockAlertDialogType"
      :form-data="stockAlertFormData" @success="handleStockAlertDialogSuccess" />

    <BatchImportDialog v-model:visible="batchImportDialogVisible" :import-type="batchImportType"
      @success="handleBatchImportSuccess" />

    <ImportDetailDialog v-model:visible="importDetailDialogVisible" :detail-data="importDetailData" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Plus, Download, Upload, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 导入对话框组件
import WarehouseDialog from './components/WarehouseDialog.vue'
import PurposeDialog from './components/PurposeDialog.vue'
import CategoryDialog from './components/CategoryDialog.vue'
import StockAlertDialog from './components/StockAlertDialog.vue'
import BatchImportDialog from './components/BatchImportDialog.vue'
import ImportDetailDialog from './components/ImportDetailDialog.vue'

// 定义接口类型
interface Warehouse {
  id: string
  warehouseCode: string
  warehouseName: string
  type: string
  address: string
  managers: string[]
  status: number
  createTime: string
}

interface Purpose {
  id: string
  purposeCode: string
  purposeName: string
  warehouseId: string
  warehouseName: string
  goodsTypes: string[]
  allowNoStock: boolean
  description: string
}

interface Category {
  id: string
  categoryCode: string
  categoryName: string
  parentId: string
  parentName: string
  sort: number
  description: string
  createTime: string
  children?: Category[]
}

interface StockAlert {
  id: string
  goodsCode: string
  goodsName: string
  warehouseId: string
  warehouseName: string
  unit: string
  minStock: number
  maxStock: number
  currentStock: number
  alertEnabled: number
  updateTime: string
}

interface InitialStock {
  id: string
  batchNo: string
  fileName: string
  totalCount: number
  successCount: number
  failCount: number
  status: string
  operator: string
  createTime: string
}

// Tab 相关
const activeTab = ref('warehouse')

// 仓库管理相关数据
const warehouseLoading = ref(false)
const warehouseSearchForm = reactive({
  warehouseCode: '',
  warehouseName: '',
  type: '',
  status: ''
})

const warehousePagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// Mock 仓库数据
const warehouseTableData = ref<Warehouse[]>([
  {
    id: '1',
    warehouseCode: 'WH001',
    warehouseName: '主仓库',
    type: 'main',
    address: '北京市朝阳区xxx路xxx号',
    managers: ['张三', '李四'],
    status: 1,
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: '2',
    warehouseCode: 'WH002',
    warehouseName: '分仓库A',
    type: 'branch',
    address: '北京市海淀区xxx路xxx号',
    managers: ['王五'],
    status: 1,
    createTime: '2024-01-16 14:20:00'
  },
  {
    id: '3',
    warehouseCode: 'WH003',
    warehouseName: '临时仓库',
    type: 'temp',
    address: '北京市丰台区xxx路xxx号',
    managers: ['赵六', '钱七'],
    status: 0,
    createTime: '2024-01-17 09:15:00'
  }
])

// 用途管理相关数据
const purposeLoading = ref(false)
const purposeSearchForm = reactive({
  purposeName: '',
  warehouseId: ''
})

const purposePagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// Mock 用途数据
const purposeTableData = ref<Purpose[]>([
  {
    id: '1',
    purposeCode: 'P001',
    purposeName: '办公用品',
    warehouseId: '1',
    warehouseName: '主仓库',
    goodsTypes: ['文具', '纸张'],
    allowNoStock: false,
    description: '日常办公所需用品'
  },
  {
    id: '2',
    purposeCode: 'P002',
    purposeName: '维修材料',
    warehouseId: '2',
    warehouseName: '分仓库A',
    goodsTypes: ['五金', '工具'],
    allowNoStock: true,
    description: '设备维修所需材料'
  }
])

// 商品类别相关数据
const categoryLoading = ref(false)
const categorySearchForm = reactive({
  categoryName: '',
  parentId: ''
})

// Mock 商品类别数据
const categoryTableData = ref<Category[]>([
  {
    id: '1',
    categoryCode: 'C001',
    categoryName: '办公用品',
    parentId: '',
    parentName: '无',
    sort: 1,
    description: '办公相关用品',
    createTime: '2024-01-15 10:30:00',
    children: [
      {
        id: '11',
        categoryCode: 'C001001',
        categoryName: '文具',
        parentId: '1',
        parentName: '办公用品',
        sort: 1,
        description: '笔、纸等文具',
        createTime: '2024-01-15 10:35:00'
      },
      {
        id: '12',
        categoryCode: 'C001002',
        categoryName: '纸张',
        parentId: '1',
        parentName: '办公用品',
        sort: 2,
        description: '各类纸张',
        createTime: '2024-01-15 10:36:00'
      }
    ]
  },
  {
    id: '2',
    categoryCode: 'C002',
    categoryName: '维修材料',
    parentId: '',
    parentName: '无',
    sort: 2,
    description: '维修相关材料',
    createTime: '2024-01-16 14:20:00'
  }
])

// 库存预警相关数据
const stockAlertLoading = ref(false)
const stockAlertSearchForm = reactive({
  goodsCode: '',
  goodsName: '',
  warehouseId: '',
  alertEnabled: ''
})

const stockAlertPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// Mock 库存预警数据
const stockAlertTableData = ref<StockAlert[]>([
  {
    id: '1',
    goodsCode: 'G001',
    goodsName: 'A4纸',
    warehouseId: '1',
    warehouseName: '主仓库',
    unit: '包',
    minStock: 10,
    maxStock: 100,
    currentStock: 5,
    alertEnabled: 1,
    updateTime: '2024-01-15 10:30:00'
  },
  {
    id: '2',
    goodsCode: 'G002',
    goodsName: '圆珠笔',
    warehouseId: '1',
    warehouseName: '主仓库',
    unit: '支',
    minStock: 50,
    maxStock: 500,
    currentStock: 120,
    alertEnabled: 1,
    updateTime: '2024-01-16 14:20:00'
  }
])

// 期初库存相关数据
const initialStockLoading = ref(false)
const initialStockPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// Mock 期初库存导入记录数据
const initialStockTableData = ref<InitialStock[]>([
  {
    id: '1',
    batchNo: 'BATCH001',
    fileName: '期初库存导入_20240115.xlsx',
    totalCount: 100,
    successCount: 95,
    failCount: 5,
    status: 'success',
    operator: '管理员',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: '2',
    batchNo: 'BATCH002',
    fileName: '期初库存导入_20240116.xlsx',
    totalCount: 50,
    successCount: 50,
    failCount: 0,
    status: 'success',
    operator: '张三',
    createTime: '2024-01-16 14:20:00'
  }
])

// 对话框相关状态
const warehouseDialogVisible = ref(false)
const warehouseDialogType = ref<'add' | 'edit'>('add')
const warehouseFormData = ref<Partial<Warehouse>>({})

const purposeDialogVisible = ref(false)
const purposeDialogType = ref<'add' | 'edit'>('add')
const purposeFormData = ref<Partial<Purpose>>({})

const categoryDialogVisible = ref(false)
const categoryDialogType = ref<'add' | 'edit'>('add')
const categoryFormData = ref<Partial<Category>>({})

const stockAlertDialogVisible = ref(false)
const stockAlertDialogType = ref<'add' | 'edit'>('add')
const stockAlertFormData = ref<Partial<StockAlert>>({})

const batchImportDialogVisible = ref(false)
const batchImportType = ref<'initialStock' | 'stockAlert'>('initialStock')

const importDetailDialogVisible = ref(false)
const importDetailData = ref<any>({})

// 计算属性
const warehouseOptions = computed(() =>
  warehouseTableData.value.map(item => ({
    id: item.id,
    warehouseName: item.warehouseName
  }))
)

const categoryOptions = computed(() =>
  categoryTableData.value.filter(item => !item.parentId)
)

// 工具方法
const getWarehouseTypeTag = (type: string) => {
  const typeMap = {
    main: 'success',
    branch: 'warning',
    temp: 'info'
  }
  return typeMap[type] || 'info'
}

const getWarehouseTypeText = (type: string) => {
  const typeMap = {
    main: '主仓库',
    branch: '分仓库',
    temp: '临时仓库'
  }
  return typeMap[type] || '未知'
}

const getImportStatusTag = (status: string) => {
  const statusMap = {
    success: 'success',
    failed: 'danger',
    processing: 'warning'
  }
  return statusMap[status] || 'info'
}

const getImportStatusText = (status: string) => {
  const statusMap = {
    success: '成功',
    failed: '失败',
    processing: '处理中'
  }
  return statusMap[status] || '未知'
}

const getStockAlertClass = (row: StockAlert) => {
  if (row.currentStock < row.minStock) {
    return 'text-red-500 font-bold'
  } else if (row.currentStock > row.maxStock) {
    return 'text-orange-500 font-bold'
  }
  return 'text-green-500'
}

// Tab 切换处理
const handleTabChange = (tabName: string) => {
  console.log('切换到标签页:', tabName)
  // 根据不同的tab加载对应的数据
  switch (tabName) {
    case 'warehouse':
      handleWarehouseSearch()
      break
    case 'purpose':
      handlePurposeSearch()
      break
    case 'category':
      handleCategorySearch()
      break
    case 'initialStock':
      handleInitialStockSearch()
      break
    case 'stockAlert':
      handleStockAlertSearch()
      break
  }
}

// 通用方法
const handleBatchImport = () => {
  batchImportType.value = 'initialStock'
  batchImportDialogVisible.value = true
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const handleBatchImportSuccess = () => {
  batchImportDialogVisible.value = false
  ElMessage.success('批量导入成功')
  // 根据当前tab刷新对应数据
  handleTabChange(activeTab.value)
}

// 仓库管理相关方法
const handleWarehouseSearch = () => {
  warehouseLoading.value = true
  console.log('仓库搜索条件:', warehouseSearchForm)
  // 模拟API调用
  setTimeout(() => {
    warehousePagination.total = warehouseTableData.value.length
    warehouseLoading.value = false
  }, 500)
}

const handleWarehouseReset = () => {
  Object.assign(warehouseSearchForm, {
    warehouseCode: '',
    warehouseName: '',
    type: '',
    status: ''
  })
  handleWarehouseSearch()
}

const handleWarehouseAdd = () => {
  warehouseDialogType.value = 'add'
  warehouseFormData.value = {}
  warehouseDialogVisible.value = true
}

const handleWarehouseEdit = (row: Warehouse) => {
  warehouseDialogType.value = 'edit'
  warehouseFormData.value = { ...row }
  warehouseDialogVisible.value = true
}

const handleWarehouseDelete = (row: Warehouse) => {
  ElMessageBox.confirm(`确认删除仓库"${row.warehouseName}"吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = warehouseTableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      warehouseTableData.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const handleWarehouseStatusChange = (row: Warehouse) => {
  console.log('仓库状态变更:', row)
  ElMessage.success(`仓库"${row.warehouseName}"状态已${row.status ? '启用' : '停用'}`)
}

const handleWarehouseSizeChange = (size: number) => {
  warehousePagination.pageSize = size
  handleWarehouseSearch()
}

const handleWarehouseCurrentChange = (page: number) => {
  warehousePagination.currentPage = page
  handleWarehouseSearch()
}

const handleWarehouseDialogSuccess = () => {
  warehouseDialogVisible.value = false
  handleWarehouseSearch()
  ElMessage.success('操作成功')
}

// 用途管理相关方法
const handlePurposeSearch = () => {
  purposeLoading.value = true
  console.log('用途搜索条件:', purposeSearchForm)
  setTimeout(() => {
    purposePagination.total = purposeTableData.value.length
    purposeLoading.value = false
  }, 500)
}

const handlePurposeReset = () => {
  Object.assign(purposeSearchForm, {
    purposeName: '',
    warehouseId: ''
  })
  handlePurposeSearch()
}

const handlePurposeAdd = () => {
  purposeDialogType.value = 'add'
  purposeFormData.value = {}
  purposeDialogVisible.value = true
}

const handlePurposeEdit = (row: Purpose) => {
  purposeDialogType.value = 'edit'
  purposeFormData.value = { ...row }
  purposeDialogVisible.value = true
}

const handlePurposeDelete = (row: Purpose) => {
  ElMessageBox.confirm(`确认删除用途"${row.purposeName}"吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = purposeTableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      purposeTableData.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const handlePurposeSizeChange = (size: number) => {
  purposePagination.pageSize = size
  handlePurposeSearch()
}

const handlePurposeCurrentChange = (page: number) => {
  purposePagination.currentPage = page
  handlePurposeSearch()
}

const handlePurposeDialogSuccess = () => {
  purposeDialogVisible.value = false
  handlePurposeSearch()
  ElMessage.success('操作成功')
}

// 商品类别管理相关方法
const handleCategorySearch = () => {
  categoryLoading.value = true
  console.log('类别搜索条件:', categorySearchForm)
  setTimeout(() => {
    categoryLoading.value = false
  }, 500)
}

const handleCategoryReset = () => {
  Object.assign(categorySearchForm, {
    categoryName: '',
    parentId: ''
  })
  handleCategorySearch()
}

const handleCategoryAdd = () => {
  categoryDialogType.value = 'add'
  categoryFormData.value = {}
  categoryDialogVisible.value = true
}

const handleCategoryEdit = (row: Category) => {
  categoryDialogType.value = 'edit'
  categoryFormData.value = { ...row }
  categoryDialogVisible.value = true
}

const handleCategoryDelete = (row: Category) => {
  ElMessageBox.confirm(`确认删除类别"${row.categoryName}"吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = categoryTableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      categoryTableData.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const handleCategoryDialogSuccess = () => {
  categoryDialogVisible.value = false
  handleCategorySearch()
  ElMessage.success('操作成功')
}

// 期初库存相关方法
const handleInitialStockSearch = () => {
  initialStockLoading.value = true
  console.log('查看导入记录')
  setTimeout(() => {
    initialStockPagination.total = initialStockTableData.value.length
    initialStockLoading.value = false
  }, 500)
}

const handleInitialStockImport = () => {
  batchImportType.value = 'initialStock'
  batchImportDialogVisible.value = true
}

const handleDownloadTemplate = () => {
  ElMessage.success('模板下载功能开发中...')
}

const handleViewImportDetail = (row: InitialStock) => {
  importDetailData.value = row
  importDetailDialogVisible.value = true
}

const handleInitialStockSizeChange = (size: number) => {
  initialStockPagination.pageSize = size
  handleInitialStockSearch()
}

const handleInitialStockCurrentChange = (page: number) => {
  initialStockPagination.currentPage = page
  handleInitialStockSearch()
}

// 库存预警相关方法
const handleStockAlertSearch = () => {
  stockAlertLoading.value = true
  console.log('库存预警搜索条件:', stockAlertSearchForm)
  setTimeout(() => {
    stockAlertPagination.total = stockAlertTableData.value.length
    stockAlertLoading.value = false
  }, 500)
}

const handleStockAlertReset = () => {
  Object.assign(stockAlertSearchForm, {
    goodsCode: '',
    goodsName: '',
    warehouseId: '',
    alertEnabled: ''
  })
  handleStockAlertSearch()
}

const handleStockAlertAdd = () => {
  stockAlertDialogType.value = 'add'
  stockAlertFormData.value = {}
  stockAlertDialogVisible.value = true
}

const handleStockAlertEdit = (row: StockAlert) => {
  stockAlertDialogType.value = 'edit'
  stockAlertFormData.value = { ...row }
  stockAlertDialogVisible.value = true
}

const handleStockAlertDelete = (row: StockAlert) => {
  ElMessageBox.confirm(`确认删除商品"${row.goodsName}"的预警配置吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = stockAlertTableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      stockAlertTableData.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const handleStockAlertStatusChange = (row: StockAlert) => {
  console.log('预警状态变更:', row)
  ElMessage.success(`商品"${row.goodsName}"预警状态已${row.alertEnabled ? '启用' : '禁用'}`)
}

const handleBatchStockAlertImport = () => {
  batchImportType.value = 'stockAlert'
  batchImportDialogVisible.value = true
}

const handleStockAlertSizeChange = (size: number) => {
  stockAlertPagination.pageSize = size
  handleStockAlertSearch()
}

const handleStockAlertCurrentChange = (page: number) => {
  stockAlertPagination.currentPage = page
  handleStockAlertSearch()
}

const handleStockAlertDialogSuccess = () => {
  stockAlertDialogVisible.value = false
  handleStockAlertSearch()
  ElMessage.success('操作成功')
}

// 初始化
onMounted(() => {
  handleWarehouseSearch()
})
</script>

<style scoped lang="scss">
.warehouse-management-container {
  .warehouse-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 1rem;
    }

    :deep(.el-tabs__content) {
      height: calc(100% - 3rem);
    }
  }

  .tab-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-area {
      flex-shrink: 0;
    }

    .operation-area {
      flex-shrink: 0;
    }

    .table-area {
      flex: 1;
      min-height: 0;
    }

    .pagination-area {
      flex-shrink: 0;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
