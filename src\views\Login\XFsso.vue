<template>
  <div class="sso-container" v-loading="true" element-loading-text="登录中..."></div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import request from '@/config/axios'
import * as authUtil from '@/utils/auth'
import { SSO_CONFIG } from '@/config/sso'
import {useTagsViewStore} from "@/store/modules/tagsView";
import {useUserStore} from "@/store/modules/user";

const userStore = useUserStore()
const tagsViewStore = useTagsViewStore()

const clearCache = () => {
  userStore.loginOut()
  tagsViewStore.delAllViews()
  authUtil.removeToken()
  localStorage.clear()
  sessionStorage.clear()
}

const redirectToUAP = () => {
  clearCache()
  const timestamp = Date.now()
  const params = new URLSearchParams({
    ...SSO_CONFIG.AUTH_PARAMS,
    time: timestamp.toString()
  })
  const finalUrl = `${SSO_CONFIG.UAP_SERVER}?${params.toString()}`
  window.location.href = finalUrl
}

const callXfssoApi = async (accessToken: string, authorizationCode?: string) => {
  try {
    const params = new URLSearchParams()
    if (accessToken) {
      params.append('access_token', accessToken)
    }
    if (authorizationCode) {
      params.append('authorizationCode', authorizationCode)
    }

    const apiUrl = `${SSO_CONFIG.API_BASE_URL}?${params.toString()}`
    const res = await request.getOriginal({
      url: apiUrl
    })
    if (res.code === 0) {
      authUtil.setToken(res.data)
      window.location.href = SSO_CONFIG.LOCAL_SERVER
    }
  } catch (error) {
    console.error('Error calling xfsso API:', error)
  }
}

onMounted(() => {
  clearCache()
  const urlParams = new URLSearchParams(window.location.search)
  const accessToken = urlParams.get('access_token')
  const authorizationCode = urlParams.get('authorizationCode')

  if (accessToken || authorizationCode) {
    callXfssoApi(accessToken || '', authorizationCode || undefined)
  } else {
    redirectToUAP()
  }
})
</script>

<style scoped lang="scss">
.sso-container {
  height: 100vh;
}
</style>
