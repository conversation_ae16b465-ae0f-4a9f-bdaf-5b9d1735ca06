<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="采样点名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入采样点名称" />
      </el-form-item>
      <el-form-item label="采样点代码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入采样点代码" />
      </el-form-item>
      <el-form-item label="采样点类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择采样点类型">
          <el-option label="进水口" value="inlet" />
          <el-option label="出水口" value="outlet" />
        </el-select>
      </el-form-item>
      <el-form-item label="位置描述" prop="location">
        <el-input v-model="formData.location" placeholder="请输入位置描述" />
      </el-form-item>
      <el-form-item label="管理人" prop="managerId">
        <el-select v-model="formData.managerId" placeholder="请选择管理人" clearable>
          <el-option label="张三" :value="1001" />
          <el-option label="李四" :value="1002" />
          <el-option label="王五" :value="1003" />
          <el-option label="赵六" :value="1004" />
        </el-select>
      </el-form-item>
      <!-- 移除采样频率字段 - 频率应该在采样计划中定义 -->
      <!-- 移除检测项目字段 - 检测项目应该在采样计划中定义 -->

      <!-- 添加说明信息 -->
      <el-alert
        title="字段说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>采样点只定义物理位置和基础信息。</p>
          <p><strong>采样频率</strong>和<strong>检测项目</strong>请在"采样计划管理"中配置。</p>
        </template>
      </el-alert>
      <el-form-item label="状态" prop="isEnabled">
        <el-radio-group v-model="formData.isEnabled">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'
import { AssayBasicApi } from '@/api/assay/basic'

defineOptions({ name: 'SamplingDialog' })

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')

// 表单数据 - 简化字段，删除不需要的字段
const formData = ref({
  id: undefined,
  factoryId: undefined as number | undefined,
  name: '',
  code: '',
  type: '',
  location: '',
  managerId: undefined as number | undefined,
  isEnabled: true,
  remark: ''
})

// 表单校验规则 - 简化验证规则
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '采样点名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '采样点代码不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '采样点类型不能为空', trigger: 'change' }],
  location: [{ required: true, message: '位置描述不能为空', trigger: 'blur' }]
})

const formRef = ref()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (type: string, data?: any, factoryId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增采样点' : '编辑采样点'
  formType.value = type
  resetForm()

  // 设置水厂ID
  if (factoryId) {
    formData.value.factoryId = factoryId
  }

  // 如果是编辑模式，设置表单数据
  if (type === 'update' && data) {
    // 深拷贝，避免直接修改原始数据
    formData.value = JSON.parse(JSON.stringify(data))
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  if (!formData.value.factoryId) {
    ElMessage.error('水厂ID未设置，无法提交')
    return
  }

  formLoading.value = true
  try {
    if (formType.value === 'create') {
      await AssayBasicApi.createSamplingPoint({
        factoryId: formData.value.factoryId,
        name: formData.value.name,
        code: formData.value.code,
        type: formData.value.type as 'inlet' | 'outlet',
        location: formData.value.location,
        managerId: formData.value.managerId,
        isEnabled: formData.value.isEnabled,
        remark: formData.value.remark
      })
    } else {
      await AssayBasicApi.updateSamplingPoint({
        id: formData.value.id!,
        factoryId: formData.value.factoryId,
        name: formData.value.name,
        code: formData.value.code,
        type: formData.value.type as 'inlet' | 'outlet',
        location: formData.value.location,
        managerId: formData.value.managerId,
        isEnabled: formData.value.isEnabled,
        remark: formData.value.remark
      })
    }

    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    factoryId: undefined,
    name: '',
    code: '',
    type: '',
    location: '',
    managerId: undefined,
    isEnabled: true,
    remark: ''
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.coordinate-input {
  :deep(.el-input-number) {
    .el-input__wrapper {
      padding-right: 1.5rem;
    }
  }
}
</style>