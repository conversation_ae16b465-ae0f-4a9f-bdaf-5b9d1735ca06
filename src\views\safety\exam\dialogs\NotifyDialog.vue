<template>
  <el-dialog
    v-model="visible"
    title="发送培训提醒"
    width="500px"
    destroy-on-close
    @closed="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="提醒方式" prop="notifyTypes">
        <el-checkbox-group v-model="form.notifyTypes">
          <el-checkbox label="email">邮件</el-checkbox>
          <el-checkbox label="sms">短信</el-checkbox>
          <el-checkbox label="wechat">微信</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="提醒时间" prop="notifyTime">
        <el-radio-group v-model="form.notifyTime">
          <el-radio label="1">培训开始前1天</el-radio>
          <el-radio label="2">培训开始前2天</el-radio>
          <el-radio label="3">培训开始前3天</el-radio>
          <el-radio label="custom">自定义</el-radio>
        </el-radio-group>
        <el-input-number
          v-if="form.notifyTime === 'custom'"
          v-model="form.customDays"
          :min="1"
          :max="30"
          class="custom-days"
        />
        <span v-if="form.notifyTime === 'custom'">天</span>
      </el-form-item>
      <el-form-item label="提醒内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入提醒内容"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">发送</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'NotifyDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    planData: {
      type: Object,
      required: true
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      form: {
        notifyTypes: [],
        notifyTime: '1',
        customDays: 1,
        content: ''
      },
      rules: {
        notifyTypes: [{ required: true, message: '请选择提醒方式', trigger: 'change' }],
        notifyTime: [{ required: true, message: '请选择提醒时间', trigger: 'change' }],
        content: [{ required: true, message: '请输入提醒内容', trigger: 'blur' }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    initForm() {
      // 初始化默认提醒内容
      this.form.content = `亲爱的同事：
您好！提醒您参加以下培训：

培训计划：${this.planData.planName}
培训时间：${this.planData.startTime}
培训地点：${this.planData.location}
培训讲师：${this.planData.trainer}

请准时参加培训，谢谢！`
    },
    handleSubmit() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          // TODO: 调用API发送提醒
          this.$emit('success')
          this.visible = false
        }
      })
    },
    handleClose() {
      this.$refs.formRef?.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.custom-days {
  margin: 0 8px;
  width: 120px;
}
</style> 