<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据对比分析"
    width="1000px"
    :before-close="handleClose"
  >
    <div class="compare-content">
      <div class="compare-header">
        <el-form :inline="true" :model="compareForm">
          <el-form-item label="对比类型">
            <el-select v-model="compareForm.type" placeholder="请选择对比类型">
              <el-option label="时间对比" value="time" />
              <el-option label="部门对比" value="department" />
            </el-select>
          </el-form-item>

          <template v-if="compareForm.type === 'time'">
            <el-form-item label="时间范围1">
              <el-date-picker
                v-model="compareForm.timeRange1"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item label="时间范围2">
              <el-date-picker
                v-model="compareForm.timeRange2"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </template>

          <template v-else>
            <el-form-item label="部门1">
              <el-select v-model="compareForm.department1" placeholder="请选择部门">
                <el-option
                  v-for="dept in departments"
                  :key="dept.value"
                  :label="dept.label"
                  :value="dept.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="部门2">
              <el-select v-model="compareForm.department2" placeholder="请选择部门">
                <el-option
                  v-for="dept in departments"
                  :key="dept.value"
                  :label="dept.label"
                  :value="dept.value"
                />
              </el-select>
            </el-form-item>
          </template>

          <el-form-item>
            <el-button type="primary" @click="handleCompare">开始对比</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div v-if="showCompareResult" class="compare-result">
        <el-table :data="compareData" border style="width: 100%">
          <el-table-column prop="name" label="指标名称" />
          <el-table-column prop="value1" :label="compareForm.type === 'time' ? '时间段1' : '部门1'" />
          <el-table-column prop="value2" :label="compareForm.type === 'time' ? '时间段2' : '部门2'" />
          <el-table-column prop="difference" label="差异" />
        </el-table>

        <div class="analysis-content mt-20">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>对比分析结论</span>
              </div>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="主要差异">{{ analysisResult.mainDifference }}</el-descriptions-item>
              <el-descriptions-item label="改进建议">{{ analysisResult.suggestions }}</el-descriptions-item>
              <el-descriptions-item label="风险提示">{{ analysisResult.riskWarning }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport" :disabled="!showCompareResult">
          导出对比结果
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'close', 'export'])

const dialogVisible = ref(props.visible)
const showCompareResult = ref(false)

// 部门列表
const departments = [
  { label: '生产部', value: 'production' },
  { label: '安全部', value: 'safety' },
  { label: '设备部', value: 'equipment' },
  { label: '质量部', value: 'quality' }
]

// 对比表单
const compareForm = reactive({
  type: 'time',
  timeRange1: [],
  timeRange2: [],
  department1: '',
  department2: ''
})

// 对比数据
const compareData = ref([
  {
    name: '安全培训完成率',
    value1: '95%',
    value2: '88%',
    difference: '-7%'
  },
  {
    name: '事故发生率',
    value1: '0.5%',
    value2: '0.8%',
    difference: '+0.3%'
  },
  {
    name: '隐患整改率',
    value1: '92%',
    value2: '85%',
    difference: '-7%'
  }
])

// 分析结果
const analysisResult = reactive({
  mainDifference: '通过对比分析发现，在安全培训完成率方面存在明显差异，第二时段/部门的完成率降低了7%。',
  suggestions: '建议加强安全培训管理，确保培训计划的执行力度，同时可以借鉴表现较好的部门的管理经验。',
  riskWarning: '需要注意的是，事故发生率有所上升，建议及时采取预防措施。'
})

// 开始对比
const handleCompare = () => {
  // 这里应该调用后端接口获取对比数据
  showCompareResult.value = true
}

// 导出对比结果
const handleExport = () => {
  emit('export', {
    compareForm,
    compareData: compareData.value,
    analysisResult
  })
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  emit('update:visible', false)
  emit('close')
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})
</script>

<style lang="scss" scoped>
.compare-content {
  .compare-header {
    margin-bottom: 20px;
  }

  .compare-result {
    .mt-20 {
      margin-top: 20px;
    }

    .analysis-content {
      padding: 20px 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 