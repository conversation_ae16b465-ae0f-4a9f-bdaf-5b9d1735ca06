<template>
  <div class="report-page" v-loading="loading" element-loading-text="报表加载中，请稍候...">
    <div class="report-header">
      <!-- 筛选条件组件 -->
      <SelectConfig v-if="selectConfig && selectConfig.length > 0" :config="selectConfig" @search="handleSearch"
        @reset="handleReset" @export="handleExport" />
      <!-- 工具栏 -->
      <div class="report-toolbar">
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button v-if="!selectConfig || selectConfig.length == 0" @click="handleExport">导出</el-button>
      </div>
    </div>

    <LuckySheetContainer ref="luckysheetRef" />
  </div>
</template>
<script lang="ts" setup>
import LuckySheetContainer from './components/luckysheet.vue'
import SelectConfig from '@/views/report/luckysheetReport/components/selectConfig.vue'
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import emitter from '@/utils/eventBus'
import { useRoute } from 'vue-router'
import { ReportApi } from '@/api/report/reportInfo/index'
import { ExcelApi } from '@/api/report/excel/index'
import { validate } from 'uuid'
import { setSheetProtection } from '@/utils/luckysheet'
import { ElMessage } from 'element-plus'

const route = useRoute()
const luckysheetRef = ref()
const reportCode = ref()
// 添加loading状态
const loading = ref(false)


// 定义所有luckySheet钩子函数
const luckysheetHooks = {

  cellMousedown: (cell: any, position: any) => {
    if (cell) {
      if (cell.isOR == 1) {
        // 只读单元格
        return;
      }
    }
  }
}

// 定义option类型
interface LuckysheetOption {
  container: string
  lang: string
  showinfobar: boolean
  editMode: boolean
  showtoolbar: boolean
  gridKey?: string,
  hook: luckysheetHooks,
  data?: any[]
}

const option = ref<LuckysheetOption>({
  container: 'luckysheet',
  lang: 'zh',
  showinfobar: false, // 隐藏信息栏
  editMode: false,
  showtoolbar: false,
  showsheetbar: false, //是否显示底部sheet页按钮
  cellRightClickConfig: {   // 鼠标右击功能
    copy: true, // 复制
    copyAs: true, // 复制为
    rowHeight: false, // 行高
    columnWidth: false, // 列宽
    clear: false, // 清除内容
    image: false, // 插入图片
    chart: false, // 图表
    link: false, // 链接
    data: false, // 数据验证
    cellFormat: false, // 设置单元格格式
    insertColumn: false, // 插入列
    deletecolumn: false, // 删除列
    hidecolumn: false, // 隐藏列
    matrix: false, // 矩阵操作
    sort: false, // 排序
    insertRow: false, //  插入行
    deleteRow: false, //  删除行
    filter: false, //  筛选选取
    deleteSelectedColum: false, //  筛选选取
    deleteCell: false, //  删除单元格
  }
})

// 筛选条件配置
const selectConfig = ref([])

const handlePreviewData = async () => {
  // 开始加载，显示加载指示器
  loading.value = true
  // 先查询excel数据，获取查询条件
  try {
    let data = await ExcelApi.getExcelByReportCode(reportCode.value);
    // 设置筛选条件配置
    if (data.selectConfig) {
      selectConfig.value = JSON.parse(data.selectConfig)
    }
  } catch (error) {
    console.error('加载预览数据失败:', error)
    ElMessage.error('加载报表数据失败，请稍后重试')
    loading.value = false
  }
}

// 处理筛选条件变化
const handleSearch = async (params: any) => {
  const data = {
    factoryId: params.factoryId,
    dataTime: params.dataTime,
    format: params.format,
  }
  const res = (await ReportApi.getReportFillByCode(reportCode.value, 2, data)) as any
  if (res.code === 0 && res.data != null) {
    let data = res.data

    option.value = {
      ...option.value,
      gridKey: data.excelId,
      data: data.sheetConfigList
    }

    nextTick(() => {
      if (luckysheetRef.value) {
        luckysheetRef.value.initLuckysheet(option.value)
      } else {
        console.error('luckysheetRef未就绪')
        ElMessage.error('报表初始化失败，请刷新页面重试')
      }
      // 数据加载和初始化完成后，隐藏加载指示器
      loading.value = false
    })
  } else {
    console.error('获取预览数据失败:', res)
    ElMessage.error('获取报表数据失败：' + (res.msg || '未知错误'))
    loading.value = false
  }
}

// 处理重置
const handleReset = () => {
  console.log('重置筛选条件')
  // 这里可以处理重置操作
}

// 处理导出
const handleExport = () => {
  luckysheetRef.value.exportExcel(`${route.query.reportName}.xlsx`)
}

// 处理提交
const handleSubmit = async () => {
  if (!luckysheetRef.value) {
    ElMessage.warning('报表尚未加载完成，请稍后再试')
    return
  }

  loading.value = true
  try {
    const excelJson = luckysheet.toJson()
    const submitData = {
      excelId: option.value.gridKey,
      reportCode: reportCode.value,
      data: excelJson.data
    }

    const res = await ReportApi.saveReportFillDataByCode(submitData)
    if (res.code === 0) {
      ElMessage.success('提交成功')
    } else {
      ElMessage.error(res.msg || '提交失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 获取路由参数 -- 报表编码
  reportCode.value = route.query.reportCode

  if (!reportCode.value) {
    ElMessage.warning('缺少报表编码参数')
    return
  }

  handlePreviewData()
})

onUnmounted(() => {
  // 组件卸载时移除事件监听
  emitter.off('preview-data', handlePreviewData)
})
</script>
<style scoped lang="scss">
.report-page {
  width: 100%;
  height: calc(100vh - 150px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;

  .report-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
  }

  .report-toolbar {
    margin-left: -2px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    height: 40px;
    padding: 0;
    border-radius: 4px;
  }

  :deep(.el-form) {
    margin-bottom: 0;
    display: flex;
    align-items: center;
  }

  :deep(.el-form-item) {
    margin-bottom: 0;
    margin-right: 16px;
  }

  :deep(.el-form-item__content) {
    display: flex;
    align-items: center;
  }

  // 调整查询按钮样式
  :deep(.el-form-item:last-child .el-button--primary) {
    background-color: #0275bb;
    border-color: #0275bb;
  }

  // 调整提交按钮样式
  .report-toolbar .el-button--primary {
    background-color: #409eff;
    border-color: #409eff;
  }

  .test-panel {
    padding: 10px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }
}
</style>
