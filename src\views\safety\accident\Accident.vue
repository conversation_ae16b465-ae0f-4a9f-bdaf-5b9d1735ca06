<template>
  <div class="accident-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>事故管理</span>
        </div>
      </template>
      <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabClick">
        <el-tab-pane label="事故分类" name="classification">
          <accident-classification v-if="activeTab === 'classification'" />
        </el-tab-pane>
        <el-tab-pane label="事故登记" name="register">
          <accident-register v-if="activeTab === 'register'" />
        </el-tab-pane>
        <el-tab-pane label="事故查询" name="query">
          <accident-query v-if="activeTab === 'query'" />
        </el-tab-pane>
        <el-tab-pane label="案例库" name="cases">
          <accident-cases v-if="activeTab === 'cases'" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import AccidentClassification from './components/AccidentClassification.vue'
import AccidentRegister from './components/AccidentRegister.vue'
import AccidentQuery from './components/AccidentQuery.vue'
import AccidentCases from './components/AccidentCases.vue'

const activeTab = ref('classification')

const handleTabClick = () => {
  // 处理标签页点击事件，如有需要可扩展
}
</script>

<style lang="scss" scoped>
.accident-container {
  padding: 10px;
  .box-card {
    width: 100%;
    margin-bottom: 20px;
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    :deep(.el-tabs__content) {
      padding: 20px;
      min-height: calc(100vh - 250px);
    }
  }
}
</style>
