<template>
  <div class="w-full h-[calc(100vh-170px)] flex flex-col">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">个人关注</span>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-200px)] flex flex-col">
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border>
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="terminalId" label="设备编号" align="center" />
                <el-table-column prop="name" label="设备名称" align="center" />
                <el-table-column prop="value" label="当前值" align="center" />
                <el-table-column prop="unit" label="单位" align="center" />
                <el-table-column prop="updateTime" label="更新时间" align="center" />
                <el-table-column label="操作" width="150" align="center">
                  <template #default="{ row }">
                    <el-button type="danger" link @click="handleDelete(row)"> 取消关注 </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const tableData = ref([
  {
    terminalId: 'WLWSB202301',
    name: '物联网设备01',
    value: '25.5',
    unit: '℃',
    updateTime: '2024-01-18 15:30:00'
  },
  {
    terminalId: 'WLWSB202302',
    name: '物联网设备02',
    value: '65',
    unit: 'dB',
    updateTime: '2024-01-18 15:30:00'
  }
])

const handleDelete = (row: any) => {
  ElMessage.success(`已取消关注设备: ${row.name}`)
}
</script>

<style scoped lang="scss"></style>
