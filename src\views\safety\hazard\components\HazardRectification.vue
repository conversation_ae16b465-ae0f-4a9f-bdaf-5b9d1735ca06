<template>
  <div class="hazard-rectification">
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">新增整改任务</el-button>
      <el-input
        v-model="searchKeyword"
        placeholder="请输入关键字搜索"
        style="width: 200px; margin-left: 16px"
        clearable
      />
      <el-select v-model="status" placeholder="整改状态" style="width: 120px; margin-left: 16px">
        <el-option label="全部" value="" />
        <el-option label="待整改" value="pending" />
        <el-option label="整改中" value="processing" />
        <el-option label="待验收" value="waiting" />
        <el-option label="已完成" value="completed" />
      </el-select>
    </div>

    <el-table :data="rectificationList" style="width: 100%; margin-top: 16px">
      <el-table-column prop="code" label="任务编号" width="120" />
      <el-table-column prop="hazardName" label="隐患名称" width="180" />
      <el-table-column prop="department" label="责任部门" width="120" />
      <el-table-column prop="responsible" label="责任人" width="120" />
      <el-table-column prop="deadline" label="整改期限" width="180" />
      <el-table-column prop="measures" label="整改措施" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="progress" label="进度" width="200">
        <template #default="{ row }">
          <el-progress :percentage="row.progress" :status="getProgressStatus(row)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button link type="primary" @click="handleView(row)">查看</el-button>
          <el-button link type="success" @click="handleUpdateProgress(row)">更新进度</el-button>
          <el-button link type="warning" @click="handleAcceptance(row)">验收</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const searchKeyword = ref('')
const status = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 模拟数据
const rectificationList = ref([
  {
    code: 'RECT001',
    hazardName: '设备防护装置缺失',
    department: '生产部',
    responsible: '张三',
    deadline: '2024-04-20',
    measures: '1. 采购防护装置\n2. 安装调试\n3. 安全培训',
    status: '整改中',
    progress: 60
  }
])

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    '待整改': 'info',
    '整改中': 'warning',
    '待验收': 'success',
    '已完成': 'success'
  }
  return types[status] || 'info'
}

const getProgressStatus = (row: any) => {
  if (row.status === '已完成') return 'success'
  if (row.status === '待验收') return 'warning'
  return ''
}

const handleAdd = () => {
  // TODO: 打开新增整改任务弹窗
}

const handleEdit = (row: any) => {
  // TODO: 打开编辑整改任务弹窗
}

const handleView = (row: any) => {
  // TODO: 打开查看整改任务弹窗
}

const handleUpdateProgress = (row: any) => {
  // TODO: 打开更新进度弹窗
}

const handleAcceptance = (row: any) => {
  // TODO: 打开验收弹窗
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  // TODO: 重新加载数据
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // TODO: 重新加载数据
}
</script>

<style lang="scss" scoped>
.hazard-rectification {
  .operation-bar {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 