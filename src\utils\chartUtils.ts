import * as echarts from 'echarts'

/**
 * ECharts容器检测和初始化工具类
 * 解决Tab切换、动态显示等场景下的DOM尺寸问题
 */
export class ChartUtils {
  /**
   * 等待容器准备就绪
   * @param container DOM容器元素
   * @param maxRetries 最大重试次数
   * @param checkInterval 检查间隔(ms)
   * @returns Promise<boolean> 是否准备就绪
   */
  static waitForContainer(
    container: HTMLElement, 
    maxRetries: number = 30, 
    checkInterval: number = 100
  ): Promise<boolean> {
    return new Promise((resolve) => {
      let retries = 0
      
      const checkContainer = () => {
        const { clientWidth, clientHeight } = container
        const computedStyle = window.getComputedStyle(container)
        const isVisible = computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden'
        const parentVisible = container.offsetParent !== null
        
        console.log(`[ChartUtils] 容器检查 ${retries + 1}/${maxRetries}:`, {
          clientWidth,
          clientHeight,
          isVisible,
          parentVisible,
          display: computedStyle.display,
          visibility: computedStyle.visibility
        })
        
        if (clientWidth > 0 && clientHeight > 0 && isVisible && parentVisible) {
          console.log('[ChartUtils] 容器准备就绪')
          resolve(true)
          return
        }
        
        retries++
        if (retries >= maxRetries) {
          console.warn('[ChartUtils] 容器准备超时，强制初始化', {
            finalWidth: clientWidth,
            finalHeight: clientHeight,
            isVisible,
            parentVisible
          })
          resolve(false)
          return
        }
        
        setTimeout(checkContainer, checkInterval)
      }
      
      checkContainer()
    })
  }

  /**
   * 安全初始化ECharts实例
   * @param container DOM容器元素
   * @param option ECharts配置选项
   * @param theme 主题
   * @returns Promise<echarts.ECharts | null>
   */
  static async safeInitChart(
    container: HTMLElement,
    option: any,
    theme?: string
  ): Promise<echarts.ECharts | null> {
    console.log('[ChartUtils] 开始安全初始化图表')
    
    if (!container) {
      console.warn('[ChartUtils] 容器不存在')
      return null
    }

    // 等待容器准备就绪
    await this.waitForContainer(container)

    try {
      // 检查是否已有图表实例
      const existingInstance = echarts.getInstanceByDom(container)
      if (existingInstance) {
        console.log('[ChartUtils] 销毁已存在的图表实例')
        existingInstance.dispose()
      }

      console.log('[ChartUtils] 创建新的图表实例')
      const chart = echarts.init(container, theme)
      
      chart.setOption(option)
      console.log('[ChartUtils] 图表配置完成')

      // 延迟resize确保正确渲染
      setTimeout(() => {
        if (chart && !chart.isDisposed()) {
          chart.resize()
          console.log('[ChartUtils] 图表resize完成')
        }
      }, 100)

      return chart
    } catch (error) {
      console.error('[ChartUtils] 初始化图表失败:', error)
      return null
    }
  }

  /**
   * 安全销毁图表实例
   * @param chart ECharts实例
   */
  static safeDisposeChart(chart: echarts.ECharts | null): void {
    if (chart && !chart.isDisposed()) {
      console.log('[ChartUtils] 销毁图表实例')
      chart.dispose()
    }
  }

  /**
   * 安全调整图表尺寸
   * @param chart ECharts实例
   */
  static safeResizeChart(chart: echarts.ECharts | null): void {
    if (chart && !chart.isDisposed()) {
      chart.resize()
    }
  }

  /**
   * 批量调整图表尺寸
   * @param charts ECharts实例数组
   */
  static batchResizeCharts(charts: (echarts.ECharts | null)[]): void {
    charts.forEach(chart => {
      this.safeResizeChart(chart)
    })
  }

  /**
   * 创建窗口大小变化监听器
   * @param charts ECharts实例数组
   * @param delay 延迟时间(ms)
   * @returns 清理函数
   */
  static createResizeListener(
    charts: (echarts.ECharts | null)[],
    delay: number = 300
  ): () => void {
    let resizeTimer: number | null = null
    
    const handleResize = () => {
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }
      
      resizeTimer = window.setTimeout(() => {
        console.log('[ChartUtils] 窗口大小变化，调整图表尺寸')
        this.batchResizeCharts(charts)
      }, delay)
    }

    window.addEventListener('resize', handleResize)
    
    // 返回清理函数
    return () => {
      window.removeEventListener('resize', handleResize)
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }
    }
  }

  /**
   * Tab切换时的图表处理
   * @param charts ECharts实例数组
   * @param delay 延迟时间(ms)
   */
  static handleTabSwitch(
    charts: (echarts.ECharts | null)[],
    delay: number = 300
  ): void {
    setTimeout(() => {
      console.log('[ChartUtils] Tab切换，调整图表尺寸')
      this.batchResizeCharts(charts)
    }, delay)
  }
}

/**
 * ECharts管理器类
 * 用于管理多个图表实例的生命周期
 */
export class ChartManager {
  private charts: Map<string, echarts.ECharts | null> = new Map()
  private resizeCleanup: (() => void) | null = null

  /**
   * 注册图表实例
   * @param key 图表标识
   * @param chart 图表实例
   */
  register(key: string, chart: echarts.ECharts | null): void {
    // 如果已存在同名图表，先销毁
    const existing = this.charts.get(key)
    if (existing) {
      ChartUtils.safeDisposeChart(existing)
    }
    
    this.charts.set(key, chart)
  }

  /**
   * 获取图表实例
   * @param key 图表标识
   * @returns 图表实例
   */
  get(key: string): echarts.ECharts | null {
    return this.charts.get(key) || null
  }

  /**
   * 获取所有图表实例
   * @returns 图表实例数组
   */
  getAll(): (echarts.ECharts | null)[] {
    return Array.from(this.charts.values())
  }

  /**
   * 调整所有图表尺寸
   */
  resizeAll(): void {
    ChartUtils.batchResizeCharts(this.getAll())
  }

  /**
   * 启用窗口大小变化监听
   * @param delay 延迟时间(ms)
   */
  enableResizeListener(delay: number = 300): void {
    this.disableResizeListener()
    this.resizeCleanup = ChartUtils.createResizeListener(this.getAll(), delay)
  }

  /**
   * 禁用窗口大小变化监听
   */
  disableResizeListener(): void {
    if (this.resizeCleanup) {
      this.resizeCleanup()
      this.resizeCleanup = null
    }
  }

  /**
   * 销毁所有图表
   */
  disposeAll(): void {
    this.charts.forEach(chart => {
      ChartUtils.safeDisposeChart(chart)
    })
    this.charts.clear()
    this.disableResizeListener()
  }

  /**
   * 处理Tab切换
   * @param delay 延迟时间(ms)
   */
  handleTabSwitch(delay: number = 300): void {
    ChartUtils.handleTabSwitch(this.getAll(), delay)
  }
}
