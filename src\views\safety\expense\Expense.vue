<template>
  <div class="safety-expense">
    <el-card class="box-card">
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="费用分类" name="category">
          <expense-category />
        </el-tab-pane>
        <el-tab-pane label="年度预算" name="budget">
          <expense-budget />
        </el-tab-pane>
        <el-tab-pane label="费用明细" name="detail">
          <expense-detail />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import ExpenseCategory from './components/ExpenseCategory.vue'
import ExpenseBudget from './components/ExpenseBudget.vue'
import ExpenseDetail from './components/ExpenseDetail.vue'

export default {
  name: 'SafetyExpense',
  components: {
    ExpenseCategory,
    ExpenseBudget,
    ExpenseDetail
  },
  data() {
    return {
      activeTab: 'category'
    }
  }
}
</script>

<style lang="scss" scoped>
.safety-expense {
  padding: 20px;
  
  .box-card {
    width: 100%;
    min-height: calc(100vh - 160px);
  }
}
</style>
