<template>
  <el-dialog
    v-model="dialogVisible"
    title="设备检测记录"
    width="650px"
    append-to-body
    destroy-on-close
  >
    <div class="device-info bg-[var(--el-fill-color-light)] p-15px rounded mb-20px">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="label">设备编号：</div>
          <div class="value">{{ deviceInfo.deviceCode }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">设备名称：</div>
          <div class="value">{{ deviceInfo.deviceName }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">设备类型：</div>
          <div class="value">
            <el-tag v-if="deviceInfo.deviceType === '流量计'" type="success" size="small">{{ deviceInfo.deviceType }}</el-tag>
            <el-tag v-else-if="deviceInfo.deviceType === '压力表'" type="info" size="small">{{ deviceInfo.deviceType }}</el-tag>
            <el-tag v-else-if="deviceInfo.deviceType === '防雷设备'" type="warning" size="small">{{ deviceInfo.deviceType }}</el-tag>
            <el-tag v-else-if="deviceInfo.deviceType === '起重设备'" type="danger" size="small">{{ deviceInfo.deviceType }}</el-tag>
            <el-tag v-else size="small">{{ deviceInfo.deviceType }}</el-tag>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mt-10px">
        <el-col :span="16">
          <div class="label">安装位置：</div>
          <div class="value">{{ deviceInfo.location }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">上次检测日期：</div>
          <div class="value">{{ deviceInfo.lastInspectionDate }}</div>
        </el-col>
      </el-row>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检测日期" prop="inspectionDate">
            <el-date-picker
              v-model="formData.inspectionDate"
              type="date"
              placeholder="选择日期"
              class="w-full"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检测人员" prop="inspector">
            <el-input v-model="formData.inspector" placeholder="请输入检测人员" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检测结果" prop="result">
            <el-select v-model="formData.result" placeholder="请选择检测结果" class="w-full">
              <el-option label="合格" value="pass" />
              <el-option label="不合格" value="fail" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="下次检测日期" prop="nextInspectionDate">
            <el-date-picker
              v-model="formData.nextInspectionDate"
              type="date"
              placeholder="选择日期"
              class="w-full"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 流量计特有检测项 -->
      <div v-if="deviceInfo.deviceType === '流量计'">
        <el-divider content-position="center">流量计检测指标</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="精度" prop="accuracy">
              <el-input v-model="formData.accuracy" placeholder="请输入精度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流量读数" prop="flowReading">
              <el-input v-model="formData.flowReading" placeholder="请输入流量读数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="零点误差" prop="zeroError">
              <el-input v-model="formData.zeroError" placeholder="请输入零点误差" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 压力表特有检测项 -->
      <div v-if="deviceInfo.deviceType === '压力表'">
        <el-divider content-position="center">压力表检测指标</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指示误差" prop="indicationError">
              <el-input v-model="formData.indicationError" placeholder="请输入指示误差" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回程误差" prop="returnError">
              <el-input v-model="formData.returnError" placeholder="请输入回程误差" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="外观检查" prop="appearanceCheck">
              <el-select v-model="formData.appearanceCheck" placeholder="请选择检查结果" class="w-full">
                <el-option label="良好" value="good" />
                <el-option label="合格" value="pass" />
                <el-option label="不合格" value="fail" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 防雷设备特有检测项 -->
      <div v-if="deviceInfo.deviceType === '防雷设备'">
        <el-divider content-position="center">防雷设备检测指标</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="接地电阻" prop="groundingResistance">
              <el-input v-model="formData.groundingResistance" placeholder="请输入接地电阻" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="连接电阻" prop="connectionResistance">
              <el-input v-model="formData.connectionResistance" placeholder="请输入连接电阻" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="外观检查" prop="appearanceCheck">
              <el-select v-model="formData.appearanceCheck" placeholder="请选择检查结果" class="w-full">
                <el-option label="良好" value="good" />
                <el-option label="合格" value="pass" />
                <el-option label="不合格" value="fail" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="检测说明" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入检测说明或备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="上传附件">
            <el-upload
              action="#"
              list-type="picture-card"
              :auto-upload="false"
              :limit="5"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :on-change="handleChange"
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="text-xs text-gray-400 mt-5px">
                  支持jpg、png、pdf格式，单个文件不超过5MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'

const emit = defineEmits(['success'])

// 对话框状态
const dialogVisible = ref(false)

// 当前设备信息
const deviceInfo = ref({
  id: 0,
  deviceCode: '',
  deviceName: '',
  deviceType: '',
  location: '',
  lastInspectionDate: '',
  nextInspectionDate: '',
  inspectionCycle: ''
})

// 表单引用
const formRef = ref<FormInstance>()

// 上传文件列表
const fileList = ref<UploadFile[]>([])

// 表单数据
const formData = reactive({
  inspectionDate: new Date().toISOString().split('T')[0], // 默认为当前日期
  inspector: '',
  result: 'pass',
  nextInspectionDate: '',
  remarks: '',
  // 流量计特有字段
  accuracy: '',
  flowReading: '',
  zeroError: '',
  // 压力表特有字段
  indicationError: '',
  returnError: '',
  appearanceCheck: '',
  // 防雷设备特有字段
  groundingResistance: '',
  connectionResistance: ''
})

// 验证规则
const rules = reactive<FormRules>({
  inspectionDate: [{ required: true, message: '请选择检测日期', trigger: 'change' }],
  inspector: [{ required: true, message: '请输入检测人员', trigger: 'blur' }],
  result: [{ required: true, message: '请选择检测结果', trigger: 'change' }],
  nextInspectionDate: [{ required: true, message: '请选择下次检测日期', trigger: 'change' }]
})

// 打开对话框
const open = (row: any) => {
  if (!row || !row.id) {
    ElMessage.error('数据错误')
    return
  }

  dialogVisible.value = true
  
  // 设置设备信息
  Object.assign(deviceInfo.value, row)
  
  // 重置表单
  resetForm()
  
  // 设置下次检测日期
  calculateNextInspectionDate()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  formData.inspectionDate = new Date().toISOString().split('T')[0]
  formData.inspector = ''
  formData.result = 'pass'
  formData.nextInspectionDate = ''
  formData.remarks = ''
  formData.accuracy = ''
  formData.flowReading = ''
  formData.zeroError = ''
  formData.indicationError = ''
  formData.returnError = ''
  formData.appearanceCheck = ''
  formData.groundingResistance = ''
  formData.connectionResistance = ''
  
  fileList.value = []
}

// 计算下次检测日期
const calculateNextInspectionDate = () => {
  if (!deviceInfo.value.inspectionCycle) return
  
  const inspectionDate = new Date(formData.inspectionDate)
  const match = deviceInfo.value.inspectionCycle.match(/(\d+)([^\d]+)/)
  
  if (match) {
    const value = parseInt(match[1])
    const unit = match[2].trim()
    
    if (unit.includes('月')) {
      inspectionDate.setMonth(inspectionDate.getMonth() + value)
    } else if (unit.includes('年')) {
      inspectionDate.setFullYear(inspectionDate.getFullYear() + value)
    } else if (unit.includes('天')) {
      inspectionDate.setDate(inspectionDate.getDate() + value)
    }
    
    formData.nextInspectionDate = inspectionDate.toISOString().split('T')[0]
  }
}

// 附件预览
const handlePreview = (file: UploadFile) => {
  console.log('预览文件:', file)
}

// 附件移除
const handleRemove = (file: UploadFile, fileList: UploadFile[]) => {
  console.log('移除文件:', file, fileList)
}

// 附件改变
const handleChange = (file: UploadFile, fileList: UploadFile[]) => {
  console.log('文件改变:', file, fileList)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构建提交数据
    const submitData = {
      deviceId: deviceInfo.value.id,
      ...formData
    }

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500))
    // await createInspectionApi(submitData)

    ElMessage.success('检测记录保存成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 暴露给父组件的方法
defineExpose({ open })
</script>

<style scoped>
.device-info {
  font-size: 14px;
}

.label {
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.value {
  font-weight: 500;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 100px;
}
</style> 