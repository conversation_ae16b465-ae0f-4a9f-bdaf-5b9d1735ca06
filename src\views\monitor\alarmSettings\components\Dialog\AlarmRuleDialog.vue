<template>
  <div>
    <el-dialog v-model="dialogVisible" :title="editMode ? '编辑告警规则' : '新增告警规则'" width="60%" :before-close="handleClose">
      <div class="w-full p-4">
        <!-- Rule Type Selection -->
        <div class="mb-6 flex justify-center">
          <el-radio-group v-model="form.ruleType" size="large" @change="handleRuleTypeChange" class="rule-type-selector"
            :disabled="ruleTypeDisabled">
            <el-radio-button label="single">单因子判断</el-radio-button>
            <el-radio-button label="combination">组合因子判断</el-radio-button>
          </el-radio-group>
        </div>

        <!-- Form -->
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="right">
          <!-- Common Fields -->
          <el-form-item label="规则名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入规则名称" />
          </el-form-item>

          <el-form-item label="告警级别" prop="level">
            <el-select v-model="form.level" placeholder="请选择告警级别" class="w-full">
              <el-option label="I级 (重要)" value="I">
                <div class="flex items-center">
                  <div class="w-4 h-4 rounded-full bg-red-500 mr-2"></div>
                  <span>I级 (重要)</span>
                </div>
              </el-option>
              <el-option label="II级 (一般)" value="II">
                <div class="flex items-center">
                  <div class="w-4 h-4 rounded-full bg-orange-400 mr-2"></div>
                  <span>II级 (一般)</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <!-- Single Factor Fields -->
          <template v-if="form.ruleType === 'single'">
            <div class="bg-gray-50 p-5 rounded-lg border border-gray-100 mb-4">
              <h3 class="text-base font-medium mb-4 text-gray-700 border-b border-gray-100 pb-2">单因子判断条件</h3>

              <el-form-item label="监测因子" prop="factor">
                <el-tree-select v-model="form.factor" :data="deviceFactorOptions" placeholder="请选择监测因子" class="w-full"
                  node-key="indicatorId" :render-after-expand="false" filterable @change="handleFactorChange"
                  check-strictly default-expand-all :show-checkbox="false" highlight-current>
                  <template #default="{ node, data }">
                    <div class="flex items-center justify-between w-full">
                      <!-- 设备节点样式 -->
                      <span v-if="data.type === 'group'" class="font-medium text-gray-700">{{ node.label }}</span>
                      <!-- 监测因子节点样式 -->
                      <div v-else class="flex items-center justify-between w-full">
                        <span class="text-sm">{{ node.label }}</span>
                        <span v-if="data.unit" class="text-gray-400 text-xs ml-2">{{ data.unit }}</span>
                      </div>
                    </div>
                  </template>
                </el-tree-select>
              </el-form-item>

              <el-form-item label="比较运算符" prop="operator">
                <el-select v-model="form.operator" placeholder="请选择比较运算符" class="w-full"
                  @change="buildSingleFactorExpression">
                  <!-- 当选中的因子类型为7(布尔值)时，只显示等于和不等于 -->
                  <template v-if="selectedFactorType === '7'">
                    <el-option label="等于 ==" value="==" />
                    <el-option label="不等于 !=" value="!=" />
                  </template>
                  <!-- 其他类型因子显示全部运算符 -->
                  <template v-else>
                    <el-option label="大于 >" value=">" />
                    <el-option label="小于 <" value="<" />
                    <el-option label="等于 ==" value="==" />
                    <el-option label="不等于 !=" value="!=" />
                    <el-option label="大于等于 ≥" value=">=" />
                    <el-option label="小于等于 ≤" value="<=" />
                  </template>
                </el-select>
              </el-form-item>

              <div class="flex gap-4">
                <el-form-item label="阈值" prop="threshold" class="flex-1">
                  <!-- 当选中的因子类型为7(布尔值)时，使用下拉框选择TRUE/FALSE -->
                  <el-select v-if="selectedFactorType === '7'" v-model="form.threshold" class="w-full"
                    @change="buildSingleFactorExpression">
                    <el-option label="true" value="true" />
                    <el-option label="false" value="false" />
                  </el-select>
                  <!-- 其他类型因子使用普通输入框 -->
                  <el-input v-else v-model="form.threshold" placeholder="请输入阈值" @input="buildSingleFactorExpression">
                    <template #append v-if="form.unit">{{ form.unit }}</template>
                  </el-input>
                </el-form-item>

                <el-form-item label="单位" prop="unit" class="flex-1">
                  <el-input v-model="form.unit" placeholder="/" disabled />
                </el-form-item>
              </div>

              <!-- 单因子表达式预览 -->
              <div class="border border-gray-100 rounded p-4 bg-white mt-4">
                <div class="font-bold mb-2 flex items-center">
                  <el-icon class="mr-1">
                    <View />
                  </el-icon>
                  表达式预览
                </div>
                <div class="p-3 border border-gray-100 rounded bg-gray-50 min-h-[60px]">
                  <div v-if="singleFactorExpression" class="text-blue-600 font-mono text-base font-semibold">{{
                    singleFactorExpression
                  }}</div>
                  <!-- 添加转换后的表达式显示，将code替换为name -->
                  <div v-if="singleFactorNameExpression" class="text-gray-400 text-xs mt-2">
                    {{ singleFactorNameExpression }}
                  </div>
                  <div v-else class="text-gray-400">请选择监测因子、运算符和阈值生成表达式</div>
                </div>
              </div>
            </div>
          </template>

          <!-- Combination Factor Fields -->
          <template v-else>
            <div class="bg-gray-50 p-5 rounded-lg border border-gray-100 mb-4">
              <h3 class="text-base font-medium mb-4 text-gray-700 border-b border-gray-100 pb-2">组合因子判断条件</h3>

              <el-form-item label="条件组合方式" prop="combinationType">
                <el-radio-group v-model="combinationType" class="flex flex-wrap">
                  <el-radio label="all" class="combination-radio">
                    <div class="flex items-center">
                      <el-icon class="mr-1 text-blue-500">
                        <Connection />
                      </el-icon>
                      满足所有条件（AND）
                    </div>
                  </el-radio>
                  <el-radio label="any" class="combination-radio">
                    <div class="flex items-center">
                      <el-icon class="mr-1 text-green-500">
                        <SwitchButton />
                      </el-icon>
                      满足任一条件（OR）
                    </div>
                  </el-radio>
                  <el-radio label="custom" class="combination-radio">
                    <div class="flex items-center">
                      <el-icon class="mr-1 text-purple-500">
                        <Operation />
                      </el-icon>
                      自定义组合
                    </div>
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 条件列表 -->
              <div class="border border-gray-100 rounded p-4 mb-4 bg-white">
                <div class="font-bold mb-2 flex items-center">
                  <el-icon class="mr-1">
                    <List />
                  </el-icon>
                  条件列表
                </div>

                <!-- 条件项 -->
                <div v-for="(condition, index) in conditions" :key="index"
                  class="mb-3 p-4 border border-gray-100 rounded bg-gray-50 condition-card">
                  <div class="flex items-center justify-between mb-3">
                    <div class="font-medium flex items-center">
                      <div
                        class="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm mr-2">
                        {{ index + 1 }}
                      </div>
                      <span>条件 {{ index + 1 }}</span>
                    </div>
                    <el-button type="danger" size="small" @click="removeCondition(index)" :icon="Delete">删除</el-button>
                  </div>

                  <div class="grid grid-cols-4 gap-4">
                    <div>
                      <label class="block text-sm mb-1 text-gray-600">监测因子</label>
                      <el-tree-select v-model="condition.factor" :data="deviceFactorOptions" placeholder="选择因子"
                        class="w-full" node-key="indicatorId" :render-after-expand="false" filterable
                        @change="(value) => handleConditionFactorChange(value, index)" check-strictly default-expand-all
                        :show-checkbox="false" highlight-current>
                        <template #default="{ node, data }">
                          <div class="flex items-center justify-between w-full">
                            <!-- 设备节点样式 -->
                            <span v-if="data.type === 'group'" class="font-medium text-gray-700">{{ node.label }}</span>
                            <!-- 监测因子节点样式 -->
                            <div v-else class="flex items-center justify-between w-full">
                              <span class="text-sm">{{ node.label }}</span>
                              <span v-if="data.unit" class="text-gray-400 text-xs ml-2">{{ data.unit }}</span>
                            </div>
                          </div>
                        </template>
                      </el-tree-select>
                    </div>

                    <div>
                      <label class="block text-sm mb-1 text-gray-600">比较运算符</label>
                      <el-select v-model="condition.operator" placeholder="选择运算符" class="w-full">
                        <!-- 当选中的因子类型为7(布尔值)时，只显示等于和不等于 -->
                        <template v-if="getConditionFactorType(condition.factor) === '7'">
                          <el-option label="等于 ==" value="==" />
                          <el-option label="不等于 !=" value="!=" />
                        </template>
                        <!-- 其他类型因子显示全部运算符 -->
                        <template v-else>
                          <el-option label="大于 >" value=">" />
                          <el-option label="小于 <" value="<" />
                          <el-option label="等于 ==" value="==" />
                          <el-option label="不等于 !=" value="!=" />
                          <el-option label="大于等于 ≥" value=">=" />
                          <el-option label="小于等于 ≤" value="<=" />
                        </template>
                      </el-select>
                    </div>

                    <div>
                      <label class="block text-sm mb-1 text-gray-600">阈值</label>
                      <!-- 当选中的因子类型为7(布尔值)时，使用下拉框选择TRUE/FALSE -->
                      <el-select v-if="getConditionFactorType(condition.factor) === '7'" v-model="condition.value"
                        class="w-full">
                        <el-option label="true" value="true" />
                        <el-option label="false" value="false" />
                      </el-select>
                      <!-- 其他类型因子使用普通输入框 -->
                      <el-input v-else v-model="condition.value" placeholder="输入阈值" class="w-full" />
                    </div>

                    <div>
                      <label class="block text-sm mb-1 text-gray-600">单位</label>
                      <el-input v-model="condition.unit" placeholder="/" disabled />
                    </div>
                  </div>
                </div>

                <!-- 添加条件按钮 -->
                <el-tooltip :content="canAddCondition ? '添加新条件' : '请先完成所有现有条件的填写'" placement="top"
                  :disabled="canAddCondition">
                  <el-button type="primary" plain @click="addCondition" class="w-full mt-2"
                    :disabled="!canAddCondition">
                    <el-icon class="mr-1">
                      <Plus />
                    </el-icon>添加条件
                  </el-button>
                </el-tooltip>
              </div>

              <!-- 自定义组合逻辑 -->
              <template v-if="combinationType === 'custom' && conditions.length > 1">
                <div class="border border-gray-100 rounded p-4 mb-4 bg-white">
                  <div class="font-bold mb-2 flex items-center">
                    <el-icon class="mr-1">
                      <Operation />
                    </el-icon>
                    自定义组合逻辑
                  </div>

                  <div class="p-4 bg-gray-50 rounded mb-3 border border-gray-100">
                    <p class="text-sm text-gray-600 mb-2">使用条件编号和逻辑运算符组合表达式：</p>
                    <ul class="text-sm text-gray-600 mb-2 list-disc pl-4">
                      <li>条件编号：条件1, 条件2, 条件3...</li>
                      <li>逻辑运算符：AND (&&), OR (||)</li>
                      <li>可以使用括号 ( ) 调整优先级</li>
                    </ul>
                  </div>

                  <div class="flex flex-wrap gap-2 mb-3">
                    <el-button v-for="i in conditions.length" :key="`cond-${i}`" size="small" type="success" plain
                      @click="insertToLogic(`条件${i}`)">
                      条件{{ i }}
                    </el-button>
                    <el-button size="small" type="primary" plain @click="insertToLogic(' AND ')">AND</el-button>
                    <el-button size="small" type="warning" plain @click="insertToLogic(' OR ')">OR</el-button>
                    <el-button size="small" type="info" plain @click="insertToLogic(' ( ')">(</el-button>
                    <el-button size="small" type="info" plain @click="insertToLogic(' ) ')">)</el-button>
                  </div>

                  <el-input v-model="customLogic" type="textarea" :rows="3" placeholder="例如: (条件1 AND 条件2) OR 条件3" />
                </div>
              </template>

              <!-- 表达式预览 -->
              <div class="border border-gray-100 rounded p-4 bg-white">
                <div class="font-bold mb-2 flex items-center">
                  <el-icon class="mr-1">
                    <View />
                  </el-icon>
                  表达式预览
                </div>
                <div class="p-3 border border-gray-100 rounded bg-gray-50 min-h-[60px]">
                  <div v-if="previewExpression" class="text-blue-600 font-mono text-base font-semibold">{{
                    previewExpression }}</div>
                  <!-- 添加转换后的表达式显示，将code替换为name -->
                  <div v-if="previewNameExpression" class="text-gray-400 text-xs mt-2">
                    {{ previewNameExpression }}
                  </div>
                  <div v-else class="text-gray-400">添加条件后将在此处显示预览</div>
                </div>
              </div>
            </div>
          </template>

          <el-form-item label="启用状态">
            <el-switch v-model="form.enabled" active-text="启用" inactive-text="禁用" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
// @ts-nocheck
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { saveAlarmRule, getFactorList, getAlarmRuleDetail } from '@/api/alarm/index'

const emit = defineEmits(['save-rule'])
const appStore = useAppStore()

const dialogVisible = ref(false)
const editMode = ref(false)
const formRef = ref()

// 监测因子列表数据
const factorOptions = ref<any[]>([])

// 设备分组的监测因子列表
const deviceFactorOptions = ref<any[]>([])

// 单因子表达式
const singleFactorExpression = ref('')

// 当前选中的监测因子类型
const selectedFactorType = ref('')

// 规则类型是否禁用编辑（编辑模式时禁用切换）
const ruleTypeDisabled = ref(false)

const form = reactive({
  id: '',
  name: '',
  ruleType: 'single',
  level: '',
  factor: '',
  operator: '',
  threshold: '',
  unit: '',
  expression: '',
  enabled: true,
  remark: '',
  factoryId: ''
})

// 组合因子相关
interface Condition {
  factor: string;
  operator: string;
  value: string;
  unit?: string;   // 添加单位字段
}

const combinationType = ref<'all' | 'any' | 'custom'>('all')
const conditions = ref<Condition[]>([])
const customLogic = ref('')

// 【1】先声明所有函数

// 构建单因子表达式
const buildSingleFactorExpression = () => {
  if (form.factor && form.operator && form.threshold) {
    // 查找选中的监测因子详细信息，获取pointCode用于表达式
    const selectedFactor = factorOptions.value.find(option => option.value === form.factor)
    const pointCode = selectedFactor?.pointCode || form.factor

    // 对于等于操作符，显示和保存时都使用==
    const displayOperator = form.operator === '=' ? '==' : form.operator;
    singleFactorExpression.value = `${pointCode} ${displayOperator} ${form.threshold}`
    form.expression = singleFactorExpression.value
  } else {
    singleFactorExpression.value = ''
  }
}

// 构建组合表达式 (稍后使用computed属性)
const buildExpression = (expr) => {
  form.expression = expr
}

// 添加条件
const addCondition = () => {
  // 检查是否允许添加新条件
  if (!canAddCondition.value) {
    ElMessage.warning('请先完成所有现有条件的填写')
    return
  }

  conditions.value.push({
    factor: '',
    operator: '',
    value: '',
    unit: ''
  })
}

// 移除条件
const removeCondition = (index: number) => {
  conditions.value.splice(index, 1)

  // 如果删除后只剩一个条件，切换到简单模式
  if (conditions.value.length <= 1 && combinationType.value === 'custom') {
    combinationType.value = 'all'
    customLogic.value = ''
  }
}

// 插入到自定义逻辑
const insertToLogic = (text: string) => {
  const textarea = document.activeElement
  if (textarea && textarea.tagName.toLowerCase() === 'textarea') {
    const start = (textarea as HTMLTextAreaElement).selectionStart || 0
    const end = (textarea as HTMLTextAreaElement).selectionEnd || 0
    const value = customLogic.value
    customLogic.value = value.substring(0, start) + text + value.substring(end)

    // 设置光标位置
    setTimeout(() => {
      const textareaElement = textarea as HTMLTextAreaElement
      textareaElement.selectionStart = start + text.length
      textareaElement.selectionEnd = start + text.length
      textareaElement.focus()
    }, 0)
  } else {
    customLogic.value += text
  }
}

// 解析简单条件列表
const parseConditions = (conditionStrings: string[]) => {
  // 清空现有条件列表
  conditions.value = []

  for (const str of conditionStrings) {
    // 增强正则表达式，支持匹配true/false或数值，同时支持==作为等于操作符
    const match = str.match(/([a-zA-Z0-9\u4e00-\u9fa5_]+)\s*(>|<|==|>=|<=|!=)\s*([a-zA-Z0-9\._]+|true|false)/)

    if (match) {
      const pointCode = match[1] // 表达式中使用的是pointCode
      const operator = match[2]
      let value = match[3]

      // 通过pointCode查找因子详细信息
      const selectedFactor = factorOptions.value.find(option => option.pointCode === pointCode)
      const factorType = selectedFactor?.pointType || selectedFactor?.type || ''

      // 处理布尔值数据
      if (factorType === '7') {
        // 如果是布尔类型，将1.0/0.0/1/0转换为true/false
        if (value === '1.0' || value === '1') {
          value = 'true'
        } else if (value === '0.0' || value === '0') {
          value = 'false'
        }
      }

      // 添加到条件列表，factor字段使用indicatorId（用于界面选择）
      conditions.value.push({
        factor: selectedFactor?.value || pointCode, // 使用indicatorId作为factor值
        operator: operator,
        value: value,
        unit: selectedFactor?.unit || ''
      })
    }
  }
}

// 从ruleFactors恢复条件（用于编辑模式，确保设备映射正确）
const restoreConditionsFromRuleFactors = (ruleFactors: any[], expression: string) => {
  // 清空现有条件列表
  conditions.value = []

  // 从ruleFactors恢复条件
  ruleFactors.forEach(ruleFactor => {
    // 通过indicatorId精确查找监测因子
    let selectedFactor = factorOptions.value.find(option =>
      option.indicatorId === ruleFactor.indicatorId
    )

    // 如果通过indicatorId找不到，则通过pointCode查找（兜底）
    if (!selectedFactor) {
      selectedFactor = factorOptions.value.find(option =>
        option.pointCode === (ruleFactor.variable || ruleFactor.factorCode)
      )
    }

    // 处理阈值显示
    let displayValue = ruleFactor.threshold
    const factorType = selectedFactor?.pointType || selectedFactor?.type || ruleFactor.factorType || ''

    // 处理布尔值类型
    if (factorType === '7') {
      // 如果是布尔类型，将1.0/0.0/1/0转换为true/false
      if (displayValue === 1.0 || displayValue === 1 || displayValue === '1.0' || displayValue === '1') {
        displayValue = 'true'
      } else if (displayValue === 0.0 || displayValue === 0 || displayValue === '0.0' || displayValue === '0') {
        displayValue = 'false'
      }
    }

    // 添加到条件列表
    conditions.value.push({
      factor: selectedFactor?.value || (ruleFactor.variable || ruleFactor.factorCode), // 使用indicatorId作为factor值
      operator: ruleFactor.operator || '==',
      value: String(displayValue),
      unit: selectedFactor?.unit || ruleFactor.unit || ''
    })
  })

  // 根据表达式判断组合类型
  if (expression) {
    if (expression.includes('||') && !expression.includes('&&')) {
      combinationType.value = 'any'
    } else if (expression.includes('&&') && !expression.includes('||')) {
      combinationType.value = 'all'
    } else if (ruleFactors.length > 1) {
      // 复杂表达式，设为自定义
      combinationType.value = 'custom'

      // 构建自定义逻辑
      let logic = expression
      ruleFactors.forEach((ruleFactor, index) => {
        const pointCode = ruleFactor.variable || ruleFactor.factorCode
        const conditionPattern = new RegExp(`${pointCode}\\s*(>|<|==|>=|<=|!=)\\s*[a-zA-Z0-9\\._]+`, 'g')
        logic = logic.replace(conditionPattern, `条件${index + 1}`)
      })

      // 替换逻辑运算符
      logic = logic.replace(/\&\&/g, ' AND ').replace(/\|\|/g, ' OR ')
      customLogic.value = logic
    }
  }

  console.log('从ruleFactors恢复的条件:', conditions.value)
}

// 从表达式解析条件（用于编辑模式）
const parseExpression = (expr: string) => {
  if (!expr) return

  // 尝试判断组合类型
  if (expr.includes('||') && !expr.includes('&&')) {
    combinationType.value = 'any'
    const parts = expr.split('||').map(s => s.trim())
    parseConditions(parts)
  } else if (expr.includes('&&') && !expr.includes('||')) {
    combinationType.value = 'all'
    const parts = expr.split('&&').map(s => s.trim())
    parseConditions(parts)
  } else {
    // 复杂表达式，设为自定义
    combinationType.value = 'custom'

    // 增强正则表达式，支持匹配true/false或数值，同时支持==作为等于操作符
    const conditionRegex = /([a-zA-Z0-9\u4e00-\u9fa5_]+)\s*(>|<|==|>=|<=|!=)\s*([a-zA-Z0-9\._]+|true|false)/g
    const matches = Array.from(expr.matchAll(conditionRegex))

    // 创建条件列表
    conditions.value = []

    for (const match of matches) {
      const pointCode = match[1] // 表达式中使用的是pointCode
      const operator = match[2]
      let value = match[3]

      // 通过pointCode查找因子详细信息
      const selectedFactor = factorOptions.value.find(option => option.pointCode === pointCode)
      const factorType = selectedFactor?.pointType || selectedFactor?.type || ''

      // 处理布尔值数据
      if (factorType === '7') {
        // 如果是布尔类型，将1.0/0.0/1/0转换为true/false
        if (value === '1.0' || value === '1') {
          value = 'true'
        } else if (value === '0.0' || value === '0') {
          value = 'false'
        }
      }

      // 添加到条件列表，factor字段使用indicatorId（用于界面选择）
      conditions.value.push({
        factor: selectedFactor?.value || pointCode, // 使用indicatorId作为factor值
        operator: operator,
        value: value,
        unit: selectedFactor?.unit || ''
      })
    }

    // 设置自定义逻辑
    let logic = expr
    matches.forEach((match, index) => {
      const condStr = match[0]
      logic = logic.replace(condStr, `条件${index + 1}`)
    })

    // 替换逻辑运算符
    logic = logic.replace(/\&\&/g, ' AND ').replace(/\|\|/g, ' OR ')
    customLogic.value = logic
  }
}

const resetForm = () => {
  form.id = ''
  form.name = ''
  form.ruleType = 'single'
  form.level = ''
  form.factor = ''
  form.operator = ''
  form.threshold = ''
  form.unit = ''
  form.expression = ''
  form.enabled = true
  form.remark = ''
  form.factoryId = appStore.currentStation?.id || ''

  // 重置表达式
  singleFactorExpression.value = ''

  // 重置组合条件
  conditions.value = []
  combinationType.value = 'all'
  customLogic.value = ''

  // 添加一个默认条件
  addCondition()

  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleRuleTypeChange = (value: string) => {
  if (formRef.value) {
    formRef.value.clearValidate()
  }

  // 如果切换到组合因子，确保至少有一个条件
  if (value === 'combination' && conditions.value.length === 0) {
    addCondition()
  } else if (value === 'single') {
    // 切换到单因子时，重新生成单因子表达式
    buildSingleFactorExpression()
  }
}

// 获取监测因子列表
const fetchFactorList = async (callback?: () => void) => {
  try {
    // 获取当前水厂ID
    const factoryId = form.factoryId || appStore.currentStation?.id || ''

    if (!factoryId) {
      console.error('获取监测因子列表失败: 未能获取到水厂ID')
      ElMessage.warning('未能获取到水厂ID，无法加载监测因子列表')
      return
    }

    // 修改为新的API地址
    const response = await getFactorList(factoryId)

    if (response && response.data && Array.isArray(response.data)) {
      // 处理分组数据结构
      deviceFactorOptions.value = response.data.map((device: any) => ({
        label: device.deviceName, // 设备名称作为分组标签
        value: device.deviceId,  // 设备ID作为分组值
        indicatorId: `device_${device.deviceId}`, // 设备节点使用特殊的indicatorId
        type: 'group', // 标记为分组类型
        disabled: true, // 禁用设备节点选择
        children: device.points.map((point: any) => ({
          label: point.pointName, // 点位名称作为子项标签
          value: point.indicatorId || `${device.deviceId}_${point.pointCode}`, // 使用indicatorId作为唯一标识
          pointCode: point.pointCode, // 保存原始pointCode用于表达式
          pointId: point.pointId,  // 保存点位ID
          indicatorId: point.indicatorId || `${device.deviceId}_${point.pointCode}`, // 保存指标唯一ID
          pointType: point.pointType, // 保存点位类型
          unit: point.unit || '',   // 保存单位信息
          deviceId: device.deviceId, // 保存设备ID
          deviceName: device.deviceName, // 保存设备名称
          leaf: true // 标记为叶子节点
        }))
      }))

      // 同时维护平铺的列表，用于查找和其他操作
      factorOptions.value = []
      response.data.forEach((device: any) => {
        device.points.forEach((point: any) => {
          factorOptions.value.push({
            label: point.pointName,
            value: point.indicatorId || `${device.deviceId}_${point.pointCode}`, // 使用indicatorId作为唯一标识
            pointCode: point.pointCode, // 保存原始pointCode用于表达式
            pointId: point.pointId,
            indicatorId: point.indicatorId || `${device.deviceId}_${point.pointCode}`, // 保存指标唯一ID
            pointType: point.pointType,
            unit: point.unit || '',
            deviceId: device.deviceId,
            deviceName: device.deviceName
          })
        })
      })

      console.log('监测因子列表:', deviceFactorOptions.value)

      // 如果有回调函数，则在数据加载完成后执行
      if (callback) {
        callback();
      }
    } else {
      console.error('获取监测因子列表返回格式异常:', response)
      ElMessage.warning('获取监测因子列表数据格式异常')
    }
  } catch (error) {
    console.error('获取监测因子列表失败:', error)
    ElMessage.error('获取监测因子列表失败')
  }
}

const openDialog = async (row?: any) => {
  resetForm()

  // 获取当前水厂ID
  form.factoryId = appStore.currentStation?.id || ''

  // 定义处理编辑数据的函数
  const processEditData = async (editData?: any) => {
    if (editData) {
      // 编辑模式
      editMode.value = true
      // 禁用规则类型切换
      ruleTypeDisabled.value = true

      Object.keys(form).forEach(key => {
        if (key in editData) {
          form[key] = editData[key]
        }
      })

      // 如果是组合因子规则，优先从ruleFactors恢复条件
      if (editData.ruleType === 'combination') {
        if (editData.ruleFactors && editData.ruleFactors.length > 0) {
          // 从ruleFactors恢复条件，确保设备映射正确
          restoreConditionsFromRuleFactors(editData.ruleFactors, editData.expression)
        } else if (editData.expression) {
          // 兜底：如果没有ruleFactors，则解析表达式
          parseExpression(editData.expression)
        }
      } else if (editData.ruleType === 'single') {
        // 单因子规则处理

        // 从ruleFactors中获取详细信息（如果存在）
        if (editData.ruleFactors && editData.ruleFactors.length > 0) {
          const ruleFactor = editData.ruleFactors[0]; // 获取第一个因子

          // 优先通过indicatorId精确查找监测因子
          let selectedFactor = factorOptions.value.find(option =>
            option.indicatorId === ruleFactor.indicatorId
          );

          // 兜底：如果通过indicatorId找不到，则通过pointCode查找
          if (!selectedFactor) {
            selectedFactor = factorOptions.value.find(option =>
              option.pointCode === (ruleFactor.variable || ruleFactor.factorCode)
            );
          }

          // 设置关键字段，form.factor使用indicatorId
          form.factor = selectedFactor?.value || (ruleFactor.variable || ruleFactor.factorCode);
          form.operator = ruleFactor.operator || form.operator;
          form.threshold = ruleFactor.threshold !== undefined ? ruleFactor.threshold : form.threshold;
          form.unit = ruleFactor.unit || form.unit;

          console.log('从ruleFactors获取的单因子数据:', {
            factor: form.factor,
            indicatorId: ruleFactor.indicatorId,
            pointCode: selectedFactor?.pointCode,
            operator: form.operator,
            threshold: form.threshold,
            unit: form.unit
          });
        }

        // 确保表达式字段存在
        if (!form.expression && form.factor) {
          const selectedFactor = factorOptions.value.find(option => option.value === form.factor);
          const pointCode = selectedFactor?.pointCode || form.factor;
          form.expression = `${pointCode} ${form.operator || '=='} ${form.threshold || '0'}`;
        }

        // 查找选中的监测因子详细信息并设置类型
        const selectedFactor = factorOptions.value.find(option => option.value === form.factor);
        if (selectedFactor) {
          selectedFactorType.value = selectedFactor.pointType || selectedFactor.type || '';

          // 处理布尔值类型
          if (selectedFactorType.value === '7' && form.threshold !== undefined) {
            // 如果是布尔类型，将1.0/0.0/1/0转换为true/false
            const thresholdNum = Number(form.threshold);
            if (thresholdNum === 1 || thresholdNum === 1.0) {
              form.threshold = 'true';
            } else if (thresholdNum === 0 || thresholdNum === 0.0) {
              form.threshold = 'false';
            }

            // 确保使用==作为等于操作符
            if (form.operator === '=') {
              form.operator = '==';
            }
          }

          // 生成表达式预览
          buildSingleFactorExpression();
          console.log('生成的单因子表达式:', singleFactorExpression.value);
        } else {
          console.warn('未找到对应的监测因子:', form.factor);
        }
      }
    } else {
      // 新增模式
      editMode.value = false
      // 允许规则类型切换
      ruleTypeDisabled.value = false
    }

    dialogVisible.value = true
  }

  // 如果是编辑模式，先获取完整的规则数据
  if (row && row.id) {
    try {
      console.log('编辑模式：获取规则详情，ID:', row.id)
      const response = await getAlarmRuleDetail(row.id)

      if (response && response.data) {
        console.log('获取到完整规则数据:', response.data)
        // 获取监测因子列表，并在完成后处理完整的编辑数据
        fetchFactorList(() => processEditData(response.data));
      } else {
        console.error('获取规则详情失败:', response)
        ElMessage.error('获取规则详情失败')
        // 兜底：使用原始数据
        fetchFactorList(() => processEditData(row));
      }
    } catch (error) {
      console.error('获取规则详情异常:', error)
      ElMessage.error('获取规则详情失败')
      // 兜底：使用原始数据
      fetchFactorList(() => processEditData(row));
    }
  } else {
    // 新增模式：获取监测因子列表后直接处理
    fetchFactorList(() => processEditData());
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return ElMessage.error('请完善表单信息')
    }

    // 验证规则类型特定字段
    if (form.ruleType === 'single') {
      if (!form.factor || !form.operator || !form.threshold) {
        return ElMessage.error('请完善单因子判断所需的所有字段')
      }

      // 确保单因子表达式已生成
      buildSingleFactorExpression()
    } else {
      if (!form.expression) {
        return ElMessage.error('请构建逻辑表达式')
      }

      // 验证条件是否完整
      const incompleteCondition = conditions.value.some(c => !c.factor || !c.operator || !c.value)
      if (incompleteCondition) {
        return ElMessage.error('请完善所有条件的因子、运算符和阈值')
      }

      // 验证自定义逻辑
      if (combinationType.value === 'custom' && conditions.value.length > 1) {
        if (!customLogic.value) {
          return ElMessage.error('请输入自定义组合逻辑')
        }

        // 检查是否包含了所有条件
        for (let i = 1; i <= conditions.value.length; i++) {
          if (!customLogic.value.includes(`条件${i}`)) {
            return ElMessage.error(`自定义逻辑中缺少条件${i}`)
          }
        }
      }
    }

    // 构建保存数据
    const saveData: any = {
      id: form.id || undefined,
      name: form.name,
      factoryId: form.factoryId,
      ruleType: form.ruleType,
      level: form.level,
      enabled: form.enabled,
      remark: form.remark || undefined
    }

    // 根据规则类型添加特定字段
    if (form.ruleType === 'single') {
      // 单因子规则
      // 删除顶层字段，只保留在ruleFactors中

      // 只有当表达式存在时才添加
      if (form.expression) {
        saveData.expression = form.expression
      }

      // 获取选中的监测因子详细信息
      const selectedFactor = factorOptions.value.find(option => option.value === form.factor)

      // 添加规则因子
      saveData.ruleFactors = [{
        factorCode: selectedFactor?.pointCode || form.factor,  // factorCode对应pointCode
        factorName: selectedFactor?.label || form.factor,  // 使用监测因子名称
        variable: selectedFactor?.pointCode || form.factor,    // 变量名使用pointCode
        unit: form.unit || selectedFactor?.unit || undefined,  // 使用指定或自动填充的单位
        description: `${selectedFactor?.label || form.factor}监测因子告警规则`,  // 添加简单描述
        indicatorId: selectedFactor?.indicatorId || null,  // 添加指标唯一ID
        factorType: selectedFactor?.pointType || selectedFactor?.type || selectedFactorType.value || '',  // 添加指标类型
        operator: form.operator, // 添加操作符
        threshold: selectedFactorType.value === '7' ?
          (form.threshold === 'true' ? 1.0 : 0.0) :
          Number(form.threshold) // 添加阈值并处理布尔类型
      }]
    } else {
      // 组合因子规则
      // 只有当表达式存在时才添加
      if (form.expression) {
        // 确保表达式中使用的是界面上显示的值（true/false）
        saveData.expression = form.expression
      }

      // 添加规则因子列表
      saveData.ruleFactors = conditions.value.map(condition => {
        // 查找选中的监测因子详细信息
        const selectedFactor = factorOptions.value.find(option => option.value === condition.factor)

        // 确定阈值类型和值
        const factorType = selectedFactor?.pointType || selectedFactor?.type || ''
        let thresholdValue: number = 0

        // 处理布尔类型值
        if (factorType === '7') {
          thresholdValue = condition.value === 'true' ? 1.0 : 0.0
        } else {
          // 尝试转换为数字
          const numValue = Number(condition.value)
          if (!isNaN(numValue)) {
            thresholdValue = numValue
          }
        }

        return {
          factorCode: selectedFactor?.pointCode || condition.factor,  // factorCode对应pointCode
          factorName: selectedFactor?.label || condition.factor,  // 使用监测因子名称
          variable: selectedFactor?.pointCode || condition.factor,    // 变量名使用pointCode
          unit: condition.unit || selectedFactor?.unit || undefined,  // 使用指定或自动填充的单位
          description: `${selectedFactor?.label || condition.factor}监测因子告警规则`,  // 添加简单描述
          indicatorId: selectedFactor?.indicatorId || null,  // 添加指标唯一ID
          factorType: factorType,  // 添加指标类型
          operator: condition.operator, // 添加操作符
          threshold: thresholdValue // 添加阈值
        }
      })
    }

    try {
      // 调用保存接口
      const response: any = await saveAlarmRule(saveData)

      if (response) {
        ElMessage.success(editMode.value ? '更新规则成功' : '创建规则成功')

        // 通知父组件刷新列表
        emit('save-rule', saveData)
        dialogVisible.value = false
      } else {
        ElMessage.error(response?.msg || '操作失败')
      }
    } catch (error) {
      console.error('保存告警规则失败:', error)
      ElMessage.error('保存失败，请重试')
    }
  })
}

// 【2】表达式预览 - 使用computed
// 单因子表达式（用户视角，code替换为name）
const singleFactorNameExpression = computed(() => {
  if (!singleFactorExpression.value) return '';

  // 查找因子详细信息
  const selectedFactor = factorOptions.value.find(option => option.value === form.factor);
  if (!selectedFactor) return singleFactorExpression.value;

  // 将pointCode替换为设备名称+因子名称
  const displayName = `${selectedFactor.deviceName} - ${selectedFactor.label}`;
  const pointCode = selectedFactor.pointCode || form.factor;
  return singleFactorExpression.value.replace(pointCode, displayName);
});

const previewExpression = computed(() => {
  if (conditions.value.length === 0) return ''

  const conditionStrings = conditions.value.map((cond) => {
    if (!cond.factor || !cond.operator || !cond.value) return ''

    // 查找选中的监测因子详细信息，获取pointCode用于表达式
    const selectedFactor = factorOptions.value.find(option => option.value === cond.factor)
    const pointCode = selectedFactor?.pointCode || cond.factor

    // 显示时始终使用原始值（true/false），不转换为数值
    return `${pointCode} ${cond.operator} ${cond.value}`
  }).filter(Boolean)

  if (conditionStrings.length === 0) return ''

  if (combinationType.value === 'custom' && customLogic.value) {
    // 替换自定义逻辑中的条件标记
    let result = customLogic.value
    for (let i = 0; i < conditions.value.length; i++) {
      const placeholder = `条件${i + 1}`
      const conditionStr = conditionStrings[i] || ''
      result = result.replace(new RegExp(placeholder, 'g'), conditionStr)
    }
    // 替换AND和OR
    result = result.replace(/\bAND\b/g, '&&').replace(/\bOR\b/g, '||')
    return result
  } else if (combinationType.value === 'any') {
    return conditionStrings.join(' || ')
  } else {
    return conditionStrings.join(' && ')
  }
})

// 组合因子表达式预览（用户视角，code替换为name）
const previewNameExpression = computed(() => {
  if (!previewExpression.value) return '';

  // 重新构建名称表达式，避免全局替换导致的问题
  if (conditions.value.length === 0) return '';

  const conditionNameStrings = conditions.value.map((cond) => {
    if (!cond.factor || !cond.operator || !cond.value) return ''

    // 查找选中的监测因子详细信息
    const selectedFactor = factorOptions.value.find(option => option.value === cond.factor)
    if (!selectedFactor) return ''

    // 组合设备名称和因子名称
    const displayName = `${selectedFactor.deviceName} - ${selectedFactor.label}`;

    // 构建条件字符串，直接使用设备名称
    return `${displayName} ${cond.operator} ${cond.value}`
  }).filter(Boolean)

  if (conditionNameStrings.length === 0) return ''

  if (combinationType.value === 'custom' && customLogic.value) {
    // 替换自定义逻辑中的条件标记
    let result = customLogic.value
    for (let i = 0; i < conditions.value.length; i++) {
      const placeholder = `条件${i + 1}`
      const conditionStr = conditionNameStrings[i] || ''
      result = result.replace(new RegExp(placeholder, 'g'), conditionStr)
    }
    // 替换AND和OR
    result = result.replace(/\bAND\b/g, '&&').replace(/\bOR\b/g, '||')
    return result
  } else if (combinationType.value === 'any') {
    return conditionNameStrings.join(' || ')
  } else {
    return conditionNameStrings.join(' && ')
  }
});

// 判断是否可以添加新条件
const canAddCondition = computed(() => {
  // 如果没有条件，允许添加
  if (conditions.value.length === 0) return true

  // 检查所有现有条件是否都已完整填写
  return conditions.value.every(condition =>
    condition.factor && condition.operator && condition.value
  )
})

// 【3】最后设置watch
// 监听条件变化，生成表达式
watch([conditions, combinationType, customLogic], () => {
  buildExpression(previewExpression.value)
}, { deep: true })

// 监听单因子相关字段变化，生成单因子表达式
watch([() => form.factor, () => form.operator, () => form.threshold], () => {
  buildSingleFactorExpression()
})

// 监听水厂ID变化
watch(() => form.factoryId, (newVal, oldVal) => {
  if (newVal && String(newVal) !== String(oldVal)) {
    fetchFactorList()
  }
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  level: [{ required: true, message: '请选择告警级别', trigger: 'change' }],
  factor: [{ required: true, message: '请选择监测因子', trigger: 'change' }],
  operator: [{ required: true, message: '请选择比较运算符', trigger: 'change' }],
  threshold: [{ required: true, message: '请输入阈值', trigger: 'blur' }],
  expression: [{ required: true, message: '请构建表达式', trigger: 'change' }]
}

// 处理监测因子选择变化
const handleFactorChange = (value) => {
  // 清空阈值，避免类型不匹配
  form.threshold = ''

  // 查找选中的监测因子详细信息
  const selectedFactor = factorOptions.value.find(option => option.value === value)

  if (selectedFactor) {
    // 更新因子类型
    selectedFactorType.value = selectedFactor.pointType || selectedFactor.type || ''

    // 自动填充单位
    form.unit = selectedFactor.unit || ''

    // 对于布尔类型(type=7)，默认设置为true
    if (selectedFactorType.value === '7') {
      form.threshold = 'true'
      // 布尔类型默认使用等于运算符
      form.operator = '=='
    }
  } else {
    // 未找到选中的因子，重置相关字段
    selectedFactorType.value = ''
    form.unit = ''
  }

  // 重新构建表达式
  buildSingleFactorExpression()
}

// 处理条件中监测因子选择变化
const handleConditionFactorChange = (value: string, index: number) => {
  // 清空阈值，避免类型不匹配
  conditions.value[index].value = ''

  // 查找选中的监测因子详细信息
  const selectedFactor = factorOptions.value.find(option => option.value === value)

  if (selectedFactor) {
    // 自动填充单位
    conditions.value[index].unit = selectedFactor.unit || ''

    // 对于布尔类型(type=7)，默认设置为true
    const factorType = selectedFactor.pointType || selectedFactor.type || ''
    if (factorType === '7') {
      conditions.value[index].value = 'true'
      // 布尔类型默认使用等于运算符
      conditions.value[index].operator = '=='
    }
  } else {
    // 未找到选中的因子，重置单位
    conditions.value[index].unit = ''
  }
}

const getConditionFactorType = (factor: string) => {
  const selectedFactor = factorOptions.value.find(option => option.value === factor)
  return selectedFactor?.pointType || selectedFactor?.type || ''
}

defineExpose({
  openDialog
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

.rule-type-selector {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.condition-card {
  transition: all 0.3s;
  border: 1px solid #ebeef5;
}

.condition-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border-color: #a0cfff;
}

.combination-radio {
  margin-right: 20px;
  margin-bottom: 10px;
}

.combination-radio :deep(.el-radio__label) {
  padding-left: 6px;
}

/* 树形选择器样式定制 */
:deep(.el-tree-node__content) {
  padding: 6px 0;
}

:deep(.el-tree-node.is-expanded > .el-tree-node__content) {
  font-weight: 600;
  color: #333;
}

:deep(.el-select-dropdown__item) {
  padding: 0 12px;
}

:deep(.el-tree-select__popper .el-tree) {
  padding: 6px 0;
}

:deep(.el-tree-node.is-disabled > .el-tree-node__content) {
  background-color: #f5f7fa;
  cursor: default;
}

:deep(.el-tree-node__label) {
  font-size: 0.95rem;
}

:deep(.el-tree-node.is-disabled .el-tree-node__label) {
  color: #606266;
  font-weight: 600;
}

/* 叶子节点（因子）样式 */
:deep(.el-tree-node.is-leaf .el-tree-node__label) {
  font-size: 0.9rem;
  color: #606266;
}
</style>