<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入详情"
    width="62.5rem"
    :before-close="handleClose"
  >
    <div class="detail-container">
      <!-- 基本信息 -->
      <div class="basic-info mb-1.5rem">
        <el-descriptions title="导入信息" :column="3" border>
          <el-descriptions-item label="批次号">
            {{ detailData.batchNo }}
          </el-descriptions-item>
          <el-descriptions-item label="文件名">
            {{ detailData.fileName }}
          </el-descriptions-item>
          <el-descriptions-item label="导入状态">
            <el-tag :type="getStatusTag(detailData.status)">
              {{ getStatusText(detailData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="总记录数">
            {{ detailData.totalCount }}
          </el-descriptions-item>
          <el-descriptions-item label="成功数">
            <span class="text-green-600 font-semibold">{{ detailData.successCount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="失败数">
            <span class="text-red-600 font-semibold">{{ detailData.failCount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="操作人">
            {{ detailData.operator }}
          </el-descriptions-item>
          <el-descriptions-item label="导入时间">
            {{ detailData.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="耗时">
            {{ detailData.duration || '2.5秒' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 统计图表 -->
      <div class="chart-area mb-1.5rem">
        <div class="chart-title mb-0.75rem">导入结果统计</div>
        <div class="chart-content">
          <div class="chart-item">
            <div class="chart-circle success">
              <div class="chart-number">{{ detailData.successCount }}</div>
              <div class="chart-label">成功</div>
            </div>
          </div>
          <div class="chart-item">
            <div class="chart-circle error">
              <div class="chart-number">{{ detailData.failCount }}</div>
              <div class="chart-label">失败</div>
            </div>
          </div>
          <div class="chart-item">
            <div class="chart-circle total">
              <div class="chart-number">{{ detailData.totalCount }}</div>
              <div class="chart-label">总计</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Tab切换 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 成功记录 -->
        <el-tab-pane label="成功记录" name="success">
          <div class="tab-content">
            <div class="table-header mb-0.75rem">
              <span class="text-0.875rem text-gray-600">
                共 {{ successList.length }} 条成功记录
              </span>
              <el-button size="small" @click="handleExportSuccess">
                <el-icon><Download /></el-icon>
                导出成功记录
              </el-button>
            </div>
            <el-table 
              :data="successList" 
              border 
              size="small"
              max-height="25rem"
              v-loading="successLoading"
            >
              <el-table-column type="index" label="序号" width="3.75rem" align="center" />
              <el-table-column prop="goodsCode" label="商品编码" width="7.5rem" align="center" />
              <el-table-column prop="goodsName" label="商品名称" width="7.5rem" align="center" />
              <el-table-column prop="warehouseCode" label="仓库编码" width="6.25rem" align="center" />
              <el-table-column prop="quantity" label="数量" width="5rem" align="center" />
              <el-table-column prop="price" label="单价" width="5rem" align="center" />
              <el-table-column prop="amount" label="金额" width="6.25rem" align="center" />
              <el-table-column prop="processTime" label="处理时间" width="10rem" align="center" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <!-- 失败记录 -->
        <el-tab-pane label="失败记录" name="error">
          <div class="tab-content">
            <div class="table-header mb-0.75rem">
              <span class="text-0.875rem text-gray-600">
                共 {{ errorList.length }} 条失败记录
              </span>
              <el-button size="small" type="danger" @click="handleExportError">
                <el-icon><Download /></el-icon>
                导出失败记录
              </el-button>
            </div>
            <el-table 
              :data="errorList" 
              border 
              size="small"
              max-height="25rem"
              v-loading="errorLoading"
            >
              <el-table-column type="index" label="序号" width="3.75rem" align="center" />
              <el-table-column prop="row" label="行号" width="4rem" align="center" />
              <el-table-column prop="goodsCode" label="商品编码" width="7.5rem" align="center" />
              <el-table-column prop="goodsName" label="商品名称" width="7.5rem" align="center" />
              <el-table-column prop="warehouseCode" label="仓库编码" width="6.25rem" align="center" />
              <el-table-column prop="errorField" label="错误字段" width="6.25rem" align="center" />
              <el-table-column prop="errorValue" label="错误值" width="6.25rem" align="center" />
              <el-table-column prop="errorMessage" label="错误信息" min-width="10rem" align="center" />
            </el-table>
          </div>
        </el-tab-pane>
        
        <!-- 处理日志 -->
        <el-tab-pane label="处理日志" name="log">
          <div class="tab-content">
            <div class="log-container">
              <div class="log-item" v-for="(log, index) in processLogs" :key="index">
                <div class="log-time">{{ log.time }}</div>
                <div class="log-content" :class="log.type">
                  <el-icon>
                    <component :is="getLogIcon(log.type)" />
                  </el-icon>
                  {{ log.message }}
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleReimport" v-if="detailData.status === 'failed'">
          重新导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, SuccessFilled, WarningFilled, InfoFilled } from '@element-plus/icons-vue'

// 定义接口
interface DetailData {
  batchNo: string
  fileName: string
  totalCount: number
  successCount: number
  failCount: number
  status: string
  operator: string
  createTime: string
  duration?: string
}

interface SuccessRecord {
  goodsCode: string
  goodsName: string
  warehouseCode: string
  quantity: number
  price: number
  amount: number
  processTime: string
}

interface ErrorRecord {
  row: number
  goodsCode: string
  goodsName: string
  warehouseCode: string
  errorField: string
  errorValue: string
  errorMessage: string
}

interface ProcessLog {
  time: string
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
}

// 定义props
interface Props {
  visible: boolean
  detailData: Partial<DetailData>
}

// 定义emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'reimport', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  detailData: () => ({})
})

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const activeTab = ref('success')
const successLoading = ref(false)
const errorLoading = ref(false)

// Mock数据
const successList = ref<SuccessRecord[]>([
  {
    goodsCode: 'G001',
    goodsName: 'A4纸',
    warehouseCode: 'WH001',
    quantity: 100,
    price: 25.5,
    amount: 2550,
    processTime: '2024-01-15 10:30:15'
  },
  {
    goodsCode: 'G002',
    goodsName: '圆珠笔',
    warehouseCode: 'WH001',
    quantity: 200,
    price: 2.5,
    amount: 500,
    processTime: '2024-01-15 10:30:16'
  }
])

const errorList = ref<ErrorRecord[]>([
  {
    row: 15,
    goodsCode: 'G999',
    goodsName: '未知商品',
    warehouseCode: 'WH001',
    errorField: '商品编码',
    errorValue: 'G999',
    errorMessage: '商品不存在'
  },
  {
    row: 23,
    goodsCode: 'G003',
    goodsName: '订书机',
    warehouseCode: 'WH001',
    errorField: '数量',
    errorValue: '-5',
    errorMessage: '数量不能为负数'
  }
])

const processLogs = ref<ProcessLog[]>([
  {
    time: '2024-01-15 10:30:00',
    type: 'info',
    message: '开始处理导入文件：期初库存导入_20240115.xlsx'
  },
  {
    time: '2024-01-15 10:30:01',
    type: 'info',
    message: '文件解析完成，共读取到 100 条记录'
  },
  {
    time: '2024-01-15 10:30:02',
    type: 'info',
    message: '开始数据验证...'
  },
  {
    time: '2024-01-15 10:30:03',
    type: 'warning',
    message: '发现 5 条数据验证失败'
  },
  {
    time: '2024-01-15 10:30:04',
    type: 'info',
    message: '开始保存有效数据...'
  },
  {
    time: '2024-01-15 10:30:05',
    type: 'success',
    message: '导入完成，成功 95 条，失败 5 条'
  }
])

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 工具方法
const getStatusTag = (status: string) => {
  const statusMap = {
    success: 'success',
    failed: 'danger',
    processing: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    success: '成功',
    failed: '失败',
    processing: '处理中'
  }
  return statusMap[status] || '未知'
}

const getLogIcon = (type: string) => {
  const iconMap = {
    info: InfoFilled,
    success: SuccessFilled,
    warning: WarningFilled,
    error: WarningFilled
  }
  return iconMap[type] || InfoFilled
}

// 事件处理
const handleClose = () => {
  dialogVisible.value = false
}

const handleExportSuccess = () => {
  ElMessage.success('成功记录导出中...')
}

const handleExportError = () => {
  ElMessage.success('失败记录导出中...')
}

const handleReimport = () => {
  emit('reimport', props.detailData)
  ElMessage.info('重新导入功能开发中...')
}
</script>

<style scoped lang="scss">
.detail-container {
  .chart-area {
    .chart-title {
      font-weight: 600;
      font-size: 0.875rem;
      color: #303133;
    }
    
    .chart-content {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 1rem 0;
      
      .chart-item {
        text-align: center;
        
        .chart-circle {
          width: 5rem;
          height: 5rem;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin: 0 auto 0.5rem;
          
          &.success {
            background: linear-gradient(135deg, #67c23a, #85ce61);
            color: white;
          }
          
          &.error {
            background: linear-gradient(135deg, #f56c6c, #f78989);
            color: white;
          }
          
          &.total {
            background: linear-gradient(135deg, #409eff, #66b1ff);
            color: white;
          }
          
          .chart-number {
            font-size: 1.25rem;
            font-weight: bold;
          }
          
          .chart-label {
            font-size: 0.75rem;
            margin-top: 0.25rem;
          }
        }
      }
    }
  }
  
  .detail-tabs {
    :deep(.el-tabs__content) {
      padding-top: 1rem;
    }
  }
  
  .tab-content {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .log-container {
    max-height: 25rem;
    overflow-y: auto;
    
    .log-item {
      display: flex;
      margin-bottom: 0.75rem;
      
      .log-time {
        width: 10rem;
        flex-shrink: 0;
        font-size: 0.75rem;
        color: #909399;
        padding-right: 1rem;
      }
      
      .log-content {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        
        &.info {
          color: #409eff;
        }
        
        &.success {
          color: #67c23a;
        }
        
        &.warning {
          color: #e6a23c;
        }
        
        &.error {
          color: #f56c6c;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}
</style>
