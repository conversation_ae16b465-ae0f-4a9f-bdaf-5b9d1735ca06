<template>
    <div>
        <el-dialog v-model="dialogVisible" title="数据补录" width="500px">
            <el-form :model="recordForm" label-width="100px">
                <el-form-item label="终端编号">
                    <span>{{ recordForm.terminalId }}</span>
                </el-form-item>
                <el-form-item label="补录时间段">
                    <span>{{ recordForm.timeRange }}</span>
                </el-form-item>
                <el-form-item label="补录文件">
                    <el-upload class="upload-demo" action="/api/upload" :on-success="handleUploadSuccess"
                        :before-upload="handleBeforeUpload">
                        <el-button type="primary">选择文件</el-button>
                    </el-upload>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitRecord">确认补录</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script lang='ts' setup>
import { reactive, ref, watch } from 'vue';

const dialogVisible = ref(false)
const recordForm = reactive({
    terminalId: '',
    timeRange: '',
    file: null
})

const props = defineProps({
    terminalId: {
        type: String,
        default: ''
    },
    timeRange: {
        type: String,
        default: ''
    }
})

// 监听props变化更新表单
watch(
    () => props,
    (newVal) => {
        recordForm.terminalId = newVal.terminalId
        recordForm.timeRange = newVal.timeRange
    },
    { immediate: true, deep: true }
)

const handleBeforeUpload = () => {
    const isValid = true
    if (!isValid) {
        ElMessage.error('文件不符合要求')
        return false
    }
    return true
}

const handleUploadSuccess = (response: any) => {
    recordForm.file = response.data
    ElMessage.success('文件上传成功')
}

const submitRecord = async () => {
    try {
        if (!recordForm.file) {
            return ElMessage.warning('请先上传补录文件')
        }
        ElMessage.success('补录成功')
        dialogVisible.value = false
        // handleSearch()
    } catch (error) {
        ElMessage.error('补录失败')
    }
}

function setVisible() {
    dialogVisible.value = !dialogVisible.value
}

defineExpose({
    setVisible
});

</script>
<style scoped lang='scss'></style>