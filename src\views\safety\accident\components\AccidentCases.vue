<template>
  <div class="accident-cases">
    <el-row :gutter="20" class="filter-row">
      <el-col :span="6">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索关键词"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><el-icon-search /></el-icon>
          </template>
        </el-input>
      </el-col>
      <el-col :span="4">
        <el-select v-model="filterType" placeholder="事故类型" clearable style="width: 100%">
          <el-option label="轻微事故" value="minor" />
          <el-option label="一般事故" value="normal" />
          <el-option label="重大事故" value="serious" />
          <el-option label="特大事故" value="critical" />
        </el-select>
      </el-col>
      <el-col :span="4">
        <el-select v-model="sortOption" placeholder="排序方式" style="width: 100%">
          <el-option label="按日期降序" value="date-desc" />
          <el-option label="按日期升序" value="date-asc" />
          <el-option label="按影响程度" value="impact" />
          <el-option label="按经济损失" value="loss" />
        </el-select>
      </el-col>
      <el-col :span="4">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-col>
      <el-col :span="6" class="text-right">
        <el-button type="success" @click="handleAddCase">
          <el-icon><el-icon-plus /></el-icon>新增案例
        </el-button>
      </el-col>
    </el-row>

    <div class="cases-container">
      <el-row :gutter="20">
        <el-col
          v-for="item in caseList"
          :key="item.id"
          :span="8"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="8"
          :xl="6"
        >
          <el-card class="case-card" shadow="hover" @click="openCaseDetail(item)">
            <div class="case-header">
              <el-tag :type="getCaseTypeTag(item.type)" effect="dark">{{ getCaseTypeLabel(item.type) }}</el-tag>
              <span class="case-date">{{ item.date }}</span>
            </div>
            <div class="case-title">{{ item.title }}</div>
            <div class="case-desc">{{ item.description }}</div>
            <div class="case-info">
              <div class="info-item">
                <el-icon><el-icon-location /></el-icon>
                <span>{{ item.location }}</span>
              </div>
              <div class="info-item">
                <el-icon><el-icon-warning /></el-icon>
                <span>伤亡: {{ item.casualties }} 人</span>
              </div>
              <div class="info-item">
                <el-icon><el-icon-money /></el-icon>
                <span>损失: {{ formatCurrency(item.loss) }}</span>
              </div>
            </div>
            <div class="case-footer">
              <div class="tags">
                <el-tag v-for="tag in item.tags" :key="tag" size="small" effect="plain">{{ tag }}</el-tag>
              </div>
              <div class="actions">
                <el-tooltip content="查看详情" placement="top">
                  <el-icon @click.stop="openCaseDetail(item)"><el-icon-view /></el-icon>
                </el-tooltip>
                <el-tooltip content="收藏案例" placement="top">
                  <el-icon @click.stop="toggleFavorite(item)">
                    <el-icon-star-filled v-if="item.favorite" style="color: #f7ba2a;" />
                    <el-icon-star v-else />
                  </el-icon>
                </el-tooltip>
                <el-tooltip content="下载案例" placement="top">
                  <el-icon @click.stop="downloadCase(item)"><el-icon-download /></el-icon>
                </el-tooltip>
              </div>
            </div>
            <div class="case-badge" v-if="item.verified">
              <el-icon><el-icon-check /></el-icon> 已验证
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[9, 12, 24, 36]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索和过滤
const searchKeyword = ref('')
const filterType = ref('')
const sortOption = ref('date-desc')
const currentPage = ref(1)
const pageSize = ref(9)
const total = ref(0)

// 模拟案例数据
const mockCases = [
  {
    id: '1',
    title: '化工厂氨气泄漏事故',
    type: 'serious',
    date: '2023-03-15',
    description: '由于管道老化导致氨气泄漏，造成多人中毒，车间生产中断数天。',
    location: '化工厂车间3区',
    casualties: 3,
    loss: 250000,
    tags: ['泄漏', '中毒', '管道老化'],
    verified: true,
    favorite: false,
    lessons: '定期检查和维护管道，确保安全阀正常工作。加强员工安全培训，教授应急处理知识。',
    measures: '更换老化管道，安装气体检测预警系统，定期组织应急演练。',
    attachments: [
      { name: '事故现场照片.jpg', type: 'image' },
      { name: '事故调查报告.pdf', type: 'document' }
    ]
  },
  {
    id: '2',
    title: '装配车间机械伤人事故',
    type: 'normal',
    date: '2023-04-22',
    description: '工人操作机械设备时未按规程操作，导致手部受伤。',
    location: '装配车间A区',
    casualties: 1,
    loss: 35000,
    tags: ['机械伤害', '违规操作'],
    verified: true,
    favorite: true,
    lessons: '严格执行操作规程，加强安全意识培训，禁止违规操作。',
    measures: '完善安全培训制度，增设安全防护装置，加强监督检查。',
    attachments: [
      { name: '事故报告.docx', type: 'document' }
    ]
  },
  {
    id: '3',
    title: '仓库货架倒塌事故',
    type: 'minor',
    date: '2023-06-10',
    description: '仓库货架超负荷堆放，导致货架倒塌，幸无人员伤亡。',
    location: '物流仓库B区',
    casualties: 0,
    loss: 80000,
    tags: ['超负荷', '倒塌', '物流'],
    verified: false,
    favorite: false,
    lessons: '严格遵守货架负荷限制，规范堆放，定期检查货架结构安全性。',
    measures: '更换加固货架，制定物料堆放标准，配备专业仓管员。',
    attachments: [
      { name: '事故照片.jpg', type: 'image' },
      { name: '损失评估.xlsx', type: 'document' }
    ]
  },
  {
    id: '4',
    title: '电气短路引发火灾事故',
    type: 'critical',
    date: '2023-07-28',
    description: '老旧电线短路引发火灾，造成整个车间设备严重损坏，并有多人烧伤。',
    location: '电子车间C区',
    casualties: 5,
    loss: 1200000,
    tags: ['火灾', '电气短路', '烧伤'],
    verified: true,
    favorite: false,
    lessons: '定期检查电气设备，及时更换老化线路，配置足够灭火器材。',
    measures: '全面排查电气安全隐患，加强消防设施配置，改进紧急疏散流程。',
    attachments: [
      { name: '火灾现场.jpg', type: 'image' },
      { name: '事故调查报告.pdf', type: 'document' },
      { name: '人员伤情统计.xlsx', type: 'document' }
    ]
  },
  {
    id: '5',
    title: '起重机吊装事故',
    type: 'serious',
    date: '2023-08-12',
    description: '重物吊装过程中，钢丝绳断裂导致重物坠落，砸伤两名工人。',
    location: '重装车间',
    casualties: 2,
    loss: 450000,
    tags: ['起重', '坠落', '重伤'],
    verified: true,
    favorite: true,
    lessons: '定期检查起重设备，严格执行作业前检查，避免超负荷作业。',
    measures: '更换所有起重设备钢丝绳，建立设备定检制度，强化安全培训。',
    attachments: [
      { name: '现场照片.jpg', type: 'image' },
      { name: '事故分析报告.pdf', type: 'document' }
    ]
  },
  {
    id: '6',
    title: '叉车撞击事故',
    type: 'minor',
    date: '2023-09-05',
    description: '叉车司机操作不当，撞击货架，造成货物掉落和轻微财产损失。',
    location: '物流中心',
    casualties: 0,
    loss: 20000,
    tags: ['叉车', '撞击', '操作失误'],
    verified: false,
    favorite: false,
    lessons: '加强叉车操作培训，规范行驶路线，控制车速。',
    measures: '优化物流通道设计，安装警示标志，强化叉车驾驶资质管理。',
    attachments: [
      { name: '现场照片.jpg', type: 'image' }
    ]
  },
  {
    id: '7',
    title: '高温液体喷溅烫伤事故',
    type: 'normal',
    date: '2023-10-18',
    description: '工人操作不当导致高温液体喷溅，造成多人烫伤。',
    location: '化工生产线',
    casualties: 3,
    loss: 60000,
    tags: ['烫伤', '高温液体', '防护不足'],
    verified: true,
    favorite: false,
    lessons: '加强高温作业防护意识，正确穿戴防护装备，规范操作流程。',
    measures: '改进防护装备，优化生产工艺，加强高温作业培训。',
    attachments: [
      { name: '事故调查.docx', type: 'document' },
      { name: '整改方案.pdf', type: 'document' }
    ]
  },
  {
    id: '8',
    title: '焊接作业引发爆炸事故',
    type: 'critical',
    date: '2023-11-07',
    description: '在易燃区域进行未经许可的焊接作业，引发爆炸，造成重大人员伤亡和财产损失。',
    location: '原料储存区',
    casualties: 7,
    loss: 3500000,
    tags: ['爆炸', '焊接', '违规作业'],
    verified: true,
    favorite: true,
    lessons: '严格执行动火作业许可制度，加强危险区域管控，杜绝违规作业。',
    measures: '完善作业许可流程，加强安全监督，建立危险作业预警机制。',
    attachments: [
      { name: '事故调查报告.pdf', type: 'document' },
      { name: '现场照片集.zip', type: 'archive' },
      { name: '专家分析.docx', type: 'document' }
    ]
  },
  {
    id: '9',
    title: '装卸过程中的挤压事故',
    type: 'normal',
    date: '2023-12-03',
    description: '装卸货物过程中，因未固定好的货物滑落，导致工人被挤压受伤。',
    location: '装卸平台',
    casualties: 1,
    loss: 75000,
    tags: ['挤压', '装卸', '固定不当'],
    verified: true,
    favorite: false,
    lessons: '规范装卸作业流程，确保货物稳固，使用适当的固定工具。',
    measures: '改进装卸设备，完善作业规程，加强装卸工安全培训。',
    attachments: [
      { name: '事故报告.pdf', type: 'document' }
    ]
  }
]

// 案例列表
const allCases = ref(mockCases)
const caseList = computed(() => {
  return allCases.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

// 获取案例数据
const fetchCaseData = () => {
  // 实际项目中，这里会调用API获取数据
  total.value = allCases.value.length
}

// 获取案例类型标签样式
const getCaseTypeTag = (type) => {
  const typeMap = {
    minor: 'info',
    normal: 'warning',
    serious: 'danger',
    critical: 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取案例类型显示文本
const getCaseTypeLabel = (type) => {
  const labelMap = {
    minor: '轻微事故',
    normal: '一般事故',
    serious: '重大事故',
    critical: '特大事故'
  }
  return labelMap[type] || '未知事故'
}

// 格式化货币
const formatCurrency = (value) => {
  if (!value && value !== 0) return ''
  return '¥ ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 搜索案例
const handleSearch = () => {
  // 在实际项目中，这里会根据搜索条件过滤或调用API
  ElMessage.success('搜索成功')
  currentPage.value = 1
}

// 重置搜索
const resetSearch = () => {
  searchKeyword.value = ''
  filterType.value = ''
  sortOption.value = 'date-desc'
  handleSearch()
}

// 打开案例详情
const openCaseDetail = (item) => {
  ElMessage.info(`查看案例详情: ${item.title}`)
  // 在实际项目中，这里会打开详情弹窗或跳转到详情页面
}

// 切换收藏状态
const toggleFavorite = (item) => {
  item.favorite = !item.favorite
  ElMessage.success(item.favorite ? '已添加到收藏' : '已取消收藏')
}

// 下载案例
const downloadCase = (item) => {
  ElMessage.success(`开始下载案例: ${item.title}`)
  // 在实际项目中，这里会下载案例文件
}

// 新增案例
const handleAddCase = () => {
  ElMessage.info('打开新增案例表单')
  // 在实际项目中，这里会打开新增案例表单
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

onMounted(() => {
  fetchCaseData()
})
</script>

<style lang="scss" scoped>
.accident-cases {
  .filter-row {
    margin-bottom: 20px;
    
    .text-right {
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .cases-container {
    margin-bottom: 20px;
    
    .case-card {
      position: relative;
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s;
      height: 320px;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      .case-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .case-date {
          color: #909399;
          font-size: 14px;
        }
      }
      
      .case-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
        line-height: 1.4;
        color: #303133;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .case-desc {
        font-size: 14px;
        color: #606266;
        margin-bottom: 16px;
        line-height: 1.6;
        height: 70px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .case-info {
        margin-bottom: 16px;
        
        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 6px;
          font-size: 13px;
          color: #606266;
          
          .el-icon {
            margin-right: 6px;
            font-size: 16px;
            color: #909399;
          }
        }
      }
      
      .case-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .tags {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
        }
        
        .actions {
          display: flex;
          gap: 10px;
          
          .el-icon {
            font-size: 18px;
            color: #909399;
            cursor: pointer;
            transition: color 0.3s;
            
            &:hover {
              color: #409eff;
            }
          }
        }
      }
      
      .case-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #67c23a;
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
        display: flex;
        align-items: center;
        
        .el-icon {
          margin-right: 4px;
          font-size: 14px;
        }
      }
    }
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
}
</style> 