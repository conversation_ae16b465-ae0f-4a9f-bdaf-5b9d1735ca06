<template>
  <el-dialog
    :title="dialogTitle"
    v-model="visible"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      :disabled="type === 'view'"
    >
      <el-form-item label="标准编号" prop="code">
        <el-input v-model="form.code" placeholder="请输入标准编号" />
      </el-form-item>
      <el-form-item label="标准名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入标准名称" />
      </el-form-item>
      <el-form-item label="所属分类" prop="categoryId">
        <el-tree-select
          v-model="form.categoryId"
          :data="categoryOptions"
          placeholder="请选择所属分类"
          check-strictly
        />
      </el-form-item>
      <el-form-item label="标准内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入标准内容"
        />
      </el-form-item>
      <el-form-item label="版本" prop="version">
        <el-input v-model="form.version" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="active">生效</el-radio>
          <el-radio label="inactive">失效</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button v-if="type !== 'view'" type="primary" @click="handleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { StandardData, CategoryNode } from '../types'

const props = defineProps<{
  modelValue: boolean
  type: 'add' | 'edit' | 'view'
  data?: StandardData | null
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  const titles = {
    add: '新增标准',
    edit: '编辑标准',
    view: '查看标准'
  }
  return titles[props.type]
})

const formRef = ref<FormInstance>()
const form = ref<Partial<StandardData>>({
  code: '',
  name: '',
  categoryId: undefined,
  content: '',
  version: '',
  status: 'active'
})

const rules: FormRules = {
  code: [{ required: true, message: '请输入标准编号', trigger: 'blur' }],
  name: [{ required: true, message: '请输入标准名称', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择所属分类', trigger: 'change' }],
  content: [{ required: true, message: '请输入标准内容', trigger: 'blur' }],
  version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 分类选项
const categoryOptions = ref<CategoryNode[]>([])

// 获取分类树数据
const getCategoryTree = async () => {
  try {
    // TODO: 实现获取分类树的接口调用
    // const { data } = await api.getCategoryTree()
    // categoryOptions.value = data
  } catch (error) {
    console.error(error)
  }
}

// 监听数据变化
watch(
  () => props.data,
  (val) => {
    if (val) {
      form.value = { ...val }
    } else {
      form.value = {
        code: '',
        name: '',
        categoryId: undefined,
        content: '',
        version: '',
        status: 'active'
      }
    }
  },
  { immediate: true }
)

// 表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // TODO: 实现提交接口调用
        // if (props.type === 'add') {
        //   await api.createStandard(form.value)
        // } else {
        //   await api.updateStandard(form.value)
        // }
        emit('success')
        visible.value = false
      } catch (error) {
        console.error(error)
      }
    }
  })
}

// 初始化
getCategoryTree()
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 