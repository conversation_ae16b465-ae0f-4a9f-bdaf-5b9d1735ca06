<template>
  <div class="app-container" v-loading="isLoading" element-loading-text="数据加载中，请稍候...">
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-content">
            <div class="header-title">月度水质水量报表</div>
            <div class="header-controls">

              <el-select v-model="selectedFactory" multiple collapse-tags collapse-tags-tooltip placeholder="请选择查询水厂范围"
                class="control-item" @change="handleFactoryChange">
                <el-option v-for="item in allFactories" :key="item.id" :label="item.name" :value="item.id"
                  :style="{ paddingLeft: 20 + (item.level - 1) * 20 + 'px' }" />
              </el-select>

              <el-select v-model="selectedIndicators" multiple collapse-tags collapse-tags-tooltip placeholder="请选择指标"
                class="control-item" clearable @change="handleFilterChange">
                <el-option-group v-for="group in indicatorOptions" :key="group.label" :label="group.label">
                  <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value" />
                </el-option-group>
              </el-select>

              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" class="control-item date-picker"
                @change="handleDateRangeChange" />
              <!-- v-if="hasPermission('asset:user:review')" -->
              <el-button type="primary" v-if="isAdmin" @click="openConfigDialog" style="margin-right: 10px;"
                v-show="false">指标标准配置
              </el-button>
              <el-button type="success" @click="handleExport">导出</el-button>
            </div>
          </div>
        </div>
      </template>

      <el-table :data="filteredTableData" border style="width: 100%" :header-cell-style="headerCellStyle"
        :span-method="objectSpanMethod" size="small" height="calc(100vh - 220px)" fixed-header>
        <!-- 名称表头（使用自定义表头） -->
        <el-table-column align="center" width="240" fixed="left">
          <template #header>
            <div class="custom-header">
              <div>名称</div>
            </div>
          </template>

          <!-- 父级水厂列 -->
          <el-table-column label=" " width="120" align="center" prop="parentFactoryName" />

          <!-- 子级水厂列 -->
          <el-table-column label=" " width="120" align="center">
            <template #default="scope">
              {{ scope.row.factoryName }}
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 年度月份列（显示行类型：当日/月最大/月最小/月平均） -->
        <el-table-column label="年度·月份" width="100" align="center" fixed="left">
          <template #header>
            <div class="header-cell">
              <div class="header-item">{{ formatYearMonth(dateRange[0]) }}</div>
              <div class="header-item">单位</div>
            </div>
          </template>
          <template #default="scope">
            {{ scope.row.rowType }}
          </template>
        </el-table-column>

        <el-table-column label="水量" align="center">
          <!-- 进水流量 -->
          <el-table-column align="center" width="80" prop="inFlowWaterVolume">
            <template #header>
              <div class="header-cell">
                <div class="header-item">进水流量</div>
                <div class="header-item">m³</div>
              </div>
            </template>
          </el-table-column>

          <!-- 出水流量 -->
          <el-table-column align="center" width="80" prop="dailyTreatment">
            <template #header>
              <div class="header-cell">
                <div class="header-item">出水流量</div>
                <div class="header-item">m³</div>
              </div>
            </template>
          </el-table-column>
        </el-table-column>



        <!-- 进水物理特性 -->
        <el-table-column v-if="shouldShowColumn('inPh') || shouldShowColumn('inTemp')" label="进水物理特性" align="center">
          <el-table-column v-if="shouldShowColumn('inPh')" label="pH" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('inPh') || '6-9' : ''"
              width="80" align="center"> -->
            <el-table-column label="/" width="80" align="center" prop="inPh" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('inTemp')" label="水温" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('inTemp') || '℃' : ''" -->
            <!-- width="80" align="center" prop="inTemp" /> -->
            <el-table-column label="℃" width="80" align="center" prop="inTemp" />
            <!-- </el-table-column> -->
          </el-table-column>
        </el-table-column>

        <!-- 进水污染物浓度 -->
        <el-table-column v-if="shouldShowColumn('inCodcr') || shouldShowColumn('inBod5') || shouldShowColumn('inSs') ||
          shouldShowColumn('inNh3n') || shouldShowColumn('inTn') || shouldShowColumn('inTp')" label="进水污染物浓度"
          align="center">
          <el-table-column v-if="shouldShowColumn('inCodcr')" label="CODcr" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('inCodcr') || '≤380' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="inCodcr" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('inBod5')" label="BOD₅" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('inBod5') || '≤180' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="inBod5" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('inSs')" label="SS(L)" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('inSs') || '≤280' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="inSs" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('inNh3n')" label="NH₃-N" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('inNh3n') || '≤35' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="inNh3n" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('inTn')" label="TN" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('inTn') || '≤50' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="inTn" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('inTp')" label="TP" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('inTp') || '≤6' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="inTp" />
            <!-- </el-table-column> -->
          </el-table-column>
        </el-table-column>

        <!-- 出水物理特性 -->
        <el-table-column v-if="shouldShowColumn('outPh') || shouldShowColumn('outTemp')" label="出水物理特性" align="center">
          <el-table-column v-if="shouldShowColumn('outPh')" label="pH" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('outPh') || '6-9' : ''"
              width="100" align="center"> -->
            <el-table-column label="/" width="80" align="center" prop="outPh" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('outTemp')" label="水温" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('inTemp') || '℃' : ''" -->
            <!-- width="80" align="center" prop="inTemp" /> -->
            <el-table-column label="℃" width="80" align="center" prop="outTemp" />
            <!-- </el-table-column> -->
          </el-table-column>
        </el-table-column>

        <!-- 出水污染物浓度 -->
        <el-table-column v-if="shouldShowColumn('outCodcr') || shouldShowColumn('outBod5') || shouldShowColumn('outSs') ||
          shouldShowColumn('outNh3n') || shouldShowColumn('outTn') || shouldShowColumn('outTp')" label="出水污染物浓度"
          align="center">
          <el-table-column v-if="shouldShowColumn('outCodcr')" label="CODcr" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('outCodcr') || '≤30' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="outCodcr" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('outBod5')" label="BOD₅" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('outBod5') || '≤6' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="outBod5" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('outSs')" label="SS(L)" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('outSs') || '≤10' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="outSs" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('outNh3n')" label="NH₃-N" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('outNh3n') || '≤1.5' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="outNh3n" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('outTn')" label="TN" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('outTn') || '≤5' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="outTn" />
            <!-- </el-table-column> -->
          </el-table-column>
          <el-table-column v-if="shouldShowColumn('outTp')" label="TP" align="center">
            <!-- <el-table-column :label="selectedFactory.values.length > 0 ? getStandardValue('outTp') || '≤0.3' : ''"
              width="80" align="center"> -->
            <el-table-column label="mg/L" width="80" align="center" prop="outTp" />
            <!-- </el-table-column> -->
          </el-table-column>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 指标标准配置对话框 -->
    <el-dialog v-model="showStandardConfigDialog" title="指标标准配置" width="700px">
      <el-form label-width="120px">
        <el-form-item label="水厂">
          <el-select v-model="editFactoryId" placeholder="请选择水厂" style="width: 100%">
            <!-- 添加一个value为null的选项来显示placeholder -->
            <el-option label="请选择水厂" :value="null" />
            <el-option v-for="item in allFactories" :key="item.id" :label="item.name" :value="item.id"
              :disabled="!item.isLeaf" :style="{ paddingLeft: 20 + (item.level - 1) * 20 + 'px' }" />
          </el-select>
        </el-form-item>

        <el-divider>指标标准值配置</el-divider>

        <el-table :data="editConfigs" border style="width: 100%">
          <el-table-column label="指标名称" prop="indicatorName" width="200">
            <template #default="{ row }">
              {{ getIndicatorLabel(row.indicatorName) }}
            </template>
          </el-table-column>
          <el-table-column label="标准值" prop="standardValue">
            <template #default="{ row }">
              <el-input v-model="row.standardValue" placeholder="请输入标准值" :validate-event="false"
                @input="handleStandardValueInput($event, row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showStandardConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="saveStandardConfigs">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { FactoryApi } from '@/api/report/factory/index'
import {
  batchSaveIndicatorStandardConfig,
  getIndicatorStandardConfig,
  IndicatorConfig
} from '@/api/report/indicatorStandardConfig'
import { ProdQualityDataAPI } from '@/api/report/prodQualityData/index'
import { useUserStore } from '@/store/modules/user'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { computed, onMounted, ref, watch } from 'vue'

interface Factory {
  id: number
  name: string
  level?: number
  children?: Factory[]
  orderNum?: number
}

// 当前选中的时间区间（默认本月1号~昨天）
const getDefaultDateRange = () => {
  const today = dayjs()

  // 如果今天是月初1号，返回上个月的日期范围
  if (today.date() === 1) {
    const lastMonth = today.subtract(1, 'month')
    const start = lastMonth.startOf('month').format('YYYY-MM-DD')
    const end = lastMonth.endOf('month').format('YYYY-MM-DD')
    return [start, end]
  }

  // 否则返回本月1号到昨天
  const start = today.startOf('month').format('YYYY-MM-DD')
  const end = today.subtract(1, 'day').format('YYYY-MM-DD')
  return [start, end]
}
const dateRange = ref<[string, string]>(getDefaultDateRange())
// 当前选中的水厂
const selectedFactory = ref<number[]>([])
// 水厂列表
const factoryList = ref<Factory[]>([])

const oldSelec = ref<number[]>([])
// 表格数据
const tableData = ref<any[]>([])
// 加载状态
const isLoading = ref(false)

// 指标标准配置
const indicatorStandards = ref<Record<string, any>>({})
// 是否显示指标标准配置对话框
const showStandardConfigDialog = ref(false)
// 当前编辑的水厂ID
const editFactoryId = ref<number | null>(null)
// 当前业务类型
const bizType = ref('in') // 水质水量类型

// 编辑的配置项
const editConfigs = ref<{ indicatorName: string, standardValue: string }[]>([])

// 用户信息
const userStore = useUserStore()

// 提取所有水厂（包括子水厂）
const allFactories = computed(() => {
  const result: { id: number; name: string; level: number; isLeaf: boolean }[] = []

  function extractFactories(factories: Factory[], level = 1) {
    factories.forEach(factory => {
      // 判断是否为叶子节点（没有子节点）
      const isLeaf = !factory.children || factory.children.length === 0
      result.push({
        id: factory.id,
        name: factory.name,
        level,
        isLeaf
      })
      if (factory.children && factory.children.length) {
        extractFactories(factory.children, level + 1)
      }
    })
  }

  extractFactories(factoryList.value)
  return result
})

// 构建父子工厂映射关系
const factoryParentChildMap = computed(() => {
  const map = new Map<number, { parent: Factory | null, children: Factory[] }>();

  // 初始化映射
  allFactories.value.forEach(factory => {
    map.set(factory.id, { parent: null, children: [] });
  });

  // 建立父子关系
  function buildRelationship(factories: Factory[], parent: Factory | null = null) {
    factories.forEach(factory => {
      // 设置父厂
      if (parent && map.has(factory.id)) {
        map.get(factory.id)!.parent = parent;
      }

      // 如果有子厂，则添加到父厂的children中
      if (parent && map.has(parent.id)) {
        map.get(parent.id)!.children.push(factory);
      }

      // 递归处理子厂
      if (factory.children && factory.children.length) {
        buildRelationship(factory.children, factory);
      }
    });
  }

  buildRelationship(factoryList.value);
  return map;
});

// 获取根工厂（没有父工厂的工厂）
const rootFactories = computed(() => {
  return allFactories.value.filter(factory => {
    const parentInfo = factoryParentChildMap.value.get(factory.id);
    return parentInfo && !parentInfo.parent;
  });
});

// 获取水厂列表
const getFactoryList = async () => {
  try {
    isLoading.value = true;
    const res = await FactoryApi.queryAllFactoryTreeAndLevelOne()
    if (res && (res as any).code === 0) {
      factoryList.value = (res as any).data || []
    } else {
      ElMessage.error('获取水厂列表失败')
    }
  } catch (error) {
    console.error('获取水厂列表失败:', error)
    ElMessage.error('获取水厂列表失败')
  } finally {
    isLoading.value = false;
  }
}

// 找到水厂的父级水厂名称
const getParentFactoryName = (factoryId: number): string => {
  const parentInfo = factoryParentChildMap.value.get(factoryId);
  if (!parentInfo || !parentInfo.parent) {
    // 如果没有父厂，返回自己的名称作为父厂名称
    const factory = allFactories.value.find(f => f.id === factoryId);
    return factory ? factory.name : '';
  }
  return parentInfo.parent.name;
}

// 加载数据
const loadData = async () => {
  isLoading.value = true
  try {
    // 清空之前的数据
    tableData.value = []

    const params = {
      startDate: dateRange.value[0],
      endDate: dateRange.value[1]
    }
    const data = await ProdQualityDataAPI.queryInOutCollectData(params)
    if (data) {
      // 准备表格数据
      const newTableData: any[] = []
      const factoryDataList = Array.isArray(data) ? data : [data]

      // 保存原始顺序索引和orderNum
      const factoryOrder = new Map<number, { index: number, orderNum: number }>()
      factoryDataList.forEach((factory, index) => {
        factoryOrder.set(factory.factoryId, {
          index,
          orderNum: factory.orderNum || 9999 // 如果没有orderNum，给一个大值
        })
      })

      // 获取所有父级水厂，保持原始顺序
      const uniqueParentFactories = new Set<string>();
      const parentFactoryOrderMap = new Map<string, { index: number, orderNum: number }>();

      // 先收集所有父级水厂和顺序
      factoryDataList.forEach((factory, index) => {
        // 获取水厂信息
        const factoryInfo = allFactories.value.find(f => f.id === factory.factoryId);
        if (factoryInfo) {
          const parentInfo = factoryParentChildMap.value.get(factoryInfo.id);
          // 如果有父水厂，则记录父水厂的名称
          if (parentInfo && parentInfo.parent) {
            if (!uniqueParentFactories.has(parentInfo.parent.name)) {
              uniqueParentFactories.add(parentInfo.parent.name);

              // 查找父水厂的orderNum
              const parentFactory = factoryDataList.find(f => f.factoryId === parentInfo.parent?.id);
              const parentOrderNum = parentFactory?.orderNum || 9999;

              parentFactoryOrderMap.set(parentInfo.parent.name, {
                index: uniqueParentFactories.size - 1,
                orderNum: parentOrderNum
              });
            }
          } else {
            // 如果没有父水厂，则记录自己的名称作为父水厂
            if (!uniqueParentFactories.has(factoryInfo.name)) {
              uniqueParentFactories.add(factoryInfo.name);

              parentFactoryOrderMap.set(factoryInfo.name, {
                index: uniqueParentFactories.size - 1,
                orderNum: factory.orderNum || 9999
              });
            }
          }
        }
      });

      // 按父子关系组织数据
      const processedFactoryIds = new Set<number>();

      // 按照原始顺序处理所有工厂数据
      for (const factory of factoryDataList) {
        if (processedFactoryIds.has(factory.factoryId)) {
          continue;
        }

        processedFactoryIds.add(factory.factoryId);

        // 获取水厂信息
        const factoryInfo = allFactories.value.find(f => f.id === factory.factoryId);
        if (!factoryInfo) continue;

        const parentInfo = factoryParentChildMap.value.get(factoryInfo.id);
        let parentName: string;
        let isChildFactory = false;

        if (parentInfo && parentInfo.parent) {
          // 是子水厂
          parentName = parentInfo.parent.name;
          isChildFactory = true;
        } else {
          // 是父水厂或独立水厂
          parentName = factoryInfo.name;
        }

        // 添加当前水厂的数据
        addFactoryDataToTable(
          factory,
          parentName,
          factoryInfo.name,
          parentFactoryOrderMap.get(parentName)?.index || 0,
          parentFactoryOrderMap.get(parentName)?.orderNum || 9999,
          isChildFactory,
          newTableData
        );

        // 如果是父水厂，添加所有子水厂的数据
        if (!isChildFactory) {
          const children = factoryParentChildMap.value.get(factoryInfo.id)?.children || [];

          for (const child of children) {
            const childData = factoryDataList.find(f => f.factoryId === child.id);
            if (childData && !processedFactoryIds.has(child.id)) {
              processedFactoryIds.add(child.id);

              addFactoryDataToTable(
                childData,
                parentName,
                child.name,
                parentFactoryOrderMap.get(parentName)?.index || 0,
                parentFactoryOrderMap.get(parentName)?.orderNum || 9999,
                true,
                newTableData
              );
            }
          }
        }
      }

      // 对数据进行排序: 优先按orderNum排序，然后按原始顺序排序，最后按行类型排序
      newTableData.sort((a, b) => {
        // 如果是不同的父工厂，按父工厂的 orderNum 排序
        if (a.parentFactoryName !== b.parentFactoryName) {
          const aParentOrderNum = factoryList.value.find(f => f.name === a.parentFactoryName)?.orderNum || 9999;
          const bParentOrderNum = factoryList.value.find(f => f.name === b.parentFactoryName)?.orderNum || 9999;
          if (aParentOrderNum !== bParentOrderNum) {
            return aParentOrderNum - bParentOrderNum;
          }
        }

        // 如果是同一个父工厂下的数据
        if (a.parentFactoryName === b.parentFactoryName) {
          // 如果一个是父工厂自己的数据，一个是子工厂的数据，父工厂数据优先
          if (a.isChildFactory !== b.isChildFactory) {
            return a.isChildFactory ? 1 : -1;
          }

          // 如果都是子工厂数据，按 orderNum 排序
          if (a.isChildFactory && b.isChildFactory) {
            const aOrderNum = a.orderNum ?? 9999;
            const bOrderNum = b.orderNum ?? 9999;
            if (aOrderNum !== bOrderNum) {
              return aOrderNum - bOrderNum;
            }
          }
        }

        // 如果 orderNum 相同，按名称排序
        if (a.factoryName !== b.factoryName) {
          return a.factoryName.localeCompare(b.factoryName);
        }

        // 最后按行类型排序
        const rowTypeOrder = { '当日': 0, '月累计': 1, '月最大': 2, '月最小': 3, '月平均': 4 };
        return rowTypeOrder[a.rowType] - rowTypeOrder[b.rowType];
      });

      tableData.value = newTableData;
    } else {
      ElMessage.warning('暂无数据')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    isLoading.value = false
  }
}

// 辅助函数：将工厂数据添加到表格数据数组中
const addFactoryDataToTable = (
  factoryData: any,
  parentName: string,
  factoryName: string,
  parentOrder: number,
  orderNum: number,
  isChildFactory: boolean,
  targetArray: any[]
) => {
  // 根据工厂ID或名称决定显示哪些行类型
  const isHefeiShuitou = factoryData.factoryId === 16 || factoryData.factoryName === '合肥水投';

  // 定义要显示的行类型
  const rowTypes = isHefeiShuitou ? [
    { type: '月最大', data: factoryData.max },
    { type: '月累计', data: factoryData.sum },
    { type: '月最小', data: factoryData.min },
    { type: '月平均', data: factoryData.avg }
  ] : [
    { type: '当日', data: factoryData.current },
    { type: '月累计', data: factoryData.sum },
    { type: '月最大', data: factoryData.max },
    { type: '月最小', data: factoryData.min },
    { type: '月平均', data: factoryData.avg }
  ];

  // 添加每种行类型的数据
  for (const { type, data } of rowTypes) {
    if (!data) continue; // 跳过无数据

    const baseRow = {
      factoryId: factoryData.factoryId,
      factoryName: factoryName,
      parentFactoryName: parentName,
      parentOrder: parentOrder,
      orderNum: factoryData.orderNum || orderNum,
      isChildFactory: isChildFactory,
      rowType: type
    };

    if (type === '月累计') {
      targetArray.push({
        ...baseRow,
        inFlowWaterVolume: formatNumber(data.inFlowWaterVolume || ''),
        dailyTreatment: formatNumber(data.dailyTreatment || ''),
        inPh: '/',
        inTemp: '/',
        inCodcr: '/',
        inBod5: '/',
        inSs: '/',
        inNh3n: '/',
        inTn: '/',
        inTp: '/',
        outPh: '/',
        outTemp: '/',
        outCodcr: '/',
        outBod5: '/',
        outSs: '/',
        outNh3n: '/',
        outTn: '/',
        outTp: '/'
      });
    } else {
      targetArray.push({
        ...baseRow,
        inFlowWaterVolume: formatNumber(data.inFlowWaterVolume || ''),
        dailyTreatment: formatNumber(data.dailyTreatment || ''),
        inPh: formatNumber(data.inPh),
        inTemp: formatNumber(data.inTemp),
        inCodcr: formatNumber(data.inCodcr),
        inBod5: formatNumber(data.inBod5),
        inSs: formatNumber(data.inSs),
        inNh3n: formatNumber(data.inNh3n),
        inTn: formatNumber(data.inTn),
        inTp: formatNumber(data.inTp),
        outPh: formatNumber(data.outPh),
        outTemp: formatNumber(data.outTemp),
        outCodcr: formatNumber(data.outCodcr),
        outBod5: formatNumber(data.outBod5),
        outSs: formatNumber(data.outSs),
        outNh3n: formatNumber(data.outNh3n),
        outTn: formatNumber(data.outTn),
        outTp: formatNumber(data.outTp)

      });
    }
  }
  // console.log(targetArray, 'targetArray')
};
const formatNumber = (val: any, digits = 4) => {
  if (val === null || val === undefined || val === '') return '';
  const num = Number(val);
  if (isNaN(num)) return '';
  return Number.isInteger(num) ? num.toString() : num.toFixed(digits);
};

// 处理日期变化
const handleDateRangeChange = (val: [string, string]) => {
  // 这里可以根据 dateRange 重新请求数据
  // 例如 loadData(val[0], val[1])
}

// 处理水厂选择变化
const handleFactoryChange = (value: number[]) => {
  // 处理父子水厂联动选择
  const currentSelection = [...value]

  console.log(value, oldSelec.value);
  const removedItems = oldSelec.value.filter(id => !value.includes(id))
  const addedItems = value.filter(id => !oldSelec.value.includes(id))

  // 处理新增的项
  addedItems.forEach(addedId => {
    // 如果是父级，添加所有子项
    const children = factoryParentChildMap.value.get(addedId)?.children || []
    if (children.length > 0) {
      children.forEach(child => {
        if (!currentSelection.includes(child.id)) {
          currentSelection.push(child.id)
        }
      })
    }

    // 如果有父级，添加父级
    const parent = factoryParentChildMap.value.get(addedId)?.parent
    if (parent && !currentSelection.includes(parent.id)) {
      currentSelection.push(parent.id)
    }
  })

  // 处理删除的项
  removedItems.forEach(removedId => {
    // 如果是父级，删除所有子项
    const children = factoryParentChildMap.value.get(removedId)?.children || []
    if (children.length > 0) {
      children.forEach(child => {
        const index = currentSelection.indexOf(child.id)
        if (index !== -1) {
          currentSelection.splice(index, 1)
        }
      })
    }

    // 如果有父级，检查父级下是否还有其他子项被选中
    const parent = factoryParentChildMap.value.get(removedId)?.parent
    if (parent) {
      const siblings = factoryParentChildMap.value.get(parent.id)?.children || []
      const hasOtherSelectedSiblings = siblings.some(sibling =>
        sibling.id !== removedId && currentSelection.includes(sibling.id)
      )

      // 如果没有其他子项被选中，则取消父级
      if (!hasOtherSelectedSiblings) {
        const index = currentSelection.indexOf(parent.id)
        if (index !== -1) {
          currentSelection.splice(index, 1)
        }
      }
    }
  })

  selectedFactory.value = currentSelection
  oldSelec.value = selectedFactory.value
}

// 格式化年月
const formatYearMonth = (dateStr: string) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月`
}

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 根据筛选条件过滤表格数据
const filteredTableData = computed(() => {
  if (!tableData.value || tableData.value.length === 0) {
    return []
  }

  // 如果没有选择任何水厂，显示所有数据
  if (selectedFactory.value.length === 0) {
    return tableData.value
  }

  // 获取所有选中的水厂ID
  const selectedFactoryIds = new Set(selectedFactory.value);

  // 只显示选中水厂的数据
  return tableData.value.filter(row => selectedFactoryIds.has(row.factoryId));
})

// 处理筛选条件变化
const handleFilterChange = () => {
  // 更新显示的指标，不需要重新加载数据
}

// 表头样式
const headerCellStyle = {
  backgroundColor: '#bde7f9',
  color: '#606266',
  fontWeight: 'bold',
  padding: '8px 0',
  textAlign: 'center' as const
}

// 自定义表头渲染
const renderHeader = ({ column, $index }) => {
  if ($index === 0) {  // 第一列
    return h('div', { style: 'text-align: center; font-weight: bold;' }, '名称');
  } else if ($index === 1) {  // 第二列 - 不显示标题
    return h('div', { style: 'visibility: hidden;' }, '');
  }
  return column.label;
}

// 指标选项
const indicatorOptions = [
  {
    label: '基本信息',
    options: [
      { value: 'inFlowWaterVolume', label: '进水流量' },
      { value: 'dailyTreatment', label: '出水流量' }
    ]
  },
  {
    label: '进水物理特性',
    options: [
      { value: 'inPh', label: '进水pH' },
      { value: 'inTemp', label: '进水水温' }
    ]
  },
  {
    label: '进水污染物浓度',
    options: [
      { value: 'inCodcr', label: '进水CODcr' },
      { value: 'inBod5', label: '进水BOD5' },
      { value: 'inSs', label: '进水SS' },
      { value: 'inNh3n', label: '进水NH3-N' },
      { value: 'inTn', label: '进水TN' },
      { value: 'inTp', label: '进水TP' }
    ]
  },
  {
    label: '出水物理特性',
    options: [
      { value: 'outPh', label: '出水pH' },
      { value: 'outTemp', label: '出水水温' }
    ]
  },
  {
    label: '出水污染物浓度',
    options: [
      { value: 'outCodcr', label: '出水CODcr' },
      { value: 'outBod5', label: '出水BOD5' },
      { value: 'outSs', label: '出水SS' },
      { value: 'outNh3n', label: '出水NH3-N' },
      { value: 'outTn', label: '出水TN' },
      { value: 'outTp', label: '出水TP' }
    ]
  }
]

// 根据指标名称获取指标标签
const getIndicatorLabel = (indicatorName: string) => {
  for (const group of indicatorOptions) {
    for (const option of group.options) {
      if (option.value === indicatorName) {
        return option.label
      }
    }
  }
  return indicatorName
}

// 获取指标标准配置
const getStandardConfigs = async (factoryId: number | null) => {
  try {
    // 如果factoryId为空，清空配置并返回
    if (factoryId === null) {
      indicatorStandards.value = {}
      return
    }

    const res = await getIndicatorStandardConfig({
      factoryId,
    })

    // 将返回的配置转换为便于使用的格式
    const standards: Record<string, any> = {}
    if (res && res && Array.isArray(res)) {
      res.forEach((item: IndicatorConfig) => {
        try {
          const config = item.configJson
          // 将中文标签转换回英文标识符
          const indicatorKey = Object.keys(indicatorOptions).reduce((acc, groupKey) => {
            const group = indicatorOptions[groupKey]
            const option = group.options.find(opt => opt.label === item.indicatorName)
            return option ? option.value : acc
          }, item.indicatorName)

          if (config && typeof config === 'object') {
            const keys = Object.keys(config)
            if (keys.includes('lowerLimit') && keys.includes('upperLimit')) {
              // 存在 lowerLimit 和 upperLimit
              standards[indicatorKey] = `${config.lowerLimit}-${config.upperLimit}`
            } else if (keys.includes('standard')) {
              // 只有 standard
              standards[indicatorKey] = config.standard || ''
            } else {
              standards[indicatorKey] = ''
            }
          } else {
            standards[indicatorKey] = ''
          }
        } catch (error) {
          console.error('解析标准值配置失败:', error)
          standards[item.indicatorName] = ''
        }
      })
    }

    indicatorStandards.value = standards
  } catch (error) {
    console.error('获取指标标准配置失败:', error)
  }
}

// 打开指标标准配置对话框
const openConfigDialog = async () => {
  // 总是初始化为 null，不默认选中任何水厂
  editFactoryId.value = null;

  // 准备编辑配置项
  editConfigs.value = []
  const allIndicators = [
    'inPh', 'inTemp', 'inCodcr', 'inBod5', 'inSs', 'inNh3n', 'inTn', 'inTp',
    'outPh', 'outTemp', 'outCodcr', 'outBod5', 'outSs', 'outNh3n', 'outTn', 'outTp'
  ]

  allIndicators.forEach(indicator => {
    editConfigs.value.push({
      indicatorName: indicator,
      standardValue: ''
    })
  })

  showStandardConfigDialog.value = true
}

// 监听月份变化
watch(dateRange, () => {
  loadData()
})

// 监听水厂选择变化
watch(editFactoryId, async (newFactoryId) => {
  if (!showStandardConfigDialog.value) return;

  // 如果选择的是"请选择水厂"（null），清空数据并返回
  if (newFactoryId === null) {
    editConfigs.value.forEach(config => {
      config.standardValue = ''
    })
    return
  }

  // 获取指标标准配置
  const res = await getIndicatorStandardConfig({
    factoryId: newFactoryId,
  })

  // 将返回的配置转换为便于使用的格式
  const standards: Record<string, any> = {}
  if (res && Array.isArray(res)) {
    res.forEach((item: IndicatorConfig) => {
      try {
        const config = item.configJson
        // 将中文标签转换回英文标识符
        const indicatorKey = Object.keys(indicatorOptions).reduce((acc, groupKey) => {
          const group = indicatorOptions[groupKey]
          const option = group.options.find(opt => opt.label === item.indicatorName)
          return option ? option.value : acc
        }, item.indicatorName)

        if (config && typeof config === 'object') {
          const keys = Object.keys(config)
          if (keys.includes('lowerLimit') && keys.includes('upperLimit')) {
            // 存在 lowerLimit 和 upperLimit
            standards[indicatorKey] = `${config.lowerLimit}-${config.upperLimit}`
          } else if (keys.includes('standard')) {
            // 只有 standard
            standards[indicatorKey] = config.standard || ''
          } else {
            standards[indicatorKey] = ''
          }
        } else {
          standards[indicatorKey] = ''
        }
      } catch (error) {
        console.error('解析标准值配置失败:', error)
        standards[item.indicatorName] = ''
      }
    })
  }

  // 更新编辑配置项的标准值
  editConfigs.value.forEach(config => {
    config.standardValue = standards[config.indicatorName] || ''
  })
})

// 保存指标标准配置
const saveStandardConfigs = async () => {
  try {
    // 添加水厂选择验证
    if (!editFactoryId.value) {
      ElMessage.warning('请先选择水厂')
      return
    }

    // 验证是否填写了标准值
    const hasStandardValue = editConfigs.value.some(item => item.standardValue && item.standardValue.trim() !== '')
    if (!hasStandardValue) {
      ElMessage.warning('请填写标准值')
      return
    }

    isLoading.value = true

    // 准备保存数据，只包含已填写的标准值
    const configs = editConfigs.value
      .filter(item => item.standardValue && item.standardValue.trim() !== '')
      .map(item => ({
        factoryId: editFactoryId.value,
        indicatorName: getIndicatorLabel(item.indicatorName),
        configJson: {
          [getIndicatorLabel(item.indicatorName)]: item.standardValue
        },
      }))

    await batchSaveIndicatorStandardConfig(configs)

    // 刷新标准配置
    await getStandardConfigs(editFactoryId.value === 0 ? null : editFactoryId.value)

    ElMessage.success('保存成功')
    showStandardConfigDialog.value = false
  } catch (error) {
    console.error('保存指标标准配置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    isLoading.value = false
  }
}

// 选中的指标
const selectedIndicators = ref<string[]>([])

// 合并单元格方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: {
  row: any,
  column: any,
  rowIndex: number,
  columnIndex: number
}) => {
  const rows = filteredTableData.value;
  if (rows.length === 0) return { rowspan: 1, colspan: 1 };

  // 获取当前水厂是否有子级
  const hasChildren = () => {
    // 查找是否有以当前水厂为父级的其他水厂
    return rows.some(r =>
      r.parentFactoryName === row.parentFactoryName &&
      r.factoryId !== row.factoryId
    );
  };

  // 合并父级水厂列
  if (columnIndex === 0) {  // 父级水厂列
    const parentFactoryName = row.parentFactoryName;

    // 查找同一父级水厂的第一行索引
    const firstSameParentIndex = rows.findIndex((r, i) =>
      r.parentFactoryName === parentFactoryName &&
      (i === 0 || rows[i - 1].parentFactoryName !== parentFactoryName)
    );

    if (firstSameParentIndex === -1) return { rowspan: 1, colspan: 1 };

    // 计算同一父级水厂的连续行数
    let sameParentCount = 0;
    for (let i = firstSameParentIndex; i < rows.length; i++) {
      if (rows[i].parentFactoryName === parentFactoryName) {
        sameParentCount++;
      } else {
        break;
      }
    }

    const hasChildFactory = hasChildren();

    // 如果是第一行，并且没有子级水厂，则横向合并父级和子级列
    if (rowIndex === firstSameParentIndex) {
      if (!hasChildFactory) {
        return { rowspan: sameParentCount, colspan: 2 };
      } else {
        return { rowspan: sameParentCount, colspan: 1 };
      }
    } else {
      // 后续行隐藏单元格
      return { rowspan: 0, colspan: 0 };
    }
  }

  // 子级水厂列
  if (columnIndex === 1) {  // 子级水厂列
    const parentFactoryName = row.parentFactoryName;
    const factoryId = row.factoryId;

    // 判断当前水厂是否有子级
    const hasChildFactory = hasChildren();

    // 如果没有子级水厂，子级水厂列不显示
    if (!hasChildFactory) {
      return { rowspan: 0, colspan: 0 };
    }

    // 查找同一水厂的第一行索引
    const firstSameFactoryIndex = rows.findIndex((r, i) =>
      r.factoryId === factoryId &&
      (i === 0 || rows[i - 1].factoryId !== factoryId)
    );

    if (firstSameFactoryIndex === -1) return { rowspan: 1, colspan: 1 };

    // 计算同一水厂的连续行数
    let sameFactoryCount = 0;
    for (let i = firstSameFactoryIndex; i < rows.length; i++) {
      if (rows[i].factoryId === factoryId) {
        sameFactoryCount++;
      } else {
        break;
      }
    }

    // 为同一水厂的第一行设置rowspan
    if (rowIndex === firstSameFactoryIndex) {
      return { rowspan: sameFactoryCount, colspan: 1 };
    } else {
      // 后续行隐藏单元格
      return { rowspan: 0, colspan: 0 };
    }
  }

  return { rowspan: 1, colspan: 1 };
}

// 判断是否显示列
const shouldShowColumn = (columnKey: string) => {
  // 基本信息始终显示
  if (columnKey === 'inFlowWaterVolume' || columnKey === 'dailyTreatment') {
    return true;
  }
  return selectedIndicators.value.includes(columnKey);
}

// 导出Excel
const handleExport = async () => {
  try {
    console.log('开始导出Excel...');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('水质数据');

    // 设置列宽
    worksheet.getColumn(1).width = 30; // 父级水厂名称列
    worksheet.getColumn(2).width = 30; // 子级水厂名称列
    worksheet.getColumn(3).width = 15; // 行类型列
    worksheet.getColumn(4).width = 12; // 日处理量第一列
    worksheet.getColumn(5).width = 12; // 日处理量第二列
    // 其他指标列宽
    for (let i = 6; i <= 20; i++) {
      worksheet.getColumn(i).width = 15;
    }

    // 表头样式
    const headerStyle = {
      font: { bold: true, size: 11 },
      alignment: { vertical: 'middle' as const, horizontal: 'center' as const, wrapText: true },
      fill: {
        type: 'pattern' as const,
        pattern: 'solid' as const,
        fgColor: { argb: 'FFE6E6E6' }
      },
      border: {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    };

    // 数据样式
    const dataStyle = {
      font: { size: 11 },
      alignment: { vertical: 'middle' as const, horizontal: 'center' as const },
      border: {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    };

    // 粗体数据样式（用于父级水厂）
    const boldDataStyle = {
      ...dataStyle,
      font: { bold: true, size: 11 }
    };

    console.log('创建表头...');
    // 添加表头
    const header1 = worksheet.addRow([
      '名称', '', '年度·月份', '水量', '',
      '进水物理特性', '进水物理特性', '进水污染物浓度', '进水污染物浓度', '进水污染物浓度', '进水污染物浓度', '进水污染物浓度', '进水污染物浓度',
      '出水物理特性', '出水污染物浓度', '出水污染物浓度', '出水污染物浓度', '出水污染物浓度', '出水污染物浓度', '出水污染物浓度'
    ]);

    const header2 = worksheet.addRow([
      '', '', formatYearMonth(dateRange.value[0]), '进水水量', '出水水量',
      'pH', '水温', 'CODcr', 'BOD₅', 'SS(L)', 'NH₃-N', 'TN', 'TP',
      'pH', '水温', 'CODcr', 'BOD₅', 'SS(L)', 'NH₃-N', 'TN', 'TP'
    ]);

    const header3 = worksheet.addRow([
      '', '', '单位', 'm³', 'm³',
      '/', '℃', 'mg/L', 'mg/L', 'mg/L', 'mg/L', 'mg/L', 'mg/L',
      '/', '℃', 'mg/L', 'mg/L', 'mg/L', 'mg/L', 'mg/L', 'mg/L'
    ]);

    // 设置表头样式
    [header1, header2, header3].forEach(row => {
      row.height = 30;
      row.eachCell(cell => {
        cell.style = headerStyle;
      });
    });

    // 使用序号格式合并表头单元格
    const mergeCellsByRowCol = (startRow, startCol, endRow, endCol) => {
      try {
        worksheet.mergeCells(startRow, startCol, endRow, endCol);
      } catch (error) {
        console.error(`合并单元格失败 [${startRow},${startCol}]-[${endRow},${endCol}]:`, error);
      }
    };

    // 合并表头
    // 名称占两列三行
    mergeCellsByRowCol(1, 1, 3, 2);  // 名称区域整体合并成3×2的一个大单元格

    // 其他表头合并
    mergeCellsByRowCol(1, 3, 2, 3);  // 年度月份
    // mergeCellsByRowCol(1, 4, 2, 5);  // 日处理量合并两行两列
    // mergeCellsByRowCol(3, 4, 3, 5);  // m³单位合并两列
    // 设置年度月份单元格的值
    const yearMonthCell = worksheet.getCell(1, 3);
    yearMonthCell.value = formatYearMonth(dateRange.value[0]);

    // 进水物理特性
    mergeCellsByRowCol(1, 4, 1, 5);  // 水量标题横向
    mergeCellsByRowCol(2, 4, 2, 4);  // 进水水量
    mergeCellsByRowCol(2, 5, 2, 5);  // 出水水量

    // 进水物理特性
    mergeCellsByRowCol(1, 6, 1, 7);  // 进水物理特性标题横向
    mergeCellsByRowCol(2, 6, 2, 6);  // pH
    mergeCellsByRowCol(2, 7, 2, 7);  // 水温

    // 进水污染物浓度
    mergeCellsByRowCol(1, 8, 1, 13); // 进水污染物浓度标题横向
    mergeCellsByRowCol(2, 8, 2, 8);  // CODcr
    mergeCellsByRowCol(2, 9, 2, 9);  // BOD₅
    mergeCellsByRowCol(2, 10, 2, 10); // SS
    mergeCellsByRowCol(2, 11, 2, 11); // NH₃-N
    mergeCellsByRowCol(2, 12, 2, 12); // TN
    mergeCellsByRowCol(2, 13, 2, 13); // TP


    // 出水物理特性
    mergeCellsByRowCol(1, 14, 1, 15);// 进水污染物浓度标题横

    mergeCellsByRowCol(2, 14, 2, 14); // pH
    mergeCellsByRowCol(2, 15, 2, 15); // 水温

    // 出水污染物浓度
    mergeCellsByRowCol(1, 16, 1, 21); // 出水污染物浓度标题横向
    mergeCellsByRowCol(2, 16, 2, 16); // CODcr
    mergeCellsByRowCol(2, 17, 2, 17); // BOD₅
    mergeCellsByRowCol(2, 18, 2, 18); // SS
    mergeCellsByRowCol(2, 19, 2, 19); // NH₃-N
    mergeCellsByRowCol(2, 20, 2, 20); // TN
    mergeCellsByRowCol(2, 21, 2, 21); // TP

    // 从第4行开始添加数据
    let currentRow = 4;

    console.log('准备数据...');
    // 按工厂分组处理数据
    // 创建一个父子关系映射
    const factoryMap = new Map(); // 存储工厂ID -> 工厂对象
    const parentChildMap = new Map(); // 父工厂ID -> 子工厂ID数组

    // 处理所有行数据，建立父子关系
    filteredTableData.value.forEach(row => {
      const factoryId = row.factoryId;

      // 如果工厂没有记录，添加记录
      if (!factoryMap.has(factoryId)) {
        factoryMap.set(factoryId, {
          id: factoryId,
          name: row.factoryName,
          parentName: row.parentFactoryName,
          isChild: row.parentFactoryName !== row.factoryName,
          orderNum: row.orderNum || 9999, // 保存orderNum
          rows: []
        });

        // 如果是子工厂，添加到父工厂的子列表中
        if (row.parentFactoryName !== row.factoryName) {
          // 查找父工厂ID
          let parentId = null;
          for (const [id, factory] of factoryMap.entries()) {
            if (factory.name === row.parentFactoryName) {
              parentId = id;
              break;
            }
          }

          if (parentId) {
            if (!parentChildMap.has(parentId)) {
              parentChildMap.set(parentId, []);
            }
            parentChildMap.get(parentId).push(factoryId);
          }
        }
      }

      // 添加行数据到对应工厂
      factoryMap.get(factoryId).rows.push(row);
    });

    console.log('开始生成Excel内容...');

    // 获取所有顶级工厂（没有父级或自己是父级）并按orderNum排序
    const topLevelFactories = Array.from(factoryMap.values())
      .filter(factory => !factory.isChild)
      .sort((a, b) => {
        // 优先按orderNum排序
        if (a.orderNum !== b.orderNum) {
          return a.orderNum - b.orderNum;
        }
        // 然后按名称排序
        return a.name.localeCompare(b.name);
      });

    // 处理行类型排序
    const getOrderedRows = (rows, isSpecial) => {
      const rowMap = new Map();
      rows.forEach(row => rowMap.set(row.rowType, row));

      const types = isSpecial ?
        ['月累计', '月最大', '月最小', '月平均'] :
        ['当日', '月累计', '月最大', '月最小', '月平均'];

      return types
        .filter(type => rowMap.has(type))
        .map(type => rowMap.get(type));
    };

    // 遍历处理所有顶级工厂
    for (const factory of topLevelFactories) {
      const factoryId = factory.id;
      const childFactoryIds = parentChildMap.get(factoryId) || [];
      const hasChildren = childFactoryIds.length > 0;

      const factoryStartRow = currentRow;
      const isSpecial = factoryId === 16 || factory.name === '合肥水投';

      // 获取当前工厂的所有行数据
      const factoryRows = getOrderedRows(factory.rows, isSpecial);
      if (factoryRows.length === 0) continue;

      // 如果是无子级的独立水厂
      if (!hasChildren) {
        // 添加独立水厂数据行
        for (let i = 0; i < factoryRows.length; i++) {
          const rowData = factoryRows[i];
          const row = worksheet.addRow([
            i === 0 ? factory.name : '', // 第一列显示水厂名称（仅第一行）
            '',                          // 第二列留空
            rowData.rowType,             // 行类型
            rowData.inFlowWaterVolume || '', // 进水水量
            rowData.dailyTreatment || '', // 出水水量
            // '',                          // 留空,后续会合并
            rowData.inPh || '',
            rowData.inTemp || '',
            rowData.inCodcr || '',
            rowData.inBod5 || '',
            rowData.inSs || '',
            rowData.inNh3n || '',
            rowData.inTn || '',
            rowData.inTp || '',
            rowData.outPh || '',
            rowData.outTemp || '',
            rowData.outCodcr || '',
            rowData.outBod5 || '',
            rowData.outSs || '',
            rowData.outNh3n || '',
            rowData.outTn || '',
            rowData.outTp || ''
          ]);

          row.height = 25;
          row.eachCell(cell => {
            cell.style = dataStyle;
          });

          if (i === 0) {
            const nameCell = row.getCell(1);
            nameCell.style = boldDataStyle;
          }

          // 合并日处理量数据列
          // mergeCellsByRowCol(currentRow, 4, currentRow, 5);

          currentRow++;
        }

        // 不要先垂直合并第一列，直接创建n×2的合并单元格
        const endRow = factoryStartRow + factoryRows.length - 1;
        // 确保合并单元格横跨两列(1-2列)，纵跨所有行
        mergeCellsByRowCol(factoryStartRow, 1, endRow, 2);
        console.log(`合并独立水厂单元格: [${factoryStartRow},1]-[${endRow},2]`);
      }
      // 如果是有子级的父级水厂
      else {
        // 父级水厂自己的数据
        if (factoryRows.length > 0) {
          // 添加父级水厂自己的数据行
          for (let i = 0; i < factoryRows.length; i++) {
            const rowData = factoryRows[i];
            const row = worksheet.addRow([
              '', // 第一列留空，随后会合并
              factory.name, // 第二列显示父级水厂自己的名称
              rowData.rowType,
              rowData.inFlowWaterVolume || '', // 进水流量
              rowData.dailyTreatment || '', // 出水水量
              // '', // 留空,后续会合并
              rowData.inPh || '',
              rowData.inTemp || '',
              rowData.inCodcr || '',
              rowData.inBod5 || '',
              rowData.inSs || '',
              rowData.inNh3n || '',
              rowData.inTn || '',
              rowData.inTp || '',
              rowData.outPh || '',
              rowData.outTemp || '',
              rowData.outCodcr || '',
              rowData.outBod5 || '',
              rowData.outSs || '',
              rowData.outNh3n || '',
              rowData.outTn || '',
              rowData.outTp || ''
            ]);

            row.height = 25;
            row.eachCell(cell => {
              cell.style = dataStyle;
            });

            // 设置第二列的父级水厂名称为粗体（只对第一行）
            if (i === 0) {
              const nameCell = row.getCell(2);
              nameCell.style = boldDataStyle;
            }

            // 合并日处理量数据列
            // mergeCellsByRowCol(currentRow, 4, currentRow, 5);

            currentRow++;
          }

          // 如果父级水厂有多行数据，垂直合并第二列的父级水厂名称
          if (factoryRows.length > 1) {
            mergeCellsByRowCol(factoryStartRow, 2, factoryStartRow + factoryRows.length - 1, 2);
          }
        }

        const parentEndRow = factoryStartRow + factoryRows.length - 1;

        // 获取并排序子级水厂
        const childFactories = childFactoryIds
          .map(id => factoryMap.get(id))
          .filter(Boolean)
          .sort((a, b) => {
            // 优先按 orderNum 排序
            if (a.orderNum !== b.orderNum) {
              return a.orderNum - b.orderNum;
            }
            // 如果 orderNum 相同，按名称排序
            return a.name.localeCompare(b.name);
          });

        // 处理所有子级水厂
        for (const childFactory of childFactories) {
          const childStartRow = currentRow;
          const childRows = getOrderedRows(childFactory.rows, childFactory.id === 16 || childFactory.name === '合肥水投');
          if (childRows.length === 0) continue;

          // 添加子级水厂数据行
          for (let i = 0; i < childRows.length; i++) {
            const rowData = childRows[i];
            const row = worksheet.addRow([
              '', // 第一列留空
              i === 0 ? childFactory.name : '', // 第二列显示子级水厂名称（仅第一行）
              rowData.rowType,
              rowData.inFlowWaterVolume || '', // 进水流量
              rowData.dailyTreatment || '', // 出水水量
              // '', // 留空,后续会合并
              rowData.inPh || '',
              rowData.inTemp || '',
              rowData.inCodcr || '',
              rowData.inBod5 || '',
              rowData.inSs || '',
              rowData.inNh3n || '',
              rowData.inTn || '',
              rowData.inTp || '',
              rowData.outPh || '',
              rowData.outTemp || '',
              rowData.outCodcr || '',
              rowData.outBod5 || '',
              rowData.outSs || '',
              rowData.outNh3n || '',
              rowData.outTn || '',
              rowData.outTp || ''
            ]);

            row.height = 25;
            row.eachCell(cell => {
              cell.style = dataStyle;
            });

            // 合并日处理量数据列
            // mergeCellsByRowCol(currentRow, 4, currentRow, 5);

            currentRow++;
          }

          // 如果子级水厂有多行数据，垂直合并第二列的子级水厂名称
          if (childRows.length > 1) {
            mergeCellsByRowCol(childStartRow, 2, childStartRow + childRows.length - 1, 2);
          }
        }

        // 父级水厂名称要横跨整个区域（包括子级水厂）
        const familyEndRow = currentRow - 1;
        if (familyEndRow >= factoryStartRow) {
          // 在第一列添加父级水厂名称（合并整个家族的单元格）
          worksheet.getCell(factoryStartRow, 1).value = factory.name;
          worksheet.getCell(factoryStartRow, 1).style = boldDataStyle;

          // 垂直合并第一列的父级水厂名称
          mergeCellsByRowCol(factoryStartRow, 1, familyEndRow, 1);
        }
      }
    }

    console.log('生成Excel文件...');
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const start = dateRange.value[0]
    const end = dateRange.value[1]
    saveAs(blob, `水质数据_${start}_${end}.xlsx`);

    console.log('导出成功!');
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出Excel时发生错误:', error);
    ElMessage.error('导出失败，请重试');
  }
};

// 获取指标的标准值
const getStandardValue = (indicatorName: string) => {
  // 如果没有水厂选择，直接返回空值
  if (selectedFactory.value.length === 0) {
    return ''
  }

  if (indicatorStandards.value[indicatorName]) {
    return indicatorStandards.value[indicatorName].standardValue || ''
  }
  return ''
}

// 在 script setup 部分添加以下代码
const handleStandardValueInput = (value: string, row: any) => {
  // pH 值特殊处理
  if (row.indicatorName === 'inPh' || row.indicatorName === 'outPh') {
    // 只允许输入数字和连字符
    const newValue = value.replace(/[^\d-]/g, '')
    // 确保只有一个连字符
    const parts = newValue.split('-')
    if (parts.length > 2) {
      row.standardValue = parts[0] + '-' + parts[1]
    } else {
      row.standardValue = newValue
    }
  } else {
    // 其他指标只允许输入数字和小数点
    const newValue = value.replace(/[^\d.]/g, '')
    // 确保只有一个小数点
    const parts = newValue.split('.')
    if (parts.length > 2) {
      row.standardValue = parts[0] + '.' + parts[1]
    } else {
      row.standardValue = newValue
    }
  }
}

// 权限检查函数
const hasPermission = (permission: string) => {
  return userStore.getRoles.includes(permission)
}

// 添加管理员权限判断
const isAdmin = computed(() => {
  return hasPermission('report_admin')
})

onMounted(async () => {
  isLoading.value = true
  // 设置默认显示的指标
  selectedIndicators.value = [
    'inPh', 'inTemp', 'inCodcr', 'inBod5', 'inSs', 'inNh3n', 'inTn', 'inTp',
    'outPh', 'outTemp', 'outCodcr', 'outBod5', 'outSs', 'outNh3n', 'outTn', 'outTp'
  ];

  await getFactoryList()
  // 默认不选择任何水厂，这样会查询全部水厂的数据
  selectedFactory.value = []
  await loadData()

  // 获取指标标准配置
  await getStandardConfigs(null)

  isLoading.value = false
})
</script>

<style scoped>
.app-container {
  display: flex;
  height: calc(100vh - 40px);
  /* padding: 20px; */
  flex-direction: column;
}

.table-card {
  display: flex;
  /* margin-top: 32px;
  margin-left: 32px; */
  overflow: hidden;
  flex: 1;
  flex-direction: column;
}

.table-card :deep(.el-card__body) {
  display: flex;
  padding: 24px 24px 0;
  overflow: hidden;
  flex: 1;
  flex-direction: column;
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.header-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.control-item {
  width: 240px !important;
}

.date-picker {
  width: 200px !important;
}

.header-cell {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
}

.header-item {
  display: flex;
  min-height: 28px;
  padding: 8px 2px;
  border-bottom: 1px solid #E9E9E9;
  align-items: center;
  justify-content: center;
}

.header-item:last-child {
  border-bottom: none;
}

.parent-factory {
  font-weight: bold;
}

.child-factory {
  display: block;
  padding-left: 20px;
}

/* 自定义表头样式 */
.custom-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-weight: bold;
}
</style>
