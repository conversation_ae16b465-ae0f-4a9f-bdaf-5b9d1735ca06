<template>
  <div>
    <el-dialog v-model="dialogVisible" ref="dialog" :before-close="handleClose" width="50%" center>
      <template #header>
        <div class="flex items-center">
          <el-icon class="mr-2"><Edit /></el-icon>
          <span class="text-lg font-bold">告警规则配置</span>
        </div>
      </template>
      <el-divider />
      <div class="flex flex-col gap-1 w-full overflow-auto">
        <el-card style="width: 100%">
          <template #header>
            <div class="card-header">
              <div class="flex items-center gap-2">
                <span
                  ><img src="@/assets/imgs/sweage/config1.png" class="w-[13px] h-[16px]"
                /></span>
                <span class="font-bold">基本信息配置</span>
              </div>
            </div>
          </template>
          <div class="w-full h-[100px] flex justify-center items-center">
            <el-form :inline="true" :model="formInline" size="small" class="flex items-center">
              <el-row justify="space-around">
                <el-col :span="9">
                  <el-form-item label="告警指标：" class="w-[300px]">
                    <el-input
                      v-model="formInline.alarmIndex"
                      placeholder="请输入指标名称"
                      clearable
                      :disabled="true"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="告警类型：" class="w-[300px]">
                    <el-select
                      v-model="formInline.alarmType"
                      placeholder="请选择告警类型"
                      clearable
                      :disabled="true"
                    >
                      <el-option label="设备告警" value="device" />
                      <el-option label="系统告警" value="system" />
                      <el-option label="环境告警" value="environment" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="指标单位：" class="w-[300px]">
                    <el-input
                      v-model="formInline.indexUnit"
                      placeholder="请输入指标单位"
                      :disabled="true"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="指标编码：" class="w-[300px]">
                    <el-input
                      v-model="formInline.indexCode"
                      placeholder="指标编码"
                      :disabled="true"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-card>
        <el-card style="width: 100%">
          <template #header>
            <div class="card-header">
              <div class="flex items-center gap-2">
                <span
                  ><img src="@/assets/imgs/sweage/config2.png" class="w-[13px] h-[16px]"
                /></span>
                <span class="font-bold">告警条件配置</span>
              </div>
            </div>
          </template>
          <div class="w-full h-[160px] flex justify-center items-center">
            <el-form :inline="true" :model="formInline" size="small" class="flex items-center">
              <el-row justify="space-around">
                <el-col :span="12">
                  <el-form-item label="条件类型" class="w-[300px]">
                    <el-select v-model="formInline.conditionType" placeholder="请选择条件类型">
                      <el-option label="大于" value="greater" />
                      <el-option label="小于" value="less" />
                      <el-option label="等于" value="equal" />
                      <el-option label="区间" value="between" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="阈值" class="w-[300px]">
                    <template v-if="formInline.conditionType === 'between'">
                      <el-input v-model="formInline.minValue" placeholder="最小值" style="width: 120px; margin-right: 8px;"/>
                      <span>~</span>
                      <el-input v-model="formInline.maxValue" placeholder="最大值" style="width: 120px; margin-left: 8px;"/>
                    </template>
                    <template v-else>
                      <el-input v-model="formInline.value" placeholder="请输入阈值" style="width: 260px;"/>
                    </template>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="持续时间(分钟)：" class="w-[300px]">
                    <el-input-number
                      v-model="formInline.duration"
                      :min="1"
                      :max="60"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-card>
        <el-card style="width: 100%">
          <template #header>
            <div class="card-header">
              <div class="flex items-center gap-2">
                <span
                  ><img src="@/assets/imgs/sweage/config3.png" class="w-[13px] h-[16px]"
                /></span>
                <span class="font-bold">数据质量配置</span>
              </div>
            </div>
          </template>
          <div class="w-full h-[60px] flex justify-center items-center">
            <el-form :inline="true" :model="formInline" size="small" class="flex items-center">
              <el-row justify="space-around">
                <el-col :span="12">
                  <el-form-item label="有效上限：" class="w-[300px]">
                    <el-input
                      v-model="formInline.upperLimit"
                      placeholder="请输入数值"
                      clearable
                    >
                      <template #append>{{ formInline.indexUnit }}</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="有效下限：" class="w-[300px]">
                    <el-input v-model="formInline.lowerLimit" placeholder="请输入数值" clearable>
                      <template #append>{{ formInline.indexUnit }}</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-card>
        <el-card style="width: 100%">
          <template #header>
            <div class="card-header">
              <div class="flex items-center gap-2">
                <span
                  ><img src="@/assets/imgs/sweage/config4.png" class="w-[13px] h-[16px]"
                /></span>
                <span class="font-bold">告警应对建议配置</span>
              </div>
            </div>
          </template>
          <div class="w-full flex items-center">
            <el-form :inline="true" :model="formInline" size="small">
              <el-row justify="center">
                <el-col :span="24">
                  <el-form-item label="告警应对建议" class="flex items-center">
                    <el-input
                      v-model="formInline.alarmSuggestion"
                      style="width: 800px"
                      :rows="2"
                      type="textarea"
                      placeholder="请输入告警应对建议"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-card>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const dialogVisible = ref(false)
const currentRow = ref(null)

const formInline = reactive({
  alarmIndex: '',         // 告警指标名称
  alarmType: '',          // 告警类型
  indexUnit: '',          // 指标单位
  indexCode: '',          // 指标编码
  conditionType: '',      // 条件类型
  value: '',              // 单值
  minValue: '',           // 区间下限
  maxValue: '',           // 区间上限
  duration: 5,            // 持续时间（分钟）
  alarmSuggestion: ''     // 告警应对建议
})

function handleClose() {
  dialogVisible.value = false
}

function handleSubmit() {
  // 验证表单
  if (!formInline.conditionType || (formInline.conditionType === 'between' && (!formInline.minValue || !formInline.maxValue)) || (formInline.conditionType !== 'between' && !formInline.value)) {
    ElMessage.warning('请填写完整的告警条件信息')
    return
  }
  // 保存数据
  // 输出格式：{ name, type, conditionType, value, minValue, maxValue, duration, ... }
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

function setVisible(row) {
  if (row) {
    currentRow.value = row
    formInline.alarmIndex = row.name || ''
    formInline.alarmType = row.type || ''
    formInline.indexCode = row.code || ''
    formInline.conditionType = row.conditionType || ''
    formInline.value = row.value || ''
    formInline.minValue = row.minValue || ''
    formInline.maxValue = row.maxValue || ''
    formInline.duration = row.duration || 5
    formInline.alarmSuggestion = row.alarmSuggestion || ''
  } else {
    Object.keys(formInline).forEach(key => {
      formInline[key] = key === 'duration' ? 5 : ''
    })
  }
  dialogVisible.value = true
}

defineExpose({
  setVisible
})
</script>
<style scoped lang="scss">
:deep(.el-card__header) {
  padding: 4px;
}

:deep(.el-card__body) {
  padding: 4px !important;
}

:deep(.el-form-item) {
  margin-bottom: 20px !important;
}
</style>
