<template>
  <div class="app-container">
    <el-card>
      <el-tabs v-model="activeName" type="card" tab-position="top" class="components_el-tabs flex flex-col h-full">
        <el-tab-pane label="告警规则配置" name="alarmInfo">
          <AlarmInfoConfig :is-active="activeName === 'alarmInfo'" />
        </el-tab-pane>
        <el-tab-pane label="告警通知设置" name="alarmNotify">
          <AlarmNotifyConfig :is-active="activeName === 'alarmNotify'" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import AlarmInfoConfig from './components/AlarmInfoConfig.vue'
import AlarmNotifyConfig from './components/AlarmNotifyConfig.vue'
import { getAlarmRules, getNotificationSettings } from '@/api/alarm'

const activeName = ref('alarmInfo')
const alarmInfoData = ref<any[]>([])
const notifyConfigData = ref<any[]>([])

// 页面初始化时使用默认标签页
onMounted(() => {
  // 不需要手动调用接口，子组件会在激活状态下自动加载数据
})

// 通过子组件的is-active属性控制数据加载，不需要在这里监听标签变化

// 获取告警规则配置 - 调用 /monitor/alarm/rules/page 接口
async function fetchAlarmRules() {
  try {
    // 调用接口获取数据
    const response = await getAlarmRules({
      page: 1,
      pageSize: 10
    })
    console.log('获取告警规则成功:', response)

    // 更新数据
    if (response && Array.isArray(response)) {
      alarmInfoData.value = response
    }
  } catch (error) {
    console.error('获取告警规则失败:', error)
  }
}

// 获取告警通知设置 - 调用 /monitor/alarm/notify-config/page 接口
async function fetchNotificationSettings() {
  try {
    // 调用接口获取数据
    const response = await getNotificationSettings({
      page: 1,
      pageSize: 10
    })
    console.log('获取告警通知设置成功:', response)

    // 更新数据
    if (response && response.data) {
      notifyConfigData.value = response.data
    }
  } catch (error) {
    console.error('获取告警通知设置失败:', error)
  }
}
</script>
<style scoped lang="scss">
:deep(.el-tabs--top) {
  flex-direction: column-reverse !important;
}
</style>
