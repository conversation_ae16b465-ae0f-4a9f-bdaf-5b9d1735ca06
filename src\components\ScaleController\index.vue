<template>
  <div class="scale-controller" :class="{ 'is-collapsed': collapsed }">
    <div class="controller-toggle" @click="collapsed = !collapsed">
      <el-icon>
        <ZoomIn />
      </el-icon>
    </div>

    <div v-if="!collapsed" class="controller-content">
      <div class="scale-info">
        <span>缩放比例: {{ scalePercent }}%</span>
        <span class="resolution-info">分辨率: {{ screenWidth }} x {{ screenHeight }}</span>
      </div>

      <div class="scale-slider">
        <el-slider v-model="scaleValue" :min="80" :max="200" :step="5" :format-tooltip="formatTooltip"
          @change="handleScaleChange" />
      </div>

      <div class="scale-buttons">
        <el-button size="small" @click="setScale(1)">重置 (100%)</el-button>
        <el-button size="small" @click="setScale(1.25)">125%</el-button>
        <el-button size="small" @click="setScale(1.5)">150%</el-button>
        <el-button size="small" @click="setScale(1.75)">175%</el-button>
        <el-button size="small" @click="setScale(2)">200%</el-button>
      </div>

      <div class="scale-fix">
        <el-button size="small" type="primary" @click="fixLayout">修复布局问题</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ZoomIn } from '@element-plus/icons-vue'
import { getCurrentScale, setManualScale, fixCurrentLayout } from '@/utils/screenScaling'

// 是否折叠控制器
const collapsed = ref(true);

// 缩放值（百分比）
const scaleValue = ref(100);

// 屏幕宽高
const screenWidth = ref(window.screen.width);
const screenHeight = ref(window.screen.height);

// 格式化缩放比例为百分比
const scalePercent = computed(() => {
  return Math.round(scaleValue.value);
});

// 格式化tooltip显示
const formatTooltip = (val: number) => {
  return `${val}%`;
};

// 处理缩放变化
const handleScaleChange = (value: number) => {
  const scale = value / 100;
  setScale(scale);
};

// 设置缩放比例
const setScale = (scale: number) => {
  scaleValue.value = Math.round(scale * 100);
  setManualScale(scale);

  // 延迟执行布局修复，确保缩放已完成
  setTimeout(() => {
    fixLayout();
  }, 300);
};

// 修复布局问题
const fixLayout = () => {
  fixCurrentLayout();
};

// 更新屏幕信息
const updateScreenInfo = () => {
  screenWidth.value = window.screen.width;
  screenHeight.value = window.screen.height;

  // 更新当前缩放值
  const currentScale = getCurrentScale();
  scaleValue.value = Math.round(currentScale * 100);
};

onMounted(() => {
  // 初始化缩放值
  const currentScale = getCurrentScale();
  scaleValue.value = Math.round(currentScale * 100);

  // 监听窗口大小变化
  window.addEventListener('resize', updateScreenInfo);

  // 监听自定义事件
  window.addEventListener('screen-scaling-auto-adjust', updateScreenInfo);

  // 初始化时修复布局
  setTimeout(() => {
    fixLayout();
  }, 500);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenInfo);
  window.removeEventListener('screen-scaling-auto-adjust', updateScreenInfo);
});
</script>

<style lang="scss" scoped>
.scale-controller {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 10px;
  z-index: 9999;
  width: 300px;
  transition: all 0.3s;

  &.is-collapsed {
    width: 40px;
    height: 40px;
    padding: 0;

    .controller-toggle {
      width: 40px;
      height: 40px;
    }
  }

  .controller-toggle {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 30px;

    .el-icon {
      font-size: 20px;
      color: #409EFF;
    }
  }

  .controller-content {
    margin-top: 10px;
  }

  .scale-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;

    .resolution-info {
      color: #909399;
      font-size: 12px;
    }
  }

  .scale-slider {
    margin-bottom: 15px;
  }

  .scale-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;

    .el-button {
      flex-grow: 1;
      min-width: 60px;
    }
  }

  .scale-fix {
    text-align: center;

    .el-button {
      width: 100%;
    }
  }
}
</style>