<template>
  <div class="flow-chart-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="zoom-controls">
        <button @click="zoomOut" class="custom-btn text-btn">
          缩小
        </button>
        <span class="zoom-level">{{ Math.round(props.zoom * 100) }}%</span>
        <button @click="zoomIn" class="custom-btn text-btn">
          放大
        </button>
        <button @click="resetZoom" class="custom-btn primary-btn reset-btn">
          重置缩放
        </button>
        <button @click="resetPosition" class="custom-btn primary-btn reset-btn">
          重置位置
        </button>
      </div>
      <div class="tools-info">
        <!-- 缩放提示 -->
        <span class="zoom-tip">鼠标滚轮可缩放</span>

        <!-- 添加拖动和添加元素提示 -->
        <span class="operation-tip">按住Ctrl键可在SVG范围内拖动监测点，位置始终保持相对于SVG的坐标</span>

        <!-- 监测点数量统计 -->
        <span class="points-count" v-if="props.points.length > 0">
          <i class="el-icon-location-information"></i> 监测点数量: {{ props.points.length }}
        </span>

        <!-- 添加位点下拉框 -->
        <div class="add-element-controls">
          <el-dropdown trigger="click" @command="handleAddCommand">
            <el-button type="primary" size="small">
              添加监测点 <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu class="custom-dropdown">
                <div class="dropdown-section-title">监测点图形</div>
                <el-dropdown-item command="point-circle">
                  <div class="dropdown-item-content">
                    <span class="point-icon circle-point"></span>
                    <span>圆形监测点</span>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item command="point-square">
                  <div class="dropdown-item-content">
                    <span class="point-icon square-point"></span>
                    <span>方形监测点</span>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item command="point-triangle">
                  <div class="dropdown-item-content">
                    <span class="point-icon triangle-point"></span>
                    <span>三角形监测点</span>
                  </div>
                </el-dropdown-item>
                <div class="dropdown-section-title">文本框</div>
                <el-dropdown-item command="text-label">
                  <div class="dropdown-item-content">
                    <span class="point-icon text-icon"></span>
                    <span>标签文本</span>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 添加元素提示 -->
        <div v-if="addingElementType" class="adding-element-tip">
          <span>{{ getAddingElementTip() }}</span>
          <el-button size="small" type="danger" @click="cancelAddElement">取消添加</el-button>
        </div>
      </div>
    </div>

    <!-- SVG显示区域 -->
    <div class="svg-container" ref="svgContainerRef" @click="handleSvgClick"
      :class="{ 'adding-element': addingElementType }">
      <!-- SVG容器 -->
      <div class="svg-outer-wrapper" @mousedown="startSvgDrag" @mousemove="onSvgDrag" @mouseup="endSvgDrag"
        @mouseleave="endSvgDrag">
        <!-- SVG内容 -->
        <div class="svg-wrapper" :style="{
          transform: `scale(${props.zoom})`,
          transformOrigin: 'center center',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          height: '100%'
        }">
          <!-- SVG内容容器 -->
          <div class="svg-content" ref="svgElement"
            style="margin: auto; display: flex; justify-content: center; align-items: center;"></div>
        </div>
      </div>
    </div>

    <!-- 临时点位预览 (仅在添加过程中显示) -->
    <div v-if="temporaryPoint" class="temporary-point-preview" :style="getTemporaryPointPreviewStyle(temporaryPoint)">
      <template v-if="temporaryPoint.isText">
        {{ temporaryPoint.name || '文本标签' }}
      </template>
      <template v-else>
        <div class="preview-shape" :class="[`preview-${temporaryPoint.type}`]"></div>
      </template>
    </div>

    <!-- 位点编辑侧边栏 -->
    <el-drawer v-model="dialogVisible" title="监测点配置" size="400px" direction="rtl" :close-on-click-modal="false"
      :append-to-body="true" @close="handleDialogClose">
      <div class="drawer-content">
        <el-form :model="editingPoint" label-position="top" label-width="auto">
          <!-- 监测点形状 -->
          <el-form-item v-if="!editingPoint.isText" class="custom-form-item">
            <template #label>监测点形状</template>
            <el-select v-model="editingPoint.type" placeholder="请选择形状" class="full-width-select">
              <el-option v-for="type in pointTypes" :key="type.value" :label="type.label" :value="type.value" />
            </el-select>
          </el-form-item>

          <!-- 监测指标 -->
          <el-form-item v-if="!editingPoint.isText" class="custom-form-item">
            <template #label>
              <div class="form-label-with-star">
                <span class="required-star">*</span> 监测指标
              </div>
            </template>
            <el-select v-model="selectedIndicator" placeholder="请选择监测指标" class="full-width-select">
              <el-option v-for="item in indicatorOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <div class="form-item-tip">选择监测指标，关联实时数据</div>
          </el-form-item>

          <!-- 编号 -->
          <el-form-item v-if="!editingPoint.isText" class="custom-form-item">
            <template #label>
              <div class="form-label-with-star">
                <span class="required-star">*</span> 编号
              </div>
            </template>
            <el-input v-model="editingPoint.name" placeholder="请输入编号" />
            <div class="form-item-tip">监测点的唯一编号，用于系统识别</div>
          </el-form-item>

          <!-- 点位大小 -->
          <el-form-item v-if="!editingPoint.isText" class="custom-form-item">
            <template #label>点位大小</template>
            <div class="size-slider-container">
              <el-slider v-model="pointSize" :min="1" :max="20" :step="1" class="size-slider" />
              <div class="size-input-group">
                <el-button size="small" @click="decreasePointSize" class="size-btn">-</el-button>
                <el-input-number v-model="pointSize" :min="1" :max="20" :step="1" controls-position="right" size="small"
                  class="size-input" :controls="false" />
                <el-button size="small" @click="increasePointSize" class="size-btn">+</el-button>
              </div>
            </div>
            <div class="form-item-tip">调整监测点的显示大小</div>
          </el-form-item>

          <!-- 自定义颜色 -->
          <el-form-item v-if="!editingPoint.isText" class="custom-form-item">
            <template #label>自定义颜色</template>
            <el-color-picker v-model="pointColor" />
          </el-form-item>

          <!-- 坐标位置 -->
          <el-form-item v-if="!editingPoint.isText" class="custom-form-item">
            <template #label>坐标位置</template>
            <div class="coordinates">
              X: {{ Math.round(editingPoint.x) }} Y: {{ Math.round(editingPoint.y) }}
            </div>
          </el-form-item>

          <!-- 文本样式，仅对文本显示 -->
          <template v-if="editingPoint.isText">
            <el-form-item label="文本内容">
              <el-input v-model="editingPoint.name" />
            </el-form-item>

            <!-- 文本监测指标（非必填） -->
            <el-form-item label="关联监测指标" class="custom-form-item">
              <el-select v-model="selectedIndicator" placeholder="可选择关联监测指标" class="full-width-select" clearable>
                <el-option v-for="item in indicatorOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <div class="form-item-tip">文本可选择关联监测指标，用于数据展示</div>
            </el-form-item>

            <!-- 文本编号 -->
            <el-form-item label="编号" class="custom-form-item">
              <el-input v-model="textPointCode" placeholder="请输入编号" />
              <div class="form-item-tip">监测点的唯一编号，用于系统识别</div>
            </el-form-item>

            <el-form-item label="字体大小">
              <el-input-number :model-value="textFontSize" @update:model-value="val => textFontSize = val as number"
                :min="10" :max="36" :step="1" />
            </el-form-item>

            <el-form-item label="字体粗细">
              <el-select v-model="textFontWeight">
                <el-option label="正常" value="normal" />
                <el-option label="粗体" value="bold" />
              </el-select>
            </el-form-item>

            <el-form-item label="字体颜色">
              <el-color-picker v-model="textColor" />
            </el-form-item>



            <el-form-item label="坐标">
              <div class="coordinates">
                X: {{ Math.round(editingPoint.x) }} Y: {{ Math.round(editingPoint.y) }}
              </div>
            </el-form-item>
          </template>

          <div class="drawer-footer">
            <el-button type="danger" @click="deletePoint">删除</el-button>
            <el-button type="primary" @click="savePoint">保存</el-button>
          </div>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { getIndicatorList } from '@/api/monitor/screenManage'
import { useAppStore } from '@/store/modules/app'



// 定义位点接口
interface Point {
  id: string;
  x: number;
  y: number;
  name: string;
  type: string;
  data?: any;
  isText: boolean;
}

const props = defineProps({
  svgContent: {
    type: String,
    default: ''
  },
  screenId: {
    type: [String, Number],
    default: ''
  },
  zoom: {
    type: Number,
    default: 1
  },
  points: {
    type: Array as () => Point[],
    default: () => []
  }
})

// 添加计算属性判断是否是SVG URL
const isSvgUrl = computed(() => {
  if (!props.svgContent) return false;
  const content = props.svgContent.trim();
  // 检查是否是URL (http, https, //) 或文件路径(.svg/.ubak结尾)或API路径
  return content.startsWith('http://') ||
    content.startsWith('https://') ||
    content.startsWith('//') ||
    content.endsWith('.svg') ||
    content.endsWith('.ubak') ||
    content.includes('/api/')
});




const emit = defineEmits(['update:svg', 'update:zoom', 'update:points', 'factory-changed', 'export-svg'])

const svgContainerRef = ref<HTMLElement | null>(null)
const svgElement = ref<HTMLElement | null>(null)
const draggingPoint = ref<Point | null>(null)
const dialogVisible = ref(false)
const isNewPoint = ref(false)
const editingPoint = ref<Point>({
  id: '',
  x: 0,
  y: 0,
  name: '',
  type: 'default',
  data: {},
  isText: false
})
const temporaryPoint = ref<Point | null>(null)
const pointDataJson = ref('')
const textFontSize = ref<number>(14)
const textFontWeight = ref('normal')
const textColor = ref('#333333')
const textBackgroundColor = ref('rgba(255, 255, 255, 0.5)')
const hasTextBackground = ref(false)
const textBorderColor = ref('rgba(0, 0, 0, 0.3)')
const textBorderWidth = ref(1)
const textPointCode = ref('') // 添加文本编号变量

// 新增属性
const selectedIndicator = ref('')
const pointSize = ref(5)
const pointColor = ref('#409EFF')

// 定义监测指标选项接口
interface IndicatorOption {
  label: string;
  value: string;
  pointCode?: string;
  data?: {
    pointCode?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

// 监测指标选项
const indicatorOptions = ref<IndicatorOption[]>([
  { label: '加载中...', value: '' }
])

// 添加元素相关状态
const addingElementType = ref<string | null>(null)

// 在 script 部分添加 pointTypes 数组
const pointTypes = [
  { label: '圆形', value: 'circle' },
  { label: '方形', value: 'square' },
  { label: '三角形', value: 'triangle' }
]

// 获取当前水厂ID
const appStore = useAppStore()
const currentFactoryId = computed(() => appStore.getCurrentStation?.id)

// 监听全局水厂变化
watch(() => currentFactoryId.value, (newFactoryId, oldFactoryId) => {
  if (newFactoryId && newFactoryId !== oldFactoryId) {
    console.log('水厂已切换，更新页面数据', newFactoryId)

    // 如果弹窗当前打开，更新监测指标列表
    if (dialogVisible.value && !editingPoint.value.isText) {
      fetchIndicatorList()
    }

    // 如果点位包含指标信息，可能需要重新关联
    if (props.points && props.points.length > 0) {
      // 通知父组件水厂已变化，可能需要重新获取或处理点位数据
      emit('factory-changed', newFactoryId)
    }
  }
})

// 获取监测指标列表
const fetchIndicatorList = async () => {
  return new Promise<void>(async (resolve) => {
    try {
      // 获取当前水厂ID
      const factoryId = currentFactoryId.value
      if (!factoryId) {
        ElMessage.warning('未选择水厂，无法获取监测指标')
        resolve();
        return
      }

      // 显示加载状态
      indicatorOptions.value = [{ label: '加载中...', value: '' }]

      // 调用API获取监测指标列表
      const res = await getIndicatorList(factoryId)

      if (res.data && Array.isArray(res.data)) {
        // 转换为下拉选项格式
        const options = res.data.map((item: any) => ({
          label: item.pointName || item.indicatorName || item.label,
          value: item.indicatorId || item.indicatorCode || item.value,
          pointCode: item.pointCode || '',
          data: item
        }))

        if (options.length > 0) {
          indicatorOptions.value = options
        } else {
          indicatorOptions.value = [{ label: '暂无监测指标', value: '' }]
        }
      } else {
        // 如果返回数据格式不符合预期
        console.error('监测指标数据格式错误:', res)
        indicatorOptions.value = [{ label: '获取监测指标失败', value: '' }]
      }
      resolve();
    } catch (error) {
      console.error('获取监测指标失败:', error)
      ElMessage.error('获取监测指标列表失败')
      indicatorOptions.value = [{ label: '获取失败', value: '' }]
      resolve();
    }
  });
}

// 监听弹窗打开状态
watch(() => dialogVisible.value, (newValue) => {
  if (newValue) {
    // 弹窗打开时，无论是监测点还是文本点位，都获取监测指标列表
    console.log('弹窗已打开，获取监测指标列表');
    fetchIndicatorList()
  }
})

// 添加监听selectedIndicator变化，自动填充编号
watch(() => selectedIndicator.value, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    console.log('监测指标已选择:', newValue);

    // 查找选中的指标信息
    const selectedOption = indicatorOptions.value.find(item => item.value === newValue);

    if (selectedOption) {
      if (!editingPoint.value.isText) {
        // 对于非文本监测点，使用指标作为编号
        // 如果指标有pointCode属性，使用它作为编号
        if (selectedOption.pointCode) {
          console.log('使用指标的pointCode作为编号:', selectedOption.pointCode);
          editingPoint.value.name = selectedOption.pointCode;
        }
        // 如果存在data.pointCode属性，使用它作为编号
        else if (selectedOption.data?.pointCode) {
          console.log('使用指标data中的pointCode作为编号:', selectedOption.data.pointCode);
          editingPoint.value.name = selectedOption.data.pointCode;
        }
        // 否则使用指标的名称作为编号
        else if (editingPoint.value.name === '' || (isNewPoint.value && !editingPoint.value.name)) {
          console.log('使用指标名称作为编号:', selectedOption.label);
          editingPoint.value.name = selectedOption.label;
        }
      } else {
        // 对于文本元素，使用指标相关信息填充编号字段，不影响文本内容
        if (selectedOption.pointCode) {
          console.log('使用指标的pointCode作为文本编号:', selectedOption.pointCode);
          textPointCode.value = selectedOption.pointCode;
        } else if (selectedOption.data?.pointCode) {
          console.log('使用指标data中的pointCode作为文本编号:', selectedOption.data.pointCode);
          textPointCode.value = selectedOption.data.pointCode;
        } else {
          // 也可以不自动填充，保持手动输入的灵活性
        }
      }
    }
  }
});

// 处理SVG内容，确保安全和正确显示
const sanitizedSvgContent = computed(() => {
  if (!props.svgContent) return '';

  // 如果是URL，不再使用img标签，而是尝试直接嵌入SVG
  if (isSvgUrl.value) {
    console.log('SVG内容是URL格式，使用嵌入式SVG显示:', props.svgContent.substring(0, 50) + '...');

    // 创建一个包含SVG的容器，使用image嵌入图像
    // 使用适中的默认viewBox，避免初始缩放问题
    // 添加onload事件以在图像加载完成后重新计算尺寸和缩放
    return `<svg xmlns="http://www.w3.org/2000/svg" width="auto" height="auto" viewBox="0 0 800 600" class="url-svg-container" style="width:auto; height:auto; max-width:90%; max-height:90%; display:block; margin:0 auto;">
      <image href="${props.svgContent}" x="0" y="0" width="100%" height="100%" preserveAspectRatio="xMidYMid" class="url-svg-image" style="max-width:90%; max-height:90%;" onload="
        if(this.naturalWidth && this.naturalHeight) {
          const svgEl = this.parentElement;
          if(svgEl) {
            svgEl.setAttribute('viewBox', '0 0 ' + this.naturalWidth + ' ' + this.naturalHeight);
            // 通知Vue组件图像已加载
            const event = new CustomEvent('image-loaded', {
              detail: { width: this.naturalWidth, height: this.naturalHeight }
            });
            document.dispatchEvent(event);
          }
        }
      " />
      <g id="monitoring-points" class="monitoring-points-layer"></g>
    </svg>`;
  }

  // 确保SVG有正确的尺寸和viewBox
  let content = props.svgContent;

  // 使用DOMParser处理SVG内容
  const parser = new DOMParser();
  const svgDoc = parser.parseFromString(content, 'image/svg+xml');

  // 检查SVG是否包含监测点
  let hasSvgPoints = false;
  try {
    const monitoringGroup = svgDoc.querySelector('#monitoring-points');
    const dataPointElements = svgDoc.querySelectorAll('[data-point="true"]');
    hasSvgPoints = !!monitoringGroup || dataPointElements.length > 0;

    console.log(`SVG是否包含监测点组或点位元素: ${hasSvgPoints}`);

    if (hasSvgPoints) {
      // 添加事件监听器到所有监测点和文本元素
      setupSvgPointEvents(svgDoc);
    } else {
      // 如果没有明确的监测点，尝试识别并隐藏可能重复的点位元素
      const potentialPoints = svgDoc.querySelectorAll('[data-indicator-code], [data-show-type], [data-point], circle[r="5"], circle[r="4"], circle[r="3"]');
      console.log(`在SVG内容中找到${potentialPoints.length}个可能的位点元素，将移除它们`);

      potentialPoints.forEach((element) => {
        // 将可能的位点元素设为不可见
        element.setAttribute('visibility', 'hidden');
        element.setAttribute('display', 'none');

        // 保存元素的data-*属性，以便在解析位点时使用
        const indicatorCode = element.getAttribute('data-indicator-code');
        const showType = element.getAttribute('data-show-type');

        if (indicatorCode || showType) {
          element.setAttribute('data-preserved', 'true');
        }
      });
    }

    // 确保SVG有监测点容器
    const rootElement = svgDoc.documentElement;
    let pointsGroup = svgDoc.querySelector('#monitoring-points');
    if (!pointsGroup) {
      pointsGroup = svgDoc.createElementNS("http://www.w3.org/2000/svg", "g");
      pointsGroup.setAttribute("id", "monitoring-points");
      pointsGroup.setAttribute("class", "monitoring-points-layer");
      rootElement.appendChild(pointsGroup);
    }

    // 将处理后的SVG转回字符串
    const serializer = new XMLSerializer();
    content = serializer.serializeToString(svgDoc);
  } catch (error) {
    console.error('处理SVG中的位点元素失败:', error);
  }

  // 确保SVG有正确的xmlns属性
  if (!content.includes('xmlns=')) {
    content = content.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
  }

  // 移除背景色和填充
  if (content.includes('style=') && (content.includes('background') || content.includes('fill:'))) {
    content = content.replace(/style="([^"]*)background[^;"]*;?([^"]*)"/gi, 'style="$1$2"');
    content = content.replace(/style="([^"]*)fill:\s*white;?([^"]*)"/gi, 'style="$1fill:transparent;$2"');
    content = content.replace(/style="([^"]*)fill:\s*#fff;?([^"]*)"/gi, 'style="$1fill:transparent;$2"');
    content = content.replace(/style="([^"]*)fill:\s*#ffffff;?([^"]*)"/gi, 'style="$1fill:transparent;$2"');
  }

  // 移除任何可能的白色背景矩形
  content = content.replace(/<rect[^>]*fill="white"[^>]*><\/rect>/gi, '');
  content = content.replace(/<rect[^>]*fill="#fff"[^>]*><\/rect>/gi, '');
  content = content.replace(/<rect[^>]*fill="#ffffff"[^>]*><\/rect>/gi, '');

  // 确保SVG有宽高，如果没有则添加
  if (!content.includes('width=') || !content.includes('height=')) {
    content = content.replace('<svg', '<svg width="100%" height="100%"');
  }

  // 确保SVG有viewBox，如果没有则添加
  if (!content.includes('viewBox=')) {
    // 尝试从宽高属性中获取数值
    const widthMatch = content.match(/width="([^"%]+)(%?)"/);
    const heightMatch = content.match(/height="([^"%]+)(%?)"/);

    // 如果宽高不是百分比，且能解析为数字
    if (widthMatch && heightMatch && !widthMatch[2] && !heightMatch[2]) {
      const width = parseFloat(widthMatch[1]);
      const height = parseFloat(heightMatch[1]);
      if (!isNaN(width) && !isNaN(height)) {
        content = content.replace('<svg', `<svg viewBox="0 0 ${width} ${height}"`);
      }
    } else {
      // 默认添加一个通用的viewBox
      content = content.replace('<svg', '<svg viewBox="0 0 1000 1000"');
    }
  }

  return content;
})

// 导出SVG（包含监测点属性）
const exportSvgWithPoints = async () => {
  // 确保有SVG内容
  if (!props.svgContent) {
    console.log('没有SVG内容可导出');
    return '<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n<svg xmlns="http://www.w3.org/2000/svg"></svg>';
  }

  // 检查是否是URL格式SVG
  if (isSvgUrl.value) {
    console.log('SVG内容是URL格式，尝试获取内容并解析');

    try {
      // 简化处理，直接获取URL内容，不添加时间戳
      const response = await fetch(props.svgContent);
      if (!response.ok) {
        console.error('获取URL内容失败:', response.status, response.statusText);
        throw new Error('获取URL内容失败: ' + response.status);
      }

      // 获取SVG文本内容
      const svgText = await response.text();
      console.log(`成功获取URL内容，长度: ${svgText.length}字符`);

      // 如果没有获取到有效的SVG内容，回退到使用image嵌入方式
      if (!svgText || !svgText.includes('<svg') || svgText.length < 50) {
        console.warn('URL返回的不是有效SVG内容，回退到使用image标签嵌入');
        return createSvgWithImageReference(props.svgContent, props.points);
      }

      // 解析SVG内容
      console.log('解析获取到的SVG内容');
      const parser = new DOMParser();
      const doc = parser.parseFromString(svgText, 'image/svg+xml');

      // 检查是否有解析错误
      const parserError = doc.querySelector('parsererror');
      if (parserError) {
        console.error('SVG解析错误:', parserError.textContent);
        throw new Error('SVG解析错误');
      }

      // 清除所有可能的旧文本和监测点
      cleanupExistingElements(doc);

      const rootElement = doc.documentElement;

      // 如果没有点位数据，直接返回解析后的SVG内容
      if (!props.points || props.points.length === 0) {
        console.log('没有点位数据，直接返回解析后的SVG内容');
        const serializer = new XMLSerializer();
        return serializer.serializeToString(doc);
      }

      console.log(`为解析后的SVG添加${props.points.length}个点位`);

      // 创建监测点容器
      const pointsGroup = doc.createElementNS("http://www.w3.org/2000/svg", "g");
      pointsGroup.setAttribute("id", "monitoring-points");
      pointsGroup.setAttribute("class", "monitoring-points-layer");
      pointsGroup.setAttribute("data-points-count", props.points.length.toString());

      // 添加所有监测点
      props.points.forEach((point, index) => {
        console.log(`添加点位 ${index + 1}/${props.points.length}:`, point.name, point.type, point.x, point.y);
        // 确保点位类型正确设置
        if (!point.type) {
          console.warn(`警告: 点位${point.id}没有type属性，设置默认值为circle`);
          point.type = 'circle';
        }
        console.log(`导出点位${point.id}的完整信息:`, {
          id: point.id,
          name: point.name,
          type: point.type,
          position: `(${point.x}, ${point.y})`,
          data: point.data
        });
        addPointToSvg(doc, pointsGroup, point, index);
      });

      // 添加点位组到SVG
      rootElement.appendChild(pointsGroup);

      // 导出SVG
      const serializer = new XMLSerializer();
      let finalSvg = serializer.serializeToString(doc);

      // 确保包含XML声明
      if (!finalSvg.trim().startsWith('<?xml')) {
        finalSvg = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n${finalSvg}`;
      } else {
        // 替换现有的XML声明以确保包含standalone属性
        finalSvg = finalSvg.replace(/^<\?xml[^>]*\?>/, '<?xml version="1.0" encoding="UTF-8" standalone="no"?>');
      }

      // 验证点位是否包含在输出中
      const hasPoints = finalSvg.includes('id="monitoring-points"');
      console.log(`导出的SVG ${hasPoints ? '包含' : '不包含'}监测点容器`);
      console.log(`导出的SVG大小: ${finalSvg.length}`);

      // 触发外部导出事件
      emit('export-svg', finalSvg);

      return finalSvg;
    } catch (error) {
      console.error('处理URL格式SVG失败，回退到使用image标签嵌入:', error);
      // 出错时回退到使用图像引用方式
      return createSvgWithImageReference(props.svgContent, props.points);
    }
  } else {
    // 普通SVG内容处理
    console.log('处理普通SVG内容');

    // 如果没有点位数据，也要确保SVG内容有XML声明
    if (!props.points || props.points.length === 0) {
      console.log('没有点位数据，返回带XML声明的SVG');
      const svgContent = props.svgContent;
      // 如果已经有XML声明，直接返回
      if (svgContent.trim().startsWith('<?xml')) {
        return svgContent;
      }
      // 如果没有XML声明，添加一个
      return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n${svgContent}`;
    }

    console.log(`开始处理${props.points.length}个点位`);

    try {
      // 解析SVG内容
      const parser = new DOMParser();
      const doc = parser.parseFromString(props.svgContent, 'image/svg+xml');

      // 检查是否有解析错误
      const parserError = doc.querySelector('parsererror');
      if (parserError) {
        console.error('SVG解析错误:', parserError.textContent);
        throw new Error('SVG解析错误');
      }

      // 清除所有可能的旧文本和监测点
      cleanupExistingElements(doc);

      const rootElement = doc.documentElement;

      // 创建监测点容器
      const pointsGroup = doc.createElementNS("http://www.w3.org/2000/svg", "g");
      pointsGroup.setAttribute("id", "monitoring-points");
      pointsGroup.setAttribute("class", "monitoring-points-layer");
      pointsGroup.setAttribute("data-points-count", props.points.length.toString());

      // 添加所有监测点
      props.points.forEach((point, index) => {
        console.log(`添加点位 ${index + 1}/${props.points.length}:`, point.name, point.type, point.x, point.y);
        // 确保点位类型正确设置
        if (!point.type) {
          console.warn(`警告: 点位${point.id}没有type属性，设置默认值为circle`);
          point.type = 'circle';
        }
        console.log(`导出点位${point.id}的完整信息:`, {
          id: point.id,
          name: point.name,
          type: point.type,
          position: `(${point.x}, ${point.y})`,
          data: point.data
        });
        addPointToSvg(doc, pointsGroup, point, index);
      });

      // 确保点位组添加到SVG
      console.log('将点位组添加到SVG');
      rootElement.appendChild(pointsGroup);

      // 检查点位组是否确实添加到了SVG
      const addedGroup = doc.getElementById('monitoring-points');
      console.log('点位组是否成功添加:', !!addedGroup);
      console.log('点位组中的点位数量:', addedGroup ? addedGroup.childNodes.length : 0);

      // 导出SVG
      const serializer = new XMLSerializer();
      let finalSvg = serializer.serializeToString(doc);

      // 确保包含XML声明
      if (!finalSvg.trim().startsWith('<?xml')) {
        finalSvg = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n${finalSvg}`;
      } else {
        // 替换现有的XML声明以确保包含standalone属性
        finalSvg = finalSvg.replace(/^<\?xml[^>]*\?>/, '<?xml version="1.0" encoding="UTF-8" standalone="no"?>');
      }

      // 验证点位是否包含在输出中
      const hasPoints = finalSvg.includes('id="monitoring-points"');
      console.log(`导出的SVG ${hasPoints ? '包含' : '不包含'}监测点容器`);
      console.log(`导出的SVG大小: ${finalSvg.length}`);

      // 触发外部导出事件
      emit('export-svg', finalSvg);

      return finalSvg;
    } catch (error) {
      console.error('处理普通SVG内容失败:', error);
      console.warn('由于错误，将返回带XML声明的原始SVG内容');

      // 确保返回的SVG有XML声明和命名空间
      let svgContent = props.svgContent;

      // 添加命名空间
      if (!svgContent.includes('xmlns=')) {
        svgContent = svgContent.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
      }

      // 添加XML声明
      if (!svgContent.trim().startsWith('<?xml')) {
        svgContent = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n${svgContent}`;
      } else {
        // 替换现有的XML声明以确保包含standalone属性
        svgContent = svgContent.replace(/^<\?xml[^>]*\?>/, '<?xml version="1.0" encoding="UTF-8" standalone="no"?>');
      }

      return svgContent;
    }
  }
};

// 清理SVG中现有的元素
const cleanupExistingElements = (doc: Document) => {
  console.log('开始清理SVG中现有的元素');

  try {
    // 1. 清除所有可能的文本元素
    const textElements = doc.querySelectorAll('text[data-type="text"]');
    console.log(`找到${textElements.length}个data-type为text的元素`);
    textElements.forEach(el => {
      if (el.parentNode) el.parentNode.removeChild(el);
    });

    // 2. 清除可能有data-id属性的文本元素
    const dataIdTexts = doc.querySelectorAll('text[data-id]');
    console.log(`找到${dataIdTexts.length}个带data-id的文本元素`);
    dataIdTexts.forEach(el => {
      if (el.parentNode) el.parentNode.removeChild(el);
    });

    // 3. 清除所有以"text-"开头的id的元素和相关背景矩形
    const textIdElements = Array.from(doc.querySelectorAll('[id]')).filter(
      el => el.id.startsWith('text-')
    );
    console.log(`找到${textIdElements.length}个id以text-开头的元素`);
    textIdElements.forEach(el => {
      if (el.parentNode) el.parentNode.removeChild(el);
    });

    // 清除所有文本背景矩形
    const bgTextElements = Array.from(doc.querySelectorAll('[id]')).filter(
      el => el.id.startsWith('bg-text-')
    );
    console.log(`找到${bgTextElements.length}个文本背景矩形元素`);
    bgTextElements.forEach(el => {
      if (el.parentNode) el.parentNode.removeChild(el);
    });

    // 4. 清除旧的监测点容器
    const oldPointsGroup = doc.getElementById('monitoring-points');
    if (oldPointsGroup && oldPointsGroup.parentNode) {
      console.log('移除旧的监测点容器');
      oldPointsGroup.parentNode.removeChild(oldPointsGroup);
    }

    // 5. 清除监测点相关的所有元素
    const pointElements = doc.querySelectorAll('[data-point="true"]');
    console.log(`找到${pointElements.length}个标记为监测点的元素`);
    pointElements.forEach(el => {
      if (el.parentNode) el.parentNode.removeChild(el);
    });

    // 6. 清除任何可能被误识别为监测点的圆形元素(小尺寸圆形元素)
    const smallCircles = doc.querySelectorAll('circle[r="5"], circle[r="4"], circle[r="3"]');
    console.log(`找到${smallCircles.length}个可能是监测点的小圆形元素`);
    smallCircles.forEach(el => {
      // 检查是否是基础图形的一部分(父元素不是g#monitoring-points)
      const parent = el.parentNode;
      if (parent &&
        parent.nodeName.toLowerCase() !== 'g' &&
        !(parent as Element).id?.includes('monitoring-points')) {
        // 不是直接删除，而是设置为隐藏，保留原始结构
        el.setAttribute('visibility', 'hidden');
        el.setAttribute('display', 'none');
        console.log('隐藏可能是监测点的小圆形元素');
      }
    });

    console.log('清理SVG元素完成');
  } catch (error) {
    console.error('清理SVG元素时发生错误:', error);
  }
};

// 辅助函数：使用image标签引用原始URL创建SVG（在获取原始内容失败时使用）
const createSvgWithImageReference = (svgUrl: string, points: Point[]): string => {
  console.log('创建包含图像引用的SVG');

  // 获取IMG元素尺寸
  const imgRef = svgElement.value?.querySelector('img');
  let svgWidth = 800;
  let svgHeight = 600;

  if (imgRef) {
    svgWidth = imgRef.naturalWidth || imgRef.clientWidth || 800;
    svgHeight = imgRef.naturalHeight || imgRef.clientHeight || 600;
    console.log(`使用图片尺寸: ${svgWidth}x${svgHeight}`);
  }

  // 创建基础SVG
  const baseSvg = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="${svgWidth}" height="${svgHeight}" viewBox="0 0 ${svgWidth} ${svgHeight}">
  <!-- 使用原始URL作为底图 -->
  <image href="${svgUrl}" x="0" y="0" width="100%" height="100%" preserveAspectRatio="xMidYMid meet" />
</svg>`;

  // 如果没有点位，直接返回基础SVG
  if (!points || points.length === 0) {
    console.log('没有点位数据，返回基础SVG');
    return baseSvg;
  }

  console.log(`准备在图像引用SVG中添加${points.length}个点位`);

  try {
    // 解析创建的SVG
    const parser = new DOMParser();
    const doc = parser.parseFromString(baseSvg, 'image/svg+xml');

    // 检查是否有解析错误
    const parserError = doc.querySelector('parsererror');
    if (parserError) {
      console.error('创建基础SVG时解析错误:', parserError.textContent);
      throw new Error('SVG解析错误');
    }

    const rootElement = doc.documentElement;

    // 清理可能存在的旧元素
    cleanupExistingElements(doc);

    // 创建监测点容器
    const pointsGroup = doc.createElementNS("http://www.w3.org/2000/svg", "g");
    pointsGroup.setAttribute("id", "monitoring-points");
    pointsGroup.setAttribute("class", "monitoring-points-layer");
    pointsGroup.setAttribute("data-points-count", points.length.toString());

    // 添加所有监测点
    points.forEach((point, index) => {
      console.log(`添加点位${index + 1}/${points.length} 到图像引用SVG:`, point.name);
      addPointToSvg(doc, pointsGroup, point, index);
    });

    // 添加点位组到SVG
    rootElement.appendChild(pointsGroup);

    // 检查点位组是否成功添加
    const addedGroup = doc.getElementById('monitoring-points');
    console.log('点位组是否成功添加到图像引用SVG:', !!addedGroup);
    console.log('点位组中的点位数量:', addedGroup ? addedGroup.childNodes.length : 0);

    // 导出SVG
    const serializer = new XMLSerializer();
    const finalSvg = serializer.serializeToString(doc);

    // 验证点位是否包含在输出中
    const hasPoints = finalSvg.includes('id="monitoring-points"');
    console.log(`导出的图像引用SVG ${hasPoints ? '包含' : '不包含'}监测点容器`);
    console.log(`导出的图像引用SVG大小: ${finalSvg.length}`);

    return finalSvg;
  } catch (error) {
    console.error('创建带点位的图像引用SVG失败:', error);
    // 出错时返回基础SVG
    return baseSvg;
  }
};

// 辅助函数：将点位添加到SVG文档
const addPointToSvg = (doc: Document, container: SVGGElement, point: Point, index: number): void => {
  console.log(`开始添加点位到SVG: ${point.isText ? '文本' : '监测点'} ID=${point.id}, 位置=(${point.x}, ${point.y})`);

  try {
    if (point.isText) {
      // 1. 先检查是否已存在相同ID的文本元素，如有则移除
      const existingText = doc.getElementById(`text-${point.id}`);
      if (existingText && existingText.parentNode) {
        console.log(`发现已存在的文本元素id=text-${point.id}，移除它`);
        existingText.parentNode.removeChild(existingText);
      }

      // 2. 查找具有相同data-id的文本元素，如有则移除
      const sameDataIdElements = doc.querySelectorAll(`text[data-id="${point.id}"]`);
      if (sameDataIdElements.length > 0) {
        console.log(`发现${sameDataIdElements.length}个data-id=${point.id}的文本元素，移除它们`);
        sameDataIdElements.forEach(el => {
          if (el.parentNode) el.parentNode.removeChild(el);
        });
      }

      // 创建文本元素（使用固定ID格式确保唯一性）
      const textElement = doc.createElementNS("http://www.w3.org/2000/svg", "text");
      textElement.setAttribute("x", point.x.toString());
      textElement.setAttribute("y", point.y.toString());
      textElement.setAttribute("id", `text-${point.id}`);
      textElement.setAttribute("data-name", point.name);
      textElement.setAttribute("data-id", point.id);
      textElement.setAttribute("data-type", "text");
      textElement.setAttribute("data-show-type", "text"); // 确保文本点位也有data-show-type属性
      textElement.setAttribute("data-index", index.toString());
      textElement.setAttribute("font-size", `${point.data?.fontSize || 14}px`);
      textElement.setAttribute("font-weight", point.data?.fontWeight || "normal");
      textElement.setAttribute("fill", point.data?.color || "#333333");
      textElement.setAttribute("text-anchor", "middle");
      textElement.setAttribute("dominant-baseline", "central");
      textElement.setAttribute("alignment-baseline", "middle");

      // 添加监测指标信息（如果有）
      if (point.data?.indicator) {
        textElement.setAttribute("data-indicator-code", point.data.indicator);

        // 如果有indicatorInfo，添加更多详细信息
        if (point.data?.indicatorInfo) {
          try {
            const info = point.data.indicatorInfo;
            if (info.label) textElement.setAttribute("data-indicator-name", info.label);
            if (info.pointCode) textElement.setAttribute("data-point-code", info.pointCode);
            textElement.setAttribute("data-indicator-info", JSON.stringify(info));
          } catch (error) {
            console.error('添加指标信息失败:', error);
          }
        }
      }

      // 确保文本元素可交互
      textElement.setAttribute("pointer-events", "all");
      textElement.setAttribute("cursor", "pointer");

      // 设置文本内容，但不使用tspan元素
      textElement.textContent = point.name;

      container.appendChild(textElement);
      console.log(`成功创建并添加文本元素: id=text-${point.id}, 内容="${point.name}"`);
    } else {
      // 创建监测点元素
      let pointElement;
      const size = point.data?.size || 5;
      const color = point.data?.color || getPointColor(point.type);

      // 根据点位类型创建不同的SVG元素
      switch (point.type) {
        case 'circle':
          pointElement = doc.createElementNS("http://www.w3.org/2000/svg", "circle");
          pointElement.setAttribute("cx", point.x.toString());
          pointElement.setAttribute("cy", point.y.toString());
          pointElement.setAttribute("r", size.toString());
          pointElement.setAttribute("fill", color);
          console.log(`创建圆形监测点: (${point.x}, ${point.y}), r=${size}`);
          break;
        case 'square':
          pointElement = doc.createElementNS("http://www.w3.org/2000/svg", "rect");
          pointElement.setAttribute("x", (point.x - size).toString());
          pointElement.setAttribute("y", (point.y - size).toString());
          pointElement.setAttribute("width", (size * 2).toString());
          pointElement.setAttribute("height", (size * 2).toString());
          pointElement.setAttribute("fill", color);
          console.log(`创建方形监测点: (${point.x}, ${point.y}), size=${size}`);
          break;
        case 'triangle':
          pointElement = doc.createElementNS("http://www.w3.org/2000/svg", "polygon");
          const pointsCoord = `${point.x},${point.y - size} ${point.x + size},${point.y + size} ${point.x - size},${point.y + size}`;
          pointElement.setAttribute("points", pointsCoord);
          pointElement.setAttribute("fill", color);
          console.log(`创建三角形监测点: (${point.x}, ${point.y}), size=${size}`);
          break;
        default:
          pointElement = doc.createElementNS("http://www.w3.org/2000/svg", "circle");
          pointElement.setAttribute("cx", point.x.toString());
          pointElement.setAttribute("cy", point.y.toString());
          pointElement.setAttribute("r", size.toString());
          pointElement.setAttribute("fill", color);
          console.log(`创建默认监测点(圆形): (${point.x}, ${point.y}), r=${size}`);
      }

      // 添加基础数据属性
      pointElement.setAttribute("id", point.id);
      pointElement.setAttribute("data-id", point.id);
      pointElement.setAttribute("data-name", point.name);
      pointElement.setAttribute("data-type", point.type);
      pointElement.setAttribute("data-index", index.toString());
      pointElement.setAttribute("data-show-type", point.type);
      pointElement.setAttribute("data-point-code", point.name); // 使用name作为监测点编号
      pointElement.setAttribute("data-point", "true");

      // 确保在point.data中也保存type信息
      if (!point.data) {
        point.data = {};
      }
      point.data.type = point.type; // 直接在data对象中保存type

      // 确保保存点位类型
      console.log(`为点位${point.id}设置data-show-type=${point.type}`);

      // 添加监测指标相关属性
      if (point.data?.indicator) {
        pointElement.setAttribute("data-indicator-code", point.data.indicator);
        console.log(`添加监测指标: ${point.data.indicator}`);

        // 如果有indicatorInfo，添加更多详细信息
        if (point.data?.indicatorInfo) {
          try {
            const info = point.data.indicatorInfo;
            if (info.label) pointElement.setAttribute("data-indicator-name", info.label);
            if (info.pointCode) pointElement.setAttribute("data-point-code", info.pointCode);
            pointElement.setAttribute("data-indicator-info", JSON.stringify(info));
          } catch (error) {
            console.error('添加指标信息失败:', error);
          }
        }
      }

      // 添加扩展属性
      if (!point.data) {
        point.data = {};
      }

      // 直接在data中保存type信息
      point.data.type = point.type;

      if (point.data?.attributes) {
        // 确保data-show-type属性在attributes中
        point.data.attributes['data-show-type'] = point.type;

        Object.entries(point.data.attributes).forEach(([key, value]) => {
          if (typeof value === 'string') {
            pointElement.setAttribute(key, value);
            // 记录设置的属性
            console.log(`为点位${point.id}设置属性: ${key}=${value}`);
          }
        });
      } else {
        // 如果没有attributes对象，创建一个并设置data-show-type
        point.data.attributes = {
          'data-show-type': point.type,
          'data-name': point.name
        };
        // 设置属性到元素
        pointElement.setAttribute('data-show-type', point.type);
        console.log(`为点位${point.id}新增并设置data-show-type属性: ${point.type}`);
      }

      // 确保data-show-type属性被设置（双重保险）
      if (pointElement.getAttribute('data-show-type') !== point.type) {
        console.log(`修正点位${point.id}的data-show-type属性: ${pointElement.getAttribute('data-show-type')} -> ${point.type}`);
        pointElement.setAttribute('data-show-type', point.type);
      }

      // 添加自定义样式属性
      if (point.data?.size) {
        pointElement.setAttribute("data-size", point.data.size.toString());
      }
      if (point.data?.color) {
        pointElement.setAttribute("data-color", point.data.color);
      }

      // 确保点位可交互
      pointElement.setAttribute("pointer-events", "all");
      pointElement.setAttribute("cursor", "pointer");
      (pointElement as SVGElement).style.cursor = "pointer";

      // 添加到容器
      container.appendChild(pointElement);

      // 确认点位已成功添加
      console.log(`成功创建并添加监测点元素: id=${point.id}, 类型=${point.type}`);

      // 验证添加的点位是否在容器中
      const addedElement = doc.getElementById(point.id);
      if (addedElement) {
        console.log(`验证成功: 点位已添加到DOM`);
      } else {
        console.warn(`警告: 点位未能成功添加到DOM`);
      }
    }
  } catch (error) {
    console.error(`添加点位过程中发生错误:`, error);
  }
};

// 暴露导出SVG的方法
defineExpose({
  exportSvgWithPoints,
  parsePointsFromSvg
});

// 根据位点类型获取颜色
const getPointColor = (type: string): string => {
  const colors: Record<string, string> = {
    circle: '#F56C6C',
    square: '#E6A23C',
    triangle: '#409EFF',
    default: '#409EFF'
  }
  return colors[type] || colors.default
}

// 获取添加元素的提示文本
const getAddingElementTip = (): string => {
  if (!addingElementType.value) return '';

  if (addingElementType.value.startsWith('text-')) {
    const textType = addingElementType.value.split('-')[1];
    return `请在SVG上点击确认${textType === 'title' ? '标题' : '标签'}文本位置`;
  }

  if (addingElementType.value.startsWith('point-')) {
    const pointType = addingElementType.value.split('-')[1];
    const pointTypeName = {
      circle: '圆形',
      square: '方形',
      triangle: '三角形'
    }[pointType] || '';

    return `请在SVG上点击确认${pointTypeName}监测点位置`;
  }

  return '请在SVG上点击确认位置';
}

// 缩放控制
const zoomIn = () => {
  const newZoom = Math.min(props.zoom + 0.1, 4)
  emit('update:zoom', newZoom)
  ElMessage.success(`缩放比例: ${Math.round(newZoom * 100)}%`)
}

const zoomOut = () => {
  const newZoom = Math.max(props.zoom - 0.1, 0.2)
  emit('update:zoom', newZoom)
  ElMessage.success(`缩放比例: ${Math.round(newZoom * 100)}%`)
}

const resetZoom = () => {
  emit('update:zoom', 1)
  ElMessage.success('已重置缩放比例: 100%')
}

// 重置SVG位置
const resetPosition = () => {
  // 重置偏移量
  svgTranslateX.value = 0;
  svgTranslateY.value = 0;

  // 应用变换
  const svgContent = svgElement.value;
  if (svgContent) {
    svgContent.style.transform = `translate(0px, 0px)`;
  }

  ElMessage.success('已重置SVG位置')
}

// 适应内容大小功能已移除

// 适应内容大小（已移除，保留函数以保持兼容性）
const fitContent = () => {
  // 直接调用calculateInitialZoom，保持代码兼容性
  console.log('fitContent已被移除，直接调用calculateInitialZoom');

  // 设置为100%缩放（1.0），但不重置位置
  if (Math.abs(props.zoom - 1.0) > 0.01) {
    emit('update:zoom', 1.0);
  }

  // 保留当前的拖拽位置
  console.log('保留当前拖拽位置:', { x: svgTranslateX.value, y: svgTranslateY.value });

  // 调用calculateInitialZoom设置样式
  calculateInitialZoom();
}

// 重置缩放状态
const resetZoomState = () => {
  console.log('重置缩放状态');
  try {
    // 重置SVG元素样式
    const svgEl = svgElement.value?.querySelector('svg');
    if (svgEl) {
      // 设置SVG样式以保持原始尺寸
      svgEl.style.maxWidth = '100%';
      svgEl.style.maxHeight = '100%';
      svgEl.style.width = 'auto'; // 使用auto而非100%，保持原始宽高比
      svgEl.style.height = 'auto';
      svgEl.style.transformOrigin = 'center center';
      // 使用margin:auto实现居中
      svgEl.style.margin = 'auto';
      svgEl.style.padding = '0';

      // 确保SVG容器样式正确
      const svgContent = svgElement.value;
      if (svgContent) {
        svgContent.style.width = 'auto';
        svgContent.style.height = 'auto';
        svgContent.style.display = 'block';
        svgContent.style.margin = 'auto'; // 使用margin:auto实现居中
        svgContent.style.transformOrigin = 'center center';

        // 获取SVG包装器
        const svgWrapper = svgContent.parentElement;
        if (svgWrapper) {
          // 设置SVG包装器样式
          svgWrapper.style.width = '100%';
          svgWrapper.style.height = '100%';
          svgWrapper.style.display = 'flex'; // 使用flex布局实现居中
          svgWrapper.style.justifyContent = 'center'; // 水平居中
          svgWrapper.style.alignItems = 'center'; // 垂直居中
          svgWrapper.style.transformOrigin = 'center center';
          svgWrapper.style.margin = '0';
          svgWrapper.style.padding = '0';

          // 获取SVG外层容器
          const svgOuterWrapper = svgWrapper.parentElement;
          if (svgOuterWrapper) {
            // 设置外层容器铺满可视区域
            svgOuterWrapper.style.width = '100%';
            svgOuterWrapper.style.height = '100%';
            svgOuterWrapper.style.display = 'flex'; // 使用flex布局实现居中
            svgOuterWrapper.style.justifyContent = 'center'; // 水平居中
            svgOuterWrapper.style.alignItems = 'center'; // 垂直居中
            svgOuterWrapper.style.overflow = 'hidden';
            svgOuterWrapper.style.margin = '0';
            svgOuterWrapper.style.padding = '0';
            svgOuterWrapper.style.transformOrigin = 'center center';
          }
        }
      }
    }

    // 重置平移状态
    svgTranslateX.value = 0;
    svgTranslateY.value = 0;
  } catch (error) {
    console.error('重置缩放状态失败:', error);
  }
}

// 鼠标滚轮缩放
const handleWheel = (e: WheelEvent) => {
  // 阻止默认行为（页面滚动）
  e.preventDefault()

  // 使用缩放系数使缩放更平滑
  const scaleFactor = 0.1

  if (e.deltaY < 0) {
    // 放大
    emit('update:zoom', Math.min(props.zoom + scaleFactor, 4))
  } else {
    // 缩小
    emit('update:zoom', Math.max(props.zoom - scaleFactor, 0.2))
  }
}

// 为SVG中的点位元素添加事件监听器
const setupSvgPointEvents = (svgDoc: Document) => {
  console.log('为SVG中的点位添加事件监听器');

  try {
    // 1. 查找监测点组元素
    const pointsGroup = svgDoc.querySelector('#monitoring-points');
    if (pointsGroup) {
      console.log('找到监测点组元素');

      // 2. 获取所有点位元素
      const pointElements = pointsGroup.querySelectorAll('[data-id], [id^="point-"], [data-point="true"]');
      console.log(`在监测点组中找到${pointElements.length}个点位元素`);

      // 3. 为每个点位元素添加事件处理
      pointElements.forEach(element => {
        // 设置点位元素样式和属性
        element.setAttribute('cursor', 'pointer');
        element.setAttribute('pointer-events', 'all');

        // 确保可见性
        element.removeAttribute('visibility');
        element.removeAttribute('display');
      });
    }

    // 4. 查找并处理所有文本元素
    const textElements = svgDoc.querySelectorAll('text[data-id], text[id^="text-"]');
    console.log(`找到${textElements.length}个文本元素`);

    textElements.forEach(element => {
      // 设置文本元素样式和属性
      element.setAttribute('cursor', 'pointer');
      element.setAttribute('pointer-events', 'all');

      // 确保可见性
      element.removeAttribute('visibility');
      element.removeAttribute('display');
    });

    console.log('已完成SVG点位事件监听器设置');
  } catch (error) {
    console.error('为SVG点位添加事件监听器失败:', error);
  }
};

// 添加调试函数，记录SVG状态
const logSvgStatus = () => {
  console.log('========= SVG状态调试信息 =========');
  console.log('当前SVG内容格式是否URL:', isSvgUrl.value);

  if (svgElement.value) {
    const imgEl = svgElement.value.querySelector('img');
    const svgEl = svgElement.value.querySelector('svg');

    console.log('SVG容器内部元素:', {
      hasImgElement: !!imgEl,
      imgSrc: imgEl ? imgEl.src.substring(0, 50) + '...' : 'N/A',
      imgNaturalSize: imgEl ? `${imgEl.naturalWidth}x${imgEl.naturalHeight}` : 'N/A',
      imgDisplaySize: imgEl ? `${imgEl.clientWidth}x${imgEl.clientHeight}` : 'N/A',
      hasSvgElement: !!svgEl,
      svgSize: svgEl ? `${svgEl.clientWidth}x${svgEl.clientHeight}` : 'N/A'
    });

    // 记录当前点位信息
    console.log('当前点位数量:', props.points.length);
    if (props.points.length > 0) {
      console.log('第一个点位坐标:', `(${props.points[0].x}, ${props.points[0].y})`);
    }

    // 检查SVG中是否有监测点
    if (svgEl) {
      const pointsGroup = svgEl.querySelector('#monitoring-points');
      const pointElements = svgEl.querySelectorAll('[data-point="true"]');
      console.log('SVG中是否有监测点组:', !!pointsGroup);
      console.log('SVG中点位元素数量:', pointElements.length);
    }
  } else {
    console.log('SVG容器元素不存在!');
  }
  console.log('==================================');
}

// 在handleAddCommand函数开头添加调试信息
const handleAddCommand = (command: string) => {
  // 添加调试信息
  logSvgStatus();

  // 根据命令设置添加元素类型
  if (command.startsWith('point-')) {
    const type = command.replace('point-', '');
    addingElementType.value = type;

    const typeName = type === 'circle' ? '圆形' : type === 'square' ? '方形' : '三角形';
    ElMessage({
      message: `请点击SVG图上的位置添加${typeName}监测点`,
      type: 'success',
      duration: 3000,
      showClose: true
    });

    // 更新默认颜色
    pointColor.value = getPointColor(type);

    // 高亮SVG容器，提示用户可以点击
    const container = svgContainerRef.value;
    if (container) {
      container.style.outline = '3px dashed #FF4500';
      container.style.outlineOffset = '-3px';

      // 添加完成后恢复样式
      const resetStyle = () => {
        if (container) {
          container.style.outline = '';
          container.style.outlineOffset = '';
        }
      };

      // 注册一次性点击事件来重置样式
      const onClick = () => {
        resetStyle();
        container.removeEventListener('click', onClick);
      };

      container.addEventListener('click', onClick);

      // 如果3秒后还没点击，也重置样式
      setTimeout(resetStyle, 5000);
    }
  } else if (command.startsWith('text-')) {
    const textType = command.replace('text-', '');
    addingElementType.value = 'text';
    textFontSize.value = textType === 'title' ? 20 : 14;
    textFontWeight.value = textType === 'title' ? 'bold' : 'normal';

    ElMessage({
      message: `请点击SVG图上的位置添加${textType === 'title' ? '标题' : '标签'}文本`,
      type: 'success',
      duration: 3000,
      showClose: true
    });

    // 高亮SVG容器，提示用户可以点击
    const container = svgContainerRef.value;
    if (container) {
      container.style.outline = '3px dashed #409EFF';
      container.style.outlineOffset = '-3px';

      // 添加完成后恢复样式
      const resetStyle = () => {
        if (container) {
          container.style.outline = '';
          container.style.outlineOffset = '';
        }
      };

      // 注册一次性点击事件来重置样式
      const onClick = () => {
        resetStyle();
        container.removeEventListener('click', onClick);
      };

      container.addEventListener('click', onClick);

      // 如果3秒后还没点击，也重置样式
      setTimeout(resetStyle, 5000);
    }
  }
}

// 取消添加元素
const cancelAddElement = () => {
  addingElementType.value = null;
}

// 处理SVG点击事件
const handleSvgClick = (event: MouseEvent) => {
  // 添加调试信息
  console.log('========= SVG点击事件触发 =========');
  console.log('点击坐标:', `(${event.clientX}, ${event.clientY})`);
  console.log('当前添加元素类型:', addingElementType.value);
  console.log('是否为URL格式SVG:', isSvgUrl.value);
  console.log('SVG位置:', svgTranslateX.value, svgTranslateY.value);
  console.log('当前缩放:', props.zoom);

  // 点击空白区域时，取消选中状态
  if (!addingElementType.value) {
    selectedPoint.value = null;
  }

  // 如果正在添加元素
  if (addingElementType.value) {
    // 获取点击位置相对于SVG的坐标
    const svgElem = svgElement.value;
    if (!svgElem) {
      console.error('SVG容器元素不存在，无法处理点击事件');
      return;
    }

    // 获取SVG元素
    const svgEl = svgElem.querySelector('svg') as SVGSVGElement;
    if (!svgEl) {
      console.warn('SVG元素不可用');
      return;
    }

    // 获取SVG的位置和尺寸信息
    const rect = svgEl.getBoundingClientRect();
    const viewBox = svgEl.viewBox?.baseVal || { x: 0, y: 0, width: 1000, height: 1000 };

    // 确保点击在SVG范围内
    if (
      event.clientX < rect.left ||
      event.clientX > rect.right ||
      event.clientY < rect.top ||
      event.clientY > rect.bottom
    ) {
      console.warn('点击位置不在SVG范围内');
      return; // 点击位置不在SVG内，忽略
    }

    // 使用统一的坐标转换函数计算SVG坐标
    const { x: svgX, y: svgY } = convertClientToSvgCoordinates(event.clientX, event.clientY, svgEl);

    console.log('SVG点击坐标计算结果', {
      svgX, svgY,
      zoom: props.zoom,
      viewBox: `${viewBox.x},${viewBox.y},${viewBox.width},${viewBox.height}`,
      rect: `${rect.width}x${rect.height}`
    });

    // 直接将元素添加到SVG中
    if (addingElementType.value === 'text') {
      addTextElementToDOM(svgX, svgY);
      ElMessage.success('文本点位创建中');
    } else {
      addPointElementToDOM(svgX, svgY, addingElementType.value);
      ElMessage.success(`${addingElementType.value}型监测点创建中`);
    }

    // 延迟重置添加元素状态，确保预览能够显示
    setTimeout(() => {
      addingElementType.value = null;
    }, 100);
  }
}

// 将点位元素直接添加到SVG DOM中
const addPointElementToDOM = (x: number, y: number, type: string) => {
  isNewPoint.value = true;

  // 首先重置表单
  resetFormFields();

  // 设置默认大小和颜色
  pointSize.value = 8; // 更大的默认尺寸
  pointColor.value = getPointColor(type);

  // 创建新位点
  const newPoint: Point = {
    id: `point-${Date.now()}`,
    x,
    y,
    name: `监测点${props.points.length + 1}`, // 添加默认名称
    type,
    data: {
      size: pointSize.value,
      color: pointColor.value,
      indicator: ''
    },
    isText: false
  };

  // 创建临时预览
  editingPoint.value = { ...newPoint };
  temporaryPoint.value = newPoint;

  // 记录临时点位创建的日志
  console.log('创建临时点位:', newPoint, '预览坐标:', { x, y });

  // 重置表单值
  selectedIndicator.value = '';
  pointDataJson.value = JSON.stringify({
    size: pointSize.value,
    color: pointColor.value,
    indicator: ''
  }, null, 2);

  // 获取监测指标列表
  fetchIndicatorList();

  // 使用nextTick确保DOM更新后预览点位正确显示
  nextTick(() => {
    console.log('临时点位已创建:', temporaryPoint.value);
  });

  dialogVisible.value = true;
}

// 将文本元素直接添加到SVG DOM中
const addTextElementToDOM = (x: number, y: number) => {
  isNewPoint.value = true;

  // 首先重置表单
  resetFormFields();

  // 创建新文本
  const newPoint: Point = {
    id: `text-${Date.now()}`,
    x,
    y,
    name: ``, // 去除默认文本内容
    type: 'text',
    data: {
      fontSize: textFontSize.value,
      fontWeight: textFontWeight.value,
      color: textColor.value,
      backgroundColor: hasTextBackground.value ? textBackgroundColor.value : 'transparent',
      borderColor: textBorderColor.value,
      borderWidth: textBorderWidth.value,
      hasBackground: hasTextBackground.value,
      indicator: ''
    },
    isText: true
  };

  // 创建临时预览
  editingPoint.value = { ...newPoint };
  temporaryPoint.value = newPoint;

  dialogVisible.value = true;
}


// 处理位点拖拽开始
const startDrag = (event: MouseEvent, point: Point) => {
  event.stopPropagation()

  // 只有按住Ctrl键时才允许拖拽
  if (!event.ctrlKey) {
    return
  }

  draggingPoint.value = point

  // 添加鼠标移动和松开事件监听
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

// 处理位点拖拽过程
const handleDrag = (event: MouseEvent) => {
  if (!draggingPoint.value) return;

  // 确保Ctrl键仍然按下
  if (!event.ctrlKey) {
    stopDrag();
    return;
  }

  // 获取SVG元素
  const svgElem = svgElement.value;
  if (!svgElem) return;

  // 获取SVG元素
  const svgEl = svgElem.querySelector('svg');
  if (!svgEl) {
    console.warn('拖拽时SVG元素不可用');
    return;
  }

  // 获取SVG的位置和尺寸信息
  const viewBox = svgEl.viewBox?.baseVal || { x: 0, y: 0, width: 1000, height: 1000 };

  // 使用统一的坐标转换函数计算SVG坐标
  const { x: svgX, y: svgY } = convertClientToSvgCoordinates(event.clientX, event.clientY, svgEl);

  // 限制在SVG viewBox范围内
  const boundedX = Math.max(Math.min(svgX, viewBox.x + viewBox.width), viewBox.x);
  const boundedY = Math.max(Math.min(svgY, viewBox.y + viewBox.height), viewBox.y);

  // 更新位点位置
  draggingPoint.value.x = boundedX;
  draggingPoint.value.y = boundedY;

  // 直接更新SVG DOM中的点位位置
  updatePointPositionInDOM(draggingPoint.value);

  // 更新位点列表
  const updatedPoints = [...props.points];
  emit('update:points', updatedPoints);
}

// 更新SVG DOM中点位的位置
const updatePointPositionInDOM = (point: Point) => {
  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) return;

  // 在SVG中查找点位元素
  let pointElement;
  if (point.isText) {
    pointElement = svgEl.querySelector(`#text-${point.id}, text[data-id="${point.id}"]`);

    if (pointElement) {
      // 更新文本元素位置
      pointElement.setAttribute("x", point.x.toString());
      pointElement.setAttribute("y", point.y.toString());

      // 确保文本居中
      pointElement.setAttribute("text-anchor", "middle");
      pointElement.setAttribute("dominant-baseline", "central");
      pointElement.setAttribute("alignment-baseline", "middle");
    }
  } else {
    pointElement = svgEl.querySelector(`#${point.id}, [data-id="${point.id}"]`);

    if (pointElement) {
      const size = point.data?.size || 5;

      // 根据元素类型更新位置属性
      if (pointElement.tagName.toLowerCase() === 'circle') {
        // 更新圆形位置
        pointElement.setAttribute("cx", point.x.toString());
        pointElement.setAttribute("cy", point.y.toString());
      } else if (pointElement.tagName.toLowerCase() === 'rect') {
        // 更新方形位置
        pointElement.setAttribute("x", (point.x - size).toString());
        pointElement.setAttribute("y", (point.y - size).toString());
      } else if (pointElement.tagName.toLowerCase() === 'polygon') {
        // 更新三角形位置
        const pointsCoord = `${point.x},${point.y - size} ${point.x + size},${point.y + size} ${point.x - size},${point.y + size}`;
        pointElement.setAttribute("points", pointsCoord);
      }
    }
  }

  if (!pointElement) {
    console.warn('找不到SVG中的点位元素:', point.id);
  }
}

// 处理位点拖拽结束
const stopDrag = () => {
  // 记录拖拽结束位置，便于调试
  if (draggingPoint.value) {
    console.log('拖拽结束，点位最终位置:', {
      id: draggingPoint.value.id,
      x: draggingPoint.value.x,
      y: draggingPoint.value.y,
      zoom: props.zoom
    });
  }

  draggingPoint.value = null

  // 移除事件监听
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 编辑位点
const editPoint = async (point: Point) => {
  // 如果正在添加元素，则不允许编辑
  if (addingElementType.value) return;

  // 先重置表单
  resetFormFields();

  // 选中该点位
  selectedPoint.value = point;

  isNewPoint.value = false;
  editingPoint.value = { ...point };

  console.log('编辑点位:', {
    id: point.id,
    name: point.name,
    type: point.type,
    data: point.data,
    attributes: point.data?.attributes || {},
    'data-point-code': point.data?.attributes?.['data-point-code'] || '',
    pointCode: point.data?.pointCode || ''
  });

  // 先获取监测指标列表，确保数据准备好
  await fetchIndicatorList();

  if (point.isText) {
    // 设置文本相关属性
    const fontSize = typeof point.data?.fontSize === 'number' ? point.data.fontSize : 14;
    textFontSize.value = fontSize as number;
    textFontWeight.value = point.data?.fontWeight || 'normal';
    textColor.value = point.data?.color || '#333333';

    // 设置背景和边框属性
    textBackgroundColor.value = point.data?.backgroundColor || 'rgba(255, 255, 255, 0.5)';
    hasTextBackground.value = point.data?.hasBackground || false;
    textBorderColor.value = point.data?.borderColor || 'rgba(0, 0, 0, 0.3)';
    textBorderWidth.value = point.data?.borderWidth || 1;

    // 设置监测指标
    selectedIndicator.value = point.data?.indicator || '';

    // 设置文本编号
    textPointCode.value = point.data?.pointCode || point.data?.attributes?.['data-point-code'] || '';
  } else {
    // 设置普通位点数据
    pointSize.value = point.data?.size || 5;
    pointColor.value = point.data?.color || getPointColor(point.type);

    // 设置监测指标
    selectedIndicator.value = point.data?.indicator || '';

    // 设置编号 - 按优先级获取
    // 1. 从data-point-code属性获取
    if (point.data?.attributes?.['data-point-code']) {
      console.log('使用data-point-code属性作为编号:', point.data.attributes['data-point-code']);
      editingPoint.value.name = point.data.attributes['data-point-code'];
    }
    // 2. 从name属性获取
    else if (point.name) {
      console.log('使用name属性作为编号:', point.name);
      editingPoint.value.name = point.name;
    }
    // 3. 从监测指标信息获取
    else if (selectedIndicator.value) {
      // 查找选中的指标信息
      const selectedOption = indicatorOptions.value.find(item => item.value === selectedIndicator.value);

      if (selectedOption) {
        // 如果指标有pointCode属性，使用它作为编号
        if (selectedOption.pointCode) {
          console.log('使用指标的pointCode作为编号:', selectedOption.pointCode);
          editingPoint.value.name = selectedOption.pointCode;
        }
        // 如果存在data.pointCode属性，使用它作为编号
        else if (selectedOption.data?.pointCode) {
          console.log('使用指标data中的pointCode作为编号:', selectedOption.data.pointCode);
          editingPoint.value.name = selectedOption.data.pointCode;
        }
        // 否则使用指标的名称作为编号
        else {
          console.log('使用指标名称作为编号:', selectedOption.label);
          editingPoint.value.name = selectedOption.label;
        }
      }
    }

    // 更新JSON数据显示
    pointDataJson.value = JSON.stringify(point.data || {}, null, 2);
  }

  // 确保编号被正确设置后再打开弹窗
  console.log('打开弹窗，编号:', editingPoint.value.isText ? textPointCode.value : editingPoint.value.name);
  dialogVisible.value = true;
}

// 保存位点编辑
const savePoint = () => {
  try {
    // 校验必填项
    if (!editingPoint.value.isText) {
      if (!selectedIndicator.value) {
        ElMessage.error('请选择监测指标');
        return;
      }

      if (!editingPoint.value.name) {
        ElMessage.error('请输入监测点编号');
        return;
      }
    } else {
      if (!editingPoint.value.name) {
        ElMessage.error('请输入文本内容');
        return;
      }
    }

    let data: any = {};

    if (editingPoint.value.isText) {
      // 保存文本相关属性
      data = {
        fontSize: textFontSize.value,
        fontWeight: textFontWeight.value,
        color: textColor.value,
        backgroundColor: 'transparent', // 使用透明背景色
        borderColor: '#000000', // 使用黑色边框
        borderWidth: hasTextBackground.value ? (textBorderWidth.value !== undefined ? textBorderWidth.value : 1) : 0, // 默认1px
        hasBackground: hasTextBackground.value, // 根据用户选择决定是否显示边框
        indicator: selectedIndicator.value || '',
        pointCode: textPointCode.value || '',
        // 如果选择了监测指标，保存指标信息
        indicatorInfo: selectedIndicator.value ?
          indicatorOptions.value.find(item => item.value === selectedIndicator.value) : null
      };

      // 添加文本的扩展属性
      data.attributes = {
        'data-indicator-code': selectedIndicator.value || '',
        'data-is-text': 'true',
        'data-name': editingPoint.value.name,
        'data-point-code': textPointCode.value || '',
        'data-show-type': 'text' // 确保文本点位也有data-show-type属性
      };
    } else {
      // 保存监测点数据
      data = {
        size: pointSize.value,
        color: pointColor.value,
        indicator: selectedIndicator.value,
        // 直接保存type信息到data对象中
        type: editingPoint.value.type,
        // 如果选择了监测指标，保存指标单位等信息
        indicatorInfo: selectedIndicator.value ?
          indicatorOptions.value.find(item => item.value === selectedIndicator.value) : null
      };

      // 添加扩展属性，用于与SVG元素属性对应
      data.attributes = {
        'data-indicator-code': selectedIndicator.value,
        'data-show-type': editingPoint.value.type,
        'data-name': editingPoint.value.name,
        'data-point-code': editingPoint.value.name // 使用name作为监测点编号
      };

      // 确保保存点位类型到data属性中
      console.log(`保存点位${editingPoint.value.id}的类型: ${editingPoint.value.type}，设置data-show-type属性`);
    }

    // 确保data对象中包含type信息
    data.type = editingPoint.value.type;

    // 更新位点
    const updatedPoint = { ...editingPoint.value, data };

    // 确保点位类型正确设置
    console.log(`保存点位 ${updatedPoint.id} 的完整信息:`, {
      id: updatedPoint.id,
      name: updatedPoint.name,
      type: updatedPoint.type,
      position: `(${updatedPoint.x}, ${updatedPoint.y})`,
      data: updatedPoint.data,
      attributes: updatedPoint.data.attributes
    });

    // 获取SVG元素
    const svgEl = svgElement.value?.querySelector('svg');

    // 将点位直接添加到SVG DOM中
    if (svgEl) {
      addOrUpdatePointInSVG(svgEl, updatedPoint, isNewPoint.value);

      // 验证点位属性是否正确设置
      setTimeout(() => {
        const pointElement = svgEl.querySelector(`#${updatedPoint.id}, [data-id="${updatedPoint.id}"]`);
        if (pointElement) {
          console.log(`验证点位 ${updatedPoint.id} 的属性:`, {
            'data-show-type': pointElement.getAttribute('data-show-type'),
            'data-type': pointElement.getAttribute('data-type'),
            'data-name': pointElement.getAttribute('data-name'),
            'data-indicator-code': pointElement.getAttribute('data-indicator-code')
          });
        }
      }, 100);
    }

    // 同步更新Vue数据模型
    let updatedPoints: Point[];
    if (isNewPoint.value) {
      updatedPoints = [...props.points, updatedPoint];
    } else {
      updatedPoints = props.points.map(p => p.id === updatedPoint.id ? updatedPoint : p);
    }
    emit('update:points', updatedPoints);

    // 清除临时点位
    temporaryPoint.value = null;

    // 如果是文本元素，强制更新所有文本元素的样式
    if (updatedPoint.isText) {
      nextTick(() => {
        updateTextElementStyles(svgEl);
      });
    }

    // 关闭对话框
    dialogVisible.value = false;
    ElMessage.success(isNewPoint.value ?
      (editingPoint.value.isText ? '文本添加成功' : '位点添加成功') :
      (editingPoint.value.isText ? '文本更新成功' : '位点更新成功'));
  } catch (error) {
    console.error('保存位点失败:', error);
    ElMessage.error(editingPoint.value.isText ? '文本属性设置错误' : '数据配置错误');
  }
}

// 监听SVG内容变化 - 增强版，添加自动解析位点功能
// 防止重复设置SVG内容标志
let isSvgContentBeingSet = false
let lastProcessedSvg = ''
// 添加防抖计时器
let svgContentTimer: any = null

watch(() => props.svgContent, (newSvg, oldSvg) => {
  console.log('SVG内容变化', newSvg ? `长度: ${newSvg.length}` : '内容为空', svgElement.value ? '容器已就绪' : '容器未就绪');

  // 清除任何现有的计时器，防止重复执行
  if (svgContentTimer) {
    clearTimeout(svgContentTimer);
    svgContentTimer = null;
  }

  // 检查是否有内容
  if (!newSvg) {
    console.warn('SVG内容为空，无法处理');
    return;
  }

  // 检查是否是URL格式
  const isUrl = isSvgUrl.value;
  console.log('SVG内容是否是URL格式:', isUrl);

  // 避免重复处理相同的SVG内容
  if (newSvg !== oldSvg && !isSvgContentBeingSet && newSvg !== lastProcessedSvg) {
    console.log('SVG内容已变化，准备设置到DOM')
    isSvgContentBeingSet = true
    lastProcessedSvg = newSvg || ''

    // 清除临时点位
    temporaryPoint.value = null

    // 重置缩放状态，但保留位置状态
    // 只重置缩放比例，不重置位置
    emit('update:zoom', 1);

    // 清理和设置SVG内容
    const setSvgContent = () => {
      try {
        if (svgElement.value) {
          console.log('设置SVG内容到DOM')

          // 直接设置SVG内容
          svgElement.value.innerHTML = sanitizedSvgContent.value
          console.log('SVG内容设置成功，长度:', sanitizedSvgContent.value.length)

          // 确保SVG元素保持原始尺寸
          const svgEl = svgElement.value.querySelector('svg');
          if (svgEl) {
            // 设置SVG样式以保持原始尺寸
            svgEl.style.maxWidth = '100%';
            svgEl.style.maxHeight = '100%';
            svgEl.style.width = 'auto'; // 使用auto而非100%，保持原始宽高比
            svgEl.style.height = 'auto';
            svgEl.style.display = 'block';
            svgEl.style.margin = 'auto'; // 使用margin:auto实现居中
            svgEl.style.transformOrigin = 'center center';
            console.log('已设置SVG元素样式以保持原始尺寸并居中');
          } else {
            console.warn('设置内容后未找到SVG元素');
          }

          // 调整SVG容器大小
          nextTick(() => {
            adjustSvgContainerSize()

            // 在SVG内容加载完成后，直接添加点位
            if (props.points && props.points.length > 0) {
              nextTick(() => {
                updateSvgOverlay();
              });
            }
          });
        }
      } catch (error) {
        console.error('设置SVG内容失败:', error)
      } finally {
        // 无论成功失败都重置标志
        isSvgContentBeingSet = false
      }
    }

    // 确保DOM准备好后设置内容
    if (svgElement.value) {
      setSvgContent()

      // 使用延迟处理SVG居中显示，不进行自适应缩放
      svgContentTimer = setTimeout(() => {
        console.log('SVG内容已更新，保持100%缩放并居中显示');
        try {
          // 检查SVG元素是否存在
          const svgExists = svgElement.value?.querySelector('svg');
          if (svgExists) {
            // 保持100%缩放并居中显示SVG
            calculateInitialZoom();
          } else {
            console.warn('SVG元素不存在，重新尝试设置内容');
            // 重新尝试设置内容
            setSvgContent();
            // 延迟再次检查
            setTimeout(() => {
              if (svgElement.value?.querySelector('svg')) {
                calculateInitialZoom();
              } else {
                console.error('多次尝试后仍未找到SVG元素');
              }
            }, 500);
          }
        } catch (e) {
          console.error('更新SVG后处理失败:', e);
          // 延迟重试一次
          setTimeout(() => {
            try {
              calculateInitialZoom();
            } catch (error) {
              console.error('重试处理失败:', error);
            }
          }, 500);
        }
      }, isUrl ? 800 : 400); // URL格式需要更长的等待时间
    } else {
      // 等待DOM更新
      console.log('DOM未就绪，等待下一个tick')
      nextTick(() => {
        if (svgElement.value) {
          setSvgContent()

          // 使用延迟处理SVG居中显示
          svgContentTimer = setTimeout(() => {
            console.log('DOM更新后保持100%缩放并居中显示');
            // 检查SVG元素是否存在
            if (svgElement.value?.querySelector('svg')) {
              calculateInitialZoom();
            } else {
              console.warn('未找到SVG元素，无法计算样式');
            }
          }, isUrl ? 800 : 400);
        } else {
          console.error('无法找到SVG元素，取消设置内容')
          isSvgContentBeingSet = false
        }
      })
    }
  }
}, { immediate: true })

// 监听外部传入的位点数据
watch(() => props.points, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    console.log('位点数据已更新，当前位点数量:', newVal?.length || 0)

    // 如果清空了点位，也清除临时点位
    if (!newVal || newVal.length === 0) {
      temporaryPoint.value = null
    }

    // 无论是什么格式的SVG，都直接在SVG中处理点位
    nextTick(() => {
      updateSvgOverlay();
    });
  }
}, { immediate: true })

// 监听screenId变化 - 简化版，不主动清除点位
watch(() => props.screenId, (newId, oldId) => {
  if (newId !== oldId) {
    console.log('画面ID已变化，从', oldId, '切换到', newId)
    // 当切换到不同的画面时，只清除临时点位
    temporaryPoint.value = null
  }
}, { immediate: true })

// 解析SVG中的位点
function parsePointsFromSvg() {
  console.log('parsePointsFromSvg 被调用')
  if (!props.svgContent) {
    console.log('没有SVG内容，无法解析位点')
    return []
  }

  // 如果是URL格式，不进行解析
  if (isSvgUrl.value) {
    console.log('SVG内容是URL格式，跳过位点解析')
    return []
  }

  if (!svgElement.value) {
    console.log('SVG元素不存在，无法解析位点')
    return []
  }

  // 使用原始的SVG内容进行解析，而不是已经处理过的内容
  const parser = new DOMParser()
  const svgDoc = parser.parseFromString(props.svgContent, 'image/svg+xml')

  // 查找可能是监测点的元素
  console.log('开始解析SVG中的监测点...')

  // 保存找到的位点
  const newPoints: Point[] = []
  let pointIndex = 1

  // 用于去重的Set，记录已处理的元素和坐标
  const processedElements = new Set<Element>()
  const processedCoordinates = new Set<string>() // 格式: "x,y"

  // 允许自动识别位点，即使已有点位数据
  // 但如果用户明确选择了不覆盖现有点位，则保留
  const shouldKeepExistingPoints = props.points && props.points.length > 0;
  if (shouldKeepExistingPoints) {
    console.log('已有点位数据，尝试合并解析出的新位点...');
  }

  // 检查点是否已存在（基于坐标）
  const isPointDuplicate = (x: number, y: number, tolerance: number = 5): boolean => {
    // 四舍五入坐标值，避免浮点误差
    const roundedX = Math.round(x)
    const roundedY = Math.round(y)

    // 检查附近区域是否已有点位
    for (let i = -tolerance; i <= tolerance; i++) {
      for (let j = -tolerance; j <= tolerance; j++) {
        const coordKey = `${roundedX + i},${roundedY + j}`
        if (processedCoordinates.has(coordKey)) {
          console.log(`跳过重复点位: (${roundedX}, ${roundedY}) - 与已有点位太近`)
          return true
        }
      }
    }

    // 如果需要检查与现有点位的冲突
    if (shouldKeepExistingPoints) {
      for (const existingPoint of props.points) {
        const existingX = Math.round(existingPoint.x);
        const existingY = Math.round(existingPoint.y);
        if (Math.abs(existingX - roundedX) <= tolerance && Math.abs(existingY - roundedY) <= tolerance) {
          console.log(`跳过与现有点位冲突的坐标: (${roundedX}, ${roundedY}) 与 (${existingX}, ${existingY})`);
          return true;
        }
      }
    }

    // 记录此点位置
    processedCoordinates.add(`${roundedX},${roundedY}`)
    console.log(`记录新点位坐标: (${roundedX},${roundedY})`)
    return false
  }

  // 检查元素是否看起来像是监测点（额外的筛选条件）
  const looksLikePoint = (element: Element): boolean => {
    // 检查元素类名是否包含特定关键词
    const className = element.getAttribute('class') || ''
    if (className.includes('background') ||
      className.includes('bg-') ||
      className.includes('decoration') ||
      className.includes('line') ||
      className.includes('grid')) {
      console.log('跳过可能是背景/装饰的元素:', className)
      return false
    }

    // 检查元素ID是否包含特定关键词
    const id = element.getAttribute('id') || ''
    if (id.includes('background') ||
      id.includes('bg-') ||
      id.includes('decoration') ||
      id.includes('line') ||
      id.includes('grid')) {
      console.log('跳过可能是背景/装饰的元素(基于ID):', id)
      return false
    }

    // 检查元素样式和属性
    const opacity = element.getAttribute('opacity')
    const visibility = element.getAttribute('visibility')
    const display = element.getAttribute('display')
    const fill = element.getAttribute('fill')
    const stroke = element.getAttribute('stroke')

    // 跳过不可见元素
    if (opacity === '0' || visibility === 'hidden' || display === 'none') {
      console.log('跳过不可见元素')
      return false
    }

    // 跳过无填充或白色/黑色填充的元素（除非有明显的彩色边框）
    if ((fill === 'none' || fill === '#FFFFFF' || fill === '#fff' || fill === '#000000' || fill === '#000') &&
      (stroke === 'none' || !stroke || stroke === '#000000' || stroke === '#000')) {
      console.log('跳过无填充/白色/黑色填充的元素')
      return false
    }

    // 检查元素是否有特定标记表明它不是监测点
    if (element.hasAttribute('data-not-point') || element.hasAttribute('data-ignore')) {
      console.log('跳过明确标记为非监测点的元素')
      return false
    }

    return true
  }

  // 添加位点到结果数组
  const addPointIfNotDuplicate = (element: Element, point: Point): boolean => {
    if (processedElements.has(element)) {
      console.log('跳过已处理的元素')
      return false // 元素已处理过
    }

    // 检查点位名称是否存在且有意义
    if (!point.name || point.name === 'undefined' || point.name === 'null') {
      point.name = `P${pointIndex}`
    }

    // 检查坐标是否重复（附近已有点位）
    if (isPointDuplicate(point.x, point.y)) {
      return false // 坐标重复
    }

    // 记录元素已处理
    processedElements.add(element)

    // 打印识别到的点位详情
    console.log(`添加监测点: ${point.name} (${point.x}, ${point.y}) 类型:${point.type} 颜色:${(point.data as any).color}`)

    // 添加点位到结果
    newPoints.push(point)
    pointIndex++
    return true
  }

  // ============ 识别优先级 1：根据data-indicator-code和data-show-type属性 ============
  const indicatorElements = svgDoc.querySelectorAll('[data-indicator-code], [data-show-type]')
  console.log('优先级1 - 找到带indicator-code或show-type的元素:', indicatorElements.length)

  indicatorElements.forEach((element, index) => {
    // 检查元素是否已处理
    if (processedElements.has(element)) {
      console.log(`跳过第${index}个已处理的indicator元素`)
      return
    }

    const id = element.getAttribute('id') || `point-${Date.now()}-${pointIndex}`
    const indicatorCode = element.getAttribute('data-indicator-code') || ''
    const showType = element.getAttribute('data-show-type') || ''

    // 根据指标代码或显示类型判断位点类型
    let type = 'circle'
    if (showType.includes('square') || showType.includes('rect')) {
      type = 'square'
    } else if (showType.includes('triangle')) {
      type = 'triangle'
    }

    // 获取元素名称，按优先级尝试不同属性
    let name = element.getAttribute('data-point-code') || // 优先使用data-point-code作为名称
      element.getAttribute('data-name') ||
      element.getAttribute('name') ||
      element.getAttribute('title') ||
      element.textContent?.trim() ||
      `P${pointIndex}`

    // 获取元素在SVG中的坐标
    const { x, y } = getElementPosition(element)

    // 检查坐标是否有效
    if (isNaN(x) || isNaN(y) || (x === 0 && y === 0)) {
      console.log(`跳过无效坐标的indicator元素: ${name}`)
      return
    }

    // 尝试获取颜色信息
    let color = getPointColor(type)
    const fill = element.getAttribute('fill')
    if (fill && fill !== 'none' && fill !== '#FFFFFF' && fill !== '#fff') {
      if (fill.includes('#F56C6C') || fill.includes('red')) {
        type = 'circle'
        color = '#F56C6C'
      } else if (fill.includes('#E6A23C') || fill.includes('orange') || fill.includes('yellow')) {
        type = 'square'
        color = '#E6A23C'
      } else if (fill.includes('#409EFF') || fill.includes('blue')) {
        type = 'triangle'
        color = '#409EFF'
      }
    }

    console.log(`识别indicator元素: ${name} (${x}, ${y}) 指标:${indicatorCode} 类型:${type}`)

    const point: Point = {
      id,
      x, y,
      name,
      type,
      data: {
        size: 5,
        color,
        indicator: indicatorCode,
        // 直接在data中保存type信息
        type: type,
        // 添加属性对象，确保保存data-show-type
        attributes: {
          'data-indicator-code': indicatorCode,
          'data-show-type': type,
          'data-name': name
        }
      },
      isText: false
    }

    addPointIfNotDuplicate(element, point)
  });

  // ============ 识别优先级 2：根据data-point属性 ============
  const dataPointElements = svgDoc.querySelectorAll('[data-point]')
  console.log('优先级2 - 找到明确标记的位点:', dataPointElements.length)

  dataPointElements.forEach((element, index) => {
    // 检查元素是否已处理
    if (processedElements.has(element)) {
      console.log(`跳过第${index}个已处理的data-point元素`)
      return
    }

    const id = element.getAttribute('id') || `point-${Date.now()}-${pointIndex}`
    const name = element.getAttribute('data-name') || `位点${pointIndex}`
    const type = element.getAttribute('data-type') || 'default'

    // 从元素的data-point属性中获取JSON数据
    let data = {}
    const dataAttr = element.getAttribute('data-point')
    if (dataAttr) {
      try {
        data = JSON.parse(dataAttr)
      } catch (e) {
        console.warn(`位点${id}的data-point属性不是有效的JSON`, e)
      }
    }

    // 获取元素在SVG中的坐标
    const { x, y } = getElementPosition(element)

    // 检查坐标是否有效
    if (isNaN(x) || isNaN(y) || (x === 0 && y === 0)) {
      console.log(`跳过无效坐标的data-point元素: ${name}`)
      return
    }

    console.log(`识别data-point元素: ${name} (${x}, ${y}) 类型:${type}`)

    const point: Point = {
      id,
      x, y,
      name,
      type: type || 'circle',
      data: {
        ...(data as object),
        size: (data as any).size || 5,
        color: (data as any).color || getPointColor(type || 'circle')
      },
      isText: false
    }

    addPointIfNotDuplicate(element, point)
  });

  // ============ 识别优先级 3：根据图形特征识别 ============
  // 只有在没有找到任何点位且没有外部传入点位时才尝试通过图形特征识别
  if (newPoints.length === 0) {
    console.log('优先级3 - 前两种方式没有找到点位，尝试根据图形特征识别');

    // 查找circle元素（可能是监测点）
    const circleElements = svgDoc.querySelectorAll('circle')
    console.log('找到圆形元素:', circleElements.length)

    circleElements.forEach((element, index) => {
      // 检查元素是否已处理
      if (processedElements.has(element)) {
        console.log(`跳过第${index}个已处理的圆形元素`)
        return
      }

      // 只选择较小的圆形为监测点(r<=5)，严格控制尺寸范围
      const radius = parseFloat(element.getAttribute('r') || '0')
      // 跳过明显过大的圆
      if (radius <= 0 || radius > 5) {
        console.log(`跳过第${index}个圆形: 半径${radius}超出范围`)
        return
      }

      const cx = parseFloat(element.getAttribute('cx') || '0')
      const cy = parseFloat(element.getAttribute('cy') || '0')

      // 检查坐标是否有效
      if (isNaN(cx) || isNaN(cy) || (cx === 0 && cy === 0)) {
        console.log(`跳过第${index}个圆形: 无效坐标(${cx}, ${cy})`)
        return
      }

      // 获取SVG文档的尺寸
      const svgElement = svgDoc.documentElement
      const svgWidth = parseFloat(svgElement.getAttribute('width') || '1000')
      const svgHeight = parseFloat(svgElement.getAttribute('height') || '1000')

      // 跳过位于边缘区域的点（可能是坐标轴标记或装饰）
      const margin = 10 // 边缘区域宽度（像素）
      if (cx < margin || cx > svgWidth - margin || cy < margin || cy > svgHeight - margin) {
        console.log(`跳过第${index}个圆形: 位于边缘区域(${cx}, ${cy})`)
        return
      }

      // 检查元素是否看起来像监测点
      if (!looksLikePoint(element)) {
        console.log(`跳过第${index}个圆形: 不符合监测点特征`)
        return
      }

      // 检查是否重复
      if (isPointDuplicate(cx, cy)) {
        console.log(`跳过第${index}个圆形: 位置(${cx}, ${cy})与现有点位重复`)
        return
      }

      // 根据填充颜色确定点位类型
      let type = 'circle'
      let color = '#F56C6C' // 默认红色

      const fill = element.getAttribute('fill')
      if (fill) {
        if (fill.includes('#E6A23C') || fill.includes('orange') || fill.includes('yellow')) {
          type = 'square'
          color = '#E6A23C'
        } else if (fill.includes('#409EFF') || fill.includes('blue')) {
          type = 'triangle'
          color = '#409EFF'
        } else if (fill.includes('#F56C6C') || fill.includes('red')) {
          type = 'circle'
          color = '#F56C6C'
        }
      }

      console.log(`识别新点位: 圆形${index}(${cx},${cy}), 半径=${radius}, 颜色=${color}`)

      const point: Point = {
        id: element.getAttribute('id') || `point-${Date.now()}-${pointIndex}`,
        x: cx,
        y: cy,
        name: element.getAttribute('data-name') || `P${pointIndex}`,
        type,
        data: {
          size: radius,
          color,
          indicator: element.getAttribute('data-indicator-code') || ''
        },
        isText: false
      }

      addPointIfNotDuplicate(element, point)
    });
  }

  // 添加完成后，检查过滤结果
  console.log(`从SVG解析出位点: ${newPoints.length}/${processedElements.size} (识别/处理元素数)`)

  // 如果识别到的位点数量异常（比如过多或过少），记录详细信息
  if (newPoints.length > 10) {
    console.warn(`警告: 识别到的位点数量(${newPoints.length})可能过多，请检查识别算法`)
    console.log('所有点位坐标:', Array.from(processedCoordinates))
  }

  if (newPoints.length > 0) {
    // 记录每个点位的详细信息
    newPoints.forEach((point, index) => {
      console.log(`点位${index + 1}: ${point.name} (${point.x}, ${point.y}) 类型:${point.type}`)
    })

    // 根据需求，不自动更新位点数据，只记录日志
    console.log('按照需求配置，不自动更新位点数据，保持原有点位不变');
    // emit('update:points', newPoints)
  } else {
    console.log('未从SVG中发现任何监测点')
  }

  // 返回解析到的点位数据，但不自动更新
  return newPoints;
}

// 获取元素在SVG中的位置
const getElementPosition = (element: Element): { x: number, y: number } => {
  let x = 0
  let y = 0

  if (element instanceof SVGElement) {
    // 对于SVG元素，尝试获取cx/cy或x/y属性
    if ('cx' in element && 'cy' in element) {
      x = parseFloat(element.getAttribute('cx') || '0')
      y = parseFloat(element.getAttribute('cy') || '0')
    } else if ('x' in element && 'y' in element) {
      const width = parseFloat(element.getAttribute('width') || '0')
      const height = parseFloat(element.getAttribute('height') || '0')
      x = parseFloat(element.getAttribute('x') || '0') + width / 2
      y = parseFloat(element.getAttribute('y') || '0') + height / 2
    } else {
      // 尝试从transform属性中获取位置
      const transform = element.getAttribute('transform')
      if (transform && transform.includes('translate')) {
        const match = transform.match(/translate\s*\(\s*([^,)]+)(?:,\s*([^)]+))?\s*\)/)
        if (match) {
          x = parseFloat(match[1] || '0')
          y = parseFloat(match[2] || '0')
        }
      } else {
        // 使用getBBox获取元素包围盒的中心
        try {
          // 检查元素是否支持getBBox
          if ('getBBox' in element) {
            const bbox = (element as SVGGraphicsElement).getBBox()
            x = bbox.x + bbox.width / 2
            y = bbox.y + bbox.height / 2
          }
        } catch (e) {
          console.warn('无法获取元素位置', e)
        }
      }
    }
  }

  return { x, y }
}


// 增加点位大小
const increasePointSize = () => {
  if (pointSize.value < 20) {
    pointSize.value++
  }
}

// 减小点位大小
const decreasePointSize = () => {
  if (pointSize.value > 1) {
    pointSize.value--
  }
}

// 删除点位
const deletePoint = () => {
  if (isNewPoint.value) {
    // 如果是新添加的点位，清除临时点位
    temporaryPoint.value = null;
  } else {
    // 从SVG DOM中删除点位元素
    const svgEl = svgElement.value?.querySelector('svg');
    if (svgEl && editingPoint.value) {
      removePointFromSVG(svgEl, editingPoint.value);
    }

    // 删除现有点位
    const updatedPoints = props.points.filter(p => p.id !== editingPoint.value.id)
    emit('update:points', updatedPoints)
    ElMessage.success(editingPoint.value.isText ? '文本删除成功' : '位点删除成功')
  }
  dialogVisible.value = false
}

// 从SVG中删除点位元素
const removePointFromSVG = (svgElement: SVGElement, point: Point) => {
  let elementToRemove;

  if (point.isText) {
    elementToRemove = svgElement.querySelector(`#text-${point.id}, text[data-id="${point.id}"]`);
  } else {
    elementToRemove = svgElement.querySelector(`#${point.id}, [data-id="${point.id}"]`);
  }

  if (elementToRemove && elementToRemove.parentNode) {
    elementToRemove.parentNode.removeChild(elementToRemove);
  } else {
    console.warn('找不到要删除的点位元素:', point.id);
  }
}

// 处理对话框关闭
const handleDialogClose = () => {
  // 如果是添加模式且正在关闭对话框，则清除临时点位
  if (isNewPoint.value) {
    temporaryPoint.value = null;
  }
}

// 新增：重置表单字段
const resetFormFields = () => {
  // 清空编辑点位相关字段
  selectedIndicator.value = '';
  editingPoint.value = {
    id: '',
    x: 0,
    y: 0,
    name: '',
    type: 'default',
    data: {},
    isText: false
  };

  // 重置样式相关字段
  pointSize.value = 5;
  pointColor.value = '#409EFF';
  textFontSize.value = 14;
  textFontWeight.value = 'normal';
  textColor.value = '#333333';
  textBackgroundColor.value = 'rgba(255, 255, 255, 0.5)';
  hasTextBackground.value = false;
  textBorderColor.value = 'rgba(0, 0, 0, 0.3)';
  textBorderWidth.value = 1;
  textPointCode.value = ''; // 重置文本编号
}

// 初始化 - 增强版，处理SVG加载和点位绑定
onMounted(() => {
  // 添加自定义事件监听器，处理URL格式SVG图像加载完成事件
  const handleImageLoaded = (event: CustomEvent) => {
    console.log('收到图像加载完成事件:', event.detail);
    if (event.detail && event.detail.width && event.detail.height) {
      // 图像加载完成，使用实际尺寸更新viewBox
      const svgEl = svgElement.value?.querySelector('svg');
      if (svgEl) {
        console.log(`更新SVG viewBox为: 0 0 ${event.detail.width} ${event.detail.height}`);
        svgEl.setAttribute('viewBox', `0 0 ${event.detail.width} ${event.detail.height}`);

        // 重新计算缩放和居中
        setTimeout(() => {
          try {
            calculateInitialZoom();
          } catch (e) {
            console.error('图像加载后重新计算缩放失败:', e);
          }
        }, 100);
      }
    }
  };

  // 添加事件监听器
  document.addEventListener('image-loaded', handleImageLoaded as EventListener);

  // 组件卸载时移除事件监听器
  onBeforeUnmount(() => {
    document.removeEventListener('image-loaded', handleImageLoaded as EventListener);
  });

  // 强制设置初始缩放比例为1（100%）
  emit('update:zoom', 1)

  // 初始化操作
  console.log('组件挂载完成，SVG内容:', props.svgContent?.substring(0, 50))
  console.log('当前画面ID:', props.screenId)
  console.log('SVG内容是否URL格式:', isSvgUrl.value)

  // 添加重试机制确保SVG元素存在后再设置内容
  let retryCount = 0;
  const maxRetries = 3;

  const initSvgContent = () => {
    if (props.svgContent && svgElement.value) {
      console.log('组件挂载后设置SVG内容')
      try {
        // 设置SVG内容
        svgElement.value.innerHTML = sanitizedSvgContent.value
        console.log('组件挂载后SVG内容设置成功')

        // 调整SVG容器大小
        nextTick(() => {
          adjustSvgContainerSize()

          // 特别处理URL格式的SVG图像加载
          if (isSvgUrl.value) {
            console.log('URL格式SVG，处理图像加载事件');
            const svgEl = svgElement.value?.querySelector('svg.url-svg-container');
            const imgEl = svgElement.value?.querySelector('image.url-svg-image');

            if (imgEl && svgEl) {
              // 创建一个新的Image对象来预加载SVG
              const img = new Image();
              img.onload = () => {
                console.log('URL图像预加载完成，实际尺寸:', img.width, 'x', img.height);

                if (img.width > 0 && img.height > 0) {
                  // 更新SVG的viewBox以匹配图像实际尺寸
                  const newViewBox = `0 0 ${img.width} ${img.height}`;
                  console.log(`设置正确的viewBox: ${newViewBox}`);
                  svgEl.setAttribute('viewBox', newViewBox);

                  // 更新后重新计算适当的缩放比例
                  setTimeout(() => {
                    console.log('图像加载完成后计算适当缩放');
                    calculateInitialZoom();
                  }, 200);
                }
              };

              // 开始加载图像
              const imgUrl = imgEl.getAttribute('href');
              if (imgUrl) {
                console.log('开始预加载URL图像:', imgUrl);
                img.src = imgUrl;
              }
            }
          }
        })

        // 设置成功后自动适应SVG大小
        setTimeout(() => {
          // 不是URL格式才立即设置样式，URL格式的会在图像加载完成后单独处理
          if (!isSvgUrl.value) {
            console.log('非URL格式SVG，立即设置样式');
            calculateInitialZoom();
          }

          // 检查SVG DOM中的点位元素并绑定事件
          const svgEl = svgElement.value?.querySelector('svg')
          if (svgEl) {
            // 查找监测点组
            const pointsGroup = svgEl.querySelector('#monitoring-points')
            if (pointsGroup) {
              console.log('找到SVG中的监测点组，处理点位元素')

              // 获取所有点位元素和文本元素
              const pointElements = pointsGroup.querySelectorAll('[data-point="true"], [data-id], [id^="point-"]')
              const textElements = pointsGroup.querySelectorAll('text')

              console.log(`SVG DOM中找到 ${pointElements.length} 个监测点和 ${textElements.length} 个文本元素`)

              // 为所有点位和文本元素添加事件监听
              const allElements = [...Array.from(pointElements), ...Array.from(textElements)]
              allElements.forEach(element => {
                // 设置鼠标样式和确保可交互
                element.setAttribute('cursor', 'pointer')
                element.setAttribute('pointer-events', 'all')

                // 确保可见性
                element.removeAttribute('visibility')
                element.removeAttribute('display')

                // 绑定点击事件 - 获取点位ID并查找对应点位
                const pointId = element.getAttribute('id') || element.getAttribute('data-id')
                if (pointId) {
                  const point = props.points.find(p => p.id === pointId ||
                    (element.tagName.toLowerCase() === 'text' &&
                      `text-${p.id}` === pointId))

                  if (point) {
                    // 只有找到匹配的点位才绑定事件
                    element.addEventListener('click', (e) => {
                      e.stopPropagation()
                      selectPoint(point)
                    })

                    element.addEventListener('dblclick', async (e) => {
                      e.stopPropagation();
                      await editPoint(point);
                    })

                    // 对非文本元素绑定拖拽事件
                    if (element.tagName.toLowerCase() !== 'text') {
                      element.addEventListener('mousedown', (e: Event) => {
                        e.stopPropagation()
                        // 将 Event 转换为 MouseEvent
                        const mouseEvent = e as MouseEvent
                        if (mouseEvent.ctrlKey) {
                          startDrag(mouseEvent, point)
                        }
                      })
                    }
                  }
                }
              })

              console.log('SVG点位事件绑定完成')
            }
          }
        }, 300)
      } catch (error) {
        console.error('组件挂载后设置SVG内容失败:', error)
      }
    } else if (props.svgContent && retryCount < maxRetries) {
      retryCount++;
      console.log(`SVG元素不存在，第${retryCount}次重试...`)
      setTimeout(initSvgContent, 100);
    } else if (props.svgContent) {
      console.error('多次重试后仍无法找到SVG元素，放弃初始化')
    }
  };

  // 初始尝试设置内容
  nextTick(initSvgContent)

  // 记录现有点位数量
  if (props.points && props.points.length > 0) {
    console.log('挂载时已有点位数据，数量:', props.points.length)
  }

  // 添加鼠标滚轮缩放事件
  if (svgContainerRef.value) {
    svgContainerRef.value.addEventListener('wheel', handleWheel, { passive: false })
  }

  // 添加窗口大小变化监听，用于重新计算位点位置
  window.addEventListener('resize', handleResize)

  // 确保SVG内容加载完成后设置初始缩放比例
  nextTick(() => {
    // 延迟一点执行，确保SVG内容已经完全加载
    setTimeout(() => {
      // 如果是URL格式，延迟更长时间等待图像加载
      if (isSvgUrl.value) {
        console.log('URL格式SVG，延迟计算初始缩放');
        setTimeout(() => {
          try {
            calculateInitialZoom();
          } catch (e) {
            console.error('URL格式SVG初始化缩放失败:', e);
          }
        }, 800);  // 减少延迟时间，因为我们现在有图像加载事件
      } else {
        try {
          calculateInitialZoom();
        } catch (e) {
          console.error('初始化缩放失败:', e);
        }
      }
    }, 300);  // 减少延迟时间
  });
})

// 处理窗口大小变化
const handleResize = () => {
  // 当窗口大小变化时，触发一次监测点位置重新计算
  // 通过创建一个点位数组的浅拷贝来触发响应式更新
  if (props.points && props.points.length > 0) {
    console.log('窗口大小变化，重新计算监测点位置')

    // 获取SVG元素
    const svgElem = svgElement.value
    if (svgElem) {
      // 重新应用布局
      const svgContent = svgElem.querySelector('svg')
      if (svgContent) {
        // 更新SVG容器样式
        console.log('重新计算SVG容器样式')

        // 重新计算点位位置，不改变原坐标
        nextTick(() => {
          emit('update:points', [...props.points])
        })
      }
    }
  }
}

// 在组件销毁前移除事件监听
onBeforeUnmount(() => {
  if (svgContainerRef.value) {
    svgContainerRef.value.removeEventListener('wheel', handleWheel)
  }
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
})

// 新增监听器：监听SVG内容变化
watch(() => svgElement.value?.innerHTML, () => {
  if (svgElement.value) {
    console.log('SVG内容变化，重新计算点位位置')
    // 重新计算所有点位位置
    if (props.points && props.points.length > 0) {
      nextTick(() => {
        console.log('刷新点位布局')
        emit('update:points', [...props.points])
      })
    }

    // 为URL格式的SVG图像添加加载事件处理
    if (isSvgUrl.value) {
      nextTick(() => {
        const svgEl = svgElement.value?.querySelector('svg.url-svg-container');
        const imgEl = svgElement.value?.querySelector('image.url-svg-image');

        if (imgEl) {
          console.log('为URL格式SVG图像添加加载事件');

          // 使用MutationObserver监听图像加载
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (mutation.type === 'attributes' && mutation.attributeName === 'href') {
                console.log('图像href属性已变化，准备处理加载事件');

                // 使用Image对象预加载并获取尺寸
                const img = new Image();
                img.onload = () => {
                  console.log('URL图像已加载，尺寸:', img.width, 'x', img.height);

                  // 如果有可用尺寸，更新SVG viewBox
                  if (img.width > 0 && img.height > 0 && svgEl) {
                    const originalViewBox = svgEl.getAttribute('viewBox');
                    const newViewBox = `0 0 ${img.width} ${img.height}`;
                    console.log(`更新viewBox从 ${originalViewBox} 到 ${newViewBox}`);
                    svgEl.setAttribute('viewBox', newViewBox);

                    // 延迟重新计算缩放以适应新尺寸
                    setTimeout(() => {
                      calculateInitialZoom();
                    }, 100);
                  }
                };

                // 获取图像URL
                const imgUrl = imgEl.getAttribute('href');
                if (imgUrl) {
                  img.src = imgUrl;
                }
              }
            });
          });

          // 开始观察href属性变化
          observer.observe(imgEl, { attributes: true, attributeFilter: ['href'] });

          // 也处理已经加载的图像
          const imgUrl = imgEl.getAttribute('href');
          if (imgUrl) {
            const img = new Image();
            img.onload = () => {
              console.log('已有URL图像尺寸:', img.width, 'x', img.height);

              if (img.width > 0 && img.height > 0 && svgEl) {
                const originalViewBox = svgEl.getAttribute('viewBox');
                const newViewBox = `0 0 ${img.width} ${img.height}`;
                console.log(`更新已加载图像viewBox从 ${originalViewBox} 到 ${newViewBox}`);
                svgEl.setAttribute('viewBox', newViewBox);

                // 延迟重新计算缩放以适应新尺寸
                setTimeout(() => {
                  calculateInitialZoom();
                }, 100);
              }
            };
            img.src = imgUrl;
          }
        }
      });
    }
  }
}, { deep: true })

// 监听zoom变化
watch(() => props.zoom, () => {
  nextTick(() => {
    // 缩放变化，重新计算点位位置
    if (props.points && props.points.length > 0) {
      console.log('缩放比例变化，重新计算点位位置')
      emit('update:points', [...props.points])
    }
  })
}, { immediate: true })

// 添加新方法，调整SVG容器大小以适应SVG内容
const adjustSvgContainerSize = () => {
  const svgEl = svgElement.value?.querySelector('svg')
  if (!svgEl) return

  const svgWrapper = svgElement.value?.parentElement
  if (!svgWrapper) return

  // 确保SVG元素保持原始尺寸
  svgEl.style.width = 'auto';
  svgEl.style.height = 'auto';
  svgEl.style.maxWidth = '100%';
  svgEl.style.maxHeight = '100%';
  svgEl.style.display = 'block';
  svgEl.style.margin = 'auto'; // 使用margin:auto实现居中

  // 确保SVG内容容器适应可视区域
  if (svgElement.value) {
    svgElement.value.style.width = 'auto';
    svgElement.value.style.height = 'auto';
    svgElement.value.style.maxWidth = '100%';
    svgElement.value.style.maxHeight = '100%';
    svgElement.value.style.display = 'block';
    svgElement.value.style.margin = 'auto'; // 使用margin:auto实现居中
  }

  // 确保包装器使用flex布局居中
  svgWrapper.style.width = '100%';
  svgWrapper.style.height = '100%';
  svgWrapper.style.display = 'flex';
  svgWrapper.style.justifyContent = 'center';
  svgWrapper.style.alignItems = 'center';

  console.log('已设置SVG及其容器使用CSS居中显示');

  // 获取SVG实际尺寸 (仅用于日志记录)
  const svgWidth = svgEl.width?.baseVal?.value || parseInt(svgEl.getAttribute('width') || '0', 10)
  const svgHeight = svgEl.height?.baseVal?.value || parseInt(svgEl.getAttribute('height') || '0', 10)

  if (svgWidth > 0 && svgHeight > 0) {
    console.log(`SVG原始尺寸: ${svgWidth}x${svgHeight}，保持原始尺寸并居中显示`);
  }
}

// 选中的点位
const selectedPoint = ref<Point | null>(null)

// 选择点位
const selectPoint = (point: Point) => {
  // 如果正在添加元素，不进行选择操作
  if (addingElementType.value) return

  // 如果选择的是当前已选中的点位，取消选择
  if (selectedPoint.value?.id === point.id) {
    selectedPoint.value = null
  } else {
    selectedPoint.value = point
    // 提示用户可以双击编辑
    ElMessage.info('点位已选中，双击可编辑点位属性')
  }
}

// 监听pointSize变化，实时更新到editingPoint
watch(() => pointSize.value, (newSize) => {
  if (!editingPoint.value.isText && editingPoint.value.data) {
    editingPoint.value.data.size = newSize;

    // 如果有临时点位，也更新临时点位的大小
    if (temporaryPoint.value && !temporaryPoint.value.isText) {
      temporaryPoint.value.data.size = newSize;
    }
  }
});

// 监听pointColor变化，实时更新到editingPoint
watch(() => pointColor.value, (newColor) => {
  if (!editingPoint.value.isText && editingPoint.value.data) {
    editingPoint.value.data.color = newColor;

    // 如果有临时点位，也更新临时点位的颜色
    if (temporaryPoint.value && !temporaryPoint.value.isText) {
      temporaryPoint.value.data.color = newColor;
    }
  }
});

// 处理SVG点位叠加层
const updateSvgOverlay = () => {
  // 不再使用覆盖层，直接在SVG中处理点位
  console.log('不再使用覆盖层，直接在SVG中处理点位');

  // 获取SVG元素
  const svgEl = svgElement.value?.querySelector('svg');
  if (!svgEl) {
    console.warn('未找到SVG元素，无法处理点位');
    return;
  }

  // 获取或创建监测点容器
  let pointsGroup = svgEl.querySelector('#monitoring-points');
  if (!pointsGroup) {
    pointsGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
    pointsGroup.setAttribute("id", "monitoring-points");
    pointsGroup.setAttribute("class", "monitoring-points-layer");
    svgEl.appendChild(pointsGroup);
  }

  // 清空现有点位
  while (pointsGroup.firstChild) {
    pointsGroup.removeChild(pointsGroup.firstChild);
  }

  // 重新添加所有点位
  if (props.points && props.points.length > 0) {
    console.log(`为SVG添加${props.points.length}个监测点`);

    props.points.forEach((point, index) => {
      // 直接在SVG中添加点位
      addOrUpdatePointInSVG(svgEl, point, true);
    });

    // 确保所有元素都有正确的样式
    nextTick(() => {
      // 处理所有文本元素
      const textElements = svgEl.querySelectorAll('#monitoring-points text');
      textElements.forEach(text => {
        // 找到对应的点位数据
        const dataId = text.getAttribute('data-id');
        const pointData = props.points.find(p => p.id === dataId);

        if (pointData && pointData.isText) {
          // 使用点位原始数据进行更新，确保颜色正确
          const color = pointData.data?.color || "#333333";
          const fontWeight = pointData.data?.fontWeight || "normal";
          const fontSize = pointData.data?.fontSize || 14;

          // 使用多种方式设置颜色
          text.setAttribute('fill', color);
          (text as SVGTextElement).style.fill = color;
          (text as SVGTextElement).style.color = color;
          (text as SVGTextElement).style.stroke = "none";

          // 设置字体样式
          text.setAttribute('font-weight', fontWeight);
          (text as SVGTextElement).style.fontWeight = fontWeight;

          // 设置字体大小
          text.setAttribute('font-size', `${fontSize}px`);
          (text as SVGTextElement).style.fontSize = `${fontSize}px`;

          // 直接设置style属性
          text.setAttribute("style", `fill:${color}; color:${color}; font-weight:${fontWeight}; font-size:${fontSize}px; stroke:none; --text-color:${color};`);
        } else {
          // 回退到基本的样式设置
          const fillColor = text.getAttribute('fill');
          if (fillColor) {
            (text as SVGTextElement).style.fill = fillColor;
            (text as SVGTextElement).style.color = fillColor;
            text.setAttribute("style", `fill:${fillColor}; color:${fillColor};`);
          }
        }

        (text as SVGTextElement).style.cursor = 'pointer';
        (text as SVGTextElement).style.pointerEvents = 'all'; // 确保文本元素可以接收鼠标事件
      });

      // 处理所有点位元素
      const pointElements = svgEl.querySelectorAll('#monitoring-points [data-point="true"], #monitoring-points circle, #monitoring-points rect, #monitoring-points polygon');
      pointElements.forEach(point => {
        (point as SVGElement).style.cursor = 'pointer';
        (point as SVGElement).style.pointerEvents = 'all'; // 确保点位元素可以接收鼠标事件
      });
    });
  }
};

// 专门用于更新文本元素样式的函数
const updateTextElementStyles = (svgElement: SVGElement | SVGSVGElement | null | undefined) => {
  if (!svgElement) return;

  // 查找所有文本元素
  const textElements = svgElement.querySelectorAll('#monitoring-points text');
  console.log(`更新${textElements.length}个文本元素的样式`);

  // 为每个文本元素设置正确的样式
  textElements.forEach(textElement => {
    const fillColor = textElement.getAttribute('fill');
    const fontWeight = textElement.getAttribute('font-weight');
    const fontSize = textElement.getAttribute('font-size');

    if (fillColor) {
      // 使用内联样式设置填充颜色，确保颜色显示正确
      (textElement as SVGTextElement).style.fill = fillColor;
      (textElement as SVGTextElement).style.color = fillColor; // 添加color属性

      // 确保没有描边干扰颜色显示
      (textElement as SVGTextElement).style.stroke = "none";

      // 使用setAttribute方式设置，提高优先级
      const currentStyles = textElement.getAttribute("style") || "";
      const newStyles = `fill:${fillColor}; color:${fillColor}; ${currentStyles}`;
      textElement.setAttribute("style", newStyles);
    }

    if (fontWeight) {
      // 使用内联样式设置字体粗细
      (textElement as SVGTextElement).style.fontWeight = fontWeight;
    }

    if (fontSize) {
      // 使用内联样式设置字体大小
      (textElement as SVGTextElement).style.fontSize = fontSize;
    }

    // 确保鼠标指针样式正确
    (textElement as SVGTextElement).style.cursor = 'pointer';
    // 确保文本元素可以接收鼠标事件
    (textElement as SVGTextElement).style.pointerEvents = 'all';
  });
};



// 获取临时点位预览样式
const getTemporaryPointPreviewStyle = (point: Point): Record<string, any> => {
  // 获取SVG元素和viewBox信息
  let clientX = 0;
  let clientY = 0;

  const svgElem = svgElement.value;
  if (svgElem) {
    const imgEl = svgElem.querySelector('img');
    const svgEl = svgElem.querySelector('svg');

    if (svgEl) {
      // 普通SVG格式
      const rect = svgEl.getBoundingClientRect();
      const viewBox = svgEl.viewBox?.baseVal || { x: 0, y: 0, width: 1000, height: 1000 };

      // 获取SVG的变换矩阵
      const svgTransform = svgEl.getScreenCTM();
      if (svgTransform) {
        // 创建SVG点
        const svgPoint = svgEl.createSVGPoint();
        svgPoint.x = point.x;
        svgPoint.y = point.y;

        // 将SVG坐标转换为屏幕坐标
        const transformedPoint = svgPoint.matrixTransform(svgTransform);
        clientX = transformedPoint.x;
        clientY = transformedPoint.y;

        // 考虑父容器的偏移和缩放
        const outerWrapper = svgContainerRef.value;
        if (outerWrapper) {
          // 添加容器滚动位置
          clientX += outerWrapper.scrollLeft;
          clientY += outerWrapper.scrollTop;
        }
      } else {
        // 回退到旧方法
        // 计算视图坐标到屏幕坐标的映射
        const scaleX = rect.width / viewBox.width;
        const scaleY = rect.height / viewBox.height;

        // 计算点位在屏幕上的位置
        clientX = rect.left + (point.x - viewBox.x) * scaleX * props.zoom;
        clientY = rect.top + (point.y - viewBox.y) * scaleY * props.zoom;

        // 考虑可视区域中心对齐的偏移量影响
        clientX += svgTranslateX.value;
        clientY += svgTranslateY.value;
      }

      // 确保坐标不超出屏幕范围
      clientX = Math.max(rect.left, Math.min(clientX, rect.right));
      clientY = Math.max(rect.top, Math.min(clientY, rect.bottom));

      // 调试信息
      console.log('临时点位预览坐标:', {
        svgPoint: `(${point.x}, ${point.y})`,
        screenPoint: `(${clientX}, ${clientY})`,
        svgRect: `${rect.left},${rect.top},${rect.width},${rect.height}`,
        zoom: props.zoom,
        translate: `(${svgTranslateX.value}, ${svgTranslateY.value})`
      });
    } else if (imgEl && isSvgUrl.value) {
      // URL格式SVG
      const rect = imgEl.getBoundingClientRect();
      const imgElem = imgEl as HTMLImageElement;
      const viewBoxWidth = imgElem.naturalWidth || rect.width;
      const viewBoxHeight = imgElem.naturalHeight || rect.height;

      // 计算点位在屏幕上的位置（百分比映射）
      const percentX = point.x / viewBoxWidth;
      const percentY = point.y / viewBoxHeight;

      clientX = rect.left + percentX * rect.width * props.zoom;
      clientY = rect.top + percentY * rect.height * props.zoom;

      // 为临时预览点考虑偏移量
      // 此处适应可视区域中心对齐的新偏移计算方式
      clientX += svgTranslateX.value;
      clientY += svgTranslateY.value;
    }
  }

  // 基本样式
  const size = point.data?.size || 5;
  const minSize = 40; // 最小预览大小

  return {
    position: 'fixed',
    left: `${clientX}px`,
    top: `${clientY}px`,
    minWidth: point.isText ? '100px' : `${Math.max(minSize, size * 6)}px`,
    minHeight: point.isText ? '40px' : `${Math.max(minSize, size * 6)}px`,
    fontSize: point.isText ? `${point.data?.fontSize || 16}px` : 'inherit',
    fontWeight: point.isText ? point.data?.fontWeight || 'bold' : 'bold',
    color: point.isText ? point.data?.color || '#333' : 'inherit'
  };
}

// 直接在SVG DOM中添加或更新点位
const addOrUpdatePointInSVG = (svgElement: SVGElement, point: Point, isNew: boolean) => {
  // 确保监测点组存在
  let pointsGroup = svgElement.querySelector('#monitoring-points');
  if (!pointsGroup) {
    // 创建监测点组
    pointsGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
    pointsGroup.setAttribute("id", "monitoring-points");
    pointsGroup.setAttribute("class", "monitoring-points-layer");
    svgElement.appendChild(pointsGroup);
  }

  // 检查是否为新建点位或更新现有点位
  if (isNew) {
    // 添加新点位
    if (point.isText) {
      // 直接创建文本元素，不使用背景矩形

      // 创建文本元素
      const textElement = document.createElementNS("http://www.w3.org/2000/svg", "text");
      textElement.setAttribute("x", point.x.toString());
      textElement.setAttribute("y", point.y.toString());
      textElement.setAttribute("id", `text-${point.id}`);
      textElement.setAttribute("data-id", point.id);
      textElement.setAttribute("data-name", point.name);
      textElement.setAttribute("data-type", "text");
      textElement.setAttribute("data-show-type", "text"); // 确保文本点位也有data-show-type属性
      textElement.setAttribute("data-point-code", point.data?.pointCode || "");
      textElement.setAttribute("font-size", `${point.data?.fontSize || 14}px`);
      textElement.setAttribute("font-weight", point.data?.fontWeight || "normal");
      textElement.setAttribute("fill", point.data?.color || "#333333");
      textElement.setAttribute("pointer-events", "all");
      textElement.setAttribute("cursor", "pointer");
      textElement.setAttribute("text-anchor", "middle");
      textElement.setAttribute("dominant-baseline", "central");
      textElement.setAttribute("alignment-baseline", "middle");

      // 添加内联样式确保颜色和字体粗细正确显示
      textElement.style.fill = point.data?.color || "#333333";
      textElement.style.color = point.data?.color || "#333333"; // 添加color属性
      textElement.style.stroke = "none"; // 防止描边干扰
      textElement.style.fontWeight = point.data?.fontWeight || "normal";
      textElement.style.fontSize = `${point.data?.fontSize || 14}px`;

      textElement.textContent = point.name;

      // 添加点击、双击和拖拽事件
      textElement.addEventListener('click', (e) => {
        e.stopPropagation();
        selectPoint(point);
      });
      textElement.addEventListener('dblclick', async (e) => {
        e.stopPropagation();
        await editPoint(point);
      });
      // 添加mousedown事件以支持拖拽
      textElement.addEventListener('mousedown', (e) => {
        e.stopPropagation();
        if (e.ctrlKey) {
          startDrag(e, point);
        }
      });

      // 添加到SVG
      pointsGroup.appendChild(textElement);
    } else {
      // 创建普通监测点元素
      let pointElement;
      const size = point.data?.size || 5;
      const color = point.data?.color || getPointColor(point.type);

      // 根据点位类型创建不同的SVG元素
      switch (point.type) {
        case 'circle':
          pointElement = document.createElementNS("http://www.w3.org/2000/svg", "circle");
          pointElement.setAttribute("cx", point.x.toString());
          pointElement.setAttribute("cy", point.y.toString());
          pointElement.setAttribute("r", size.toString());
          pointElement.setAttribute("fill", color);
          break;
        case 'square':
          pointElement = document.createElementNS("http://www.w3.org/2000/svg", "rect");
          pointElement.setAttribute("x", (point.x - size).toString());
          pointElement.setAttribute("y", (point.y - size).toString());
          pointElement.setAttribute("width", (size * 2).toString());
          pointElement.setAttribute("height", (size * 2).toString());
          pointElement.setAttribute("fill", color);
          break;
        case 'triangle':
          pointElement = document.createElementNS("http://www.w3.org/2000/svg", "polygon");
          const pointsCoord = `${point.x},${point.y - size} ${point.x + size},${point.y + size} ${point.x - size},${point.y + size}`;
          pointElement.setAttribute("points", pointsCoord);
          pointElement.setAttribute("fill", color);
          break;
        default:
          pointElement = document.createElementNS("http://www.w3.org/2000/svg", "circle");
          pointElement.setAttribute("cx", point.x.toString());
          pointElement.setAttribute("cy", point.y.toString());
          pointElement.setAttribute("r", size.toString());
          pointElement.setAttribute("fill", color);
      }

      // 添加数据属性
      pointElement.setAttribute("id", point.id);
      pointElement.setAttribute("data-id", point.id);
      pointElement.setAttribute("data-name", point.name);
      pointElement.setAttribute("data-type", point.type);
      pointElement.setAttribute("data-show-type", point.type); // 确保图形监测点有data-show-type属性
      pointElement.setAttribute("data-point-code", point.name); // 使用name作为监测点编号
      pointElement.setAttribute("data-point", "true");
      pointElement.setAttribute("pointer-events", "all");

      // 添加监测指标相关属性
      if (point.data?.indicator) {
        pointElement.setAttribute("data-indicator-code", point.data.indicator);

        // 如果有indicatorInfo，添加更多详细信息
        if (point.data?.indicatorInfo) {
          const info = point.data.indicatorInfo;
          if (info.label) pointElement.setAttribute("data-indicator-name", info.label);
          if (info.pointCode) pointElement.setAttribute("data-point-code", info.pointCode);
        }
      }

      // 确保data-point-code属性被设置，优先使用point.name
      if (!pointElement.hasAttribute("data-point-code") || !pointElement.getAttribute("data-point-code")) {
        pointElement.setAttribute("data-point-code", point.name);
      }

      // 添加点击、双击和拖拽事件
      pointElement.addEventListener('click', (e) => {
        e.stopPropagation();
        selectPoint(point);
      });
      pointElement.addEventListener('dblclick', async (e) => {
        e.stopPropagation();
        await editPoint(point);
      });
      pointElement.addEventListener('mousedown', (e) => {
        e.stopPropagation();
        if (e.ctrlKey) {
          startDrag(e, point);
        }
      });

      // 添加到SVG
      pointsGroup.appendChild(pointElement);
    }
  } else {
    // 更新现有点位
    if (point.isText) {
      // 计算文本尺寸用于背景
      const fontSize = point.data?.fontSize || 14;
      // 增加系数确保文本不溢出边框
      const textWidth = point.name.length * fontSize * 0.8;
      const textHeight = fontSize * 1.2;
      // 设置水平和垂直内边距
      const horizontalPadding = 20; // 左右各10px
      const verticalPadding = 10;   // 上下各5px

      // 更新文本元素
      const selector = `text#text-${point.id}, text[data-id="${point.id}"]`;
      const textElement = svgElement.querySelector(selector) as SVGTextElement;

      // 查找相关的背景元素
      const bgSelector = `rect#bg-text-${point.id}, rect[data-for-text="${point.id}"]`;
      let bgElement = svgElement.querySelector(bgSelector) as SVGRectElement;

      if (textElement) {
        // 更新现有文本元素
        textElement.setAttribute("x", point.x.toString());
        textElement.setAttribute("y", point.y.toString());
        textElement.setAttribute("data-name", point.name);
        textElement.setAttribute("data-show-type", "text"); // 确保文本点位也有data-show-type属性
        textElement.setAttribute("data-point-code", point.data?.pointCode || "");
        textElement.setAttribute("font-size", `${point.data?.fontSize || 14}px`);
        textElement.setAttribute("font-weight", point.data?.fontWeight || "normal");
        textElement.setAttribute("fill", point.data?.color || "#333333");
        textElement.setAttribute("text-anchor", "middle");
        textElement.setAttribute("dominant-baseline", "central");
        textElement.setAttribute("alignment-baseline", "middle");
        (textElement as SVGTextElement).style.fill = point.data?.color || "#333333";
        (textElement as SVGTextElement).style.color = point.data?.color || "#333333"; // 添加color属性
        (textElement as SVGTextElement).style.stroke = "none"; // 防止描边干扰
        (textElement as SVGTextElement).style.fontWeight = point.data?.fontWeight || "normal";
        (textElement as SVGTextElement).style.fontSize = `${point.data?.fontSize || 14}px`;

        // 使用setAttribute设置样式，确保最高优先级
        textElement.setAttribute("style", `fill:${point.data?.color || "#333333"}; color:${point.data?.color || "#333333"}; font-weight:${point.data?.fontWeight || "normal"}; font-size:${point.data?.fontSize || 14}px;`);

        (textElement as SVGTextElement).style.cursor = "pointer";
        textElement.setAttribute("cursor", "pointer");
        textElement.textContent = point.name;

        // 不再处理文本背景元素，直接使用文本

        // 确保有事件监听器
        const newElement = textElement.cloneNode(true) as SVGTextElement;

        // 重新设置样式属性，确保颜色和字体粗细正确应用
        newElement.style.fill = point.data?.color || "#333333";
        newElement.style.color = point.data?.color || "#333333"; // 添加color属性
        newElement.style.stroke = "none"; // 防止描边干扰
        newElement.style.fontWeight = point.data?.fontWeight || "normal";
        newElement.style.fontSize = `${point.data?.fontSize || 14}px`;
        newElement.style.cursor = "pointer";

        newElement.addEventListener('click', (e) => {
          e.stopPropagation();
          selectPoint(point);
        });
        newElement.addEventListener('dblclick', async (e) => {
          e.stopPropagation();
          await editPoint(point);
        });
        // 添加mousedown事件支持拖拽
        newElement.addEventListener('mousedown', (e) => {
          e.stopPropagation();
          if (e.ctrlKey) {
            startDrag(e, point);
          }
        });
        textElement.parentNode?.replaceChild(newElement, textElement);
      } else {
        // 找不到现有元素，添加新元素
        addOrUpdatePointInSVG(svgElement, point, true);
      }
    } else {
      // 更新普通监测点
      const selector = `#${point.id}, [data-id="${point.id}"]`;
      const pointElement = svgElement.querySelector(selector);

      if (pointElement) {
        const size = point.data?.size || 5;
        const color = point.data?.color || getPointColor(point.type);

        // 添加鼠标手型指针样式
        pointElement.setAttribute("cursor", "pointer");
        (pointElement as SVGElement).style.cursor = "pointer";

        // 根据元素类型更新属性
        if (pointElement.tagName.toLowerCase() === 'circle') {
          pointElement.setAttribute("cx", point.x.toString());
          pointElement.setAttribute("cy", point.y.toString());
          pointElement.setAttribute("r", size.toString());
          pointElement.setAttribute("fill", color);
        } else if (pointElement.tagName.toLowerCase() === 'rect') {
          pointElement.setAttribute("x", (point.x - size).toString());
          pointElement.setAttribute("y", (point.y - size).toString());
          pointElement.setAttribute("width", (size * 2).toString());
          pointElement.setAttribute("height", (size * 2).toString());
          pointElement.setAttribute("fill", color);
        } else if (pointElement.tagName.toLowerCase() === 'polygon') {
          const pointsCoord = `${point.x},${point.y - size} ${point.x + size},${point.y + size} ${point.x - size},${point.y + size}`;
          pointElement.setAttribute("points", pointsCoord);
          pointElement.setAttribute("fill", color);
        }

        // 更新数据属性
        pointElement.setAttribute("data-name", point.name);
        pointElement.setAttribute("data-point-code", point.name); // 使用name作为监测点编号
        pointElement.setAttribute("data-show-type", point.type); // 添加data-show-type属性
        console.log(`更新点位${point.id}的data-show-type属性为${point.type}`);

        // 确保在point对象中保存type信息
        if (!point.data) {
          point.data = {};
        }
        // 直接在data对象中保存type
        point.data.type = point.type;

        // 确保point.data中也包含data-show-type属性和type信息
        if (!point.data) {
          point.data = {};
        }
        // 直接在data中保存type信息
        point.data.type = point.type;

        if (!point.data.attributes) {
          point.data.attributes = {};
        }
        point.data.attributes['data-show-type'] = point.type;

        // 更新监测指标相关属性
        if (point.data?.indicator) {
          pointElement.setAttribute("data-indicator-code", point.data.indicator);
        }

        // 确保点位属性被正确设置
        console.log(`点位${point.id}更新后的属性:`, {
          'data-name': pointElement.getAttribute('data-name'),
          'data-show-type': pointElement.getAttribute('data-show-type'),
          'data-indicator-code': pointElement.getAttribute('data-indicator-code')
        });

        // 确保有事件监听器
        const newElement = pointElement.cloneNode(true) as SVGElement;
        newElement.addEventListener('click', (e) => {
          e.stopPropagation();
          selectPoint(point);
        });
        newElement.addEventListener('dblclick', async (e) => {
          e.stopPropagation();
          await editPoint(point);
        });
        newElement.addEventListener('mousedown', (e) => {
          e.stopPropagation();
          if (e.ctrlKey) {
            startDrag(e, point);
          }
        });
        pointElement.parentNode?.replaceChild(newElement, pointElement);
      } else {
        // 找不到现有元素，添加新元素
        addOrUpdatePointInSVG(svgElement, point, true);
      }
    }
  }
}

// 添加拖拽相关状态
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartY = ref(0)
const svgTranslateX = ref(0)
const svgTranslateY = ref(0)

// SVG拖拽功能
const startSvgDrag = (e: MouseEvent) => {
  // 如果正在添加元素，不启用拖拽
  if (addingElementType.value) {
    return;
  }

  // 设置拖拽状态
  isDragging.value = true;
  dragStartX.value = e.clientX;
  dragStartY.value = e.clientY;

  // 更改鼠标样式
  if (svgContainerRef.value) {
    svgContainerRef.value.style.cursor = 'move';
  }

  console.log('开始拖拽SVG');
}

const onSvgDrag = (e: MouseEvent) => {
  // 如果没有处于拖拽状态，不执行任何操作
  if (!isDragging.value) {
    return;
  }

  // 计算拖拽距离
  const deltaX = e.clientX - dragStartX.value;
  const deltaY = e.clientY - dragStartY.value;

  // 更新SVG位置
  const svgContent = svgElement.value;
  if (svgContent) {
    // 更新偏移量
    svgTranslateX.value += deltaX;
    svgTranslateY.value += deltaY;

    // 应用变换
    svgContent.style.transform = `translate(${svgTranslateX.value}px, ${svgTranslateY.value}px)`;
  }

  // 更新起始点，为下一次移动做准备
  dragStartX.value = e.clientX;
  dragStartY.value = e.clientY;
}

const endSvgDrag = () => {
  // 结束拖拽状态
  isDragging.value = false;

  // 恢复鼠标样式
  if (svgContainerRef.value) {
    svgContainerRef.value.style.cursor = 'default';
  }

  console.log('结束拖拽SVG');
}

// 设置SVG样式防抖标志
let stylingInProgress = false;

// 不计算偏移量，让SVG在容器中自然居中
const centerSvg = (zoom: number, svgWidth: number, svgHeight: number, containerWidth: number, containerHeight: number) => {
  // 防止频繁调用导致的抖动
  if (stylingInProgress) {
    console.log('SVG样式设置操作正在进行中，跳过本次调用');
    return;
  }

  stylingInProgress = true;

  console.log('设置SVG样式，不计算偏移量，使用CSS居中...');
  // 确保所有参数都有效
  if (!svgWidth || !svgHeight || !containerWidth || !containerHeight) {
    console.warn('设置SVG样式失败：无效的参数');
    stylingInProgress = false;
    return;
  }

  try {
    // 获取SVG元素，确保存在
    const svgEl = svgElement.value?.querySelector('svg');
    if (!svgEl) {
      console.warn('找不到SVG元素，无法设置样式');
      stylingInProgress = false;
      return;
    }

    // 不重置偏移量，保留用户的拖拽位置
    // 如果需要重置位置，可以通过其他按钮触发
    // svgTranslateX.value = 0;
    // svgTranslateY.value = 0;

    console.log('不进行偏移计算，使用CSS居中显示:', {
      zoom,
      svgSize: `${svgWidth}x${svgHeight}`,
      containerSize: `${containerWidth}x${containerHeight}`,
      translate: '(0, 0)'
    });

    // 确保SVG样式正确设置
    nextTick(() => {
      try {
        // 获取SVG内容元素
        const svgContent = svgElement.value;
        if (svgContent) {
          // 设置SVG内容元素样式，使用CSS居中，同时允许拖拽
          svgContent.style.width = 'auto'; // 使用auto而不是100%，让SVG保持其原始尺寸
          svgContent.style.height = 'auto';
          svgContent.style.display = 'block';
          svgContent.style.margin = 'auto'; // 使用margin:auto实现居中
          svgContent.style.transformOrigin = 'center center';
          svgContent.style.transform = `translate(${svgTranslateX.value}px, ${svgTranslateY.value}px)`;
          svgContent.style.cursor = isDragging.value ? 'move' : 'default';

          // 获取SVG外层容器
          const svgOuterWrapper = svgContent.parentElement;
          if (svgOuterWrapper) {
            // 设置外层容器使用flex布局居中
            svgOuterWrapper.style.width = '100%';
            svgOuterWrapper.style.height = '100%';
            svgOuterWrapper.style.display = 'flex';
            svgOuterWrapper.style.justifyContent = 'center'; // 水平居中
            svgOuterWrapper.style.alignItems = 'center'; // 垂直居中
            svgOuterWrapper.style.padding = '0';
            svgOuterWrapper.style.margin = '0';
            svgOuterWrapper.style.overflow = 'hidden';
            svgOuterWrapper.style.transformOrigin = 'center center';

            // 获取svg-outer-wrapper的父元素 (svg-container)
            const svgContainer = svgOuterWrapper.parentElement;
            if (svgContainer) {
              svgContainer.style.width = '100%';
              svgContainer.style.height = '100%';
              svgContainer.style.display = 'flex';
              svgContainer.style.justifyContent = 'center'; // 水平居中
              svgContainer.style.alignItems = 'center'; // 垂直居中
              svgContainer.style.overflow = 'hidden';
              svgContainer.style.transformOrigin = 'center center';
            }
          }

          // 获取SVG元素本身
          const svgEl = svgContent.querySelector('svg');
          if (svgEl) {
            // 确保SVG元素保持其原始尺寸，不被拉伸
            svgEl.style.maxWidth = '100%';
            svgEl.style.maxHeight = '100%';
            svgEl.style.width = 'auto'; // 使用auto而不是100%
            svgEl.style.height = 'auto';
            svgEl.style.display = 'block';
            svgEl.style.margin = 'auto'; // 使用margin:auto实现居中
            svgEl.style.padding = '0';
            svgEl.style.transformOrigin = 'center center';
          }

          console.log('已设置所有SVG相关容器样式，使用CSS实现居中显示');
        }

        // 允许下一次操作
        setTimeout(() => {
          stylingInProgress = false;
        }, 200);
      } catch (error) {
        console.error('设置SVG容器样式失败:', error);
        stylingInProgress = false;
      }
    });
  } catch (error) {
    console.error('设置SVG样式失败:', error);
    // 确保偏移量为0
    svgTranslateX.value = 0;
    svgTranslateY.value = 0;
    stylingInProgress = false;
  }
}

// 防止重复计算的标志
let isCalculatingZoom = false;

// 获取SVG尺寸和设置样式（不计算偏移量，保持原始位置）
const calculateInitialZoom = () => {
  // 如果已在计算中，避免重复计算
  if (isCalculatingZoom) {
    console.log('SVG处理正在进行中，跳过此次调用');
    return;
  }

  // 设置标志，防止重复计算
  isCalculatingZoom = true;

  // 检查是否有SVG内容
  if (!props.svgContent) {
    console.warn('没有SVG内容，无法处理样式');
    isCalculatingZoom = false;
    return;
  }

  console.log('开始处理SVG样式设置（不计算偏移量）...');
  // 确保SVG容器和SVG元素都已加载
  if (!svgContainerRef.value || !svgElement.value) {
    console.warn('SVG容器或元素未加载，无法处理');
    isCalculatingZoom = false;
    return;
  }

  const svgEl = svgElement.value.querySelector('svg');
  if (!svgEl) {
    console.warn('未找到SVG元素，无法处理');
    // 尝试重新设置SVG内容
    if (props.svgContent && svgElement.value) {
      console.log('尝试重新设置SVG内容');
      svgElement.value.innerHTML = sanitizedSvgContent.value;

      // 延迟检查是否成功设置
      setTimeout(() => {
        const newSvgEl = svgElement.value?.querySelector('svg');
        if (newSvgEl) {
          console.log('SVG内容已重新设置，重新调用计算');
          isCalculatingZoom = false;
          calculateInitialZoom();
        } else {
          console.error('重新设置SVG内容失败');
          isCalculatingZoom = false;
        }
      }, 200);
      return;
    }
    isCalculatingZoom = false;
    return;
  }

  // 获取SVG容器的尺寸
  const containerRect = svgContainerRef.value.getBoundingClientRect();
  const containerWidth = containerRect.width;
  const containerHeight = containerRect.height;

  // 检查容器尺寸是否合理
  if (containerWidth < 50 || containerHeight < 50) {
    console.warn('容器尺寸异常小，延迟处理');
    setTimeout(() => {
      isCalculatingZoom = false;
      calculateInitialZoom();
    }, 500);
    return;
  }

  console.log('容器尺寸:', containerWidth, 'x', containerHeight);

  // 获取SVG的尺寸
  let svgWidth = 0;
  let svgHeight = 0;

  // 优先从viewBox获取尺寸
  if (svgEl.viewBox?.baseVal) {
    svgWidth = svgEl.viewBox.baseVal.width;
    svgHeight = svgEl.viewBox.baseVal.height;
    console.log('从viewBox获取尺寸:', svgWidth, 'x', svgHeight);
  }

  // 如果viewBox没有提供有效尺寸，尝试从width/height属性获取
  if (svgWidth <= 10 || svgHeight <= 10) {
    svgWidth = parseInt(svgEl.getAttribute('width') || '0');
    svgHeight = parseInt(svgEl.getAttribute('height') || '0');
    console.log('从width/height属性获取尺寸:', svgWidth, 'x', svgHeight);
  }

  // 特别处理URL格式的SVG
  if (isSvgUrl.value) {
    console.log('处理URL格式SVG的尺寸');

    // 找到image元素并尝试获取图像尺寸
    const imgEl = svgElement.value.querySelector('image.url-svg-image');
    if (imgEl) {
      // 直接检查图像是否已加载
      const imgElem = imgEl as HTMLImageElement;
      if (imgElem && imgElem.naturalWidth > 0 && imgElem.naturalHeight > 0) {
        // 图像已加载，更新尺寸
        console.log('图像已加载，获取宽高:', imgElem.naturalWidth, imgElem.naturalHeight);

        // 如果图像尺寸可用且合理，使用它们
        if (imgElem.naturalWidth > 10 && imgElem.naturalHeight > 10) {
          svgWidth = imgElem.naturalWidth;
          svgHeight = imgElem.naturalHeight;

          // 更新viewBox以匹配图像尺寸
          svgEl.setAttribute('viewBox', `0 0 ${svgWidth} ${svgHeight}`);
        }
      } else {
        // 图像尚未加载，尝试通过Image对象加载
        console.log('图像未加载，尝试预加载获取尺寸');

        const img = new Image();
        img.onload = () => {
          console.log('图像预加载完成，尺寸:', img.width, 'x', img.height);
          if (img.width > 10 && img.height > 10) {
            // 更新SVG的viewBox
            svgEl.setAttribute('viewBox', `0 0 ${img.width} ${img.height}`);

            // 重新调用处理，但确保重置标志
            isCalculatingZoom = false;
            setTimeout(() => calculateInitialZoom(), 200);
          } else {
            console.warn('获取到的图像尺寸异常，使用默认尺寸');
            // 使用更合理的默认尺寸
            svgEl.setAttribute('viewBox', '0 0 800 600');

            // 重置标志，允许重新处理
            isCalculatingZoom = false;
          }
        };

        img.onerror = () => {
          console.error('图像加载失败，使用默认尺寸');
          // 使用默认尺寸
          svgEl.setAttribute('viewBox', '0 0 800 600');

          // 重置标志，允许重新处理
          isCalculatingZoom = false;
        };

        // 获取图像URL
        const imgUrl = imgEl.getAttribute('href');
        if (imgUrl) {
          console.log('开始加载图像:', imgUrl);
          img.src = imgUrl;
        } else {
          console.warn('无法获取图像URL');
          isCalculatingZoom = false;
        }

        // 此时可能无法立即获取尺寸，使用合理默认值
        if (svgWidth <= 10 || svgHeight <= 10) {
          console.log('暂时使用默认尺寸');
          svgWidth = 800;
          svgHeight = 600;
        }

        // 如果正在等待图像加载，返回，避免后续处理
        return;
      }
    }
  }

  // 如果仍然没有获取到有效尺寸，尝试从元素的客户端尺寸获取
  if (svgWidth <= 10 || svgHeight <= 10) {
    // 获取客户端尺寸
    const rect = svgEl.getBoundingClientRect();
    const naturalWidth = svgEl.clientWidth || rect.width;
    const naturalHeight = svgEl.clientHeight || rect.height;

    // 只有在客户端尺寸合理时使用它们
    if (naturalWidth > 100 && naturalHeight > 100) {
      svgWidth = naturalWidth;
      svgHeight = naturalHeight;
      console.log(`使用客户端尺寸: ${svgWidth}x${svgHeight}`);

      // 更新viewBox以匹配实际尺寸
      if (!svgEl.hasAttribute('viewBox')) {
        svgEl.setAttribute('viewBox', `0 0 ${svgWidth} ${svgHeight}`);
      }
    } else {
      // 使用合理的默认值
      svgWidth = 800;
      svgHeight = 600;
      console.log(`使用默认尺寸: ${svgWidth}x${svgHeight}`);

      // 设置默认viewBox
      svgEl.setAttribute('viewBox', `0 0 ${svgWidth} ${svgHeight}`);
    }
  }

  // 确保尺寸在合理范围内
  svgWidth = Math.max(svgWidth, 200);
  svgHeight = Math.max(svgHeight, 200);
  console.log('最终使用的SVG尺寸:', svgWidth, 'x', svgHeight);

  // 始终固定使用100%缩放比例
  const newZoom = 1.0;

  console.log(`使用固定缩放比例: ${newZoom.toFixed(2)}，SVG尺寸: ${svgWidth}x${svgHeight}，容器尺寸: ${containerWidth}x${containerHeight}`);

  // 仅当当前缩放不是100%时才更新
  if (Math.abs(props.zoom - newZoom) > 0.01) {
    // 应用100%缩放比例
    emit('update:zoom', newZoom);
  } else {
    console.log('当前已是100%缩放，不更新缩放比例');
  }

  // 确保SVG居中显示，添加小延迟确保缩放已应用
  setTimeout(() => {
    try {
      // 在100%缩放下居中SVG
      const updatedContainerRect = svgContainerRef.value?.getBoundingClientRect();
      if (updatedContainerRect) {
        centerSvg(newZoom, svgWidth, svgHeight, updatedContainerRect.width, updatedContainerRect.height);
      } else {
        centerSvg(newZoom, svgWidth, svgHeight, containerWidth, containerHeight);
      }
    } catch (error) {
      console.error('居中SVG时出错:', error);
    } finally {
      // 重置处理标志
      isCalculatingZoom = false;
    }
  }, 200);
};

// 修复缩放控件功能
const fixZoomControls = () => {
  console.log('修复缩放控件...');
  // 延迟执行，确保DOM已更新
  setTimeout(() => {
    try {
      // 触发一个微小的缩放变化，确保缩放控制可用
      const currentZoom = props.zoom;
      // 先增加一点缩放值
      emit('update:zoom', currentZoom + 0.01);

      // 然后恢复原始缩放值
      setTimeout(() => {
        emit('update:zoom', currentZoom);
      }, 50);

      // 确保SVG元素样式正确
      const svgEl = svgElement.value?.querySelector('svg');
      if (svgEl) {
        // 确保SVG元素有适当的样式
        svgEl.style.maxWidth = 'none'; // 移除maxWidth限制
        svgEl.style.maxHeight = 'none'; // 移除maxHeight限制
        svgEl.style.width = '100%';
        svgEl.style.height = '100%';
      }
    } catch (error) {
      console.error('修复缩放控件失败:', error);
    }
  }, 200);
};

// 更新适应内容函数

// 监听SVG内容变化，在内容变化后重新计算缩放
watch(() => sanitizedSvgContent.value, (newVal, oldVal) => {
  if (sanitizedSvgContent.value) {
    // 只有当SVG内容真正变化时才重新计算缩放
    if (newVal !== oldVal) {
      console.log('SVG内容变化，准备重新计算缩放');

      // 记录当前缩放比例
      const currentZoom = props.zoom;

      // 延迟执行，确保DOM已更新
      nextTick(() => {
        // 如果缩放比例已经手动设置过（不是默认值1），则不自动更改
        if (Math.abs(currentZoom - 1) > 0.05) {
          console.log('保持当前缩放比例:', currentZoom);
          // 仅调整位置，不改变缩放比例
          setTimeout(() => {
            const svgEl = svgElement.value?.querySelector('svg');
            if (svgEl) {
              const svgWidth = svgEl.viewBox?.baseVal?.width || parseInt(svgEl.getAttribute('width') || '1000');
              const svgHeight = svgEl.viewBox?.baseVal?.height || parseInt(svgEl.getAttribute('height') || '600');
              const containerRect = svgContainerRef.value?.getBoundingClientRect();
              if (containerRect) {
                centerSvg(currentZoom, svgWidth, svgHeight, containerRect.width, containerRect.height);
              }
            }
          }, 500);
        } else {
          // 使用默认缩放值，可以重新计算
          console.log('使用自动计算的缩放比例');
          setTimeout(calculateInitialZoom, 500);
        }
      });
    } else {
      console.log('SVG内容相同，不重新计算缩放');
    }
  }
});

// 已移除重复声明的变量和函数

// 已删除重复的centerSvg函数

// 添加统一的坐标转换辅助函数
const convertClientToSvgCoordinates = (clientX: number, clientY: number, svgEl: SVGSVGElement) => {
  // 获取SVG的位置和尺寸信息
  const rect = svgEl.getBoundingClientRect();
  const viewBox = svgEl.viewBox?.baseVal || { x: 0, y: 0, width: 1000, height: 1000 };

  // 使用改进的坐标计算方法，正确处理缩放和平移
  let svgX, svgY;

  // 获取SVG元素的变换矩阵
  const svgTransform = svgEl.getScreenCTM();

  if (svgTransform && svgTransform.inverse()) {
    // 创建一个SVG点
    const point = svgEl.createSVGPoint();
    point.x = clientX;
    point.y = clientY;

    // 使用逆转换矩阵转换屏幕坐标到SVG坐标
    // 这会自动处理所有变换，包括缩放、平移等
    const svgPoint = point.matrixTransform(svgTransform.inverse());
    svgX = svgPoint.x;
    svgY = svgPoint.y;

    console.log('使用矩阵转换计算坐标:', svgX, svgY);
  } else {
    // 备用方法：手动计算坐标转换
    console.warn('无法获取SVG变换矩阵，使用备用坐标计算方法');

    // 1. 计算点击位置相对于SVG元素左上角的偏移
    const offsetX = clientX - rect.left;
    const offsetY = clientY - rect.top;

    // 2. 考虑SVG的平移
    const adjustedX = offsetX - svgTranslateX.value;
    const adjustedY = offsetY - svgTranslateY.value;

    // 3. 考虑缩放因素
    const scaledX = adjustedX / props.zoom;
    const scaledY = adjustedY / props.zoom;

    // 4. 将物理位置映射到SVG逻辑坐标
    const viewBoxScaleX = viewBox.width / (rect.width / props.zoom);
    const viewBoxScaleY = viewBox.height / (rect.height / props.zoom);

    svgX = viewBox.x + scaledX * viewBoxScaleX;
    svgY = viewBox.y + scaledY * viewBoxScaleY;

    console.log('使用备用方法计算坐标:', svgX, svgY);
  }

  return { x: svgX, y: svgY };
};
</script>

<style lang="scss" scoped>
.flow-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* SVG容器相关样式 */
.svg-container {
  flex: 1;
  overflow: auto;
  /* 改为auto，当内容超出容器时显示滚动条 */
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  transform-origin: center center;
  /* 确保以中心点为缩放原点 */

  /* 添加元素时显示十字架光标 */
  &.adding-element {
    cursor: crosshair;
  }
}

.svg-outer-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
  /* 改为visible，允许内容超出容器边界 */
  position: relative;
  cursor: default;
  /* 改为默认光标，因为拖拽功能已禁用 */
  transform-origin: center center;
  /* 确保以中心点为缩放原点 */

  &:active {
    cursor: default;
    /* 拖拽功能已禁用 */
  }
}

.svg-wrapper {
  /* 使用inline-block或flex可以根据内容调整大小 */
  display: flex;
  justify-content: center;
  align-items: center;
  transform-origin: center center;
  /* 确保以中心点为缩放原点 */
  /* 确保平移操作不受overflow限制 */
  position: relative;
  transition: transform 0.1s ease;
  /* 添加平滑过渡效果 */
  will-change: transform;
  /* 提示浏览器该元素会频繁变化 */
}

.svg-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transform-origin: center center;
  /* 确保以中心点为缩放原点 */
}

:deep(svg) {
  max-width: 100%;
  max-height: 100%;
  display: block;
  transform-origin: center center;
  /* 确保以中心点为缩放原点 */
}

/* 文本元素样式 */

/* 确保所有监测点位元素显示手型指针 */
:deep(#monitoring-points [data-point="true"]),
:deep(#monitoring-points circle),
:deep(#monitoring-points rect),
:deep(#monitoring-points polygon) {
  cursor: pointer !important;
  pointer-events: all !important;
  /* 确保元素可以接收鼠标事件 */
}

/* 确保所有文本元素显示正确的颜色和手型指针 */
:deep(#monitoring-points text) {
  cursor: pointer !important;
  pointer-events: all !important;
  /* 确保文本元素可以接收鼠标事件 */
  /* 确保文本元素字体设置生效 */
  font-weight: attr(font-weight) !important;
  fill: attr(fill) !important;
  /* 使用更强的选择器和!important确保颜色生效 */
  color: inherit !important;
}

/* 添加特定的文本元素样式规则 */
:deep(#monitoring-points text[data-type="text"]) {
  fill: var(--text-color, attr(fill)) !important;
  color: var(--text-color, attr(fill)) !important;
  stroke: none !important;
  paint-order: stroke;
  stroke-width: 0;
}

/* 确保文本居中显示 */
:deep(text[id^="text-"]) {
  text-anchor: middle !important;
  dominant-baseline: central !important;
  alignment-baseline: middle !important;
}

.toolbar {
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-bottom: 2px solid #dcdfe6;
  border-radius: 4px 4px 0 0;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 1px;
  z-index: 10;

  .zoom-controls {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 4px;
    padding: 3px 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid #ebeef5;
  }

  .tools-info {
    display: flex;
    align-items: center;
    gap: 15px;

    span {
      font-size: 14px;
      color: #666;
    }

    .zoom-tip {
      background-color: #f0f9eb;
      color: #67c23a;
      padding: 2px 8px;
      border-radius: 4px;
      border: 1px solid #e1f3d8;
      font-size: 12px;
    }

    .operation-tip {
      background-color: #ecf5ff;
      color: #409EFF;
      padding: 2px 8px;
      border-radius: 4px;
      border: 1px solid #d9ecff;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .points-count {
      background-color: #f2f6fc;
      color: #606266;
      padding: 2px 8px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .action-btn {
      font-size: 12px;
      padding: 6px 10px;
    }

    .adding-element-tip {
      display: flex;
      align-items: center;
      gap: 10px;
      background-color: #ecf5ff;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 14px;
      color: #409EFF;

      button {
        padding: 2px 6px;
        font-size: 12px;
      }
    }
  }
}

.point-type-dropdown {
  min-width: 160px;
  padding: 0;

  .dropdown-section {
    padding: 5px 0;

    &:not(:last-child) {
      border-bottom: 1px solid #ebeef5;
    }

    .section-title {
      padding: 8px 16px;
      font-size: 14px;
      color: #909399;
      background-color: #f5f7fa;
    }

    .point-type-item {
      display: flex;
      align-items: center;
      padding: 0 5px;

      .point-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 10px;
      }

      .circle-point {
        background-color: #F56C6C;
        border-radius: 50%;
      }

      .square-point {
        background-color: #E6A23C;
      }

      .triangle-point {
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 16px solid #409EFF;
      }

      i {
        margin-right: 10px;
        font-size: 16px;
      }
    }
  }
}

.drawer-content {
  padding: 15px;
  height: 100%;
  overflow-y: auto;

  .el-form {
    .custom-form-item {
      margin-bottom: 15px;

      .el-form-item__label {
        padding-bottom: 4px;
        font-weight: normal;
        font-size: 14px;
        color: #606266;
        line-height: 1.2;
      }

      .el-form-item__content {
        margin-left: 0 !important;
      }
    }

    .full-width-select {
      width: 100%;
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #EBEEF5;
  }

  .form-item-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
    line-height: 1.2;
  }

  .coordinates {
    font-size: 14px;
    color: #606266;
    padding: 2px 0;
  }
}

.form-label-with-star {
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1.2;
  color: #606266;
  display: flex;
  align-items: center;
}

.required-star {
  color: #F56C6C;
  margin-right: 4px;
  font-size: 14px;
}

.size-slider-container {
  display: flex;
  flex-direction: column;
  gap: 6px;

  .size-slider {
    margin-top: 5px;
  }
}

.size-input-group {
  display: flex;
  align-items: center;
  gap: 5px;

  .size-btn {
    min-width: 34px;
    height: 34px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .size-input {
    width: 100px;

    :deep(.el-input-number__decrease),
    :deep(.el-input-number__increase) {
      display: none;
    }
  }
}

.zoom-level {
  display: inline-block;
  min-width: 50px;
  text-align: center;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  margin: 0 4px;
  padding: 0 6px;
}

.reset-btn,
.fit-btn {
  margin-left: 5px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
  z-index: 10;

  .custom-btn {
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.3s;
    border: none;
    outline: none;

    &.text-btn {
      background: transparent;
      color: #409EFF;

      &:hover {
        background-color: rgba(64, 158, 255, 0.1);
      }
    }

    &.primary-btn {
      background-color: #409EFF;
      color: white;

      &:hover {
        background-color: #66b1ff;
      }
    }
  }
}

.add-element-controls {
  display: flex;
  gap: 10px;
}

.point-shape {
  &.shape-circle {
    border-radius: 50%;
  }

  &.shape-square {
    border-radius: 0;
  }

  &.shape-triangle {
    width: 0 !important;
    height: 0 !important;
  }
}

.text-element {
  white-space: nowrap;
  pointer-events: none;
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.8); // 增加文本阴影提高可见度
  padding: 2px 4px;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.2); // 半透明背景提高对比度
}

.point-element {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: move;
  z-index: 10;
  pointer-events: auto;
  transform-origin: center center;
  // 增加点位可见度
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.7));

  &.is-dragging {
    z-index: 20;
    opacity: 0.8;
  }

  &.is-selected {
    z-index: 15;
    outline: 2px dashed #409EFF;
    outline-offset: 2px;
  }

  &.temporary-point {
    opacity: 0.7;
    border: 1px dashed #fff;
    padding: 3px;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(255, 255, 255, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.custom-dropdown {
  min-width: 150px;
  padding: 0;

  :deep(.el-dropdown-menu__item) {
    line-height: 36px;
    padding: 0 15px;
    font-size: 14px;
  }
}

.dropdown-section-title {
  padding: 5px 15px;
  font-size: 14px;
  color: #606266;
  background: #f5f7fa;
  line-height: 24px;
  border-bottom: 1px solid #ebeef5;
  border-top: 1px solid #ebeef5;
  margin-top: -1px;
}

.dropdown-item-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.point-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.circle-point {
  border-radius: 50%;
  background-color: #F56C6C;
}

.square-point {
  border-radius: 0;
  background-color: #E6A23C;
}

.triangle-point {
  width: 0 !important;
  height: 0 !important;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 12px solid #409EFF;
}

.text-icon {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1024 1024'%3E%3Cpath fill='%23606266' d='M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V136h560v752zM456 698.8l85.5-204.4h32.3l86.4 204.4H626l-19.5-58.4h-98.9l-18.5 58.4h-33.1zm42.3-86.2h79.8l-40.3-118.1-39.5 118.1z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.temporary-point-preview {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.7);
  border: 3px dashed #FF4500;
  border-radius: 4px;
  padding: 8px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  box-shadow: 0 0 15px rgba(255, 69, 0, 0.8);
  animation: pulse 1.5s infinite;
  min-width: 40px;
  min-height: 40px;
}

.preview-shape {
  width: 30px;
  height: 30px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  position: relative;
}

.preview-circle {
  background-color: #F56C6C;
  border-radius: 50%;
  border: 2px solid white;
}

.preview-square {
  background-color: #E6A23C;
  border: 2px solid white;
}

.preview-triangle {
  width: 0 !important;
  height: 0 !important;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 30px solid #409EFF;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.5);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

:deep(svg text) {
  text-anchor: middle !important;
  dominant-baseline: central !important;
  alignment-baseline: middle !important;
  font-family: Arial, sans-serif !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow: visible !important;
}

/* 添加背景矩形样式 */
.text-background-rect {
  fill: transparent !important;
  stroke: #333333 !important;
  stroke-width: 1px !important;
  opacity: 1 !important;
  visibility: visible !important;
  rx: 4px !important;
  ry: 4px !important;
}

:global(svg) {

  /* 确保SVG内的所有元素都可见 */
  rect,
  circle,
  path,
  polygon,
  text {
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* 确保背景矩形始终可见 */
  rect[id^="bg-text-"] {
    fill: transparent !important;
    stroke: #333333 !important;
    stroke-width: 1px !important;
    visibility: visible !important;
    rx: 4px !important;
    ry: 4px !important;
  }
}
</style>