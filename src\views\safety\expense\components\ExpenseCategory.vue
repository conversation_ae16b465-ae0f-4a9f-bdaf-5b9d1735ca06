<template>
  <div class="expense-category">
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">新增分类</el-button>
    </div>
    <el-table :data="categoryList" border style="width: 100%">
      <el-table-column prop="code" label="分类编码" width="120" />
      <el-table-column prop="name" label="分类名称" width="180" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <category-dialog
      v-model="dialogVisible"
      :type="dialogType"
      :form-data="currentRow"
      @success="handleSuccess"
    />
  </div>
</template>

<script>
import CategoryDialog from '../dialogs/CategoryDialog.vue'

export default {
  name: 'ExpenseCategory',
  components: {
    CategoryDialog
  },
  data() {
    return {
      categoryList: [
        { id: 1, code: 'SC001', name: '安全设备', description: '包括各类安全防护设备及维护', status: 1 },
        { id: 2, code: 'SC002', name: '安全培训', description: '员工安全培训及相关课程费用', status: 1 },
        { id: 3, code: 'SC003', name: '安全检查', description: '定期安全检查及审核费用', status: 1 },
        { id: 4, code: 'SC004', name: '应急演练', description: '安全应急演练相关费用', status: 1 },
        { id: 5, code: 'SC005', name: '咨询服务', description: '安全咨询及评估服务', status: 0 }
      ],
      dialogVisible: false,
      dialogType: 'add',
      currentRow: null
    }
  },
  methods: {
    handleAdd() {
      this.dialogType = 'add'
      this.currentRow = null
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentRow = row
      this.dialogVisible = true
    },
    handleDelete(row) {
      // 实现删除逻辑
    },
    handleSuccess() {
      this.dialogVisible = false
      // 重新加载列表数据
    }
  }
}
</script>

<style lang="scss" scoped>
.expense-category {
  .operation-bar {
    margin-bottom: 20px;
  }
}
</style> 