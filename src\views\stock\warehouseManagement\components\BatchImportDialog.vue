<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="50rem"
    :before-close="handleClose"
  >
    <div class="import-container">
      <!-- 导入说明 -->
      <el-alert
        :title="importConfig.title"
        type="info"
        :closable="false"
        show-icon
        class="mb-1rem"
      >
        <template #default>
          <div class="text-0.875rem">
            <p v-for="(desc, index) in importConfig.descriptions" :key="index">
              {{ index + 1 }}. {{ desc }}
            </p>
          </div>
        </template>
      </el-alert>
      
      <!-- 文件上传区域 -->
      <div class="upload-area mb-1.5rem">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :action="uploadAction"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :file-list="fileList"
          :limit="1"
          :auto-upload="false"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip text-0.75rem text-gray-500">
              只能上传 xlsx/xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons mb-1.5rem">
        <el-button @click="handleDownloadTemplate">
          <el-icon><Download /></el-icon>
          下载模板
        </el-button>
        <el-button 
          type="primary" 
          @click="handleStartImport"
          :loading="importing"
          :disabled="fileList.length === 0"
        >
          <el-icon><Upload /></el-icon>
          开始导入
        </el-button>
      </div>
      
      <!-- 导入进度 -->
      <div v-if="showProgress" class="progress-area mb-1.5rem">
        <div class="progress-title mb-0.5rem">导入进度</div>
        <el-progress 
          :percentage="importProgress" 
          :status="progressStatus"
          :stroke-width="8"
        />
        <div class="progress-info mt-0.5rem text-0.875rem text-gray-600">
          {{ progressText }}
        </div>
      </div>
      
      <!-- 导入结果 -->
      <div v-if="importResult" class="result-area">
        <div class="result-title mb-0.5rem">导入结果</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="总记录数">
            {{ importResult.totalCount }}
          </el-descriptions-item>
          <el-descriptions-item label="成功数">
            <span class="text-green-600">{{ importResult.successCount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="失败数">
            <span class="text-red-600">{{ importResult.failCount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="导入时间">
            {{ importResult.importTime }}
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 错误详情 -->
        <div v-if="importResult.failCount > 0" class="error-details mt-1rem">
          <div class="error-title mb-0.5rem">错误详情</div>
          <el-table 
            :data="importResult.errors" 
            border 
            size="small"
            max-height="12.5rem"
          >
            <el-table-column prop="row" label="行号" width="4rem" align="center" />
            <el-table-column prop="field" label="字段" width="6rem" align="center" />
            <el-table-column prop="value" label="值" width="8rem" align="center" />
            <el-table-column prop="error" label="错误信息" align="center" />
          </el-table>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ importResult ? '关闭' : '取消' }}
        </el-button>
        <el-button 
          v-if="importResult && importResult.failCount > 0"
          type="warning"
          @click="handleDownloadErrorReport"
        >
          <el-icon><Download /></el-icon>
          下载错误报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type UploadInstance, type UploadProps, type UploadUserFile } from 'element-plus'
import { UploadFilled, Download, Upload } from '@element-plus/icons-vue'

// 定义接口
interface ImportConfig {
  title: string
  descriptions: string[]
  templateUrl: string
  columns: string[]
}

interface ImportResult {
  totalCount: number
  successCount: number
  failCount: number
  importTime: string
  errors: Array<{
    row: number
    field: string
    value: string
    error: string
  }>
}

// 定义props
interface Props {
  visible: boolean
  importType: 'initialStock' | 'stockAlert'
}

// 定义emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  importType: 'initialStock'
})

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadUserFile[]>([])
const importing = ref(false)
const showProgress = ref(false)
const importProgress = ref(0)
const progressStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const progressText = ref('')
const importResult = ref<ImportResult | null>(null)

// 导入配置
const importConfigs: Record<string, ImportConfig> = {
  initialStock: {
    title: '期初库存导入说明',
    descriptions: [
      '支持Excel文件批量导入商品的初始数量与金额',
      '导入前请确保商品信息已在系统中存在',
      '导入文件格式：商品编码、商品名称、仓库编码、初始数量、单价、总金额',
      '请严格按照模板格式填写数据，避免导入失败'
    ],
    templateUrl: '/templates/initial-stock-template.xlsx',
    columns: ['商品编码', '商品名称', '仓库编码', '初始数量', '单价', '总金额']
  },
  stockAlert: {
    title: '库存预警配置导入说明',
    descriptions: [
      '支持Excel文件批量导入库存预警配置',
      '可以批量设置商品的库存上下限',
      '导入文件格式：商品编码、商品名称、仓库编码、库存下限、库存上限、单位',
      '预警状态默认为启用，可在导入后手动调整'
    ],
    templateUrl: '/templates/stock-alert-template.xlsx',
    columns: ['商品编码', '商品名称', '仓库编码', '库存下限', '库存上限', '单位']
  }
}

// 计算属性
const dialogTitle = computed(() => {
  return props.importType === 'initialStock' ? '期初库存导入' : '库存预警配置导入'
})

const importConfig = computed(() => {
  return importConfigs[props.importType]
})

const uploadAction = computed(() => {
  return `/api/import/${props.importType}`
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetDialog()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置对话框状态
const resetDialog = () => {
  fileList.value = []
  importing.value = false
  showProgress.value = false
  importProgress.value = 0
  progressStatus.value = ''
  progressText.value = ''
  importResult.value = null
}

// 上传前检查
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 开始导入
const handleStartImport = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  importing.value = true
  showProgress.value = true
  importProgress.value = 0
  progressStatus.value = ''
  progressText.value = '正在解析文件...'

  try {
    // 模拟导入过程
    await simulateImport()
    
    progressStatus.value = 'success'
    progressText.value = '导入完成'
    ElMessage.success('导入完成')
    
    // 触发成功事件
    emit('success')
  } catch (error) {
    progressStatus.value = 'exception'
    progressText.value = '导入失败'
    ElMessage.error('导入失败，请检查文件格式')
  } finally {
    importing.value = false
  }
}

// 模拟导入过程
const simulateImport = async () => {
  const steps = [
    { progress: 20, text: '正在验证文件格式...' },
    { progress: 40, text: '正在解析数据...' },
    { progress: 60, text: '正在验证数据...' },
    { progress: 80, text: '正在保存数据...' },
    { progress: 100, text: '导入完成' }
  ]

  for (const step of steps) {
    await new Promise(resolve => setTimeout(resolve, 800))
    importProgress.value = step.progress
    progressText.value = step.text
  }

  // 模拟导入结果
  const mockResult: ImportResult = {
    totalCount: 100,
    successCount: 95,
    failCount: 5,
    importTime: new Date().toLocaleString(),
    errors: [
      { row: 15, field: '商品编码', value: 'G999', error: '商品不存在' },
      { row: 23, field: '数量', value: '-5', error: '数量不能为负数' },
      { row: 34, field: '仓库编码', value: 'WH999', error: '仓库不存在' },
      { row: 67, field: '单价', value: 'abc', error: '单价格式错误' },
      { row: 89, field: '商品名称', value: '', error: '商品名称不能为空' }
    ]
  }

  importResult.value = mockResult
}

// 上传成功
const handleUploadSuccess = () => {
  ElMessage.success('文件上传成功')
}

// 上传失败
const handleUploadError = () => {
  ElMessage.error('文件上传失败')
}

// 下载模板
const handleDownloadTemplate = () => {
  const link = document.createElement('a')
  link.href = importConfig.value.templateUrl
  link.download = `${dialogTitle.value}模板.xlsx`
  link.click()
  ElMessage.success('模板下载中...')
}

// 下载错误报告
const handleDownloadErrorReport = () => {
  if (!importResult.value) return
  
  // 这里应该生成并下载错误报告
  ElMessage.success('错误报告下载中...')
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped lang="scss">
.import-container {
  .upload-area {
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 10rem;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 0.5rem;
  }
  
  .progress-title,
  .result-title,
  .error-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: #303133;
  }
  
  .error-details {
    border: 1px solid #e4e7ed;
    border-radius: 0.25rem;
    padding: 0.75rem;
    background-color: #fafafa;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}
</style>
