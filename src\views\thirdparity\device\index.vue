<template>
  <ContentWrap :bodyStyle="{ padding: '0px' }" class="!mb-0">
    <div class="iframe-container" ref="loadingContainer">
      <iframe v-show="!loading" :src="url" :key="iframeKey" ref="iframeRef" @load="handleIframeLoaded"
        @error="handleIframeError" style="width: 100%; height: 100%; border: none;"></iframe>
      <!-- 加载失败提示（仅在3次重试后显示） -->
      <div v-if="loadFailed" class="error-container">
        <el-empty :image-size="200" description="页面加载失败，已尝试重连3次，可能是网络问题或无法访问该资源">
          <el-button type="primary" @click="retryLoading">重试</el-button>
        </el-empty>
      </div>
    </div>
  </ContentWrap>
</template>
<script lang="ts" setup>
import * as ConfigApi from '@/api/infra/config'
import { ElLoading, ElMessage } from 'element-plus'
import type { LoadingInstance } from 'element-plus/es/components/loading/src/loading'
import { useAppStore } from '@/store/modules/app' // 导入应用存储，获取全局水厂数据
import { useTagsView } from '@/hooks/web/useTagsView' // 导入tags view hook

const loading = ref(true) // 是否加载中
const loadFailed = ref(false) // 是否加载失败
const url = ref('')
const iframeKey = ref(0) // iframe的key，用于强制重新渲染
const { currentRoute } = useRouter()
const iframeRef = ref(null) // iframe的引用
const loadingContainer = ref<HTMLElement | null>(null) // 加载容器引用
const iframeLoaded = ref(false) // iframe是否加载成功
let loadingInstance: LoadingInstance | null = null // ElLoading实例
// 获取应用存储
const appStore = useAppStore()
// 获取tab相关功能
const { refreshPage } = useTagsView()

// 在script区域顶部添加storage key常量
const STORAGE_KEY_REFRESH_COUNT = 'iframe_refresh_count'
// 添加刷新锁变量，防止重复刷新
const isRefreshing = ref(false)

// 当前选中的水厂
const selectedWaterPlant = computed(() => appStore.getCurrentStation)

// 刷新计数相关常量
const MAX_PAGE_REFRESH = 3 // 最大页面刷新次数
const refreshCount = ref(0) // 刷新次数计数器

/** 移除父级section的padding */
const removeSectionPadding = () => {
  nextTick(() => {
    // 查找父级section元素
    const parentSection = document.querySelector('section.p-\\[var\\(--app-content-padding\\)\\]')
    if (parentSection) {
      // 移除padding类
      parentSection.classList.remove('p-[var(--app-content-padding)]')
      // 添加无padding类
      parentSection.classList.add('!p-0')
      console.log('已移除section的padding')
    }
  })
}

/** 获取当前刷新次数 */
const getRefreshCount = (): number => {
  // 优先从本地存储获取刷新次数
  const storedCount = localStorage.getItem(STORAGE_KEY_REFRESH_COUNT)
  if (storedCount) {
    const parsedCount = parseInt(storedCount, 10)
    refreshCount.value = parsedCount // 同步内存中的值
    console.log(`从localStorage获取刷新次数: ${parsedCount}`)
    return parsedCount
  }
  console.log(`无localStorage记录，使用内存中的刷新次数: ${refreshCount.value}`)
  return refreshCount.value
}

/** 增加刷新次数 */
const increaseRefreshCount = (): number => {
  const oldCount = refreshCount.value
  refreshCount.value++
  const newCount = refreshCount.value

  console.log(`增加刷新次数: ${oldCount} -> ${newCount}`)

  // 保存到本地存储
  localStorage.setItem(STORAGE_KEY_REFRESH_COUNT, String(newCount))
  console.log(`已将新的刷新次数 ${newCount} 保存到localStorage`)

  return newCount
}

/** 重置刷新次数 */
const resetRefreshCount = (): void => {
  const oldCount = refreshCount.value
  refreshCount.value = 0
  console.log(`重置刷新次数: ${oldCount} -> 0`)

  // 从本地存储中清除
  localStorage.removeItem(STORAGE_KEY_REFRESH_COUNT)
  console.log('已从localStorage中清除刷新次数')
}

/** 显示加载动画 */
const showLoading = () => {
  // 确保不会创建多个loading实例
  if (loadingInstance) {
    loadingInstance.close()
  }

  // 重置失败状态
  loadFailed.value = false

  // 创建新的loading实例
  nextTick(() => {
    if (loadingContainer.value) {
      loadingInstance = ElLoading.service({
        target: loadingContainer.value,
        fullscreen: false,
        text: '加载中...'
      })
    }
    loading.value = true
  })
}

/** 隐藏加载动画 */
const hideLoading = () => {
  if (loadingInstance) {
    loadingInstance.close()
    loadingInstance = null
  }

  loading.value = false
}

/** 刷新当前页签 */
const refreshCurrentTab = () => {
  // 防止正在刷新时重复触发
  if (isRefreshing.value) {
    console.log('刷新操作进行中，忽略重复调用')
    return
  }

  // 设置刷新锁
  isRefreshing.value = true
  console.log('已设置刷新锁，防止重复刷新')

  try {
    // 检查是否已经达到最大刷新次数 - 先从localStorage获取最新值
    const currentRefreshCount = getRefreshCount()
    console.log(`检查刷新次数: ${currentRefreshCount}/${MAX_PAGE_REFRESH}`)

    if (currentRefreshCount >= MAX_PAGE_REFRESH) {
      console.log(`❌ 已达到最大页签刷新次数(${MAX_PAGE_REFRESH}次)，停止刷新，显示失败状态`)
      // 达到最大刷新次数，显示失败状态
      loadFailed.value = true
      hideLoading()
      return
    }

    // 增加刷新计数
    const newCount = increaseRefreshCount()
    console.log(`✅ 执行页签刷新 (${newCount}/${MAX_PAGE_REFRESH})`)

    // 执行页签刷新
    refreshPage()
  } finally {
    // 延时释放刷新锁，防止短时间内连续触发
    setTimeout(() => {
      isRefreshing.value = false
      console.log('刷新锁已释放')
    }, 500)
  }
}

/** 处理iframe加载成功事件 */
const handleIframeLoaded = (event) => {
  console.log('IFrame加载成功', url.value, event)

  // 如果有跨域限制，刷新当前页签
  if (event && event.crossOrigin) {
    console.warn('IFrame存在跨域限制，刷新当前页签')
    // 刷新当前页签
    refreshCurrentTab()
    return // 不执行后续成功处理
  }

  let iframeAccessible = false;
  try {
    // 正确类型转换
    const iframe = iframeRef.value as HTMLIFrameElement | null
    if (iframe?.contentWindow) {
      const href = iframe.contentWindow.location.href
      console.log('可以访问iframe内容，href:', href)
      iframeAccessible = true;

      // 设置iframe已加载标志
      iframeLoaded.value = true
      loadFailed.value = false

      // 立即尝试注入样式
      fixIframeElTableWidth()

      // 注册表格变化观察器
      observeIframeTableChanges()

      // 等待一点时间再做 DOM 操作，避免 JS 执行没结束
      setTimeout(() => {
        updateIframeSelection()

        // 页面完全加载成功，重置刷新计数（延迟重置，确保页面已完全可用）
        console.log('iframe完全加载成功，重置刷新计数')
        resetRefreshCount()
      }, 1000) // 增加等待时间到1秒
    }
  } catch (e) {
    console.log('无法访问iframe location，可能是跨域限制:', e)

    // 避免重复调用刷新
    if (!isRefreshing.value) {
      console.log('iframe内容无法访问，执行刷新')
      refreshCurrentTab()
    } else {
      console.log('刷新操作已在进行中，跳过重复刷新')
    }
  }

  // 注意：不再在这里关闭loading状态
  // 而是在样式注入成功后关闭loading
}

/** 处理iframe加载错误事件 */
const handleIframeError = (error) => {
  console.error('IFrame加载错误', error)
  iframeLoaded.value = false

  // 避免重复调用刷新
  if (!isRefreshing.value) {
    // 尝试刷新当前页签
    refreshCurrentTab()
  } else {
    console.log('刷新操作已在进行中，跳过重复刷新')
  }
}

/** 重新尝试加载（用户手动点击重试） */
const retryLoading = () => {
  // 重置刷新计数
  resetRefreshCount()
  // 重置加载状态
  loadFailed.value = false
  // 刷新当前页签
  refreshCurrentTab()
}
/** 更新iframe选择框 */
const updateIframeSelection = () => {
  console.log('执行选择框更新updateIframeSelection', iframeRef.value, selectedWaterPlant.value)
  if (!iframeRef.value || !selectedWaterPlant.value) {
    console.log('iframe引用或水厂信息不存在，无法更新选择框')
    return
  }

  // 确保iframe已经加载完成
  if (!iframeLoaded.value) {
    console.log('iframe尚未加载完成，无法更新选择框')
    return
  }

  // 延迟执行，确保iframe有足够时间加载
  setTimeout(() => {
    // 安全地尝试访问iframe内容
    try {
      const iframe = iframeRef.value as HTMLIFrameElement | null
      if (!iframe) {
        console.log('iframe引用已失效')
        return
      }

      // 检查iframe是否已完成加载
      if (iframe.contentDocument === null && iframe.contentWindow === null) {
        console.log('iframe尚未完成加载，contentDocument和contentWindow均为null')
        return
      }

      // 尝试安全地获取iframe内容，这会立即触发跨域错误而不会导致无限加载
      let canAccess = false
      try {
        // 检查iframe是否已经加载
        if (iframe.contentWindow === null) {
          console.log('iframe.contentWindow为null，iframe可能未完成加载')
          return
        }

        // 尝试访问contentWindow属性
        const iframeWindow = iframe.contentWindow
        console.log('成功获取iframe.contentWindow', iframeWindow)

        // 尝试访问location.href属性，这会触发跨域检查
        try {
          const href = iframeWindow.location.href
          canAccess = true
          console.log('可以访问iframe内容，href:', href)
        } catch (e) {
          console.log('无法访问iframe location，可能是跨域限制:', e)
          canAccess = false
        }
      } catch (e) {
        console.log('访问iframe.contentWindow时出错:', e)
        canAccess = false
      }

      // 如果不能访问iframe内容，直接返回，避免后续操作
      if (!canAccess) {
        console.log('因跨域或其他原因，无法操作iframe内容')
        return
      }

      // 使用另一个延时确保iframe内容完全加载
      setTimeout(() => {
        try {
          const iframeWindow = iframe.contentWindow
          if (!iframeWindow) {
            console.log('无法获取iframe窗口对象')
            return
          }

          try {
            // 尝试访问document属性，这也可能触发跨域检查
            const iframeDoc = iframeWindow.document
            if (!iframeDoc) {
              console.log('无法获取iframe文档 - 延迟后仍未成功')
              return
            }

            // 修复控制台log错误
            console.log('成功获取iframe文档', '当前选中水厂:', selectedWaterPlant.value)

            // 查找所有 leftSpan（树节点显示文本）
            try {
              const targetText = selectedWaterPlant.value.name // 使用当前选中水厂的名称
              console.log(`尝试在iframe中查找并点击水厂节点: "${targetText}"`)

              // 查找所有 leftSpan（树节点显示文本）
              const candidates = Array.from(iframeDoc.querySelectorAll('span.leftSpan'))
              console.log(`找到 ${candidates.length} 个可能的节点`)

              // 水厂名称映射表
              const plantNameMapping: Record<string, string> = {
                '经开净水厂一二期': '经开区净水厂',
                '经开净水厂三期': '经开区净水厂',
                '经开净水厂四期': '经开区净水厂',
                '长临河镇净水站': '长临河净水站',
                '元疃镇净水站': '元疃净水站',
                '梁园镇净水站': '梁园净水站',
                '石塘镇净水站': '石塘净水站',
                '肥东净水厂四期': '肥东四期净水厂'
              }

              const match = candidates.find(el => {
                const trimmedContent = el.textContent?.trim() || ''
                // 如果在映射表中存在对应关系，使用映射后的名称比较，否则直接比较
                const mappedName = plantNameMapping[targetText] || targetText
                return trimmedContent === mappedName
              })

              if (match) {
                (match as HTMLElement).click()
                console.log(`✅ 已成功点击节点: "${targetText}"`)
                  // 高亮它（临时视觉确认）
                  ; (match as HTMLElement).style.backgroundColor = '#ffd54f'
                setTimeout(() => {
                  ; (match as HTMLElement).style.backgroundColor = '' // 1秒后恢复原样
                }, 1000)
              } else {
                console.warn(`❌ 没找到节点 "${targetText}"`)
                console.log('👇 当前所有节点有：')
                candidates.forEach(el => console.log('-', el.textContent?.trim()))
                //弹出当前运维管理系统中不存在该水厂的提示
                ElMessage.warning(`当前运维管理系统中不存在该水厂: "${targetText}"`)
              }
              // 强制重排
              fixIframeElTableWidth()
              observeIframeTableChanges()
              // setTimeout(() => {

              //   // 多次触发 fix，防止 doLayout 重置
              //   const retries = [1000, 2000, 3000]
              //   retries.forEach(delay => {
              //     setTimeout(() => {
              //     }, delay)
              //   })
              // }, 1000)


            } catch (error) {
              console.error('操作iframe DOM失败:', error)
            }
          } catch (error) {
            console.error('获取iframe.document失败 - 可能是跨域限制:', error)
          }
        } catch (error) {
          console.error('获取iframe文档失败:', error)
        }
      }, 800) // 额外等待800ms确保文档完全加载
    } catch (error) {
      console.error('更新iframe选择框失败:', error)
    }
  }, 1000) // 初始等待1000ms
}


const fixIframeElTableWidth = () => {
  // 修复类型转换
  const iframe = iframeRef.value as HTMLIFrameElement | null
  if (!iframe?.contentWindow?.document) {
    // 如果获取不到document，也需要关闭loading状态
    hideLoading()
    return
  }

  try {
    const doc = iframe.contentWindow.document

    // 注入样式（避免重复）
    if (!doc.getElementById('force-el-table-style')) {
      const style = doc.createElement('style')
      style.id = 'force-el-table-style'
      style.innerHTML = `
        .el-table {
          width: 100% !important;
        }

        .el-table table {
          table-layout: fixed !important;
          width: 100% !important;
        }

        .el-table__header-wrapper {
          overflow-x: hidden !important;
        }

        .el-table__body-wrapper {
          overflow-x: auto !important;
        }

        .el-table__body-wrapper::-webkit-scrollbar {
          height: 8px;
        }

        .check-point .content-wrap {
          height: auto !important;
          max-height: none !important;
        }
      `
      doc.head.appendChild(style)
    }

    // 锁定 col 宽度
    const colEls = doc.querySelectorAll('.el-table colgroup col')
    colEls.forEach((col: HTMLElement) => {
      const w = col.getAttribute('width') || '120'
      col.style.width = w + 'px'
      col.style.minWidth = w + 'px'
      col.style.maxWidth = w + 'px'
    })

    // 确保 wrapper 宽度受限，滚动出现在 body-wrapper 内部
    const tables = doc.querySelectorAll('.el-table')
    tables.forEach(el => {
      const parent = (el as HTMLElement).parentElement
      if (parent) {
        parent.style.overflow = 'hidden' // 不让外部再滚动
        parent.style.width = '100%'
        parent.style.boxSizing = 'border-box'
      }
    })

    console.log('✅ 表头表体滚动条已统一')

    // 样式注入成功后关闭loading状态
    hideLoading()
  } catch (e) {
    console.error('❌ 修复 el-table 滚动样式失败:', e)
    // 即使失败也关闭loading
    hideLoading()
  }
}


const observeIframeTableChanges = () => {
  // 修复类型转换
  const iframe = iframeRef.value as HTMLIFrameElement | null
  if (!iframe?.contentWindow?.document) return

  const doc = iframe.contentWindow.document
  const tableRoot = doc.body

  const observer = new MutationObserver(() => {
    fixIframeElTableWidth()
  })

  observer.observe(tableRoot, {
    childList: true,
    subtree: true
  })

  console.log('✅ 启用 iframe 表格 DOM 监听器')
}




/** 初始化IFrame */
const initIframe = async () => {
  console.log('初始化IFrame')

  // 确保初始化时显示loading状态
  showLoading()

  try {
    const componentName = String(currentRoute.value.name)
    const configKey = `${componentName}`
    const data = await ConfigApi.getConfigKey(configKey)
    if (data && data.length > 0) {
      url.value = data
      // 更新key，强制iframe重新渲染
      iframeKey.value = Date.now()
    }
  } catch (error) {
    console.error('初始化IFrame失败:', error)
  }
}

/** 初始化 */
onMounted(async () => {
  console.log('页面组件挂载，初始化...')
  console.log('currentRoute', currentRoute.value)

  // 从本地存储加载刷新计数
  const storedCount = localStorage.getItem(STORAGE_KEY_REFRESH_COUNT)
  if (storedCount) {
    const count = parseInt(storedCount, 10)
    refreshCount.value = count
    console.log(`从本地存储恢复刷新计数: ${count}，检查是否已超过最大刷新次数`)

    // 在初始化时就检查是否已超过最大刷新次数
    if (count >= MAX_PAGE_REFRESH) {
      console.log(`已达到最大刷新次数(${MAX_PAGE_REFRESH}次)，显示失败状态`)
      loadFailed.value = true
      // 延迟执行，确保DOM已更新
      nextTick(() => {
        hideLoading()
      })
    }
  } else {
    console.log('本地存储中没有刷新计数记录，初始化为0')
    refreshCount.value = 0
  }

  // 移除父级section的padding
  removeSectionPadding()

  // 如果未达到最大刷新次数，初始化IFrame
  if (!loadFailed.value) {
    // 初始化IFrame
    initIframe()
  }
})

// 监听url变化，强制iframe重新加载
watch(() => url.value, (newUrl) => {
  console.log('newUrl', newUrl)
  if (newUrl) {
    iframeLoaded.value = false
    loadFailed.value = false
    iframeKey.value = Date.now()
  }
})

/** 监听水厂变化，刷新iframe */
watch(() => selectedWaterPlant.value, (newWaterPlant) => {
  console.log('水厂变化', newWaterPlant)
  //轮询iframe是否加载成功，水厂变换后修改iframe中的水厂选择框
  if (!iframeLoaded.value) {
    console.log('iframe加载失败1111')
    return;
  } else {
    updateIframeSelection()
  }
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 清除loading实例
  if (loadingInstance) {
    loadingInstance.close()
    loadingInstance = null
  }
})
</script>

<style scoped>
.iframe-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 6rem);
  /* Set explicit height based on viewport height */
  min-height: 30rem;
}

.error-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

/* 确保父级section没有padding */
:deep(section) {
  padding: 0 !important;
}

/* 确保iframe填充整个容器 */
iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>

<style>
/* 全局样式，去除设备页面的section padding */
section.p-\[var\(--app-content-padding\)\] {
  padding: 0 !important;
}
</style>
