<template>
  <div class="attendance">
    <div class="filter-bar">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="培训计划">
          <el-select v-model="filterForm.planId" placeholder="请选择培训计划" @change="handleSearch">
            <el-option
              v-for="item in planOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="签到日期">
          <el-date-picker
            v-model="filterForm.date"
            type="date"
            placeholder="选择日期"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="operation-bar">
      <el-button type="primary" @click="handleSignIn">扫码签到</el-button>
      <el-button type="success" @click="handleExport">导出签到记录</el-button>
    </div>

    <el-table :data="attendanceList" border style="width: 100%">
      <el-table-column prop="planName" label="培训计划" />
      <el-table-column prop="userName" label="姓名" />
      <el-table-column prop="department" label="部门" />
      <el-table-column prop="position" label="岗位" />
      <el-table-column prop="signTime" label="签到时间" />
      <el-table-column prop="signType" label="签到方式">
        <template #default="{ row }">
          <el-tag :type="row.signType === '扫码' ? 'success' : 'primary'">
            {{ row.signType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.status === '正常' ? 'success' : 'danger'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import * as XLSX from 'xlsx'

export default {
  name: 'Attendance',
  data() {
    return {
      filterForm: {
        planId: '',
        date: ''
      },
      planOptions: [],
      attendanceList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  created() {
    this.fetchPlanOptions()
    this.fetchData()
  },
  methods: {
    handleSearch() {
      this.currentPage = 1
      this.fetchData()
    },
    handleReset() {
      this.filterForm = {
        planId: '',
        date: ''
      }
      this.fetchData()
    },
    handleSignIn() {
      this.$message.success('请使用手机扫描二维码进行签到')
    },
    handleExport() {
      try {
        // 准备需要导出的数据
        const exportData = this.attendanceList.map(item => ({
          '培训计划': item.planName,
          '姓名': item.userName,
          '部门': item.department,
          '岗位': item.position,
          '签到时间': item.signTime,
          '签到方式': item.signType,
          '状态': item.status
        }))
        
        // 创建工作簿
        const workbook = XLSX.utils.book_new()
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        
        // 设置列宽
        const colWidth = [
          { wch: 30 }, // 培训计划
          { wch: 15 }, // 姓名
          { wch: 15 }, // 部门
          { wch: 15 }, // 岗位
          { wch: 20 }, // 签到时间
          { wch: 15 }, // 签到方式
          { wch: 10 }  // 状态
        ]
        worksheet['!cols'] = colWidth
        
        // 添加worksheet到workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, '签到记录')
        
        // 导出Excel文件
        XLSX.writeFile(workbook, `签到记录数据_${new Date().toLocaleDateString()}.xlsx`)
        
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    fetchData() {
      // 模拟获取签到记录列表
      // 这里应该使用filterForm中的条件进行筛选
      let mockData = [
        {
          id: '1',
          planName: '2024年度安全生产培训计划',
          userName: '张三',
          department: '生产部',
          position: '操作工',
          signTime: '2024-03-01 08:55',
          signType: '扫码',
          status: '正常'
        },
        {
          id: '2',
          planName: '特种设备操作人员培训',
          userName: '李四',
          department: '设备维护部',
          position: '设备操作员',
          signTime: '2024-03-05 13:25',
          signType: '扫码',
          status: '正常'
        },
        {
          id: '3',
          planName: '消防安全知识培训',
          userName: '王五',
          department: '安全部',
          position: '安全员',
          signTime: '2024-03-10 09:10',
          signType: '手动',
          status: '迟到'
        },
        {
          id: '4',
          planName: '新员工安全培训',
          userName: '赵六',
          department: '人力资源部',
          position: '新员工',
          signTime: '2024-03-15 08:45',
          signType: '扫码',
          status: '正常'
        },
        {
          id: '5',
          planName: '危险化学品安全管理培训',
          userName: '钱七',
          department: '生产部',
          position: '化学品管理员',
          signTime: '2024-03-20 08:50',
          signType: '扫码',
          status: '正常'
        }
      ]
      
      // 根据筛选条件过滤数据
      if (this.filterForm.planId) {
        const planName = this.planOptions.find(option => option.value === this.filterForm.planId)?.label
        mockData = mockData.filter(item => item.planName === planName)
      }
      
      if (this.filterForm.date) {
        const filterDate = new Date(this.filterForm.date).toISOString().split('T')[0]
        mockData = mockData.filter(item => {
          const itemDate = item.signTime.split(' ')[0]
          return itemDate === filterDate
        })
      }
      
      this.total = mockData.length // 总记录数
      this.attendanceList = mockData
    },
    fetchPlanOptions() {
      // 模拟获取培训计划选项
      this.planOptions = [
        { value: '1', label: '2024年度安全生产培训计划' },
        { value: '2', label: '特种设备操作人员培训' },
        { value: '3', label: '消防安全知识培训' },
        { value: '4', label: '新员工安全培训' },
        { value: '5', label: '危险化学品安全管理培训' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.attendance {
  .filter-bar {
    margin-bottom: 20px;
  }

  .operation-bar {
    margin-bottom: 20px;
  }

  .el-pagination {
    margin-top: 20px;
    justify-content: flex-end;
  }
}
</style> 