<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="文件编号" prop="fileCode">
            <el-input v-model="formData.fileCode" placeholder="请输入文件编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文件名称" prop="fileName">
            <el-input v-model="formData.fileName" placeholder="请输入文件名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="文件分类" prop="fileType">
            <el-select v-model="formData.fileType" placeholder="请选择文件分类" style="width: 100%">
              <el-option label="入职教育" value="entry" />
              <el-option label="三级教育" value="three-level" />
              <el-option label="主题教育" value="theme" />
              <el-option label="安全考试" value="exam" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上传人" prop="uploader">
            <el-input v-model="formData.uploader" placeholder="请输入上传人" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="文件描述" prop="fileDesc">
            <el-input v-model="formData.fileDesc" type="textarea" placeholder="请输入文件描述" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="附件上传" prop="fileList">
            <el-upload
              v-model:file-list="fileList"
              :action="uploadAction"
              :before-upload="beforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-exceed="handleExceed"
              :limit="5"
              multiple
              :auto-upload="true"
              list-type="picture-card"
              accept=".jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.mp4,.mp3"
            >
              <el-icon class="el-icon--upload"><Plus /></el-icon>
              <div class="el-upload__text">拖拽文件或 <em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传图片、视频、文档等多种格式文件，单个文件不超过50MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      
      <template v-if="formData.fileType === 'theme'">
        <el-divider content-position="center">主题教育专属配置</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      
      <template v-if="formData.fileType === 'entry' || formData.fileType === 'three-level'">
        <el-divider content-position="center">教育类型配置</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="适用岗位" prop="applyPost">
              <el-select
                v-model="formData.applyPost"
                multiple
                placeholder="请选择适用岗位"
                style="width: 100%"
              >
                <el-option label="操作工" value="operator" />
                <el-option label="电工" value="electrician" />
                <el-option label="焊工" value="welder" />
                <el-option label="车间管理" value="workshop-manager" />
                <el-option label="行政人员" value="admin-staff" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="培训课时" prop="trainHours">
              <el-input-number
                v-model="formData.trainHours"
                :min="0.5"
                :max="100"
                :step="0.5"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { Plus } from '@element-plus/icons-vue'
import { UploadProps, UploadUserFile } from 'element-plus'

/** 安全教育文件管理 表单 */
defineOptions({ name: 'FileEdit' })

const emit = defineEmits(['success'])
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中
const formTypeValue = ref('') // 表单的类型：create - 新增；update - 修改
const fileList = ref<UploadUserFile[]>([]) // 上传的文件列表

const formData = ref({
  id: undefined as number | undefined,
  fileCode: undefined as string | undefined,
  fileName: undefined as string | undefined,
  fileDesc: undefined as string | undefined,
  fileType: undefined as string | undefined,
  uploader: undefined as string | undefined,
  fileSize: undefined as number | undefined,
  uploadTime: undefined as string | undefined,
  // 主题教育专属字段
  startTime: undefined as Date | undefined,
  endTime: undefined as Date | undefined,
  // 入职/三级教育专属字段
  applyPost: [] as string[],
  trainHours: 1 as number
})

// 表单校验规则
const formRules = reactive({
  fileCode: [{ required: true, message: '请输入文件编号', trigger: 'blur' }],
  fileName: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
  fileType: [{ required: true, message: '请选择文件分类', trigger: 'change' }],
  uploader: [{ required: true, message: '请输入上传人', trigger: 'blur' }]
})

// 上传操作的URL
const uploadAction = ref('/api/file/upload') // 实际环境替换为真实的上传接口

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  formTypeValue.value = type
  resetForm()
  
  // 设置标题
  dialogTitle.value = type === 'create' ? '新增文件' : '修改文件'
  
  // 修改时，设置表单数据
  if (id) {
    formLoading.value = true
    try {
      // 模拟获取数据
      // const data = await FileApi.getDetail(id)
      // formData.value = data
      
      // 模拟数据
      setTimeout(() => {
        if (id === 1) {
          formData.value = {
            id: 1,
            fileCode: 'F001',
            fileName: '入职安全培训教案.docx',
            fileDesc: '新员工入职安全培训使用教案',
            fileType: 'entry',
            uploader: '张安全',
            fileSize: 1024 * 1024 * 2.5,
            uploadTime: '2023-10-15 09:30:45',
            applyPost: ['operator', 'electrician'],
            trainHours: 2,
            startTime: undefined,
            endTime: undefined
          }
        }
        formLoading.value = false
      }, 500)
    } catch (error) {
      console.error('获取文件详情失败:', error)
    } finally {
      formLoading.value = false
    }
  }
}

/** 上传前的校验 */
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 文件大小限制：50MB
  const maxSize = 50 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过50MB!')
    return false
  }
  return true
}

/** 上传成功回调 */
const handleUploadSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  message.success('文件上传成功')
  console.log('文件上传成功:', response, uploadFile)
  // 上传成功后的处理逻辑
}

/** 上传失败回调 */
const handleUploadError: UploadProps['onError'] = (error) => {
  message.error('文件上传失败')
  console.error('文件上传失败:', error)
}

/** 文件超出限制回调 */
const handleExceed: UploadProps['onExceed'] = (files) => {
  message.warning(`最多只能上传5个文件，本次选择了 ${files.length} 个文件`)
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    fileCode: undefined,
    fileName: undefined,
    fileDesc: undefined,
    fileType: undefined,
    uploader: undefined,
    fileSize: undefined,
    uploadTime: undefined,
    startTime: undefined,
    endTime: undefined,
    applyPost: [],
    trainHours: 1
  }
  fileList.value = []
}

/** 提交表单 */
const submitForm = async () => {
  // 表单校验
  const formRef = ref()
  try {
    formLoading.value = true
    // 校验表单
    await formRef.value?.validate()
    
    // 构建提交的数据
    const data = {
      ...formData.value,
      fileList: fileList.value
    }
    
    // 保存操作
    if (formTypeValue.value === 'create') {
      // 模拟创建操作
      // await FileApi.create(data)
      console.log('创建文件:', data)
    } else {
      // 模拟更新操作
      // await FileApi.update(data)
      console.log('更新文件:', data)
    }
    
    // 提示信息
    message.success(formTypeValue.value === 'create' ? '创建成功' : '修改成功')
    // 关闭弹窗
    dialogVisible.value = false
    // 通知父组件刷新
    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
  } finally {
    formLoading.value = false
  }
}

// 向父组件暴露方法
defineExpose({ open })
</script>

<style scoped lang="scss">
.el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon--upload {
  font-size: 28px;
  color: #8c939d;
  width: 40px;
  height: 40px;
  text-align: center;
}

.el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;

  em {
    color: var(--el-color-primary);
    font-style: normal;
  }
}
</style>