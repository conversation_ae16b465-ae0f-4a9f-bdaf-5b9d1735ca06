<script lang="tsx">
import RouterSearch from '@/components/RouterSearch/index.vue'
import { useDesign } from '@/hooks/web/useDesign'
import { Message } from '@/layout/components//Message'
import { Breadcrumb } from '@/layout/components/Breadcrumb'
import { Collapse } from '@/layout/components/Collapse'
import { LocaleDropdown } from '@/layout/components/LocaleDropdown'
import { Screenfull } from '@/layout/components/Screenfull'
// import { SizeDropdown } from '@/layout/components/SizeDropdown'
import StationSelector from '@/components/StationSelector'
import { UserInfo } from '@/layout/components/UserInfo'
import { useAppStore } from '@/store/modules/app'
import { computed, defineComponent } from 'vue'

const { getPrefixCls, variables } = useDesign()

const prefixCls = getPrefixCls('tool-header')

const appStore = useAppStore()

// const title = computed(() => appStore.getTitle)
// 面包屑
const breadcrumb = computed(() => appStore.getBreadcrumb)

// 折叠图标
const hamburger = computed(() => appStore.getHamburger)

// 全屏图标
const screenfull = computed(() => appStore.getScreenfull)

// 搜索图片
const search = computed(() => appStore.search)

// 尺寸图标
// const size = computed(() => appStore.getSize)

// 布局
const layout = computed(() => appStore.getLayout)

// 多语言图标
const locale = computed(() => appStore.getLocale)

// 消息图标
const message = computed(() => appStore.getMessage)

export default defineComponent({
  name: 'ToolHeader',
  setup() {
    // 处理水厂切换逻辑
    const handleStationChange = (station) => {
      console.log('水厂切换:', station)
      // 存储到全局状态
      appStore.setCurrentStation(station)
    }

    return () => (
      <div
        id={`${variables.namespace}-tool-header`}
        class={[
          prefixCls,
          'h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between',
          'bg-[url(@/assets/imgs/bg-nav.png)] bg-no-repeat bg-center bg-cover'
        ]}
      >
        {layout.value !== 'top' ? (
          <div class="h-full flex items-center">
            {hamburger.value && layout.value !== 'cutMenu' ? (
              <Collapse class="custom-hover" color="var(--top-header-text-color)"></Collapse>
            ) : undefined}
            {breadcrumb.value ? (
              <div class="flex items-center">
                <Breadcrumb class="lt-md:hidden"></Breadcrumb>
              </div>
            ) : undefined}
          </div>
        ) : undefined}
        <div class="h-full flex items-center">
          {screenfull.value ? (
            <Screenfull class="custom-hover" color="var(--top-header-text-color)"></Screenfull>
          ) : undefined}
          {search.value ? <RouterSearch class="user-select" isModal={false} /> : undefined}
          {/* {size.value ? (
            <SizeDropdown class="custom-hover" color="var(--top-header-text-color)"></SizeDropdown>
          ) : undefined} */}
          {locale.value ? (
            <LocaleDropdown
              class="custom-hover user-select"
              color="var(--top-header-text-color)"
            ></LocaleDropdown>
          ) : undefined}
          {message.value ? (
            <Message class="custom-hover  user-select" color="var(--top-header-text-color)"></Message>
          ) : undefined}
          <div class="h-[20px] mx-4 border-r-2 border-r-[#6a6a6a] opacity-80"></div>
          <StationSelector
            class="lt-md:hidden"
            breadcrumbStyle={true}
            onlyActive={true}
            onChange={handleStationChange}
          />
          <div class="h-[20px] mx-4 border-r-2 border-r-[#6a6a6a] opacity-80"></div>
          <UserInfo></UserInfo>
        </div>
      </div>
    )
  }
})
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-tool-header;

.#{$prefix-cls} {
  transition: left var(--transition-time-02);
}

.user-select {
  user-select: none;
}
</style>
