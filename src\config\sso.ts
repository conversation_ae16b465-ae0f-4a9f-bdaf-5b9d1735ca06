export const SSO_CONFIG = {
  // UAP认证服务器地址
  UAP_SERVER: 'http://172.16.198.101:8080/uap-server/Oauth2/login',

  // 本地服务地址
  LOCAL_SERVER: 'http://172.16.198.101:8080/factory/',

  // loginOut跳转路径
  SSO_URL: 'http://172.16.198.101:8080/factory/xfsso',

  // 认证参数
  AUTH_PARAMS: {
    service: 'http://172.16.198.101:8080/factory/xfsso',
    at: 'myuap',
    client_id: 'uap-manager',
    Authorization_Type: 'authorization-code',
    appCode: 'uap-manager',
    redirect: 'http://172.16.198.101:8080/factory/xfsso'
  },

  // API地址
  API_BASE_URL: 'http://172.16.198.101:8080/factory/admin-api/system/auth/xf-sso'
}
