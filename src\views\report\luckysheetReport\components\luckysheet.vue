<template>
  <div class="luckysheet-container">
    <div id="luckysheet" class="luckysheetStyle"></div>
  </div>
</template>
<script lang="ts" setup>
import { exportExcel } from '@/utils/excelExport'
import { onUnmounted } from 'vue'

// 初始化
function initLuckysheet(option: any) {
  luckysheet.create(option)
}
// 销毁
const destroyLuckysheet = () => {
  return luckysheet.destroy()
}

// 获取所有sheet数据
const closeWebSocket = () => {
  console.log('关闭WebSocket')
  window.luckysheet?.closeWebsocket()
}

// 获取所有sheet数据
const getTableData = () => {
  return luckysheet.getAllSheets()
}

// 获取当前sheet数据
const getSheet = () => {
  return luckysheet.getSheet()
}

// 导出Excel
const handleExportExcel = async (fileName: string = 'default.xlsx') => {
  const sheets = getTableData()
  await exportExcel(sheets, fileName)
}

onUnmounted(() => {
  destroyLuckysheet()
  closeWebSocket()
})

// 暴露方法出去
defineExpose({
  getTableData,
  initLuckysheet,
  destroyLuckysheet,
  closeWebSocket,
  exportExcel: handleExportExcel,
  getSheet
})
</script>
<style scoped lang="scss">
.luckysheet-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.luckysheetStyle {
  margin: 0px;
  padding: 0px;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
}
</style>
