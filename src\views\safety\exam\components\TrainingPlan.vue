<template>
  <div class="training-plan">
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">新增培训计划</el-button>
      <el-button type="success" @click="handleExport">导出计划</el-button>
    </div>
    
    <el-table :data="planList" border style="width: 100%">
      <el-table-column prop="planName" label="计划名称" />
      <el-table-column prop="department" label="培训部门" />
      <el-table-column prop="position" label="培训岗位" />
      <el-table-column prop="trainer" label="培训讲师" />
      <el-table-column prop="startTime" label="开始时间" />
      <el-table-column prop="endTime" label="结束时间" />
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.status === '已完成' ? 'success' : row.status === '进行中' ? 'primary' : 'warning'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 培训计划弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formData.id ? '编辑培训计划' : '新增培训计划'"
      width="600px"
      append-to-body
    >
      <el-form :model="formData" label-width="120px" :rules="formRules" ref="formRef">
        <el-form-item label="计划名称" prop="planName">
          <el-input v-model="formData.planName" placeholder="请输入计划名称" />
        </el-form-item>
        <el-form-item label="培训部门" prop="department">
          <el-input v-model="formData.department" placeholder="请输入培训部门" />
        </el-form-item>
        <el-form-item label="培训岗位" prop="position">
          <el-input v-model="formData.position" placeholder="请输入培训岗位" />
        </el-form-item>
        <el-form-item label="培训讲师" prop="trainer">
          <el-input v-model="formData.trainer" placeholder="请输入培训讲师" />
        </el-form-item>
        <el-form-item label="培训时间" prop="timeRange">
          <el-date-picker
            v-model="formData.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态">
            <el-option label="未开始" value="未开始" />
            <el-option label="进行中" value="进行中" />
            <el-option label="已完成" value="已完成" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import * as XLSX from 'xlsx'

export default {
  name: 'TrainingPlan',
  data() {
    return {
      planList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      formData: {
        id: '',
        planName: '',
        department: '',
        position: '',
        trainer: '',
        timeRange: [],
        status: '未开始'
      },
      formRules: {
        planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
        department: [{ required: true, message: '请输入培训部门', trigger: 'blur' }],
        position: [{ required: true, message: '请输入培训岗位', trigger: 'blur' }],
        trainer: [{ required: true, message: '请输入培训讲师', trigger: 'blur' }],
        timeRange: [{ required: true, message: '请选择培训时间', trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      }
    }
  },
  methods: {
    handleAdd() {
      this.formData = {
        id: '',
        planName: '',
        department: '',
        position: '',
        trainer: '',
        timeRange: [],
        status: '未开始'
      }
      this.dialogVisible = true
    },
    
    handleEdit(row) {
      this.formData = {
        id: row.id,
        planName: row.planName,
        department: row.department,
        position: row.position,
        trainer: row.trainer,
        timeRange: [row.startTime, row.endTime],
        status: row.status
      }
      this.dialogVisible = true
    },
    
    handleDelete(row) {
      this.$confirm('确认删除该培训计划吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.fetchData()
      }).catch(() => {})
    },
    
    handleExport() {
      try {
        // 准备需要导出的数据
        const exportData = this.planList.map(item => ({
          '计划名称': item.planName,
          '培训部门': item.department,
          '培训岗位': item.position,
          '培训讲师': item.trainer,
          '开始时间': item.startTime,
          '结束时间': item.endTime,
          '状态': item.status
        }))
        
        // 创建工作簿
        const workbook = XLSX.utils.book_new()
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        
        // 设置列宽
        const colWidth = [
          { wch: 30 }, // 计划名称
          { wch: 15 }, // 培训部门
          { wch: 15 }, // 培训岗位 
          { wch: 15 }, // 培训讲师
          { wch: 15 }, // 开始时间
          { wch: 15 }, // 结束时间
          { wch: 10 }  // 状态
        ]
        worksheet['!cols'] = colWidth
        
        // 添加worksheet到workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, '培训计划')
        
        // 导出Excel文件
        XLSX.writeFile(workbook, `培训计划数据_${new Date().toLocaleDateString()}.xlsx`)
        
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    },
    
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          // 处理表单数据
          const formattedData = {
            id: this.formData.id || new Date().getTime().toString(),
            planName: this.formData.planName,
            department: this.formData.department,
            position: this.formData.position,
            trainer: this.formData.trainer,
            startTime: this.formData.timeRange[0],
            endTime: this.formData.timeRange[1],
            status: this.formData.status
          }
          
          if (this.formData.id) {
            // 编辑现有计划
            this.$message.success(`已更新培训计划：${formattedData.planName}`)
          } else {
            // 添加新计划
            this.$message.success(`已添加培训计划：${formattedData.planName}`)
          }
          
          this.dialogVisible = false
          this.fetchData()
        }
      })
    },
    
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    
    fetchData() {
      // 模拟获取培训计划列表数据
      const mockData = [
        {
          id: '1',
          planName: '2024年度安全生产培训计划',
          department: '全公司',
          position: '全体员工',
          trainer: '张安全',
          startTime: '2024-01-01',
          endTime: '2024-12-31',
          status: '进行中'
        },
        {
          id: '2',
          planName: '特种设备操作人员培训',
          department: '生产部',
          position: '特种设备操作工',
          trainer: '李工程',
          startTime: '2024-03-05',
          endTime: '2024-03-07',
          status: '已完成'
        },
        {
          id: '3',
          planName: '新员工安全培训',
          department: '人力资源部',
          position: '新入职员工',
          trainer: '王安全',
          startTime: '2024-03-15',
          endTime: '2024-03-16',
          status: '未开始'
        },
        {
          id: '4',
          planName: '消防安全知识培训',
          department: '全公司',
          position: '全体员工',
          trainer: '赵消防',
          startTime: '2024-04-01',
          endTime: '2024-04-01',
          status: '未开始'
        },
        {
          id: '5',
          planName: '危险化学品安全管理培训',
          department: '生产部',
          position: '化学品管理员',
          trainer: '钱工程',
          startTime: '2024-04-10',
          endTime: '2024-04-12',
          status: '未开始'
        }
      ]
      
      this.total = 15 // 总记录数
      this.planList = mockData
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style lang="scss" scoped>
.training-plan {
  .operation-bar {
    margin-bottom: 20px;
  }
  
  .el-pagination {
    margin-top: 20px;
    justify-content: flex-end;
  }
}
</style> 