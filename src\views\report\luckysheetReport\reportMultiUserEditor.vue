<template>
  <div class="report-page">
    <div class="report-header">
      <div class="title">{{ reportName }}</div>
      <div class="actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handlePreview">预览</el-button>
      </div>
    </div>
    <div class="report-content">
      <LuckySheetContainer ref="luckysheetRef" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ReportApi } from '@/api/report/reportInfo'
import { getRefreshToken } from '@/utils/auth'
import LuckySheetContainer from './components/luckysheet.vue'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { getRandom, defaultSheetData, WS_SERVER_URL } from './config'

const route = useRoute()
const router = useRouter()
const reportName = ref('')
const luckysheetRef = ref()
const tagsViewStore = useTagsViewStore()

// 初始化Luckysheet
const initLuckysheet = async () => {
  const reportCode = route.query.reportCode as string
  console.log('reportCode', reportCode)
  if (!reportCode) {
    ElMessage.error('报表编码不能为空')
    router.back()
    return
  }

  const id = getRandom()
  const username = `user_${id}`

  const options = {
    lang: 'zh',
    title: 'Luckysheet',
    allowUpdate: true,
    loadUrl: '',
    updateUrl: '',
    plugins: [],
    showinfobar: true,
    showtoolbar: true,
    enableAddRow:false,
    enableAddColumn: false,
    showtoolbarConfig: {
      undoRedo: false, //撤销重做，注意撤消重做是两个按钮，由这一个配置决定显示还是隐藏
      paintFormat: false, //格式刷
      currencyFormat: false, //货币格式
      // percentageFormat: false, //百分比格式
      // numberDecrease: false, // '减少小数位数'
      // numberIncrease: false, // '增加小数位数
      // moreFormats: false, // '更多格式'
      // font: false, // '字体'
      // fontSize: false, // '字号大小'
      // bold: false, // '粗体 (Ctrl+B)'
      // italic: false, // '斜体 (Ctrl+I)'
      // strikethrough: false, // '删除线 (Alt+Shift+5)'
      // underline: false, // '下划线 (Alt+Shift+6)'
      // textColor: false, // '文本颜色'
      // fillColor: false, // '单元格颜色'
      // border: false, // '边框'
      mergeCell: false, // '合并单元格'
      horizontalAlignMode: false, // '水平对齐方式'
      verticalAlignMode: false, // '垂直对齐方式'
      textWrapMode: false, // '换行方式'
      textRotateMode: false, // '文本旋转方式'
      image:false, // '插入图片'
      // link:false, // '插入链接'
      chart: false, // '图表'（图标隐藏，但是如果配置了chart插件，右击仍然可以新建图表）
      postil:  false, //'批注'
      pivotTable: false,  //'数据透视表'
      function: false, // '公式'
      frozenMode: false, // '冻结方式'
      // sortAndFilter: false, // '排序和筛选'
      conditionalFormat: false, // '条件格式'
      dataVerification: false, // '数据验证'
      splitColumn: false, // '分列'
      screenshot: false, // '截图'
      // findAndReplace: false, // '查找替换'
      protection:false, // '工作表保护'
      print:false, // '打印'
    },
    cellRightClickConfig: {
      insertRow: false, // 插入行
      insertColumn: false, // 插入列
      deleteRow: false, // 删除选中行
      deleteColumn: false, // 删除选中列
      data: false, // 数据验证
      cellFormat: false, // 设置单元格格式
      chart: false, // 图表生成
      image: false, // 插入图片
      filter: false // 筛选选区
    }
  }

  try {
    // 获取报表数据
    const res = await ReportApi.getReportDesignByCode(reportCode, 2)
    if (res.code === 0 && res.data) {
      const data = res.data
      reportName.value = data.reportName

      // 设置协同配置
      options.title = data.reportName
      // options.loadUrl = getLoadUrl()
      options.loadUrl = `http://localhost:48084/admin-api/report/report/report-editor-view-ws/${reportCode}?loadType=2`
      options.updateUrl = `${WS_SERVER_URL}?type=luckysheet&userid=${id}&username=${username}&gridkey=${reportCode}&token=${getRefreshToken()}`

      // 设置图片处理
      // Reflect.set(options, 'uploadImage', uploadImage)
      // Reflect.set(options, 'imageUrlHandle', imageUrlHandle)
      console.log('options', options)
      // 等待组件准备好后初始化
      await nextTick()
      if (luckysheetRef.value) {
        luckysheetRef.value.initLuckysheet(options)
      } else {
        throw new Error('Luckysheet组件未初始化')
      }
    } else {
      throw new Error(res.msg || '获取报表数据失败')
    }
  } catch (error) {
    console.error('初始化Luckysheet失败:', error)
    ElMessage.error('初始化报表失败')
    // 使用默认数据初始化
    options.data = defaultSheetData
    await nextTick()
    if (luckysheetRef.value) {
      luckysheetRef.value.initLuckysheet(options)
    }
  }
}

// 保存报表
const handleSave = async () => {
  if (!luckysheetRef.value) {
    ElMessage.error('Luckysheet未初始化')
    return
  }

  try {
    const excelJson = luckysheetRef.value.toJson()
    const saveData = {
      reportCode: route.query.reportCode,
      data: excelJson.data
    }
    const res = await ReportApi.saveReportFill(saveData)
    if (res.code === 0) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 预览报表
const handlePreview = () => {
  const previewUrl = `/report/reportPreview?reportCode=${route.query.reportCode}`
  window.open(previewUrl, '_blank')
}

// 监听路由变化
const unwatch = watch(
  () => route.fullPath,
  (newPath, oldPath) => {
    // 如果路由变化，说明标签页被切换
    if (newPath !== oldPath) {
      if (luckysheetRef.value) {
        console.log('路由变化，关闭WebSocket')
        luckysheetRef.value.closeWebSocket()
      }
    }
  }
)

// 监听标签页关闭
const unwatchTags = watch(
  () => tagsViewStore.getVisitedViews,
  (newViews) => {
    // 检查当前路由是否还在已访问的视图中
    const currentView = newViews.find((v) => v.fullPath === route.fullPath)
    if (!currentView && luckysheetRef.value) {
      console.log('标签页被关闭，关闭WebSocket')
      luckysheetRef.value.closeWebSocket()
    }
  },
  { deep: true }
)

onMounted(() => {
  initLuckysheet()
  // 浏览器关闭、刷新时执行
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onUnmounted(() => {
  if (luckysheetRef.value) {
    console.log('组件卸载，关闭WebSocket')
    luckysheetRef.value.closeWebSocket()
  }
  // 移除全局事件监听
  window.removeEventListener('beforeunload', handleBeforeUnload)
  // 移除路由监听
  unwatch()
  unwatchTags()
})

function handleBeforeUnload() {
  if (luckysheetRef.value) {
    console.log('浏览器关闭或刷新，关闭WebSocket')
    luckysheetRef.value.closeWebSocket()
  }
}
</script>

<style scoped lang="scss">
.report-page {
  width: 100%;
  height: calc(100vh - 170px);
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .report-header {
    height: 60px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #ebeef5;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }

    .actions {
      display: flex;
      gap: 10px;
    }
  }

  .report-content {
    flex: 1;
    overflow: hidden;
    position: relative;
  }
}
</style>
