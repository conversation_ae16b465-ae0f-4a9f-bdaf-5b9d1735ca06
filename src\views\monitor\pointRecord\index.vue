<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">补点记录</span>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-260px)] flex flex-col">
        <div class="w-full h-[50px] mb-2 gap-2 flex items-center">
          <el-form :model="searchForm" inline>
            <el-form-item label="终端编号：">
              <el-input v-model="searchForm.terminalId" placeholder="请输入终端编号" />
            </el-form-item>
            <el-form-item label="掉线时间：">
              <el-date-picker
                v-model="searchForm.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
              <el-button @click="resetForm" :icon="RefreshRight">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border>
                <el-table-column prop="terminalId" label="终端编号" align="center" />
                <el-table-column prop="offlineStartTime" label="掉线开始时间" align="center" />
                <el-table-column prop="offlineEndTime" label="掉线结束时间" align="center" />
                <el-table-column prop="status" label="补录状态" align="center">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 1 ? 'success' : 'warning'">
                      {{ row.status === 1 ? '已补录' : '未补录' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" align="center">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      link
                      :disabled="row.status === 1"
                      @click="handleRecord(row)"
                    >
                      补录数据
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </el-card>
    <RecordDialog
      ref="recordDialogRef"
      :terminal-id="recordForm.terminalId"
      :time-range="recordForm.timeRange"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, RefreshRight } from '@element-plus/icons-vue'
import RecordDialog from './components/recordDialog.vue'

const recordDialogRef = ref<InstanceType<typeof RecordDialog> | null>(null)

// 查询表单
const searchForm = reactive({
  terminalId: '',
  timeRange: []
})

const tableData = ref([
  {
    terminalId: 'T001',
    name: '1号监测点',
    status: '离线',
    offlineStartTime: '2024-01-01 10:00:00',
    offlineEndTime: '2024-01-01 12:00:00',
    duration: '2小时'
  },
  {
    terminalId: 'T002',
    name: '2号监测点',
    status: '离线',
    offlineStartTime: '2024-01-02 14:00:00',
    offlineEndTime: '2024-01-02 16:00:00',
    duration: '2小时'
  },
  {
    terminalId: 'T003',
    name: '3号监测点',
    status: '离线',
    offlineStartTime: '2024-01-03 09:00:00',
    offlineEndTime: '2024-01-03 11:00:00',
    duration: '2小时'
  }
])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(3)

const recordForm = reactive({
  terminalId: '',
  timeRange: '',
  file: null
})

const handleSearch = async () => {
  try {
    ElMessage.success('查询成功')
  } catch (error) {
    ElMessage.error('查询失败')
  }
}

const resetForm = () => {
  searchForm.terminalId = ''
  searchForm.timeRange = []
}
const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}

const handleRecord = (row: any) => {
  recordForm.terminalId = row.terminalId
  recordForm.timeRange = `${row.offlineStartTime} 至 ${row.offlineEndTime}`
  recordDialogRef.value?.setVisible()
}
</script>
<style scoped></style>
