<template>
  <el-card class="">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="按水厂选择" name="factory">
        <div class="tree-container">
          <el-tree ref="factoryTree" :data="treeData.byFactory" :props="treeProps" @node-click="handleNodeClick"
            :render-content="renderTreeNode" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="按指标选择" name="label">
        <div class="tree-container">
          <el-tree ref="labelTree" :data="treeData.byLabel" :props="treeProps" @node-click="handleNodeClick"
            :render-content="renderTreeNode" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="按时间选择" name="timeType">
        <div class="tree-container">
          <el-tree ref="timeTree" :data="treeData.byTime" :props="treeProps" @node-click="handleNodeClick"
            :render-content="renderTreeNode" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="内置参数" name="innerParam">
        <div class="tree-container">
          <el-tree ref="innerParam" :data="treeData.innerParam" :props="treeProps" @node-click="handleNodeClick"
            :render-content="renderTreeNode" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup>
import { reactive, ref, onMounted, nextTick, h } from 'vue';
import { ElMessage } from 'element-plus';
import { MetaDataApi } from '@/api/report/metadata/index';

// 定义要发送到父组件的事件
const emit = defineEmits(['drag-start', 'drag', 'drag-end']);

const debug = ref(true); // 设置为true显示调试信息
const activeTab = ref('factory');
const paramOptions = ref(['factoryId', 'labelId', 'timeType']);
const lastDragData = ref(null); // 存储上次拖拽的数据
const isDragging = ref(false); // 是否正在拖拽

const selectedParams = reactive({
  factoryId: null,
  labelId: null,
  labelName: null,
  type: null,
  internal: null
});

// 处理节点点击
const handleNodeClick = (data) => {
  console.log('节点点击:', data);
  if (data && data.type && paramOptions.value.includes(data.type)) {
    selectedParams[data.type] = data.value;
  }
};

const treeProps = {
  label: 'label',
  children: 'children'
};

// 获取节点的完整路径数据
const getNodePathData = (nodeData) => {
  const pathData = {
    factoryId: null,
    labelId: null,
    labelName: null,
    type: null,
    internal: null
  };

  // 递归获取父节点数据
  const getParentData = (node) => {
    if (!node) return;

    // 根据节点类型设置对应的字段
    if (node.type === 'factoryId') {
      pathData.factoryId = node.value;
    } else if (node.type === 'labelId') {
      pathData.labelId = node.value;
      pathData.labelName = node.label;
    } else if (node.type === 'timeType') {
      pathData.type = node.value;
    } else if (node.type === 'internal') {
      pathData.internal = node.value;
    }

    // 如果有父节点，继续获取
    if (node.parent) {
      getParentData(node.parent);
    }
  };

  // 从当前节点开始获取数据
  getParentData(nodeData);

  return pathData;
};

// 构建拖拽数据JSON
const buildDragData = (nodeData) => {
  // 获取节点的完整路径数据
  const pathData = getNodePathData(nodeData);

  // 添加当前已选择的其他参数
  if (selectedParams.factoryId) pathData.factoryId = selectedParams.factoryId;
  if (selectedParams.labelId) pathData.labelId = selectedParams.labelId;
  if (selectedParams.type) pathData.type = selectedParams.type;
  if (selectedParams.labelName) pathData.labelName = selectedParams.labelName;
  if (selectedParams.internal) pathData.internal = selectedParams.internal;

  return pathData;
};

// 节点拖拽开始事件
const handleNodeDragStart = (event, nodeData) => {
  // 构建拖拽数据
  const dragData = buildDragData(nodeData);
  lastDragData.value = dragData;
  isDragging.value = true;

  // 设置拖拽数据
  event.dataTransfer.setData('text/plain', JSON.stringify(dragData));
  event.dataTransfer.effectAllowed = 'copy';

  // 向父组件发送拖拽开始事件
  emit('drag-start', dragData);
};

// 拖拽过程中
const handleDrag = (event) => {
  if (isDragging.value && lastDragData.value) {
    // 向父组件发送拖拽事件和位置信息
    emit('drag', {
      data: lastDragData.value,
      position: {
        x: event.clientX,
        y: event.clientY
      }
    });
  }
};

// 拖拽结束
const handleDragEnd = (event) => {
  isDragging.value = false;
  // 向父组件发送拖拽结束事件
  if (lastDragData.value) {
    emit('drag-end', {
      data: lastDragData.value,
      success: true
    });
  }
};

// 修改树数据
const treeData = ref({
  byFactory: [],
  byLabel: [],
  byTime: [],
  innerParam: [
    {
      label: '日',
      value: 'day',
      type: 'internal'
    },
    {
      label: '月',
      value: 'month',
      type: 'internal'
    },
    {
      label: '年',
      value: 'year',
      type: 'internal'
    }
  ]
});

// 修改树节点渲染函数
const renderTreeNode = (h, { node, data }) => {
  // 为每个节点添加父节点引用
  if (data.children) {
    data.children.forEach(child => {
      child.parent = data;
    });
  }

  return h(
    'div',
    {
      class: 'custom-tree-node',
      draggable: true,
      onClick: (event) => event.stopPropagation(),
      onDragstart: (event) => handleNodeDragStart(event, data),
      onDrag: handleDrag,
      onDragend: handleDragEnd,
    },
    [
      h(
        'div',
        {
          class: 'node-content'
        },
        [
          h(
            'span',
            {
              class: 'node-label'
            },
            data.label
          ),
          data.children ? h(
            'span',
            {
              class: 'node-icon'
            },
            ''
          ) : null
        ]
      )
    ]
  );
};

// 初始化构建指标维度树
const initFactoryMetaDataTree = async () => {
  let data = await MetaDataApi.buildFactoryMetaDataTree();
  console.log("data----", data);
  if (data.length > 0) {
    for (let i = 0; i < data.length; i++) {
      console.log("data----", data[i]);
      let treeRoot = data[i];
      if (treeRoot.label == 'byFactory') {
        treeData.value.byFactory = treeRoot.children;
      } else if (treeRoot.label == 'byLabel') {
        treeData.value.byLabel = treeRoot.children;
      } else if (treeRoot.label == 'byTime') {
        treeData.value.byTime = treeRoot.children;
      }
    }
  }
};

// 组件挂载后执行
onMounted(() => {
  initFactoryMetaDataTree();
  console.log('IndicatorSelector组件已挂载');
  console.log('树数据:', treeData);
});

// 对外暴露接口
defineExpose({
  getSelectedParams: () => {
    return { ...selectedParams };
  }
});
</script>

<style scoped>
.el-tag {
  margin-right: 8px;
  margin-top: 4px;
  cursor: grab;
}

.el-tag:active {
  cursor: grabbing;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.drag-preview {
  margin-top: 15px;
  padding: 10px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  background-color: #f9f9f9;
}

/* 自定义树节点样式 */
:deep(.custom-tree-node) {
  width: 100%;
  padding: 4px 8px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: grab;
  user-select: none;
  transition: all 0.2s;
}

:deep(.custom-tree-node:hover) {
  background-color: #f5f7fa;
}

:deep(.custom-tree-node:active) {
  cursor: grabbing;
  background-color: #ecf5ff;
}

:deep(.node-content) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

:deep(.node-label) {
  flex: 1;
  padding: 2px 0;
}

:deep(.node-description) {
  margin: 0 8px;
  color: #909399;
  cursor: help;
  font-size: 14px;
}

:deep(.node-icon) {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
  transform: rotate(0deg);
  transition: transform 0.2s;
}

:deep(.el-tree-node.is-expanded > .el-tree-node__content .node-icon) {
  transform: rotate(90deg);
}

/* 分组节点样式 */
:deep(.group-node) {
  padding: 8px;
  margin: 4px 0;
  background-color: #f5f7fa;
  border-radius: 4px;
  user-select: none;
}

:deep(.group-label) {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

/* 为树容器添加滚动条 */
.tree-container {
  height: calc(100vh - 150px);
  /* 设置高度为视口高度减去200px的边距 */
  overflow-y: auto;
  /* 添加垂直滚动条 */
  padding: 10px;
  /* 可选：添加内边距 */
  border: 1px solid #ebeef5;
  /* 可选：添加边框 */
  border-radius: 4px;
  /* 可选：添加圆角 */
}
</style>
