<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="search-form"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="数据源编码" prop="sourceCode">
        <el-input
          v-model="queryParams.sourceCode"
          placeholder="请输入数据源编码"
          clearable
          @keyup.enter="handleQuery"
          class="search-input"
        />
      </el-form-item>
<!--      <el-form-item label="数据源类型 " prop="sourceType">-->
<!--        <el-select-->
<!--          v-model="queryParams.sourceType"-->
<!--          placeholder="请选择数据源类型"-->
<!--          clearable-->
<!--          class="search-input"-->
<!--        >-->
<!--          <el-option label="请选择字典生成" value="" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="数据源状态" prop="enableFlag">
        <el-select
          v-model="queryParams.enableFlag"
          placeholder="请选择数据源状态"
          clearable
          class="search-input"
        >
          <el-option
            v-for="item in enableFlagOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="button-icon" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="button-icon" /> 重置
        </el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['report:data-source:create']"
        >
          <Icon icon="ep:plus" class="button-icon" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['report:data-source:export']"
        >
          <Icon icon="ep:download" class="button-icon" /> 导出
        </el-button> -->
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="button-icon" /> 新增
        </el-button>
<!--        <el-button type="success" plain @click="handleExport" :loading="exportLoading">-->
<!--          <Icon icon="ep:download" class="button-icon" /> 导出-->
<!--        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="数据源编码" align="center" prop="sourceCode" />
      <el-table-column label="数据源名称" align="center" prop="sourceName" />
      <el-table-column label="数据源描述" align="center" prop="sourceDesc">
        <template #default="{ row }">
          <div class="cell-wrap">{{ row.sourceDesc }}</div>
        </template>
      </el-table-column>
      <el-table-column label="数据源类型" align="center" prop="sourceType" />
<!--      <el-table-column label="数据源连接配置json" align="center" prop="sourceConfig">-->
<!--        <template #default="{ row }">-->
<!--          <div class="cell-wrap">{{ row.sourceConfig }}}</div>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="数据源状态" align="center" prop="enableFlag">
        <template #default="scope">
          <el-tag :type="scope.row.enableFlag === 0 ? 'danger' : 'success'">
            {{ scope.row.enableFlag === 0 ? '已禁用' : '已启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
<!--      <el-table-column label="版本" align="center" prop="version" />-->
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['report:data-source:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['report:data-source:delete']"
          >
            删除
          </el-button> -->
          <el-button link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DataSourceForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { DataSourceApi, DataSourceVO } from '@/api/report/datasource'
import DataSourceForm from './components/DataSourceForm.vue'

/** 数据源管理 列表 */
defineOptions({ name: 'DataSource' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const enableFlagOptions = ref([
  { label: '已启用', value: 1 },
  { label: '已禁用', value: 0 }
])

const loading = ref(true) // 列表的加载中
const list = ref<DataSourceVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  sourceCode: undefined,
  sourceName: undefined,
  sourceDesc: undefined,
  sourceType: undefined,
  sourceConfig: undefined,
  enableFlag: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DataSourceApi.getDataSourcePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DataSourceApi.deleteDataSource(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DataSourceApi.exportDataSource(queryParams)
    download.excel(data, '数据源管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
  padding: 10px 0;
}

.search-input {
  width: 240px;
}

.date-picker {
  width: 220px;
}

.button-icon {
  margin-right: 5px;
}
.cell-wrap {
  white-space: normal;
  word-break: break-word;
  line-height: 20px;
}
</style>
