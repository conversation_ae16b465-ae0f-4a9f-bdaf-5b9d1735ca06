<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="数据源编码" prop="sourceCode">
            <el-select v-model="formData.sourceCode" placeholder="请选择数据源编码" style="width: 100%">
              <el-option
                v-for="item in dataSourceList"
                :key="item.sourceCode"
                :label="item.sourceName"
                :value="item.sourceCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据集编码" prop="setCode">
            <el-input v-model="formData.setCode" placeholder="请输入数据集编码" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据集名称" prop="setName">
            <el-input v-model="formData.setName" placeholder="请输入数据集名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="数据集描述" prop="setDesc">
            <el-input v-model="formData.setDesc" type="textarea" placeholder="请输入数据集描述" />
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="datasetType === 'sql'">
        <el-row>
          <el-col :span="24">
            <el-form-item label="查询SQL" prop="dynSentence">
              <CodeEditor
                v-model="formData.dynSentence"
                language="sql"
                height="200px"
                hintTables="sqlHintTables"
                @update:value="handleEditorChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-else>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请求方式" prop="requestMethod">
              <el-input v-model="httpInput" placeholder="">
                <template #prepend>
                  <el-select v-model="httpSelect" placeholder="Select" style="width: 100px">
                    <el-option label="GET" value="GET" />
                    <el-option label="POST" value="POST" />
                    <el-option label="PUT" value="PUT" />
                    <el-option label="DELETE" value="DELETE" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请求路径" prop="requestPath">
              <el-input v-model="formData.requestPath" placeholder="请输入请求路径" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请求头" prop="requestHeaders">
              <el-input v-model="formData.requestHeaders" placeholder="请输入请求头" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请求体" prop="requestBody">
              <el-input v-model="formData.requestBody" placeholder="请输入请求体" />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="查询参数" name="first">
        <el-button v-if="tableData.length == 0" type="text" size="small" @click="addRow()"
          >添加</el-button
        >
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column align="center" label="序号" type="index" min-width="80" />
          <el-table-column label="参数名" align="center">
            <template #default="{ $index }">
              <el-input
                v-model.trim="tableData[$index].paramName"
                :disabled="
                  tableData[$index].paramName == 'pageSize' ||
                  tableData[$index].paramName == 'pageNumber'
                "
              />
            </template>
          </el-table-column>
          <el-table-column label="描述" align="center">
            <template #default="{ $index }">
              <el-input v-model.trim="tableData[$index].paramDesc" />
            </template>
          </el-table-column>
          <el-table-column label="数据类型" align="center">
            <template #default="{ $index }">
              <el-input v-model.trim="tableData[$index].paramType" />
            </template>
          </el-table-column>
          <el-table-column label="示例值" align="center">
            <template #default="{ $index }">
              <el-input v-model.trim="tableData[$index].sampleItem" />
            </template>
          </el-table-column>
          <el-table-column label="校验" width="220" align="center">
            <template #default="{ row, $index }">
              <div class="table-content">
                <el-checkbox v-model="tableData[$index].mandatory" @change="Mandatory($index)"
                  >必选
                </el-checkbox>
                <el-button
                  type="primary"
                  :icon="Plus"
                  size="small"
                  @click="permissionClick(row, $index)"
                >
                  高级规则
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template #default="{ row, $index }">
              <el-button type="text" size="small" @click="cutOutRow($index)">删除 </el-button>
              <el-button type="text" size="small" @click="addRow(row)">追加 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="数据转换" name="second" @click="toDataTransform">
        <div class="jsonEditor-content">
          <JsonEditorVue v-model="formData.caseResult" />
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-dialog v-model="rulesDialogVisible" title="高级规则" width="50%">
      <CodeEditor
        v-model="formData.rulesCode"
        language="javascript"
        height="400px"
        hintTables="jsHintTables"
        @update:value="handleEditorChange"
      />
      <template #footer>
        <el-button type="warning" :disabled="formLoading">测 试</el-button>
        <el-button type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="rulesDialogVisible = false">取 消</el-button>
      </template>
    </el-dialog>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DataSetApi, DataSetVO ,TestTransformVO} from '@/api/report/dataset'
import { DataSourceApi } from '@/api/report/datasource'
import CodeEditor from '@/components/CodeEditor/index.vue'
import { Plus } from '@element-plus/icons-vue'
import JsonEditorVue from 'json-editor-vue3'

/** 数据集管理 表单 */
defineOptions({ name: 'DataSetForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const rulesDialogVisible = ref(false) // 高级规则弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formTypeValue = ref('') // 表单的类型：create - 新增；update - 修改
const datasetType = ref('') // 数据集类型
const formData = ref({
  id: undefined as number | undefined,
  setCode: undefined as string | undefined,
  setName: undefined as string | undefined,
  setDesc: undefined as string | undefined,
  sourceCode: undefined as string | undefined,
  dynSentence: undefined as string | undefined,
  requestMethod: undefined as string | undefined,
  requestPath: undefined as string | undefined,
  requestHeaders: undefined as string | undefined,
  requestBody: undefined as string | undefined,
  caseResult: [] as any[],
  setType: undefined as string | undefined,
  rulesCode:
    'function verification(data){\n //自定义脚本内容\n //可返回true/false单纯校验键入的data正确性\n //可返回文本，实时替换,比如当前时间等\n //return "2099-01-01 00:00:00";\n return true\n}',
  dataSetTransformDtoList: [] as any[],
  dataSetParamDtoList: [] as any[]
})
const formRules = reactive({})
const formRef = ref() // 表单 Ref
const httpInput = ref('')
const httpSelect = ref('GET')

// 表格数据
const tableData = ref<
  Array<{
    paramName: string
    paramDesc: string
    paramType: string
    sampleItem: string
    mandatory: boolean
  }>
>([])

// Add data source list ref
const dataSourceList = ref<Array<{sourceCode: string, sourceName: string}>>([])

// Add data transformation test status
const isDataTransformTested = ref(false)

// Add method to fetch data sources based on type
const fetchDataSources = async (type: string) => {
  try {
    const res = await DataSourceApi.queryAllDataSource({ sourceType: type })
    if (res.code === 0) {
      dataSourceList.value = res.data.map((item: any) => ({
        sourceCode: item.sourceCode,
        sourceName: item.sourceName
      }))
    }
  } catch (error) {
    console.error('获取数据源列表失败:', error)
    message.error('获取数据源列表失败')
  }
}

// Watch dataset type changes
watch(datasetType, (newType) => {
  if (newType) {
    formData.value.setType = newType
    fetchDataSources(newType)
  }
})

// 添加行
const addRow = (row?: any) => {
  const newRow = {
    paramName: '',
    paramDesc: '',
    paramType: '',
    sampleItem: '',
    mandatory: false
  }
  if (row) {
    const index = tableData.value.findIndex((item) => item === row)
    tableData.value.splice(index + 1, 0, newRow)
  } else {
    tableData.value.push(newRow)
  }
}

// 删除行
const cutOutRow = (index: number) => {
  tableData.value.splice(index, 1)
}

// 必选状态改变
const Mandatory = (index: number) => {
  console.log('🚀 ~ Mandatory ~ index:', index)
  // 可以在这里添加必选状态改变后的逻辑
}

// 高级规则点击
const permissionClick = (row: any, index: number) => {
  console.log('🚀 ~ permissionClick ~ index:', index)
  console.log('🚀 ~ permissionClick ~ row:', row)
  rulesDialogVisible.value = true
}

// 数据转换点击事件
const toDataTransform = async () => {

  // 构建数据转换测试参数
  const dataTransform = {
    sourceCode: formData.value.sourceCode,
    setType: formData.value.setType,
    dynSentence: formData.value.dynSentence,
    dataSetTransformDtoList: [],  // 暂时没做脚本转换
    dataSetParamDtoList: tableData.value.map(item => ({
      paramName: item.paramName,
      paramDesc: item.paramDesc,
      paramType: item.paramType,
      sampleItem: item.sampleItem,
      mandatory: item.mandatory
    }))
  }

  try {
    formLoading.value = true
    const res = await DataSetApi.testTransform(dataTransform)
    if (res.code === 0) {
      // 更新数据转换结果到编辑器
      if (res.data && res.data.data) {
        formData.value.caseResult = res.data.data
      }
      message.success('数据转换测试成功')
      isDataTransformTested.value = true
    } else {
      message.error(res.msg || '数据转换测试失败')
      isDataTransformTested.value = false
    }
  } catch (error) {
    console.error('数据转换测试失败:', error)
    message.error('数据转换测试失败')
    isDataTransformTested.value = false
  } finally {
    formLoading.value = false
  }
}

const activeName = ref('first')

// 监听编辑器内容变化
const handleEditorChange = (value: string) => {
  formData.value.dynSentence = value
}

/** 打开弹窗 */
async function open(formType: string, id?: number, dataType?: string) {
  datasetType.value = dataType || ''
  dialogVisible.value = true
  dialogTitle.value = t('action.' + formType)
  formTypeValue.value = formType
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DataSetApi.getDataSet(id)
      console.log('🚀 ~ open ~ formData.value:', formData.value)
      // 如果是从编辑按钮进入，确保datasetType与当前数据一致
      if (formData.value.setType) {
        datasetType.value = formData.value.setType
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 检查是否已进行数据转换测试
  if (!isDataTransformTested.value) {
    message.warning('请先进行数据转换测试')
    return
  }

  // 提交请求
  formLoading.value = true
  try {
    formData.value.requestMethod = httpSelect.value + httpInput.value

    const data = formData.value as unknown as DataSetVO
    data.caseResult = JSON.stringify(formData.value.caseResult)
    console.log('🚀 ~ submitForm ~ data:', data)
    if (formTypeValue.value === 'create') {
      await DataSetApi.createDataSet(data)
      message.success(t('common.createSuccess'))
    } else {
      await DataSetApi.updateDataSet(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    setCode: undefined,
    setName: undefined,
    setDesc: undefined,
    sourceCode: undefined,
    dynSentence: undefined,
    requestMethod: 'GET',
    requestPath: undefined,
    requestHeaders: undefined,
    requestBody: undefined,
    dataConvert: [],
    setType: undefined,
    rulesCode:
      'function verification(data){\n //自定义脚本内容\n //可返回true/false单纯校验键入的data正确性\n //可返回文本，实时替换,比如当前时间等\n //return "2099-01-01 00:00:00";\n return true\n}',
    dataSetTransformDtoList: [],
    dataSetParamDtoList: []
  }
  isDataTransformTested.value = false
  formRef.value?.resetFields()
}

// 标签页点击事件
const handleTabClick = (tab: any) => {
  if (tab.props.name === 'second') {
    toDataTransform()
  }
}
</script>

<style lang="scss" scoped>
.code-editor {
  width: 100%;
}
.table-content {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  gap: 2;
}
.jsonEditor-content {
  height: 220px;
  width: 100%;
  overflow: auto;
}
</style>
