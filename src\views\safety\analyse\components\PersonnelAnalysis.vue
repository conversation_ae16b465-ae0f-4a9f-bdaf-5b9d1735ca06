<template>
  <div class="personnel-analysis">
    <div class="filter-section">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filterForm.department" placeholder="请选择部门">
            <el-option
              v-for="item in departments"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="data-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="mb-20">
            <template #header>
              <div class="card-header">
                <span>安全培训完成率</span>
              </div>
            </template>
            <div class="statistics-container">
              <div class="statistics-item">
                <div class="statistics-label">已完成</div>
                <div class="statistics-value">95%</div>
              </div>
              <div class="statistics-item">
                <div class="statistics-label">未完成</div>
                <div class="statistics-value">5%</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="mb-20">
            <template #header>
              <div class="card-header">
                <span>安全意识评分</span>
              </div>
            </template>
            <div class="statistics-container">
              <div class="statistics-item">
                <div class="statistics-label">平均分</div>
                <div class="statistics-value">85.6</div>
              </div>
              <div class="statistics-item">
                <div class="statistics-label">同比提升</div>
                <div class="statistics-value">+3.2%</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="mb-20">
            <template #header>
              <div class="card-header">
                <span>证书持有率</span>
              </div>
            </template>
            <div class="statistics-container">
              <div class="statistics-item">
                <div class="statistics-label">持证率</div>
                <div class="statistics-value">92.5%</div>
              </div>
              <div class="statistics-item">
                <div class="statistics-label">未持证</div>
                <div class="statistics-value">7.5%</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <span>事故类型统计</span>
          </div>
        </template>
        <el-table :data="accidentData" border style="width: 100%">
          <el-table-column prop="type" label="事故类型" />
          <el-table-column prop="count" label="数量" />
          <el-table-column prop="percentage" label="占比" />
        </el-table>
      </el-card>

      <el-card>
        <template #header>
          <div class="card-header">
            <span>安全事故趋势分析</span>
          </div>
        </template>
        <el-table :data="trendData" border style="width: 100%">
          <el-table-column prop="month" label="月份" />
          <el-table-column prop="minor" label="轻微事故" />
          <el-table-column prop="normal" label="一般事故" />
          <el-table-column prop="serious" label="重大事故" />
          <el-table-column prop="total" label="合计" />
        </el-table>
      </el-card>
    </div>

    <!-- 详情弹窗 -->
    <DetailDialog
      v-model:visible="detailDialogVisible"
      :type="'personnel'"
      :detail-data="currentDetailData || {}"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import DetailDialog from '../dialogs/DetailDialog.vue'

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  department: ''
})

// 部门列表
const departments = [
  { label: '生产部', value: 'production' },
  { label: '安全部', value: 'safety' },
  { label: '设备部', value: 'equipment' },
  { label: '质量部', value: 'quality' }
]

// 事故类型数据
const accidentData = ref([
  { type: '操作失误', count: 12, percentage: '42.9%' },
  { type: '设备故障', count: 8, percentage: '28.6%' },
  { type: '违规作业', count: 5, percentage: '17.8%' },
  { type: '其他原因', count: 3, percentage: '10.7%' }
])

// 事故趋势数据
const trendData = ref([
  { month: '1月', minor: 5, normal: 2, serious: 0, total: 7 },
  { month: '2月', minor: 4, normal: 1, serious: 1, total: 6 },
  { month: '3月', minor: 6, normal: 3, serious: 0, total: 9 },
  { month: '4月', minor: 3, normal: 1, serious: 0, total: 4 },
  { month: '5月', minor: 2, normal: 0, serious: 0, total: 2 },
  { month: '6月', minor: 1, normal: 1, serious: 0, total: 2 }
])

// 弹窗控制
const detailDialogVisible = ref(false)
const currentDetailData = ref(null)

// 查询方法
const handleSearch = () => {
  // TODO: 实现查询逻辑
}

// 重置方法
const handleReset = () => {
  filterForm.dateRange = []
  filterForm.department = ''
}

// 详情方法
const handleDetail = (row: any) => {
  currentDetailData.value = row
  detailDialogVisible.value = true
}

const handleDetailClose = () => {
  detailDialogVisible.value = false
  currentDetailData.value = null
}
</script>

<style lang="scss" scoped>
.personnel-analysis {
  padding: 20px;

  .filter-section {
    margin-bottom: 20px;
  }

  .data-section {
    .mb-20 {
      margin-bottom: 20px;
    }
  }

  .statistics-container {
    display: flex;
    justify-content: space-around;
    padding: 20px 0;

    .statistics-item {
      text-align: center;

      .statistics-label {
        color: #666;
        margin-bottom: 8px;
      }

      .statistics-value {
        font-size: 24px;
        font-weight: bold;
        color: #409EFF;
      }
    }
  }
}
</style> 