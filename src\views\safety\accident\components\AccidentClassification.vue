<template>
  <div class="accident-classification">
    <div class="operation-bar">
      <el-button type="primary" @click="openAddDialog">新增分类</el-button>
      <el-button type="danger" :disabled="!selectedRows.length" @click="handleBatchDelete">批量删除</el-button>
    </div>
    
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      row-key="id"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="code" label="分类编码" min-width="120" />
      <el-table-column prop="name" label="分类名称" min-width="150" />
      <el-table-column prop="level" label="事故等级" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getTagType(row.level)">{{ getLevelLabel(row.level) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip />
      <el-table-column prop="standardRef" label="标准引用" min-width="150" show-overflow-tooltip />
      <el-table-column prop="createdTime" label="创建时间" min-width="160" />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 引入对话框组件 -->
    <add-classification-dialog
      v-model:visible="addDialogVisible"
      @success="handleAddSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import AddClassificationDialog from '../dialogs/AddClassificationDialog.vue'

// 对话框控制
const addDialogVisible = ref(false)

// 表格数据
const loading = ref(false)
const tableData = ref([])
const tableRef = ref()
const selectedRows = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 模拟数据
const mockData = [
  {
    id: '1',
    code: 'AC-001',
    name: '轻微事故',
    level: 'minor',
    description: '仅造成轻微财产损失，无人员伤亡',
    standardRef: 'GB/T 33000-2016',
    createdTime: '2023-05-15 10:30:45'
  },
  {
    id: '2',
    code: 'AC-002',
    name: '一般事故',
    level: 'normal',
    description: '造成少量财产损失，或者轻微人员受伤',
    standardRef: 'GB/T 33000-2016',
    createdTime: '2023-06-22 14:20:33'
  },
  {
    id: '3',
    code: 'AC-003',
    name: '重大事故',
    level: 'serious',
    description: '造成重大财产损失，或者人员重伤',
    standardRef: 'GB/T 33000-2016',
    createdTime: '2023-07-08 09:15:27'
  },
  {
    id: '4',
    code: 'AC-004',
    name: '特大事故',
    level: 'critical',
    description: '造成特大财产损失，或者人员死亡',
    standardRef: 'GB/T 33000-2016',
    createdTime: '2023-08-17 16:45:12'
  }
]

// 获取数据
const fetchData = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    tableData.value = mockData
    total.value = mockData.length
    loading.value = false
  }, 500)
}

// 获取事故等级标签类型
const getTagType = (level) => {
  const typeMap = {
    minor: 'info',
    normal: 'warning',
    serious: 'danger',
    critical: 'danger'
  }
  return typeMap[level] || 'info'
}

// 获取事故等级显示文本
const getLevelLabel = (level) => {
  const labelMap = {
    minor: '轻微',
    normal: '一般',
    serious: '重大',
    critical: '特大'
  }
  return labelMap[level] || '未知'
}

// 处理表格选择变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

// 新增分类
const openAddDialog = () => {
  addDialogVisible.value = true
}

// 处理新增成功
const handleAddSuccess = (data) => {
  // 在实际项目中，这里应该重新获取数据
  mockData.unshift({
    ...data,
    createdTime: new Date().toLocaleString()
  })
  fetchData()
  ElMessage.success(`成功添加分类: ${data.name}`)
}

// 编辑分类
const handleEdit = (row) => {
  ElMessage.info(`编辑分类: ${row.name}`)
  // 这里可以打开编辑分类对话框
}

// 删除分类
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除分类 ${row.name}?`, '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success(`已删除分类: ${row.name}`)
    // 这里可以调用删除API
    fetchData()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) return
  
  ElMessageBox.confirm(`确认删除选中的 ${selectedRows.value.length} 项分类?`, '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success(`已删除 ${selectedRows.value.length} 项分类`)
    // 这里可以调用批量删除API
    fetchData()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.accident-classification {
  .operation-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
    gap: 10px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 