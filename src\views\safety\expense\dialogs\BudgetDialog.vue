<template>
  <el-dialog
    :title="dialogTitle"
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    width="600px"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="预算年度" prop="year">
        <el-select v-model="form.year" placeholder="请选择年度">
          <el-option
            v-for="year in yearOptions"
            :key="year"
            :label="year + '年'"
            :value="year"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="费用类别" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="请选择费用类别">
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预算金额" prop="amount">
        <el-input-number
          v-model="form.amount"
          :min="0"
          :precision="2"
          :step="1000"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="分配方式" prop="distributionType">
        <el-radio-group v-model="form.distributionType">
          <el-radio label="average">平均分配</el-radio>
          <el-radio label="custom">自定义分配</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="form.distributionType === 'custom'">
        <el-form-item
          v-for="(month, index) in form.monthlyDistribution"
          :key="index"
          :label="(index + 1) + '月'"
          :prop="'monthlyDistribution.' + index + '.amount'"
          :rules="[
            { required: true, message: '请输入金额', trigger: 'blur' },
            { type: 'number', message: '请输入数字' }
          ]"
        >
          <el-input-number
            v-model="month.amount"
            :min="0"
            :precision="2"
            :step="100"
            style="width: 150px"
          />
        </el-form-item>
      </template>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'BudgetDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    type: {
      type: String,
      required: true,
      validator: (value) => ['add', 'edit'].includes(value)
    },
    formData: {
      type: Object,
      default: () => null
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      form: {
        year: new Date().getFullYear(),
        categoryId: null,
        amount: 0,
        distributionType: 'average',
        monthlyDistribution: Array(12).fill(null).map(() => ({ amount: 0 })),
        remark: ''
      },
      yearOptions: [],
      categoryOptions: [],
      rules: {
        year: [
          { required: true, message: '请选择预算年度', trigger: 'change' }
        ],
        categoryId: [
          { required: true, message: '请选择费用类别', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入预算金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '金额必须大于0', trigger: 'blur' }
        ],
        distributionType: [
          { required: true, message: '请选择分配方式', trigger: 'change' }
        ],
        remark: [
          { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.type === 'add' ? '新增预算' : '编辑预算'
    }
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.initYearOptions()
        this.loadCategoryOptions()
        if (this.formData) {
          this.form = { ...this.formData }
        }
      }
    },
    'form.distributionType'(val) {
      if (val === 'average') {
        this.distributeEvenly()
      }
    },
    'form.amount'(val) {
      if (this.form.distributionType === 'average') {
        this.distributeEvenly()
      }
    }
  },
  methods: {
    initYearOptions() {
      const currentYear = new Date().getFullYear()
      this.yearOptions = [
        currentYear - 1,
        currentYear,
        currentYear + 1
      ]
    },
    loadCategoryOptions() {
      // 加载费用类别选项
    },
    distributeEvenly() {
      const monthlyAmount = this.form.amount / 12
      this.form.monthlyDistribution = Array(12).fill(null).map(() => ({
        amount: Number(monthlyAmount.toFixed(2))
      }))
    },
    handleCancel() {
      this.$emit('update:modelValue', false)
    },
    async handleSubmit() {
      if (!this.$refs.formRef) return
      
      try {
        await this.$refs.formRef.validate()
        // 这里实现提交逻辑
        this.$emit('success')
      } catch (error) {
        // 表单验证失败
        return false
      }
    },
    resetForm() {
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
      this.form = {
        year: new Date().getFullYear(),
        categoryId: null,
        amount: 0,
        distributionType: 'average',
        monthlyDistribution: Array(12).fill(null).map(() => ({ amount: 0 })),
        remark: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 