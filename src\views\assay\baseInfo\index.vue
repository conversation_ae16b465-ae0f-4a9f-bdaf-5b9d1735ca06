<template>
  <ContentWrap title="基础信息管理">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="检测项目管理" name="testItem">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.testItem" class="search-form">
              <el-form-item label="项目类型">
                <el-select v-model="searchForm.testItem.categoryId" placeholder="请选择项目类型" clearable style="min-width: 10rem;">
                  <el-option label="水质检测" :value="1" />
                  <el-option label="污泥检测" :value="2" />
                  <el-option label="气体检测" :value="3" />
                  <el-option label="噪声检测" :value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="项目名称">
                <el-input v-model="searchForm.testItem.name" placeholder="请输入项目名称" clearable />
              </el-form-item>
              <el-form-item label="项目状态" >
                <el-select style="min-width: 8rem;" v-model="searchForm.testItem.isEnabled" placeholder="请选择状态" clearable>
                  <el-option label="启用" :value="true" />
                  <el-option label="停用" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch('testItem')">
                  <el-icon><Search /></el-icon>搜索
                </el-button>
                <el-button @click="resetSearch('testItem')">
                  <el-icon><Refresh /></el-icon>重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格操作栏 -->
          <div class="mb-4">
            <el-button type="primary" @click="handleAddCategory">
              <el-icon><Plus /></el-icon>新增项目类型
            </el-button>

          </div>
          <!-- 表格 -->
          <el-table
            v-loading="loading.testItem"
            :data="tableData.testItem"
            border
            style="width: 100%"
            row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :expand-row-keys="expandedRows"
            :row-class-name="getRowClassName"
            @expand-change="handleExpandChange"
          >
            <el-table-column prop="name" label="项目名称" min-width="20rem">
              <template #default="{ row }">
                <div class="flex items-center tree-node" :class="`tree-level-${getNodeLevel(row)}`">
                  <!-- 层级缩进 -->
                  <div :style="{ width: `${getNodeLevel(row) * 20}px` }" class="tree-indent"></div>

                  <!-- 图标和标签 -->
                  <div class="flex items-center">
                    <!-- 一级：项目类型 -->
                    <template v-if="row.type === 'category'">
                      <el-icon class="mr-2 text-blue-600" size="18">
                        <FolderOpened />
                      </el-icon>
                      <el-tag type="primary" size="small" class="mr-2">类型</el-tag>
                      <span class="font-bold text-blue-800 text-base">{{ row.name }}</span>
                    </template>

                    <!-- 二级：检测项目 -->
                    <template v-else-if="row.type === 'project'">
                      <el-icon class="mr-2 text-green-600" size="16">
                        <Document />
                      </el-icon>
                      <el-tag type="success" size="small" class="mr-2">项目</el-tag>
                      <span class="font-semibold text-green-700">{{ row.name }}</span>
                    </template>

                    <!-- 三级：检测指标 -->
                    <template v-else>
                      <el-icon class="mr-2 text-orange-600" size="14">
                        <DataLine />
                      </el-icon>
                      <el-tag type="warning" size="small" class="mr-2">指标</el-tag>
                      <span class="text-orange-700">{{ row.name }}</span>
                      <el-tag v-if="row.code" size="small" class="ml-2" type="info">{{ row.code }}</el-tag>
                    </template>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="code" label="编码" min-width="10rem">
              <template #default="{ row }">
                <span v-if="row.code" class="font-mono text-sm">{{ row.code }}</span>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column label="检测方法" min-width="12rem" show-overflow-tooltip>
              <template #default="{ row }">
                <template v-if="row.type === 'indicator'">
                  <div class="text-sm">
                    <div class="font-medium">{{ row.method }}</div>
                    <div class="text-gray-500 text-xs mt-1" v-if="row.methodPrinciple">
                      {{ row.methodPrinciple.substring(0, 30) }}...
                    </div>
                  </div>
                </template>
                <span v-else-if="row.type === 'project'" class="text-gray-500 text-sm">
                  {{ row.children?.length || 0 }} 个指标
                </span>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column label="单位/标准值" min-width="12rem">
              <template #default="{ row }">
                <template v-if="row.type === 'indicator'">
                  <div class="text-sm">
                    <el-tag size="small" type="info" class="mb-1">{{ row.unit }}</el-tag>
                    <div class="text-gray-600">
                      <span v-if="row.standardMin !== undefined && row.standardMax !== undefined">
                        {{ row.standardMin }} - {{ row.standardMax }}
                      </span>
                      <span v-else-if="row.standardMin !== undefined">
                        ≥ {{ row.standardMin }}
                      </span>
                      <span v-else-if="row.standardMax !== undefined">
                        ≤ {{ row.standardMax }}
                      </span>
                      <span v-else>-</span>
                    </div>
                  </div>
                </template>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column label="检测仪器/样品信息" min-width="15rem" show-overflow-tooltip>
              <template #default="{ row }">
                <template v-if="row.type === 'indicator'">
                  <div class="text-sm">
                    <div class="font-medium text-blue-600">{{ row.equipment }}</div>
                    <div class="text-gray-500 text-xs mt-1">
                      <span v-if="row.sampleVolume">样品量: {{ row.sampleVolume }}mL</span>
                      <span v-if="row.detectionTimeMinutes" class="ml-2">耗时: {{ row.detectionTimeMinutes }}min</span>
                    </div>
                    <div class="text-gray-500 text-xs mt-1" v-if="row.precisionLimit">
                      精度: {{ row.precisionLimit }}
                    </div>
                  </div>
                </template>
                <template v-else-if="row.type === 'project'">
                  <div class="text-sm text-gray-600">{{ row.description }}</div>
                </template>
                <template v-else>
                  <div class="text-sm text-gray-600">{{ row.description }}</div>
                </template>
              </template>
            </el-table-column>
            <el-table-column prop="isEnabled" label="状态" min-width="8rem">
              <template #default="{ row }">
                <el-tag v-if="row.isEnabled !== undefined" :type="row.isEnabled ? 'success' : 'danger'">
                  {{ row.isEnabled ? '启用' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="20rem" fixed="right">
              <template #default="{ row }">
                <template v-if="row.type === 'category'">
                  <el-button link type="primary" @click="handleAddProject(row)">
                    <el-icon><Plus /></el-icon>添加项目
                  </el-button>
                  <el-button link type="warning" @click="handleEditCategory(row)">编辑</el-button>
                  <el-button link type="danger" @click="handleDeleteCategory(row)">删除</el-button>
                </template>
                <template v-else-if="row.type === 'project'">
                  <el-button link type="primary" @click="handleAddIndicator(row)">
                    <el-icon><Plus /></el-icon>添加指标
                  </el-button>
                  <el-button link type="warning" @click="handleEditProject(row)">编辑</el-button>
                  <el-button link type="danger" @click="handleDeleteProject(row)">删除</el-button>
                </template>
                <template v-else>
                  <el-button link type="primary" @click="handleViewIndicator(row)">查看详情</el-button>
                  <el-button link type="warning" @click="handleEditIndicator(row)">编辑</el-button>
                  <el-button link type="danger" @click="handleDeleteIndicator(row)">删除</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.testItem.current"
              v-model:page-size="pagination.testItem.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.testItem.total"
              @size-change="handleSizeChange('testItem')"
              @current-change="handleCurrentChange('testItem')"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="采样点管理" name="samplingPoint">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.samplingPoint" class="search-form">
              <el-form-item label="采样点名称">
                <el-input v-model="searchForm.samplingPoint.name" placeholder="请输入采样点名称" clearable />
              </el-form-item>
              <el-form-item label="采样点类型">
                <el-select style="min-width: 8rem;" v-model="searchForm.samplingPoint.type" placeholder="请选择类型" clearable>
                  <el-option label="进水口" value="inlet" />
                  <el-option label="出水口" value="outlet" />
                  <el-option label="处理单元" value="process" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch('samplingPoint')">
                  <el-icon><Search /></el-icon>搜索
                </el-button>
                <el-button @click="resetSearch('samplingPoint')">
                  <el-icon><Refresh /></el-icon>重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格操作栏 -->
          <div class="mb-4">
            <el-button type="primary" @click="handleAdd('samplingPoint')">
              <el-icon><Plus /></el-icon>新增采样点
            </el-button>
          </div>
          <!-- 表格 -->
          <el-table v-loading="loading.samplingPoint" :data="tableData.samplingPoint" border style="width: 100%">
            <el-table-column prop="id" label="采样点编号" min-width="8rem" />
            <el-table-column prop="name" label="采样点名称" min-width="12rem" />
            <el-table-column prop="code" label="采样点代码" min-width="8rem" />
            <el-table-column prop="type" label="采样点类型" min-width="8rem">
              <template #default="{ row }">
                <el-tag :type="row.type === 'inlet' ? 'primary' : row.type === 'outlet' ? 'success' : 'warning'">
                  {{ row.type === 'inlet' ? '进水口' : row.type === 'outlet' ? '出水口' : '处理单元' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="location" label="位置描述" min-width="12rem" show-overflow-tooltip />
            <el-table-column label="管理人" min-width="8rem">
              <template #default="{ row }">
                <span v-if="row.managerId">{{ getManagerName(row.managerId) }}</span>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="isEnabled" label="状态" min-width="8rem">
              <template #default="{ row }">
                <el-tag v-if="row.isEnabled !== undefined" :type="row.isEnabled ? 'success' : 'danger'">
                  {{ row.isEnabled ? '启用' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="15rem" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleEdit('samplingPoint', row)">编辑</el-button>
                <el-button link type="danger" @click="handleDelete('samplingPoint', row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.samplingPoint.current"
              v-model:page-size="pagination.samplingPoint.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.samplingPoint.total"
              @size-change="handleSizeChange('samplingPoint')"
              @current-change="handleCurrentChange('samplingPoint')"
            />
          </div>
        </el-card>
      </el-tab-pane>


    </el-tabs>
  </ContentWrap>

  <!-- 项目类型对话框 -->
  <TestCategoryDialog
    ref="testCategoryDialogRef"
    @success="refreshTable('testItem')"
  />

  <!-- 检测项目对话框 -->
  <TestProjectDialog
    ref="testProjectDialogRef"
    @success="refreshTable('testItem')"
  />

  <!-- 检测指标对话框 -->
  <TestIndicatorDialog
    ref="testIndicatorDialogRef"
    @success="refreshTable('testItem')"
  />

  <!-- 检测项目对话框（旧版） -->
  <TestItemDialog
    ref="testItemDialogRef"
    @success="refreshTable('testItem')"
  />

  <!-- 采样点对话框 -->
  <SamplingDialog
    ref="samplingDialogRef"
    @success="refreshTable('samplingPoint')"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { Search, Refresh, Plus, Document, DataLine, FolderOpened } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { SystemFactoryApi } from '@/api/system/factory'
import { AssayBasicApi, type TestTreeNodeVO, type SamplingPointVO } from '@/api/assay/basic'
import TestCategoryDialog from './components/TestCategoryDialog.vue'
import TestProjectDialog from './components/TestProjectDialog.vue'
import TestIndicatorDialog from './components/TestIndicatorDialog.vue'
import TestItemDialog from './components/TestItemDialog.vue'
import SamplingDialog from './components/SamplingDialog.vue'

defineOptions({ name: 'AssayBaseInfo' })

// 类型定义已在API文件中定义，这里不需要重复定义

// 当前激活的标签页
const activeTab = ref('testItem')

// 当前选择的水厂ID
const currentFactoryId = ref<number | null>(null)

// 加载状态
const loading = reactive({
  testItem: false,
  samplingPoint: false
})

// 搜索表单
const searchForm = reactive({
  testItem: {
    categoryId: '',
    name: '',
    isEnabled: ''
  },
  samplingPoint: {
    name: '',
    type: '',
    isEnabled: ''
  }
})

// 分页配置
const pagination = reactive({
  testItem: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  samplingPoint: {
    current: 1,
    pageSize: 10,
    total: 0
  }
})

// 表格数据
const tableData = reactive({
  testItem: [] as TestTreeNodeVO[],
  samplingPoint: [] as SamplingPointVO[]
})

// 展开的行
const expandedRows = ref<string[]>([])

// 对话框引用
const testCategoryDialogRef = ref()
const testProjectDialogRef = ref()
const testIndicatorDialogRef = ref()
const testItemDialogRef = ref()
const samplingDialogRef = ref()

// 生命周期钩子
onMounted(async () => {
  await fetchFactoryId()
  fetchTableData('testItem')
})

// 获取用户有权限的水厂ID
const fetchFactoryId = async () => {
  try {
    console.log('开始获取用户有权限的水厂列表...')

    // 调用真实的API接口
    const res = await SystemFactoryApi.treeFactoryByModuleCodeAndRoleCode({
      moduleCode: "assay", // 化验模块
      roleCode: "manager"  // 管理员角色
    })

    console.log('获取到的水厂数据:', res)

    if (res && res.data && res.data.length > 0) {
      // 查找第一个level=3的水厂（实际的污水处理厂）
      const findFirstLevel3Factory = (factories: any[]): any => {
        for (const factory of factories) {
          if (factory.level === 3) {
            return factory
          }
          if (factory.children && factory.children.length > 0) {
            const found = findFirstLevel3Factory(factory.children)
            if (found) return found
          }
        }
        return null
      }

      const firstFactory = findFirstLevel3Factory(res.data)
      if (firstFactory) {
        currentFactoryId.value = firstFactory.id
        console.log('✅ 成功获取水厂ID:', currentFactoryId.value)
        console.log('✅ 水厂信息:', firstFactory)
      } else {
        // 如果没有level=3的水厂，取第一个有效的水厂
        currentFactoryId.value = res.data[0].id
        console.log('⚠️ 未找到level=3水厂，使用第一个水厂ID:', currentFactoryId.value)
      }
    } else {
      console.warn('⚠️ 未获取到任何水厂数据')
      ElMessage.warning('未获取到水厂权限，请联系管理员')
    }
  } catch (error) {
    console.error('❌ 获取水厂信息失败:', error)
    ElMessage.error('获取水厂信息失败')
  }
}

// 获取管理人姓名
const getManagerName = (managerId: number) => {
  const managers = {
    1001: '张三',
    1002: '李四',
    1003: '王五',
    1004: '赵六'
  }
  return managers[managerId] || '未知'
}

// 监听标签页切换
watch(activeTab, (newVal) => {
  fetchTableData(newVal)
})

// 获取表格数据
const fetchTableData = async (type: string) => {
  if (!currentFactoryId.value) {
    console.warn('⚠️ 水厂ID未获取，无法加载数据')
    return
  }

  loading[type] = true
  try {
    if (type === 'testItem') {
      // 调用API获取检测项目树形数据
      const params = {
        factoryId: currentFactoryId.value,
        pageNo: pagination.testItem.current,
        pageSize: pagination.testItem.pageSize,
        categoryId: searchForm.testItem.categoryId ? Number(searchForm.testItem.categoryId) : undefined,
        name: searchForm.testItem.name || undefined,
        isEnabled: searchForm.testItem.isEnabled !== '' ? Boolean(searchForm.testItem.isEnabled) : undefined
      }

      console.log('🔄 正在获取检测项目数据...', params)
      const res = await AssayBasicApi.getTestIndicatorTree(params)

      if (res && res.list) {
        Object.assign(tableData, { testItem: res.list })
        pagination.testItem.total = res.total || 0
        console.log('✅ 检测项目数据获取成功:', res)
      } else {
        console.warn('⚠️ 检测项目数据格式异常:', res)
        Object.assign(tableData, { testItem: [] })
        pagination.testItem.total = 0
      }
    } else if (type === 'samplingPoint') {
      // 调用API获取采样点分页数据
      const params = {
        factoryId: currentFactoryId.value,
        pageNo: pagination.samplingPoint.current,
        pageSize: pagination.samplingPoint.pageSize,
        name: searchForm.samplingPoint.name || undefined,
        type: searchForm.samplingPoint.type || undefined,
        isEnabled: searchForm.samplingPoint.isEnabled !== '' ? Boolean(searchForm.samplingPoint.isEnabled) : undefined
      }

      console.log('🔄 正在获取采样点数据...', params)
      const res = await AssayBasicApi.getSamplingPointPage(params)

      if (res && res.list) {
        Object.assign(tableData, { samplingPoint: res.list })
        pagination.samplingPoint.total = res.total || 0
        console.log('✅ 采样点数据获取成功:', res)
      } else {
        console.warn('⚠️ 采样点数据格式异常:', res)
        Object.assign(tableData, { samplingPoint: [] })
        pagination.samplingPoint.total = 0
      }
    }
  } catch (error) {
    console.error(`❌ 获取${type}数据失败:`, error)
    ElMessage.error(`获取${type === 'testItem' ? '检测项目' : '采样点'}数据失败`)
    tableData[type] = []
    pagination[type].total = 0
  } finally {
    loading[type] = false
  }
}



// 搜索
const handleSearch = (type: string) => {
  pagination[type].current = 1
  fetchTableData(type)
}

// 重置搜索
const resetSearch = (type: string) => {
  if (type === 'testItem') {
    searchForm.testItem.categoryId = ''
    searchForm.testItem.name = ''
    searchForm.testItem.isEnabled = ''
  } else if (type === 'samplingPoint') {
    searchForm.samplingPoint.name = ''
    searchForm.samplingPoint.type = ''
    searchForm.samplingPoint.isEnabled = ''
  }
  handleSearch(type)
}

// 刷新表格
const refreshTable = (type: string) => {
  fetchTableData(type)
}

// 添加
const handleAdd = (type: string) => {
  if (!currentFactoryId.value) {
    ElMessage.error('水厂ID未获取，无法新增')
    return
  }

  if (type === 'testItem') {
    testItemDialogRef.value.open('create', null, currentFactoryId.value)
  } else if (type === 'samplingPoint') {
    samplingDialogRef.value.open('create', null, currentFactoryId.value)
  }
}

// 编辑
const handleEdit = (type: string, row: any) => {
  if (!currentFactoryId.value) {
    ElMessage.error('水厂ID未获取，无法编辑')
    return
  }

  if (type === 'testItem') {
    testItemDialogRef.value.open('update', row, currentFactoryId.value)
  } else if (type === 'samplingPoint') {
    samplingDialogRef.value.open('update', row, currentFactoryId.value)
  }
}

// 删除
const handleDelete = async (type: string, row: any) => {
  if (!currentFactoryId.value) {
    ElMessage.error('水厂ID未获取，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除该${type === 'testItem' ? '检测项目' : '采样点'}吗?`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (type === 'samplingPoint') {
      await AssayBasicApi.deleteSamplingPoint(row.id, currentFactoryId.value)
      ElMessage.success('删除成功')
      fetchTableData(type)
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 分页大小变化
const handleSizeChange = (type: string) => {
  fetchTableData(type)
}

// 页码变化
const handleCurrentChange = (type: string) => {
  fetchTableData(type)
}

// 展开/收起行
const handleExpandChange = (row: any, expandedRows: any[]) => {
  console.log('展开状态变化:', row, expandedRows)
}

// 获取节点层级
const getNodeLevel = (row: any) => {
  if (row.type === 'category') return 0      // 一级：项目类型
  if (row.type === 'project') return 1       // 二级：检测项目
  if (row.type === 'indicator') return 2     // 三级：检测指标
  return 0
}

// 获取表格行的CSS类名
const getRowClassName = ({ row }: { row: any }) => {
  return `tree-level-${getNodeLevel(row)}`
}

// 新增项目类型
const handleAddCategory = () => {
  if (!currentFactoryId.value) {
    ElMessage.error('水厂ID未获取，无法新增')
    return
  }
  testCategoryDialogRef.value.open('create', null, currentFactoryId.value)
}

// 编辑项目类型
const handleEditCategory = (row: any) => {
  if (!currentFactoryId.value) {
    ElMessage.error('水厂ID未获取，无法编辑')
    return
  }
  testCategoryDialogRef.value.open('update', row, currentFactoryId.value)
}

// 删除项目类型
const handleDeleteCategory = async (row: any) => {
  if (!currentFactoryId.value) {
    ElMessage.error('水厂ID未获取，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除项目类型"${row.name}"吗？删除后该类型下的所有项目和指标都将被删除！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await AssayBasicApi.deleteTestCategory(row.id, currentFactoryId.value)
    ElMessage.success('删除成功')
    fetchTableData('testItem')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除项目类型失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 添加检测项目
const handleAddProject = (category: any) => {
  if (!currentFactoryId.value) {
    ElMessage.error('水厂ID未获取，无法新增')
    return
  }
  testProjectDialogRef.value.open('create', { categoryId: category.id }, currentFactoryId.value)
}

// 编辑检测项目
const handleEditProject = (row: any) => {
  testProjectDialogRef.value.open('update', row)
}

// 删除检测项目
const handleDeleteProject = async (row: any) => {
  if (!currentFactoryId.value) {
    ElMessage.error('水厂ID未获取，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除检测项目"${row.name}"吗？删除后该项目下的所有指标都将被删除！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await AssayBasicApi.deleteTestProject(row.id, currentFactoryId.value)
    ElMessage.success('删除成功')
    fetchTableData('testItem')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除检测项目失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 添加检测指标
const handleAddIndicator = (project: any) => {
  testIndicatorDialogRef.value.open('create', null, project.id)
}

// 查看指标详情
const handleViewIndicator = (row: any) => {
  testIndicatorDialogRef.value.open('view', row)
}

// 编辑检测指标
const handleEditIndicator = (row: any) => {
  testIndicatorDialogRef.value.open('update', row)
}

// 删除检测指标
const handleDeleteIndicator = async (row: any) => {
  if (!currentFactoryId.value) {
    ElMessage.error('水厂ID未获取，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除检测指标"${row.name}"吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await AssayBasicApi.deleteTestIndicator(row.id, currentFactoryId.value)
    ElMessage.success('删除成功')
    fetchTableData('testItem')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除检测指标失败:', error)
      ElMessage.error('删除失败')
    }
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 1rem;
}

/* 树形结构样式 */
.tree-node {
  position: relative;
}

.tree-indent {
  flex-shrink: 0;
}

/* 不同层级的背景色 */
:deep(.el-table__row) {
  &.tree-level-0 {
    background-color: #f8faff;
  }

  &.tree-level-1 {
    background-color: #f0f9f0;
  }

  &.tree-level-2 {
    background-color: #fff7e6;
  }
}

/* 悬停效果 */
:deep(.el-table__row:hover) {
  &.tree-level-0 {
    background-color: #e6f0ff !important;
  }

  &.tree-level-1 {
    background-color: #e6f7e6 !important;
  }

  &.tree-level-2 {
    background-color: #fff2d9 !important;
  }
}

/* 树形连接线 */
.tree-node::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 50%;
  width: 1px;
  background-color: #dcdfe6;
}

.tree-level-0 .tree-node::before {
  display: none;
}

.tree-level-1 .tree-node::before {
  left: 20px;
}

.tree-level-2 .tree-node::before {
  left: 40px;
}

/* 水平连接线 */
.tree-node::after {
  content: '';
  position: absolute;
  left: 10px;
  top: 50%;
  width: 10px;
  height: 1px;
  background-color: #dcdfe6;
}

.tree-level-0 .tree-node::after {
  display: none;
}

.tree-level-1 .tree-node::after {
  left: 20px;
}

.tree-level-2 .tree-node::after {
  left: 40px;
}

/* 标签样式优化 */
.el-tag {
  font-weight: 500;
  border-radius: 4px;
}

/* 字体大小层级 */
.tree-level-0 span {
  font-size: 16px;
}

.tree-level-1 span {
  font-size: 14px;
}

.tree-level-2 span {
  font-size: 13px;
}
</style>