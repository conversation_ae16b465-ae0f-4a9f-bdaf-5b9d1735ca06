<template>
  <div class="app-container">
    <el-card class="box-card" v-loading="loading" element-loading-text="数据加载中，请稍候...">
      <template #header>
        <div class="card-header">
          <div class="header-title">数据分析中心</div>
          <div class="header-controls">
            <el-button type="primary" @click="openConfig" class="flex items-center gap-2">
              <el-icon>
                <Setting />
              </el-icon>
              配置分析面板
            </el-button>
          </div>
        </div>
      </template>

      <div class="mb-4">
        <el-card shadow="hover" class="w-full mb-4">
          <template #header>
            <div class="flex justify-between items-center w-full">
              <div class="flex items-center">
                <span class="font-medium text-lg">数据趋势分析</span>
                <el-tooltip placement="right" effect="light">
                  <template #content>
                    <div class="text-xs text-gray-700 leading-relaxed min-w-[220px]">
                      <div class="font-bold mb-1 flex items-center">
                        <el-icon class="mr-1"><QuestionFilled /></el-icon>
                        功能说明
                      </div>
                      <ul class="pl-4 list-disc space-y-1">
                        <li>同单位指标自动分组到同一Y轴，不同单位使用不同Y轴</li>
                        <li>支持X轴和Y轴缩放，鼠标滚轮或拖拽滑块</li>
                        <li>右侧面板可控制指标显示/隐藏</li>
                        <li>图例支持点击筛选，支持滚动查看更多指标</li>
                      </ul>
                    </div>
                  </template>
                  <el-icon class="ml-2 cursor-help text-gray-400">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
              <div class="flex items-center gap-4">
                <el-select v-model="selectedTheme" style="width: 120px">
                  <el-option v-for="theme in themeOptions" :key="theme.value" :label="theme.label"
                    :value="theme.value" />
                </el-select>
                <el-select v-model="selectedTrendMetrics" multiple style="width: 280px" collapse-tags>
                  <el-option v-for="item in currentSelectedMetricOptions" :key="item.value" :label="item.label"
                    :value="item.value">
                    <span>{{ item.label }}</span>
                    <span class="text-xs text-gray-400 ml-1">{{ item.unit }}</span>
                  </el-option>
                </el-select>
                <el-date-picker v-model="trendDateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                  end-placeholder="结束日期" :shortcuts="dateShortcuts" style="width: 400px" />
                <el-button @click="resetZoom"  class="flex items-center gap-1">
                  <el-icon><Refresh /></el-icon>
                  重置缩放
                </el-button>
                <el-select v-model="selectedYAxisIndex" style="width: 120px; margin-left: 16px;" :placeholder="'Y轴缩放单位'">
                  <el-option
                    v-for="(group, idx) in unitGroupsForSelect"
                    :key="group.unit"
                    :label="group.label"
                    :value="idx"
                  />
                </el-select>
              </div>
            </div>
          </template>
          
          <div class="relative">
            <div v-if="noData" class="absolute inset-0 flex items-center justify-center">
              <el-empty description="该厂站暂无数据" :image-size="100">
                <template #image>
                  <el-icon class="text-gray-400" style="font-size: 40px;">
                    <DataLine />
                  </el-icon>
                </template>
              </el-empty>
            </div>
            <div ref="trendChartRef" class="w-full min-h-[600px] max-h-[1000px]" style="height:auto;"></div>
            
            <!-- 图例控制面板 -->
            <div v-if="false" class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg border">
              <div class="text-sm font-medium text-gray-700 mb-2">指标显示控制</div>
              <div class="space-y-1 max-h-32 overflow-y-auto">
                <div v-for="metric in currentSelectedMetricOptions.filter(m => selectedTrendMetrics.includes(m.value))" 
                     :key="metric.value" 
                     class="flex items-center gap-2 text-xs">
                  <el-checkbox 
                    :model-value="visibleMetrics.includes(metric.value)"
                    @change="(checked: any) => toggleMetricVisibility(metric.value, !!checked)"
                    size="small">
                    <span class="flex items-center">
                      <span class="w-3 h-3 rounded-full mr-2" 
                            :style="{ backgroundColor: getMetricColor(metric.value) }"></span>
                      {{ metric.label }}
                    </span>
                  </el-checkbox>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <template v-if="!noData">
        <el-card shadow="hover" class="mb-4">
          <template #header>
            <div class="flex justify-between items-center w-full">
              <div class="flex items-center">
                <span class="font-medium text-lg">历史数据分析</span>
                <el-tooltip content="展示历史数据及变化趋势，支持筛选和搜索，点击卡片查看详情分析" placement="top">
                  <el-icon class="ml-2 cursor-help text-gray-400">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
              <div class="flex items-center gap-2">
                <el-input v-model="searchKeyword" placeholder="搜索指标" prefix-icon="Search" clearable class="w-[200px]" />
              </div>
            </div>
          </template>

          <div class="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            <template v-for="theme in availableItems" :key="theme.themeName">
              <div v-if="filteredWaterQualityData.filter(item => item.themeName === theme.themeName).length > 0"
                class="col-span-full">
                <div class="text-lg font-medium text-gray-700 mb-3 px-2">{{ theme.themeName }}</div>
                <div class="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                  <el-card v-for="item in filteredWaterQualityData.filter(item => item.themeName === theme.themeName)"
                    :key="item.id" shadow="never" class="cursor-pointer relative overflow-hidden min-h-[120px] pb-2"
                    :class="getBorderClass(item.status)" @click="showItemDetail(item)">
                    <div class="absolute top-0 right-0 w-1 h-full" :class="getStatusBgClass(item.status)"></div>
                    <div class="flex justify-between items-center mb-2">
                      <span class="font-medium">{{ item.indicatorName }}</span>
<!--                      <el-tag :type="getTagType(item.status)" size="small">{{ item.statusText }}</el-tag>-->
                    </div>
                    <div class="text-3xl font-bold my-2">
                      {{ formatValue(item.value) }}
                      <span class="text-sm font-normal text-gray-500">{{ item.unit }}</span>
                    </div>
                    <div class="flex justify-between items-center mt-2">
                      <div class="text-xs text-gray-500">
                        <span>标准范围: {{ item.standardRange || '暂无' }}</span>
                      </div>
                      <div class="flex items-center">
                        <span class="text-xs text-gray-500 mr-2">{{ item.updateTime }}</span>
                      </div>
                    </div>
                  </el-card>
                </div>
              </div>
            </template>
          </div>
        </el-card>
      </template>
    </el-card>

    <el-dialog v-model="itemDetailVisible" :title="`${currentItem?.indicatorName || ''} 指标详细分析`" width="800px">
      <template v-if="currentItem">
        <div class="p-4">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="指标名称">{{ currentItem.indicatorName }}</el-descriptions-item>
            <el-descriptions-item label="当前值">
              <span class="font-bold" :class="getTextColorClass(currentItem.status)">
                {{ formatValue(currentItem.value) }} {{ currentItem.unit }}
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="标准范围">{{
              currentItem.standardRange
            }}
            </el-descriptions-item>
<!--            <el-descriptions-item label="状态">-->
<!--              <el-tag :type="getTagType(currentItem.status)">{{ currentItem.statusText }}</el-tag>-->
<!--            </el-descriptions-item>-->
            <el-descriptions-item label="更新时间">{{
              currentItem.updateTime
            }}
            </el-descriptions-item>
          </el-descriptions>

          <div class="mt-6">
            <div class="font-medium mb-2">历史趋势分析 (近24小时)</div>
            <div class="h-[200px]" ref="detailChartRef"></div>
          </div>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="configVisible" title="分析面板配置" width="650px">
      <el-tabs v-model="configTab">
        <el-tab-pane label="指标选择" name="metrics">
          <el-form :model="configForm" class="space-y-4">
            <p class="text-gray-500 mb-2">选择需要在分析面板上显示的监测指标：</p>
            <div class="space-y-6">
              <div v-for="theme in availableItems" :key="theme.themeName" class="border rounded-lg p-4">
                <div class="font-medium text-gray-700 mb-3">{{ theme.themeName }}</div>
                <el-checkbox-group v-model="tempSelectedItems" class="grid grid-cols-3 gap-3">
                  <el-checkbox v-for="item in theme.indicatorList" :key="item.id" :label="item.id" class="flex items-center"
                    border>
                    <span class="flex items-center">
                      <span>{{ item.indicatorName }}</span>
                      <span class="text-xs text-gray-400 ml-1">{{ item.unit }}</span>
                    </span>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="flex justify-end gap-4">
          <el-button @click="configVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfig">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { useECharts } from '@/hooks/web/useEChart'
import { useAppStore } from '@/store/modules/app'
import {
  ArrowDown,
  ArrowUp,
  Minus,
  QuestionFilled,
  Refresh,
  Setting,
  DataLine
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, watch, onUnmounted, watchEffect } from 'vue'
import { DashBoardApi } from '@/api/monitor/dashboard'
import { debounce } from 'lodash-es'

interface DataBoardSearchReqDTO {
  factoryId?: string;
  factoryCode?: string;
  pointCodeList: string[];
  startTime?: string;
  endTime?: string;
}

interface IndicatorTrendReqDTO {
  factoryId?: string;
  factoryCode?: string;
  indicatorCode: string;
}

interface MetricItem {
  id: string
  indicatorName: string
  indicatorCode: string
  unit: string
  standardRange: string
  defaultShow: number
  themeName?: string
}

interface ThemeGroup {
  themeName: string
  indicatorList: MetricItem[]
}

interface LatestDataResponse {
  code: number
  data: Array<{
    indicatorCode: string
    indicatorName: string
    lastValue: string
    lastTime: number
  }>
  msg: string
}

const appStore = useAppStore()
const currentFactory = computed(() => appStore.getCurrentStation)

const trendChartRef = ref<HTMLDivElement | null>(null)
const detailChartRef = ref<HTMLDivElement | null>(null)
const { setOption: setTrendOption, getInstance: getTrendInstance } = useECharts(trendChartRef)
const { setOption: setDetailOption } = useECharts(detailChartRef)

const availableItems = ref<ThemeGroup[]>([])

const configVisible = ref(false)
const configTab = ref('metrics')
const configForm = reactive({
  selectedItems: [] as string[]
})
const tempSelectedItems = ref<string[]>([])

const trendDateRange = ref<[Date, Date]>([
  dayjs().subtract(7, 'day').toDate(),
  dayjs().toDate()
])

const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

const themeOptions = computed(() => {
  const options = availableItems.value.map(theme => ({
    label: theme.themeName,
    value: theme.themeName
  }))
  return options
})

const selectedTheme = ref('')

const selectedTrendMetrics = ref<string[]>([])

const currentSelectedMetricOptions = computed(() => {
  const theme = availableItems.value.find(t => t.themeName === selectedTheme.value)
  if (!theme) return []
  return theme.indicatorList
    .filter(item => configForm.selectedItems.includes(item.id))
    .map(item => ({ label: item.indicatorName, value: item.id, unit: item.unit }))
})

const debouncedInitTrendChart = debounce(() => {
  if (selectedTrendMetrics.value.length > 0 && !isChartInitializing.value) {
    initTrendChart()
  }
}, 300)

const searchKeyword = ref('')
const filterStatus = ref('')

const waterQualityData = ref<any[]>([])

const filteredWaterQualityData = computed(() => {
  return waterQualityData.value.filter(item => {
    const matchesKeyword = searchKeyword.value === '' ||
      item.indicatorName.toLowerCase().includes(searchKeyword.value.toLowerCase())
    const matchesStatus = filterStatus.value === '' ||
      item.status === filterStatus.value
    return matchesKeyword && matchesStatus
  })
})

const getTagType = (status: string) => {
  return status as 'success' | 'warning' | 'info' | 'primary' | 'danger'
}

const getStatusBgClass = (status: string) => {
  switch (status) {
    case 'success':
      return 'bg-green-500'
    case 'warning':
      return 'bg-yellow-500'
    case 'danger':
      return 'bg-red-500'
    case 'info':
      return 'bg-blue-500'
    default:
      return 'bg-gray-500'
  }
}

const getBorderClass = (status: string) => {
  switch (status) {
    case 'success':
      return 'border-2 border-green-500'
    case 'warning':
      return 'border-2 border-yellow-500'
    case 'danger':
      return 'border-2 border-red-500'
    case 'info':
      return 'border-2 border-blue-500'
    default:
      return 'border-2 border-gray-500'
  }
}

const itemDetailVisible = ref(false)
const currentItem = ref<any>(null)

const showItemDetail = (item: any) => {
  currentItem.value = item
  itemDetailVisible.value = true

  nextTick(() => {
    initDetailChart(item)
  })
}

const initDetailChart = async (item: any) => {
  try {
    if (!item.indicatorCode) {
      ElMessage.warning('指标编码不存在')
      return
    }

    const data = {
      factoryId: currentFactory.value?.id,
      factoryCode: currentFactory.value?.code,
      pointCodeList: [],
      indicatorCode: item.indicatorCode
    }

    const res = await DashBoardApi.singlePointTrendData24h(data) as any
    if (!res.data || res.data.length === 0) {
      ElMessage.warning('暂无历史数据')
      return
    }

    const timePoints = res.data.map((point: any) => {
      return dayjs(point.time).format('HH:mm')
    })

    const values = res.data.map((point: any) => formatValue(point.value))

    let color
    switch (item.id) {
      case '1':
        color = '#8b5cf6'
        break
      case '2':
        color = '#3b82f6'
        break
      case '3':
        color = '#10b981'
        break
      case '4':
        color = '#f59e0b'
        break
      case '5':
        color = '#ec4899'
        break
      case '6':
        color = '#06b6d4'
        break
      case '7':
        color = '#6366f1'
        break
      default:
        color = '#8b5cf6'
    }

    const option: any = {
      color: [color],
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#fff',
        borderColor: '#e0e0e0',
        borderWidth: 1,
        padding: [8, 12],
        textStyle: { color: '#222', fontWeight: 500 },
        formatter: function (params: any) {
          const val = params[0].value;
          return `${params[0].name}<br/>${params[0].seriesName}：${formatValue(val)} ${item.unit}`
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: timePoints,
        axisLabel: {
          fontSize: 10
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value: any) => isFinite(Number(value)) && value !== '' ? `${formatValue(value)}${item.unit}` : `${value}${item.unit}`
        }
      },
      series: [
        {
          name: item.indicatorName,
          type: 'line',
          data: values,
          smooth: true,
          showSymbol: false,
          lineStyle: {
            width: 3,
            color: color
          },
          areaStyle: {
            opacity: 0.2,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: color },
                { offset: 1, color: 'rgba(255,255,255,0.1)' }
              ]
            }
          }
        }
      ]
    }

    setDetailOption(option)
  } catch (error) {
    console.error('Failed to fetch historical data:', error)
    ElMessage.error('Failed to fetch historical data')
  }
}

const getTextColorClass = (status: string) => {
  switch (status) {
    case 'success':
      return 'text-green-600'
    case 'warning':
      return 'text-yellow-600'
    case 'danger':
      return 'text-red-600'
    case 'info':
      return 'text-blue-600'
    default:
      return 'text-gray-600'
  }
}

const isChartInitializing = ref(false)

const loading = ref(false)
const noData = ref(false)

const visibleMetrics = ref<string[]>([])

const selectedYAxisIndex = ref(0)

const getFactoryIndicators = async () => {
  if (!currentFactory.value?.id) {
    handleNoData()
    return
  }

  loading.value = true
  try {
    const res = await DashBoardApi.getFactoryMetrics(currentFactory.value.id) as any
    if (res.code === 0 && Array.isArray(res.data) && res.data.length > 0) {
      availableItems.value = res.data
      noData.value = false

      // 1. 所有主题下 defaultShow 为 1 的指标都默认选中
      configForm.selectedItems = availableItems.value.flatMap(theme =>
        theme.indicatorList.filter(item => item.defaultShow === 1).map(item => item.id)
      )

      // 2. 当前主题下 defaultShow 为 1 的第一个指标用于趋势分析
      if (availableItems.value.length > 0) {
        const firstTheme = availableItems.value[0]
        selectedTheme.value = firstTheme.themeName
        const currentThemeDefault = firstTheme.indicatorList.filter(item => item.defaultShow === 1)
        selectedTrendMetrics.value = currentThemeDefault.length > 0
          ? [currentThemeDefault[0].id]
          : []
      } else {
        selectedTrendMetrics.value = []
      }

      waterQualityData.value = availableItems.value.flatMap(theme =>
        theme.indicatorList.filter(item => configForm.selectedItems.includes(item.id)).map(item => ({
          ...item,
          themeName: theme.themeName,
          value: '0',
          status: 'success',
          statusText: '正常',
          updateTime: '刚刚',
          trend: 'stable',
          trendValue: ''
        }))
      )

      await fetchLatestData()

    } else {
      handleNoData()
    }
  } catch (error) {
    ElMessage.error('无法获取指标数据。请重试。')
    handleNoData()
  } finally {
    loading.value = false
  }
}

const fetchLatestData = async () => {
  if (!currentFactory.value?.code || waterQualityData.value.length === 0) return

  try {
    const data: DataBoardSearchReqDTO = {
      factoryId: currentFactory.value.id,
      factoryCode: currentFactory.value.code,
      pointCodeList: waterQualityData.value.map(item => item.indicatorCode)
    }

    const res = await DashBoardApi.queryPointLastData(data) as any
    if (res.data) {
      updateWaterQualityData(res.data)
    }
  } catch (error) {
    ElMessage.error('未能获取最新数据')
  }
}

const updateWaterQualityData = (latestData: any[]) => {
  const updatedData = waterQualityData.value.map(item => {
    const latest = latestData.find(d => d.indicatorCode === item.indicatorCode)
    if (latest) {
      let val = latest.lastValue || '';
      let numVal = formatValue(val);
      return {
        ...item,
        value: numVal,
        updateTime: latest.lastTime,
        status: 'success',
        statusText: '正常'
      }
    }
    return item
  })
  waterQualityData.value = [...updatedData]
}

const handleNoData = () => {
  waterQualityData.value = []
  selectedTrendMetrics.value = []
  configForm.selectedItems = []
  availableItems.value = []
  selectedTheme.value = ''
  noData.value = true

  nextTick(() => {
    const chart = getTrendInstance()
    if (chart) {
      chart.clear()
    }
  })
}

const initTrendChart = async () => {
  if (isChartInitializing.value || noData.value) return
  isChartInitializing.value = true

  try {
    const theme = availableItems.value.find(t => t.themeName === selectedTheme.value)
    if (!theme || selectedTrendMetrics.value.length === 0 || !trendChartRef.value) {
      isChartInitializing.value = false
      return
    }

    const selectedMetrics = theme.indicatorList.filter(item => selectedTrendMetrics.value.includes(item.id))
    if (selectedMetrics.length === 0) {
      isChartInitializing.value = false
      return
    }

    if (!currentFactory.value?.code) {
      isChartInitializing.value = false
      return
    }

    const data: DataBoardSearchReqDTO = {
      factoryId: currentFactory.value.id,
      factoryCode: currentFactory.value.code,
      pointCodeList: selectedMetrics.map(item => item.indicatorCode),
      startTime: dayjs(trendDateRange.value[0]).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(trendDateRange.value[1]).format('YYYY-MM-DD HH:mm:ss')
    }

    const res = await DashBoardApi.queryPointTrendData(data) as any
    if (!res.data) {
      isChartInitializing.value = false
      return
    }

    const chartData = processChartData(res.data, selectedMetrics)
    renderTrendChart(chartData)
  } catch (error) {
    ElMessage.error('未能获取最新数据')
  } finally {
    isChartInitializing.value = false
  }
}

const processChartData = (responseData: any, selectedMetrics: any[]) => {
  // 收集所有时间点（ISO字符串）
  let allTimes = new Set<string>()
  selectedMetrics.forEach(item => {
    const metricData = responseData?.[item.indicatorCode] || []
    metricData.forEach(point => allTimes.add(point.time))
  })
  const timePoints = Array.from(allTimes).sort()

  // 统一分组
  const unitGroups = groupMetricsByUnit(selectedMetrics)
  // 构建Y轴配置
  const yAxis = buildYAxisConfig(unitGroups)
  // 构建series（传递timePoints用于补全数据）
  const series = buildSeriesConfig(selectedMetrics, timePoints, responseData, unitGroups)

  return { timePoints, series, selectedMetrics, yAxis, unitGroups }
}

// 统一单位分组逻辑
function groupMetricsByUnit(metrics: any[]) {
  const unitMap = new Map<string, any[]>();
  metrics.forEach(metric => {
    const unit = getUnitKey(metric.unit);
    if (!unitMap.has(unit)) unitMap.set(unit, []);
    unitMap.get(unit)!.push(metric);
  });
  // 保证顺序稳定
  return Array.from(unitMap.entries()).map(([unit, items], index) => ({
    unit,
    label: unit === 'unknown' ? '无单位' : unit,
    items,
    groupIndex: index
  }));
}

// 构建Y轴配置
function buildYAxisConfig(unitGroups: any[]) {
  return unitGroups.map((group, index) => ({
    type: 'value',
    name: group.unit === 'unknown' ? '无单位' : group.unit,
    nameLocation: 'end',
    nameGap: 15,
    nameTextStyle: {
      color: '#000',
      fontSize: 14,
      fontWeight: 'bold',
      align: 'center',
      verticalAlign: 'bottom'
    },
    position: index % 2 === 0 ? 'left' : 'right',
    offset: Math.floor(index / 2) * 60,
    axisLine: { show: true, lineStyle: { color: '#000' } },
    axisLabel: {
      color: '#000',
      fontSize: 12,
      formatter: (value: any) => isFinite(Number(value)) && value !== '' ? formatValue(value) : value
    },
    splitLine: { show: index === 0, lineStyle: { color: 'rgba(0,0,0,0.05)' } },
    min: 'dataMin',
    max: 'dataMax'
  }));
}

// 构建series配置
function buildSeriesConfig(selectedMetrics: any[], timePoints: string[], responseData: any, unitGroups: any[]) {
  const colorList = ['#8b5cf6', '#3b82f6', '#10b981', '#f59e0b', '#ec4899', '#06b6d4', '#6366f1', '#67c23a', '#e6a23c', '#f56c6c'];
  return selectedMetrics.map((item, idx) => {
    const metricData = responseData?.[item.indicatorCode] || [];
    // 用Map方便查找
    const valueMap = new Map(metricData.map(p => [p.time, formatValue(p.value)]));
    // 正确格式：[[时间, 数值], ...]
    const data = timePoints.map(t => [t, valueMap.get(t) ?? null]);
    const unitKey = getUnitKey(item.unit);
    const unitGroup = unitGroups.find(group => group.unit === unitKey);
    const yAxisIndex = unitGroup ? unitGroup.groupIndex : 0;
    return {
      name: `${item.indicatorName} (${item.unit || '无单位'})`,
      type: 'line',
      yAxisIndex,
      data,
      smooth: true,
      showSymbol: false,
      symbolSize: 6,
      lineStyle: { width: 3, color: colorList[idx % colorList.length] },
      itemStyle: { borderWidth: 2, color: colorList[idx % colorList.length] },
      areaStyle: { opacity: 0.1, color: colorList[idx % colorList.length] },
      emphasis: { focus: 'series', itemStyle: { borderWidth: 3 } }
    };
  });
}

const renderTrendChart = ({ timePoints, series, selectedMetrics, yAxis, unitGroups }: any) => {
  if (!trendChartRef.value || noData.value) return

  const option = {
    color: ['#8b5cf6', '#3b82f6', '#10b981', '#f59e0b', '#ec4899', '#06b6d4', '#6366f1', '#67c23a', '#e6a23c', '#f56c6c'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      borderColor: '#e0e0e0',
      borderWidth: 1,
      padding: [8, 12],
      textStyle: { color: '#222', fontWeight: 500 },
      formatter: function (params: any) {
        let html = `<div style=\"font-weight:bold;margin-bottom:4px;\">${params[0].axisValueLabel || params[0].name}</div>`
        params.forEach((p: any) => {
          const val = Array.isArray(p.value) ? p.value[1] : p.value;
          const metric = selectedMetrics.find(m => `${m.indicatorName} (${m.unit})` === p.seriesName)
          html += `<div style=\"display:flex;align-items:center;justify-content:space-between;\">
            <span>${p.marker}${p.seriesName}：</span>
            <span style=\"font-weight:bold;\">${formatValue(val)} ${metric?.unit || ''}</span>
          </div>`
        })
        return html
      }
    },
    legend: {
      data: series.map((s: any) => s.name),
      type: 'scroll',
      bottom: 50,
      textStyle: { fontSize: 12 }
    },
    grid: { 
      left: '3%', 
      right: '4%', 
      bottom: 80, 
      top: '5%', 
      containLabel: true 
    },
    xAxis: {
      type: 'time',
      boundaryGap: false,
      axisLine: { lineStyle: { color: '#ccc' } },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        rotate: 0,
        formatter: (value: string) => {
          const date = new Date(value);
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hour = String(date.getHours()).padStart(2, '0');
          const minute = String(date.getMinutes()).padStart(2, '0');
          return `${month}-${day} ${hour}:${minute}`;
        }
      }
    },
    yAxis,
    dataZoom: [
      // 横向滚动条
      {
        type: 'slider',
        xAxisIndex: 0,
        bottom: 30,
        height: 20,
        borderColor: 'transparent',
        backgroundColor: '#f5f5f5',
        fillerColor: 'rgba(139, 92, 246, 0.1)',
        handleStyle: {
          color: '#8b5cf6',
          borderColor: '#8b5cf6'
        }
      },
      // 纵向滚动条
      {
        type: 'slider',
        yAxisIndex: selectedYAxisIndex.value,
        right: 5,
        width: 20,
        borderColor: 'transparent',
        backgroundColor: '#f5f5f5',
        fillerColor: 'rgba(139, 92, 246, 0.1)',
        handleStyle: {
          color: '#8b5cf6',
          borderColor: '#8b5cf6'
        }
      },
      // 横向鼠标缩放
      {
        type: 'inside',
        xAxisIndex: 0
      }
    ],
    series
  }

  const chart = getTrendInstance()
  if (chart) {
    chart.clear()
    setTrendOption(option)
  }
}

watch(() => appStore.getCurrentStation, async (newStation, oldStation) => {
  if (!newStation || (oldStation && newStation.id === oldStation.id)) return
  await getFactoryIndicators()
}, { immediate: true })

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const handleResize = () => {
  const chart = getTrendInstance()
  if (chart) {
    chart.resize()
  }
}

watch(selectedTheme, () => {
  const theme = availableItems.value.find(t => t.themeName === selectedTheme.value)
  if (theme && theme.indicatorList.length > 0) {
    const configuredMetricIdsForCurrentTheme = theme.indicatorList
      .filter(item => configForm.selectedItems.includes(item.id))
      .map(item => item.id);

    const currentSelectedInNewTheme = selectedTrendMetrics.value.filter(id => configuredMetricIdsForCurrentTheme.includes(id));

    if (currentSelectedInNewTheme.length > 0) {
      selectedTrendMetrics.value = currentSelectedInNewTheme;
    } else if (configuredMetricIdsForCurrentTheme.length > 0) {
      selectedTrendMetrics.value = [configuredMetricIdsForCurrentTheme[0]];
    } else {
      selectedTrendMetrics.value = [];
    }
  } else {
    selectedTrendMetrics.value = [];
  }
}, { immediate: false });

watch(selectedTrendMetrics, () => {
  // 初始化可见指标列表
  visibleMetrics.value = [...selectedTrendMetrics.value]
  
  nextTick(() => {
    debouncedInitTrendChart()
  })
}, { immediate: true })

watch(trendDateRange, () => {
  debouncedInitTrendChart()
}, { deep: true })

const openConfig = () => {
  configVisible.value = true
  tempSelectedItems.value = [...configForm.selectedItems]
}

const saveConfig = () => {
  configForm.selectedItems = [...tempSelectedItems.value]
  configVisible.value = false
  ElMessage.success('保存成功')

  waterQualityData.value = availableItems.value.flatMap(theme =>
    theme.indicatorList.filter(item => configForm.selectedItems.includes(item.id)).map(item => ({
      ...item,
      themeName: theme.themeName,
      value: '0',
      status: 'success',
      statusText: '正常',
      updateTime: '刚刚',
      trend: 'stable',
      trendValue: ''
    }))
  )

  fetchLatestData()

  const configuredMetricIdsForCurrentTheme = availableItems.value
    .find(t => t.themeName === selectedTheme.value)?.indicatorList
    .filter(item => configForm.selectedItems.includes(item.id))
    .map(item => item.id) || [];

  selectedTrendMetrics.value = selectedTrendMetrics.value.filter(id =>
    configuredMetricIdsForCurrentTheme.includes(id)
  );

  if (selectedTrendMetrics.value.length === 0 && configuredMetricIdsForCurrentTheme.length > 0) {
    selectedTrendMetrics.value = [configuredMetricIdsForCurrentTheme[0]];
  }

  // 新增：保存后强制重置单位选择索引，避免越界和脏数据
  selectedYAxisIndex.value = 0
  debouncedInitTrendChart()
}

const dateRange = ref('today')
const currentDateRange = computed(() => {
  switch (dateRange.value) {
    case 'today':
      return '今日数据'
    case 'week':
      return '本周数据'
    case 'month':
      return '本月数据'
    default:
      return '今日数据'
  }
})

const changeDateRange = (range: string) => {
  dateRange.value = range;
  ElMessage.success(`已切换到${currentDateRange.value}`);
};

let refreshTimer: number | null = null

onMounted(async () => {
  await getFactoryIndicators()

  refreshTimer = window.setInterval(() => {
    if (currentFactory.value?.code && waterQualityData.value.length > 0) {
      const data: DataBoardSearchReqDTO = {
        factoryId: currentFactory.value.id,
        factoryCode: currentFactory.value.code,
        pointCodeList: waterQualityData.value.map(item => item.indicatorCode)
      }

      DashBoardApi.queryPointLastData(data).then((res: any) => {
        if (res.data) {
          updateWaterQualityData(res.data)
        }
      }).catch(error => {
        console.error('未能获取刷新的最新数据：', error)
      })
    }
  }, 5 * 60 * 1000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})

const selectedTrendMetricLabel = computed(() => {
  const theme = selectedTheme.value
  const metrics = currentSelectedMetricOptions.value.filter(opt => selectedTrendMetrics.value.includes(opt.value))
  return `${theme}：${metrics.map(m => m.label).join('、')}`
})

function formatValue(val: any) {
  if (isFinite(Number(val)) && val !== '') {
    // 四舍五入到3位小数，去掉多余的0
    return parseFloat((Math.round(Number(val) * 1000) / 1000).toString());
  }
  return val;
}

const resetZoom = () => {
  const chart = getTrendInstance()
  if (chart) {
    chart.dispatchAction({
      type: 'dataZoom',
      start: 0,
      end: 100
    })
    // 重置所有Y轴缩放
    const option = chart.getOption() as any
    if (option.yAxis) {
      option.yAxis.forEach((_: any, index: number) => {
        chart.dispatchAction({
          type: 'dataZoom',
          yAxisIndex: index,
          start: 0,
          end: 100
        })
      })
    }
  }
}

const toggleMetricVisibility = (value: string, checked: boolean) => {
  const chart = getTrendInstance()
  if (chart) {
    const metricName = currentSelectedMetricOptions.value.find(m => m.value === value)?.label
    if (metricName) {
      chart.dispatchAction({
        type: checked ? 'legendSelect' : 'legendUnSelect',
        name: metricName
      })
    }
  }
}

const getMetricColor = (value: string): string => {
  const colorList = ['#8b5cf6', '#3b82f6', '#10b981', '#f59e0b', '#ec4899', '#06b6d4', '#6366f1', '#67c23a', '#e6a23c', '#f56c6c']
  const index = currentSelectedMetricOptions.value.findIndex(m => m.value === value)
  return colorList[index % colorList.length] || '#8b5cf6'
}

// 下拉选择的单位分组也用统一分组函数
const unitGroupsForSelect = computed(() => {
  const theme = availableItems.value.find(t => t.themeName === selectedTheme.value);
  if (!theme) return [];
  const selectedMetrics = theme.indicatorList.filter(item => configForm.selectedItems.includes(item.id));
  return groupMetricsByUnit(selectedMetrics);
});
// 保证selectedYAxisIndex和unitGroupsForSelect同步
watch(unitGroupsForSelect, (newGroups) => {
  if (selectedYAxisIndex.value >= newGroups.length) {
    selectedYAxisIndex.value = 0;
  }
});

// 监听切换Y轴缩放单位时，重新渲染图表
watch(selectedYAxisIndex, () => {
  debouncedInitTrendChart()
})

// 统一单位分组逻辑
function getUnitKey(unit: string | undefined | null): string {
  return unit && String(unit).trim() ? unit : 'unknown'
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

:deep(.el-card__header) {
  padding: 14px 20px;
  font-weight: bold;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
