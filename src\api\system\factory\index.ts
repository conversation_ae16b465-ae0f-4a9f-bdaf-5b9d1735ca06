import request from '@/config/axios'

// 厂站相关类型定义
export interface FactorySaveReqVO {
  id?: number
  name: string
  type?: string
  level?: number
  parentId?: number
  orderNum?: number
  region?: string
  isActive?: boolean
  userIdList?: number[]
  auditorId?: number
  cascadeUpdateAudit?: boolean
}

export interface FactoryTreeVO {
  id: number
  name: string
  code: string
  type: string
  level: number
  region: string
  parentId: number
  extraInfo: any
  orderNum: number
  isActive: boolean
  children: FactoryTreeVO[]
}

export interface FactoryDetailVO {
  id: number
  name: string
  type: string
  level: number
  parentId: number
  region: string
  extraInfo: any
  orderNum: number
  isActive: boolean
  deptId: number
  children: any[]
  createTime: string
  updateTime: string
  creator: string
  updater: string
  deleted: boolean
  code: string
}

// 厂站用户角色相关类型定义
export interface FactoryUserRoleInsertReqVO {
  factoryId: number
  userId: number
  moduleCode: string
  roleCode: string
}

export interface FactoryUserRoleUpdateReqVO {
  id: number
  factoryId: number
  userId: number
  moduleCode: string
  roleCode: string
}

export interface FactoryUserRoleSearchReqVO {
  factoryId?: number
  userId?: number
  moduleCode: string
  roleCode: string
}

export interface FactoryUserRoleInfoRespVO {
  id: number
  factoryId: number
  userId: number
  moduleCode: string
  roleCode: string
}

/**
 * 厂站管理接口
 */
export const SystemFactoryApi = {
  /**
   * 创建水厂
   */
  createFactory: async (data: FactorySaveReqVO) => {
    return await request.postOriginal({
      url: '/system/factory/create',
      data
    })
  },

  /**
   * 删除水厂
   */
  deleteFactory: async (id: number) => {
    return await request.deleteOriginal({
      url: '/system/factory/delete',
      params: { id }
    })
  },

  /**
   * 获得水厂详情
   */
  getFactory: async (id: number) => {
    return await request.getOriginal({
      url: '/system/factory/get',
      params: { id }
    })
  },

  /**
   * 获得水厂树
   */
  getFactoryTree: async (params: {
    name?: string
    type?: string
    level?: string
    region?: string
    parentId?: string
    orderNum?: string
    isActive?: string
    createTime?: string
  }) => {
    return await request.getOriginal({
      url: '/system/factory/get-tree',
      params
    })
  },

  /**
   * 更新水厂
   */
  updateFactory: async (data: FactorySaveReqVO) => {
    return await request.putOriginal({
      url: '/system/factory/update',
      data
    })
  },

  /**
   * 根据模快编码、角色编码、和当前登录人、查询水厂列表
   */
  treeFactoryByModuleCodeAndRoleCode: async (data: FactoryUserRoleSearchReqVO) => {
    return await request.postOriginal({
      url: '/system/factory/treeFactoryByModuleCodeAndRoleCode',
      data
    })
  }
}

/**
 * 厂站用户角色关联接口
 */
export const SystemFactoryUserRoleApi = {
  /**
   * 新增厂站-用户-角色关系
   */
  addFactoryUserRole: async (data: FactoryUserRoleInsertReqVO) => {
    return await request.postOriginal({
      url: '/system/factory-user-role/add',
      data
    })
  },

  /**
   * 根据ID删除厂站-用户-角色关系
   */
  deleteFactoryUserRole: async (id: number) => {
    return await request.deleteOriginal({
      url: `/system/factory-user-role/delete/${id}`
    })
  },

  /**
   * 根据ID获取厂站-用户-角色关系
   */
  getFactoryUserRole: async (id: number) => {
    return await request.getOriginal({
      url: `/system/factory-user-role/get/${id}`
    })
  },

  /**
   * 根据厂站ID和模快编码 获取厂站-用户-角色关系列表
   */
  listByFactoryIdAndModuleCode: async (data: FactoryUserRoleSearchReqVO) => {
    return await request.postOriginal({
      url: '/system/factory-user-role/listByFactoryIdAndModuleCode',
      data
    })
  },

  /**
   * 修改厂站-用户-角色关系
   */
  updateFactoryUserRole: async (data: FactoryUserRoleUpdateReqVO) => {
    return await request.putOriginal({
      url: '/system/factory-user-role/update',
      data
    })
  },

  /**
   * 老方案人员与厂站关联同步新表，并复制全局选择器权限关系
   */
  syncFactoryUserRole: async () => {
    return await request.getOriginal({
      url: '/system/factory-user-role/syncFactoryUserRole'
    })
  }
}
