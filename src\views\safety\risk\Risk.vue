<template>
  <div class="risk-container">
    <el-card>
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="风险评估" name="assessment">
          <risk-assessment />
        </el-tab-pane>
        <el-tab-pane label="风险清单" name="list">
          <risk-list />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import RiskAssessment from './components/RiskAssessment.vue'
import RiskList from './components/RiskList.vue'

export default {
  name: 'Risk',
  components: {
    RiskAssessment,
    RiskList
  },
  data() {
    return {
      activeTab: 'assessment'
    }
  }
}
</script>

<style lang="scss" scoped>
.risk-container {
  padding: 20px;
  
  :deep(.el-tabs__content) {
    padding: 20px;
  }
}
</style>
