<template>
  <div class="work-ticket-list">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="作业类型">
          <el-select v-model="searchForm.type" placeholder="请选择作业类型">
            <el-option label="高空作业" value="height" />
            <el-option label="动火作业" value="fire" />
            <el-option label="受限空间作业" value="confined" />
            <el-option label="临时用电" value="electricity" />
            <el-option label="吊装作业" value="lifting" />
          </el-select>
        </el-form-item>
        <el-form-item label="作业状态">
          <el-select v-model="searchForm.status" placeholder="请选择作业状态">
            <el-option label="待审批" value="pending" />
            <el-option label="已批准" value="approved" />
            <el-option label="已驳回" value="rejected" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请人">
          <el-input v-model="searchForm.applicant" placeholder="请输入申请人姓名" />
        </el-form-item>
        <el-form-item label="作业日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleAdd">申请作业票</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="ticketNo" label="作业票编号" width="150" />
      <el-table-column prop="type" label="作业类型" width="120">
        <template #default="scope">
          <el-tag :type="getTypeTag(scope.row.type)">
            {{ getTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="applicant" label="申请人" width="100" />
      <el-table-column prop="department" label="作业部门" width="120" />
      <el-table-column prop="startDate" label="开始日期" width="120" />
      <el-table-column prop="endDate" label="结束日期" width="120" />
      <el-table-column prop="location" label="作业地点" width="150" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTag(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="approver" label="审批人" width="100" />
      <el-table-column prop="notes" label="备注" min-width="200" show-overflow-tooltip />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button 
            type="primary" 
            link 
            @click="handleView(scope.row)"
          >
            查看
          </el-button>
          <el-button 
            type="success" 
            link 
            v-if="scope.row.status === 'pending'"
            @click="handleApprove(scope.row)"
          >
            审批
          </el-button>
          <el-button 
            type="warning" 
            link 
            v-if="scope.row.status === 'approved'"
            @click="handleComplete(scope.row)"
          >
            完成
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 作业票弹窗 -->
    <work-ticket-dialog
      v-model="dialogVisible"
      :type="dialogType"
      :form-data="currentForm"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script>
import WorkTicketDialog from '../dialogs/WorkTicketDialog.vue'

export default {
  name: 'WorkTicketList',
  components: {
    WorkTicketDialog
  },
  data() {
    return {
      searchForm: {
        type: '',
        status: '',
        applicant: '',
        dateRange: []
      },
      tableData: [
        {
          id: 1,
          ticketNo: '*********',
          type: 'height',
          applicant: '张三',
          department: '维修部',
          startDate: '2024-03-15',
          endDate: '2024-03-16',
          location: '3号厂房屋顶',
          status: 'pending',
          approver: '',
          notes: '更换排气扇，需要高空作业'
        },
        {
          id: 2,
          ticketNo: '*********',
          type: 'fire',
          applicant: '李四',
          department: '生产部',
          startDate: '2024-03-14',
          endDate: '2024-03-14',
          location: '2号车间焊接区',
          status: 'approved',
          approver: '王主管',
          notes: '设备维修需要进行焊接作业'
        },
        {
          id: 3,
          ticketNo: 'WP2024003',
          type: 'confined',
          applicant: '王五',
          department: '设备部',
          startDate: '2024-03-13',
          endDate: '2024-03-13',
          location: '废水处理池',
          status: 'completed',
          approver: '赵经理',
          notes: '清理沉淀池淤泥'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3,
      dialogVisible: false,
      dialogType: 'add',
      currentForm: null
    }
  },
  methods: {
    getTypeTag(type) {
      const tags = {
        height: 'danger',
        fire: 'warning',
        confined: 'info',
        electricity: 'success',
        lifting: 'primary'
      }
      return tags[type] || 'info'
    },
    getTypeText(type) {
      const texts = {
        height: '高空作业',
        fire: '动火作业',
        confined: '受限空间作业',
        electricity: '临时用电',
        lifting: '吊装作业'
      }
      return texts[type] || type
    },
    getStatusTag(status) {
      const tags = {
        pending: 'info',
        approved: 'success',
        rejected: 'danger',
        completed: 'primary',
        cancelled: 'warning'
      }
      return tags[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        pending: '待审批',
        approved: '已批准',
        rejected: '已驳回',
        completed: '已完成',
        cancelled: '已取消'
      }
      return texts[status] || status
    },
    handleSearch() {
      // 实现搜索逻辑
      console.log('搜索条件：', this.searchForm)
      this.fetchData()
    },
    resetSearch() {
      this.searchForm = {
        type: '',
        status: '',
        applicant: '',
        dateRange: []
      }
    },
    handleAdd() {
      this.dialogType = 'add'
      this.currentForm = null
      this.dialogVisible = true
    },
    handleView(row) {
      this.dialogType = 'view'
      this.currentForm = { ...row }
      this.dialogVisible = true
    },
    handleApprove(row) {
      this.$confirm('确定要批准该作业票申请吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实现审批逻辑
        const updatedRow = { ...row, status: 'approved', approver: '当前用户' }
        // 更新表格数据
        const index = this.tableData.findIndex(item => item.id === row.id)
        if (index !== -1) {
          this.tableData[index] = updatedRow
        }
        console.log('批准作业票：', updatedRow)
        this.$message.success('审批成功')
        this.fetchData()
      }).catch(() => {
        this.$message.info('已取消审批')
      })
    },
    handleComplete(row) {
      this.$confirm('确定要标记该作业为已完成吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实现完成逻辑
        const updatedRow = { ...row, status: 'completed' }
        // 更新表格数据
        const index = this.tableData.findIndex(item => item.id === row.id)
        if (index !== -1) {
          this.tableData[index] = updatedRow
        }
        console.log('完成作业：', updatedRow)
        this.$message.success('操作成功')
        this.fetchData()
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    handleDialogSuccess() {
      this.dialogVisible = false
      this.fetchData()
      this.$message.success(
        this.dialogType === 'add' 
          ? '申请提交成功' 
          : '操作成功'
      )
    },
    fetchData() {
      // 实现获取表格数据的逻辑
      console.log('获取数据，页码：', this.currentPage, '每页条数：', this.pageSize)
    }
  }
}
</script>

<style lang="scss" scoped>
.work-ticket-list {
  .search-area {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 