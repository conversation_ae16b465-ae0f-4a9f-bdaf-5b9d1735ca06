<template>
  <div class="material-management">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="物资名称">
          <el-input v-model="searchForm.name" placeholder="请输入物资名称" />
        </el-form-item>
        <el-form-item label="存放位置">
          <el-input v-model="searchForm.location" placeholder="请输入存放位置" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleAdd">新增物资</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="name" label="物资名称" />
      <el-table-column prop="type" label="物资类型" />
      <el-table-column prop="quantity" label="数量" />
      <el-table-column prop="unit" label="单位" />
      <el-table-column prop="location" label="存放位置" />
      <el-table-column prop="lastCheckTime" label="最后检查时间" />
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 弹窗 -->
    <material-dialog
      ref="dialogRef"
      :dialog-type="dialogType"
      :material-data="currentMaterial"
      @submit="handleDialogSubmit"
    />
  </div>
</template>

<script>
import MaterialDialog from '../dialogs/MaterialDialog.vue'

export default {
  name: 'MaterialManagement',
  components: {
    MaterialDialog
  },
  data() {
    return {
      searchForm: {
        name: '',
        location: ''
      },
      tableData: [
        {
          id: 1,
          name: '灭火器',
          type: 'fire',
          quantity: 50,
          unit: '个',
          location: '各楼层消防箱',
          status: 'normal',
          lastCheckTime: '2024-03-10 14:30:00',
          remark: '定期检查压力表和喷头'
        },
        {
          id: 2,
          name: '应急药箱',
          type: 'medical',
          quantity: 10,
          unit: '套',
          location: '各部门办公区',
          status: 'shortage',
          lastCheckTime: '2024-03-12 09:15:00',
          remark: '需要补充部分药品'
        },
        {
          id: 3,
          name: '防毒面具',
          type: 'tool',
          quantity: 20,
          unit: '个',
          location: '应急物资库',
          status: 'maintenance',
          lastCheckTime: '2024-03-08 16:45:00',
          remark: '部分面具需要更换滤芯'
        },
        {
          id: 4,
          name: '应急照明灯',
          type: 'tool',
          quantity: 100,
          unit: '个',
          location: '各楼层走道',
          status: 'normal',
          lastCheckTime: '2024-03-14 11:20:00',
          remark: '每月检查电池状态'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 4,
      dialogType: 'add',
      currentMaterial: {}
    }
  },
  methods: {
    // 保留这些方法以备将来使用
    getStatusType(status) {
      const types = {
        normal: 'success',
        shortage: 'warning',
        maintenance: 'danger'
      }
      return types[status] || 'info'
    },
    getStatusText(status) {
      const texts = {
        normal: '正常',
        shortage: '待补充',
        maintenance: '待维护'
      }
      return texts[status] || status
    },
    handleSearch() {
      // 实现搜索逻辑
      console.log('搜索条件：', this.searchForm)
    },
    resetSearch() {
      this.searchForm = {
        name: '',
        location: ''
      }
    },
    handleAdd() {
      this.dialogType = 'add'
      this.currentMaterial = {
        status: 'normal' // 设置默认状态为正常
      }
      this.$refs.dialogRef.open()
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentMaterial = { ...row }
      this.$refs.dialogRef.open()
    },
    handleDelete(row) {
      this.$confirm('确定要删除该物资吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('删除物资：', row)
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    fetchData() {
      // 获取表格数据
      console.log('获取数据，页码：', this.currentPage, '每页条数：', this.pageSize)
    },
    handleDialogSubmit(formData) {
      // 处理弹窗提交
      console.log('提交数据：', formData)
      if (this.dialogType === 'add') {
        // 确保新增物资时设置了默认状态
        const newMaterial = {
          ...formData,
          status: 'normal' // 设置默认状态为正常
        }
        console.log('新增物资数据：', newMaterial)
        this.$message.success('新增成功')
      } else if (this.dialogType === 'edit') {
        this.$message.success('修改成功')
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.material-management {
  .search-area {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 