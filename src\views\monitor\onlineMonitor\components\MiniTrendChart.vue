<template>
  <div ref="chartRef" :style="{ width: '100%', height: height + 'px' }"></div>
</template>

<script lang="ts" setup>
import type { EChartsType } from 'echarts';
import * as echarts from 'echarts';
import { onMounted, ref, watch } from 'vue';

interface ChartDataItem {
  time: string;
  value: number;
}

const props = defineProps({
  data: {
    type: Array as () => ChartDataItem[],
    required: true,
    default: () => []
  },
  height: {
    type: Number,
    default: 40
  }
})

const chartRef = ref(null)
let chartInstance: EChartsType | null = null

onMounted(() => {
  initChart()
})

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

function initChart() {
  if (chartRef.value) {
    // 销毁旧实例
    if (chartInstance) {
      chartInstance.dispose()
    }

    // 创建新实例
    chartInstance = echarts.init(chartRef.value)
    updateChart()
  }
}

function updateChart() {
  if (!chartInstance || !props.data.length) return

  const values = props.data.map((item: ChartDataItem) => item.value)

  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      boundaryGap: false
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        type: 'line',
        data: values,
        showSymbol: false,
        smooth: true,
        lineStyle: {
          width: 2,
          color: '#409EFF'
        }
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        const dataIndex = params[0].dataIndex
        const time = props.data[dataIndex]?.time || ''
        const value = props.data[dataIndex]?.value || 0
        return `${time}: ${value}`
      },
      axisPointer: {
        type: 'none'
      },
      backgroundColor: 'rgba(50, 50, 50, 0.7)',
      borderColor: 'rgba(50, 50, 50, 0.7)',
      textStyle: {
        color: '#fff'
      }
    }
  }

  chartInstance.setOption(option)
}
</script>

<style scoped></style>