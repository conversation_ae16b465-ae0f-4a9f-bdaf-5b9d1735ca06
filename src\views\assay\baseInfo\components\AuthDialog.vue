<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基本信息" name="basic">
        <el-form
          ref="formRef"
          v-loading="formLoading"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="formData.username" placeholder="请输入用户名" :disabled="formType === 'update'" />
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input v-model="formData.name" placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="密码" prop="password" v-if="formType === 'create'">
            <el-input v-model="formData.password" type="password" placeholder="请输入密码" show-password />
          </el-form-item>
          <el-form-item label="角色" prop="role">
            <el-select v-model="formData.role" placeholder="请选择角色" @change="handleRoleChange">
              <el-option label="化验员" value="technician" />
              <el-option label="化验主管" value="supervisor" />
              <el-option label="系统管理员" value="admin" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属部门" prop="department">
            <el-select v-model="formData.department" placeholder="请选择部门">
              <el-option label="化验室" value="化验室" />
              <el-option label="运维部" value="运维部" />
              <el-option label="系统管理部" value="系统管理部" />
            </el-select>
          </el-form-item>
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入联系电话" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入邮箱" />
          </el-form-item>
          <el-form-item label="工号" prop="employeeId">
            <el-input v-model="formData.employeeId" placeholder="请输入工号" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="0">启用</el-radio>
              <el-radio :label="1">停用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="功能权限" name="permission" v-if="formType === 'update'">
        <div class="permission-container">
          <div class="permission-title">模块权限配置</div>
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTreeData"
            show-checkbox
            node-key="id"
            :props="{ label: 'name' }"
            :default-checked-keys="formData.permissions"
          />
          
          <div class="permission-title mt-4">操作权限配置</div>
          <el-checkbox-group v-model="formData.operations">
            <el-checkbox v-for="item in operationOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="数据权限" name="dataPermission" v-if="formType === 'update'">
        <div class="data-permission-container">
          <div class="permission-title">数据访问范围</div>
          <el-radio-group v-model="formData.dataScope">
            <el-radio label="all">所有数据</el-radio>
            <el-radio label="department">所属部门数据</el-radio>
            <el-radio label="personal">个人数据</el-radio>
            <el-radio label="custom">自定义数据范围</el-radio>
          </el-radio-group>
          
          <div class="permission-title mt-4" v-if="formData.dataScope === 'custom'">可访问的水厂</div>
          <el-checkbox-group v-model="formData.accessiblePlants" v-if="formData.dataScope === 'custom'">
            <el-checkbox v-for="item in plantOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
          
          <div class="permission-title mt-4">数据敏感级别</div>
          <el-select v-model="formData.dataSensitivityLevel" placeholder="请选择数据敏感级别">
            <el-option label="普通" value="normal" />
            <el-option label="敏感" value="sensitive" />
            <el-option label="高度敏感" value="highly_sensitive" />
          </el-select>
        </div>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'

defineOptions({ name: 'AuthDialog' })

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')
const activeTab = ref('basic')

// 功能权限树
const permissionTreeData = [
  {
    id: 'baseInfo',
    name: '基础信息管理',
    children: [
      { id: 'testItem', name: '检测项目管理' },
      { id: 'samplingPoint', name: '采样点管理' },
      { id: 'auth', name: '人员权限管理' }
    ]
  },
  {
    id: 'testPlan',
    name: '检测计划管理',
    children: [
      { id: 'regularPlan', name: '常规检测计划' },
      { id: 'temporaryPlan', name: '临时检测计划' }
    ]
  },
  {
    id: 'sampling',
    name: '采样执行管理',
    children: [
      { id: 'samplingTask', name: '采样任务管理' },
      { id: 'sampleTracking', name: '样品流转跟踪' },
      { id: 'inspectionReport', name: '送检报告管理' }
    ]
  },
  {
    id: 'dataManagement',
    name: '检测数据管理',
    children: [
      { id: 'dataEntry', name: '数据录入' },
      { id: 'dataQuery', name: '数据查询' }
    ]
  },
  {
    id: 'qualityManagement',
    name: '数据质量管理',
    children: [
      { id: 'dataReview', name: '数据审核' },
      { id: 'exceptionHandling', name: '异常数据处理' },
      { id: 'reExamination', name: '复检管理' },
      { id: 'reportView', name: '检测报告查看' }
    ]
  },
  {
    id: 'dataAnalysis',
    name: '数据分析展示',
    children: [
      { id: 'monitorDashboard', name: '实时监控大屏' },
      { id: 'trendAnalysis', name: '趋势分析' },
      { id: 'reportGeneration', name: '报表生成' }
    ]
  }
]

// 操作权限选项
const operationOptions = [
  { label: '新增', value: 'create' },
  { label: '修改', value: 'update' },
  { label: '删除', value: 'delete' },
  { label: '导出', value: 'export' },
  { label: '导入', value: 'import' },
  { label: '审核', value: 'review' },
  { label: '打印', value: 'print' }
]

// 水厂选项
const plantOptions = [
  { label: '北涝圩污水厂', value: 'north' },
  { label: '南湖污水厂', value: 'south' },
  { label: '西区污水厂', value: 'west' },
  { label: '东部污水厂', value: 'east' }
]

// 表单数据
const formData = ref({
  id: undefined,
  username: '',
  name: '',
  password: '',
  role: '',
  department: '',
  phone: '',
  email: '',
  employeeId: '',
  status: 0,
  remark: '',
  // 功能权限
  permissions: [] as string[],
  operations: [] as string[],
  // 数据权限
  dataScope: 'personal',
  accessiblePlants: [] as string[],
  dataSensitivityLevel: 'normal'
})

// 表单校验规则
const formRules = reactive<FormRules>({
  username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
  password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
  role: [{ required: true, message: '角色不能为空', trigger: 'change' }],
  department: [{ required: true, message: '部门不能为空', trigger: 'change' }],
  phone: [
    { 
      pattern: /^1[3-9]\d{9}$/, 
      message: '请输入正确的手机号码', 
      trigger: 'blur' 
    }
  ],
  email: [
    { 
      type: 'email', 
      message: '请输入正确的邮箱地址', 
      trigger: 'blur' 
    }
  ]
})

const formRef = ref()
const permissionTreeRef = ref()
const emit = defineEmits(['success'])

// 处理角色变化，自动设置默认权限
const handleRoleChange = (role: string) => {
  // 根据角色设置默认权限
  if (role === 'admin') {
    // 系统管理员拥有全部权限
    formData.value.permissions = getAllPermissionKeys()
    formData.value.operations = operationOptions.map(op => op.value)
    formData.value.dataScope = 'all'
    formData.value.dataSensitivityLevel = 'highly_sensitive'
  } else if (role === 'supervisor') {
    // 化验主管拥有除了人员管理外的所有权限
    formData.value.permissions = ['testItem', 'samplingPoint', 
                                 'regularPlan', 'temporaryPlan',
                                 'samplingTask', 'sampleTracking', 'inspectionReport',
                                 'dataEntry', 'dataQuery',
                                 'dataReview', 'exceptionHandling', 'reExamination', 'reportView',
                                 'monitorDashboard', 'trendAnalysis', 'reportGeneration']
    formData.value.operations = ['create', 'update', 'delete', 'export', 'review', 'print']
    formData.value.dataScope = 'department'
    formData.value.dataSensitivityLevel = 'sensitive'
  } else if (role === 'technician') {
    // 化验员拥有基础数据录入和查询权限
    formData.value.permissions = ['samplingTask', 'sampleTracking',
                                 'dataEntry', 'dataQuery',
                                 'reportView']
    formData.value.operations = ['create', 'update', 'export']
    formData.value.dataScope = 'personal'
    formData.value.dataSensitivityLevel = 'normal'
  }
}

// 获取所有权限的键
const getAllPermissionKeys = () => {
  const keys: string[] = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      if (node.id) keys.push(node.id)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(permissionTreeData)
  return keys
}

// 打开对话框
const open = async (type: string, data?: any) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增用户' : '编辑用户'
  formType.value = type
  activeTab.value = 'basic'
  resetForm()
  
  // 如果是编辑模式，设置表单数据
  if (type === 'update' && data) {
    // 深拷贝，避免直接修改原始数据
    formData.value = JSON.parse(JSON.stringify(data))
    // 编辑模式不校验密码
    delete formRules.password
    
    // 确保数组类型的字段有默认值
    if (!formData.value.permissions) formData.value.permissions = []
    if (!formData.value.operations) formData.value.operations = []
    if (!formData.value.accessiblePlants) formData.value.accessiblePlants = []

    // 设置默认的数据范围和敏感级别
    if (!formData.value.dataScope) formData.value.dataScope = 'personal'
    if (!formData.value.dataSensitivityLevel) formData.value.dataSensitivityLevel = 'normal'
  } else {
    // 新增模式需要校验密码
    formRules.password = [{ required: true, message: '密码不能为空', trigger: 'blur' }]
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 如果在权限或数据权限标签页，先切换到基本信息标签页进行校验
  activeTab.value = 'basic'
  
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 获取权限树选中的节点
    if (formType.value === 'update' && permissionTreeRef.value) {
      formData.value.permissions = permissionTreeRef.value.getCheckedKeys()
    }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟数据流转到权限配置库
    console.log('用户权限数据入库:', formData.value)

    // 模拟数据流转到系统访问控制
    console.log('数据流转到系统访问控制，控制系统访问权限')
    
    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    username: '',
    name: '',
    password: '',
    role: '',
    department: '',
    phone: '',
    email: '',
    employeeId: '',
    status: 0,
    remark: '',
    permissions: [],
    operations: [],
    dataScope: 'personal',
    accessiblePlants: [],
    dataSensitivityLevel: 'normal'
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.permission-title {
  font-weight: bold;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.permission-container,
.data-permission-container {
  padding: 1rem;
}

.mt-4 {
  margin-top: 1rem;
}
</style> 