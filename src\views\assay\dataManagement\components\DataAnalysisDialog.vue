<template>
  <el-dialog
    v-model="visible"
    title="检测数据分析"
    width="1200px"
    :close-on-click-modal="false"
  >
    <div class="analysis-container">
      <!-- 分析配置 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <span>分析配置</span>
        </template>
        <el-form :model="analysisForm" :inline="true">
          <el-form-item label="检测项目">
            <el-select v-model="analysisForm.testItem" placeholder="请选择检测项目">
              <el-option label="COD" value="COD" />
              <el-option label="BOD5" value="BOD5" />
              <el-option label="氨氮" value="NH3-N" />
              <el-option label="总磷" value="TP" />
              <el-option label="总氮" value="TN" />
            </el-select>
          </el-form-item>
          <el-form-item label="采样点">
            <el-select v-model="analysisForm.samplingPoint" placeholder="请选择采样点">
              <el-option label="进水总口" value="inlet" />
              <el-option label="生化池" value="biochemical" />
              <el-option label="出水总口" value="outlet" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="analysisForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="generateAnalysis">
              <el-icon><TrendCharts /></el-icon>生成分析
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 分析结果 -->
      <el-row :gutter="20">
        <!-- 趋势图表 -->
        <el-col :span="16">
          <el-card shadow="never">
            <template #header>
              <div class="flex justify-between items-center">
                <span>数据趋势分析</span>
                <div>
                  <el-button size="small" @click="exportChart">导出图表</el-button>
                </div>
              </div>
            </template>
            <div ref="trendChartRef" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 统计信息 -->
        <el-col :span="8">
          <el-card shadow="never">
            <template #header>
              <span>统计信息</span>
            </template>
            <div class="statistics-container">
              <div class="stat-item">
                <div class="stat-label">样本数量</div>
                <div class="stat-value">{{ statistics.sampleCount }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">平均值</div>
                <div class="stat-value">{{ statistics.average }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">最大值</div>
                <div class="stat-value">{{ statistics.max }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">最小值</div>
                <div class="stat-value">{{ statistics.min }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">标准差</div>
                <div class="stat-value">{{ statistics.stdDev }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">超标率</div>
                <div class="stat-value text-red-500">{{ statistics.exceedRate }}%</div>
              </div>
            </div>
          </el-card>

          <!-- 数据质量评估 -->
          <el-card shadow="never" class="mt-4">
            <template #header>
              <span>数据质量评估</span>
            </template>
            <div class="quality-assessment">
              <div class="quality-item">
                <span class="quality-label">数据完整性</span>
                <el-progress :percentage="qualityMetrics.completeness" :color="getProgressColor(qualityMetrics.completeness)" />
              </div>
              <div class="quality-item">
                <span class="quality-label">数据准确性</span>
                <el-progress :percentage="qualityMetrics.accuracy" :color="getProgressColor(qualityMetrics.accuracy)" />
              </div>
              <div class="quality-item">
                <span class="quality-label">数据一致性</span>
                <el-progress :percentage="qualityMetrics.consistency" :color="getProgressColor(qualityMetrics.consistency)" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细数据表格 -->
      <el-card shadow="never" class="mt-4">
        <template #header>
          <div class="flex justify-between items-center">
            <span>详细数据</span>
            <el-button size="small" @click="exportData">导出数据</el-button>
          </div>
        </template>
        <el-table :data="analysisData" border style="width: 100%" max-height="300">
          <el-table-column prop="testDate" label="检测日期" width="120" />
          <el-table-column prop="sampleCode" label="样品编号" width="150" />
          <el-table-column prop="testValue" label="检测值" width="100">
            <template #default="{ row }">
              <span :class="getValueClass(row.status)">
                {{ row.testValue }} {{ row.unit }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="tester" label="检测人" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'normal' ? 'success' : row.status === 'warning' ? 'warning' : 'danger'">
                {{ row.status === 'normal' ? '正常' : row.status === 'warning' ? '警告' : '超标' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="150" />
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="generateReport">
          <el-icon><Document /></el-icon>生成报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

defineOptions({ name: 'DataAnalysisDialog' })

// 对话框状态
const visible = ref(false)
const trendChartRef = ref<HTMLDivElement>()
let trendChart: echarts.ECharts | null = null

// 分析表单
const analysisForm = reactive({
  testItem: 'COD',
  samplingPoint: 'outlet',
  dateRange: []
})

// 统计数据
const statistics = reactive({
  sampleCount: 0,
  average: '0.00',
  max: '0.00',
  min: '0.00',
  stdDev: '0.00',
  exceedRate: 0
})

// 质量指标
const qualityMetrics = reactive({
  completeness: 95,
  accuracy: 88,
  consistency: 92
})

// 分析数据
const analysisData = ref([])

// 打开对话框
const open = () => {
  visible.value = true
  // 默认生成分析
  setTimeout(() => {
    generateAnalysis()
  }, 100)
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
}

// 生成分析
const generateAnalysis = () => {
  // 模拟数据
  const mockData = [
    { testDate: '2023-07-15', sampleCode: 'SP20230715001', testValue: 45, unit: 'mg/L', tester: '张三', status: 'normal', remark: '' },
    { testDate: '2023-07-16', sampleCode: 'SP20230716001', testValue: 52, unit: 'mg/L', tester: '李四', status: 'exceed', remark: '超标' },
    { testDate: '2023-07-17', sampleCode: 'SP20230717001', testValue: 38, unit: 'mg/L', tester: '王五', status: 'normal', remark: '' },
    { testDate: '2023-07-18', sampleCode: 'SP20230718001', testValue: 48, unit: 'mg/L', tester: '张三', status: 'warning', remark: '接近上限' },
    { testDate: '2023-07-19', sampleCode: 'SP20230719001', testValue: 42, unit: 'mg/L', tester: '李四', status: 'normal', remark: '' }
  ]
  
  analysisData.value = mockData
  
  // 更新统计数据
  const values = mockData.map(item => item.testValue)
  statistics.sampleCount = values.length
  statistics.average = (values.reduce((a, b) => a + b, 0) / values.length).toFixed(2)
  statistics.max = Math.max(...values).toFixed(2)
  statistics.min = Math.min(...values).toFixed(2)
  statistics.stdDev = calculateStdDev(values).toFixed(2)
  statistics.exceedRate = Math.round((mockData.filter(item => item.status === 'exceed').length / mockData.length) * 100)
  
  // 渲染图表
  renderTrendChart(mockData)
  
  ElMessage.success('分析生成完成')
}

// 计算标准差
const calculateStdDev = (values: number[]) => {
  const avg = values.reduce((a, b) => a + b, 0) / values.length
  const squareDiffs = values.map(value => Math.pow(value - avg, 2))
  const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length
  return Math.sqrt(avgSquareDiff)
}

// 渲染趋势图表
const renderTrendChart = (data: any[]) => {
  if (!trendChartRef.value) return
  
  if (trendChart) {
    trendChart.dispose()
  }
  
  trendChart = echarts.init(trendChartRef.value)
  
  const option = {
    title: {
      text: `${analysisForm.testItem} 浓度趋势`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}<br/>${data.seriesName}: ${data.value} mg/L`
      }
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.testDate)
    },
    yAxis: {
      type: 'value',
      name: '浓度 (mg/L)'
    },
    series: [{
      name: analysisForm.testItem,
      type: 'line',
      data: data.map(item => item.testValue),
      markLine: {
        data: [
          { yAxis: 50, name: '排放标准' }
        ]
      },
      itemStyle: {
        color: (params: any) => {
          const item = data[params.dataIndex]
          return item.status === 'exceed' ? '#f56c6c' : item.status === 'warning' ? '#e6a23c' : '#67c23a'
        }
      }
    }]
  }
  
  trendChart.setOption(option)
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#e6a23c'
  return '#f56c6c'
}

// 获取数值样式类
const getValueClass = (status: string) => {
  return {
    'text-green-600': status === 'normal',
    'text-yellow-600': status === 'warning',
    'text-red-600': status === 'exceed'
  }
}

// 导出图表
const exportChart = () => {
  if (trendChart) {
    const url = trendChart.getDataURL({
      type: 'png',
      backgroundColor: '#fff'
    })
    const link = document.createElement('a')
    link.download = `${analysisForm.testItem}_趋势图.png`
    link.href = url
    link.click()
    ElMessage.success('图表导出成功')
  }
}

// 导出数据
const exportData = () => {
  ElMessage.success('数据导出成功')
}

// 生成报告
const generateReport = () => {
  ElMessage.success('分析报告生成成功')
}

// 组件卸载时清理图表
onUnmounted(() => {
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
})

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.analysis-container {
  max-height: 700px;
  overflow-y: auto;
}

.chart-container {
  height: 400px;
  width: 100%;
}

.statistics-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.quality-assessment {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.quality-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quality-label {
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}
</style>
