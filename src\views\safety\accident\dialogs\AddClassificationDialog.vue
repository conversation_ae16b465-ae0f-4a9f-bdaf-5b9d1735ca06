<template>
  <el-dialog
    title="添加事故分类"
    v-model="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      :disabled="formDisabled"
    >
      <el-form-item label="分类编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入分类编码" />
      </el-form-item>
      
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入分类名称" />
      </el-form-item>
      
      <el-form-item label="事故等级" prop="level">
        <el-select v-model="formData.level" placeholder="请选择事故等级" style="width: 100%">
          <el-option label="轻微" value="minor" />
          <el-option label="一般" value="normal" />
          <el-option label="重大" value="serious" />
          <el-option label="特大" value="critical" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入分类描述"
        />
      </el-form-item>
      
      <el-form-item label="标准引用" prop="standardRef">
        <el-input v-model="formData.standardRef" placeholder="请输入相关标准规范" />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="formDisabled">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(props.visible)
const formRef = ref<FormInstance>()
const formDisabled = ref(false)

// 监听props变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
  }
)

// 监听内部状态变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val)
  }
)

// 表单数据
const formData = reactive({
  code: '',
  name: '',
  level: '',
  description: '',
  standardRef: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9-]+$/, message: '编码只能包含大写字母、数字和连字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择事故等级', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入分类描述', trigger: 'blur' },
    { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
  ]
})

// 关闭弹窗
const handleClose = () => {
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      formDisabled.value = true
      // 这里提交表单数据到后端API
      setTimeout(() => {
        ElMessage.success('添加分类成功')
        formDisabled.value = false
        dialogVisible.value = false
        emit('success', { ...formData, id: Date.now().toString() })
        resetForm()
      }, 1000)
    } else {
      console.log('验证失败', fields)
      ElMessage.error('表单验证失败，请检查并填写正确信息')
    }
  })
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 