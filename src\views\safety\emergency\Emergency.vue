<template>
  <div class="emergency-container">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="预案管理" name="plan">
        <plan-management />
      </el-tab-pane>
      <el-tab-pane label="物资管理" name="material">
        <material-management />
      </el-tab-pane>
      <el-tab-pane label="演练管理" name="drill">
        <drill-management />
      </el-tab-pane>
      <el-tab-pane label="应急处置" name="handle">
        <emergency-handle />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import PlanManagement from './components/PlanManagement.vue'
import MaterialManagement from './components/MaterialManagement.vue'
import DrillManagement from './components/DrillManagement.vue'
import EmergencyHandle from './components/EmergencyHandle.vue'

export default {
  name: 'Emergency',
  components: {
    PlanManagement,
    MaterialManagement,
    DrillManagement,
    EmergencyHandle
  },
  data() {
    return {
      activeTab: 'plan'
    }
  }
}
</script>

<style lang="scss" scoped>
.emergency-container {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  
  :deep(.el-tabs) {
    height: 100%;
    
    .el-tabs__content {
      height: calc(100% - 55px);
      overflow-y: auto;
    }
  }
}
</style>
