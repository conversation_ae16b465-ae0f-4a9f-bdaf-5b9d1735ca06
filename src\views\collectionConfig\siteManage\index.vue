<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">站点管理</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-1" />添加点位
            </el-button>
            <el-button type="success" @click="handleImport">
              <Icon icon="ep:upload" class="mr-1" />导入点位
            </el-button>
            <el-button type="warning" @click="handleExport">
              <Icon icon="ep:download" class="mr-1" />导出点位
            </el-button>
          </div>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex flex-col">
        <!-- 统计信息 -->
        <div class="mb-4">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>总点位数量</span>
                  <Icon icon="ep:location" class="text-[32px] text-blue-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">256</span>
                  <span class="text-green-500">↑ 5.2%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>数据点位</span>
                  <Icon icon="ep:data-line" class="text-[32px] text-green-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">180</span>
                  <span class="text-green-500">↑ 3.5%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>视频点位</span>
                  <Icon icon="ep:video-camera" class="text-[32px] text-red-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">76</span>
                  <span class="text-green-500">↑ 1.2%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>点位组数</span>
                  <Icon icon="ep:folder" class="text-[32px] text-yellow-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">12</span>
                  <span class="text-green-500">↑ 0.8%</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 搜索表单 -->
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="点位类型" style="width: 200px;">
              <el-select v-model="searchForm.type" placeholder="请选择点位类型" clearable>
                <el-option label="数据点位" value="data" />
                <el-option label="视频点位" value="video" />
              </el-select>
            </el-form-item>
            <el-form-item label="点位组" style="width: 200px;">
              <el-select v-model="searchForm.group" placeholder="请选择点位组" clearable>
                <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" style="width: 200px;">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="正常" value="normal" />
                <el-option label="异常" value="abnormal" />
                <el-option label="离线" value="offline" />
              </el-select>
            </el-form-item>
            <el-form-item label="关键字" style="width: 200px;">
              <el-input v-model="searchForm.keyword" placeholder="请输入关键字" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 点位列表 -->
        <div class="flex-1">
          <el-table :data="tableData" border style="width: 100%" height="100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="name" label="点位名称" min-width="150" />
            <el-table-column prop="code" label="点位编码" width="150" align="center" />
            <el-table-column prop="type" label="点位类型" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.type === 'data' ? 'success' : 'warning'">
                  {{ row.type === 'data' ? '数据点位' : '视频点位' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="group" label="点位组" width="120" align="center" />
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastUpdate" label="最后更新时间" width="160" align="center" />
            <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
            <el-table-column label="操作" width="250" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                <el-button type="primary" link @click="handleConfig(row)">配置</el-button>
                <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>

    <!-- 点位编辑对话框 -->
    <el-dialog v-model="editDialogVisible" :title="isEdit ? '编辑点位' : '添加点位'" width="800px">
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="点位名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入点位名称" />
        </el-form-item>
        <el-form-item label="点位编码" prop="code">
          <el-input v-model="editForm.code" placeholder="请输入点位编码" />
        </el-form-item>
        <el-form-item label="点位类型" prop="type">
          <el-select v-model="editForm.type" placeholder="请选择点位类型">
            <el-option label="数据点位" value="data" />
            <el-option label="视频点位" value="video" />
          </el-select>
        </el-form-item>
        <el-form-item label="点位组" prop="group">
          <el-select v-model="editForm.group" placeholder="请选择点位组">
            <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="editForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="配置参数" prop="config">
          <el-input v-model="editForm.config" type="textarea" placeholder="请输入配置参数" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 点位配置对话框 -->
    <el-dialog v-model="configDialogVisible" title="点位配置" width="800px">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本配置" name="basic">
          <el-form ref="configFormRef" :model="configForm" :rules="configRules" label-width="120px">
            <el-form-item label="采集周期" prop="interval">
              <el-input-number v-model="configForm.interval" :min="1" :max="60" />
              <span class="ml-2">秒</span>
            </el-form-item>
            <el-form-item label="数据格式" prop="format">
              <el-select v-model="configForm.format" placeholder="请选择数据格式">
                <el-option label="JSON" value="json" />
                <el-option label="XML" value="xml" />
                <el-option label="CSV" value="csv" />
              </el-select>
            </el-form-item>
            <el-form-item label="数据单位" prop="unit">
              <el-input v-model="configForm.unit" placeholder="请输入数据单位" />
            </el-form-item>
            <el-form-item label="数据精度" prop="precision">
              <el-input-number v-model="configForm.precision" :min="0" :max="6" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="运算配置" name="calculation">
          <el-form ref="calcFormRef" :model="calcForm" :rules="calcRules" label-width="120px">
            <el-form-item label="运算公式" prop="formula">
              <el-input v-model="calcForm.formula" type="textarea" placeholder="请输入运算公式" />
            </el-form-item>
            <el-form-item label="参考点位" prop="references">
              <el-select v-model="calcForm.references" multiple placeholder="请选择参考点位">
                <el-option v-for="item in referenceOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="运算周期" prop="cycle">
              <el-select v-model="calcForm.cycle" placeholder="请选择运算周期">
                <el-option label="实时" value="realtime" />
                <el-option label="分钟" value="minute" />
                <el-option label="小时" value="hour" />
                <el-option label="天" value="day" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="configDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveConfig">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  type: '',
  group: '',
  status: '',
  keyword: ''
})

// 点位组选项
const groupOptions = [
  { label: '水质监测组', value: 'water' },
  { label: '设备监控组', value: 'device' },
  { label: '视频监控组', value: 'video' }
]

// 表格数据
const tableData = ref([
  {
    id: '1',
    name: '进水流量',
    code: 'FLOW_IN',
    type: 'data',
    group: '水质监测组',
    status: 'normal',
    lastUpdate: '2024-04-21 10:00:00',
    description: '进水流量监测点位',
    config: '{"interval": 5, "unit": "m³/h"}'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框
const editDialogVisible = ref(false)
const configDialogVisible = ref(false)
const isEdit = ref(false)
const activeTab = ref('basic')
const editFormRef = ref<FormInstance>()
const configFormRef = ref<FormInstance>()
const calcFormRef = ref<FormInstance>()

// 编辑表单
const editForm = reactive({
  name: '',
  code: '',
  type: '',
  group: '',
  description: '',
  config: ''
})

// 配置表单
const configForm = reactive({
  interval: 5,
  format: 'json',
  unit: '',
  precision: 2
})

// 运算表单
const calcForm = reactive({
  formula: '',
  references: [],
  cycle: 'realtime'
})

// 参考点位选项
const referenceOptions = [
  { label: '进水流量', value: 'FLOW_IN' },
  { label: '出水流量', value: 'FLOW_OUT' },
  { label: '进水pH值', value: 'PH_IN' }
]

// 表单验证规则
const editRules = {
  name: [{ required: true, message: '请输入点位名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入点位编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择点位类型', trigger: 'change' }],
  group: [{ required: true, message: '请选择点位组', trigger: 'change' }]
}

const configRules = {
  interval: [{ required: true, message: '请输入采集周期', trigger: 'blur' }],
  format: [{ required: true, message: '请选择数据格式', trigger: 'change' }],
  unit: [{ required: true, message: '请输入数据单位', trigger: 'blur' }]
}

const calcRules = {
  formula: [{ required: true, message: '请输入运算公式', trigger: 'blur' }],
  references: [{ required: true, message: '请选择参考点位', trigger: 'change' }],
  cycle: [{ required: true, message: '请选择运算周期', trigger: 'change' }]
}

// 方法
const getStatusType = (status: string) => {
  switch (status) {
    case 'normal':
      return 'success'
    case 'abnormal':
      return 'danger'
    case 'offline':
      return 'info'
    default:
      return 'info'
  }
}

const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', searchForm)
}

const resetSearch = () => {
  searchForm.type = ''
  searchForm.group = ''
  searchForm.status = ''
  searchForm.keyword = ''
}

const handleAdd = () => {
  isEdit.value = false
  editForm.name = ''
  editForm.code = ''
  editForm.type = ''
  editForm.group = ''
  editForm.description = ''
  editForm.config = ''
  editDialogVisible.value = true
}

const handleEdit = (row: any) => {
  isEdit.value = true
  Object.assign(editForm, row)
  editDialogVisible.value = true
}

const handleConfig = (row: any) => {
  // 从row.config中解析配置数据
  const config = JSON.parse(row.config)
  Object.assign(configForm, config)
  configDialogVisible.value = true
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该点位吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实现删除逻辑
    ElMessage.success('删除成功')
  }).catch(() => { })
}

const handleImport = () => {
  // 实现导入逻辑
  ElMessage.success('导入成功')
}

const handleExport = () => {
  // 实现导出逻辑
  ElMessage.success('导出成功')
}

const handleSave = async () => {
  if (!editFormRef.value) return
  await editFormRef.value.validate((valid) => {
    if (valid) {
      // 实现保存逻辑
      console.log('保存数据:', editForm)
      editDialogVisible.value = false
      ElMessage.success('保存成功')
    }
  })
}

const handleSaveConfig = async () => {
  if (!configFormRef.value || !calcFormRef.value) return
  await Promise.all([
    configFormRef.value.validate(),
    calcFormRef.value.validate()
  ]).then(() => {
    // 实现保存逻辑
    console.log('保存配置:', { ...configForm, ...calcForm })
    configDialogVisible.value = false
    ElMessage.success('保存成功')
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
