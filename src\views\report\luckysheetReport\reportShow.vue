<template>
  <div class="report-page" v-loading="loading" element-loading-text="报表加载中，请稍候...">
    <!-- 始终显示导出按钮，不受筛选条件影响 -->
    <div class="report-toolbar" v-if="!selectConfig || selectConfig.length == 0">
      <el-button @click="handleExport">导出</el-button>
    </div>

    <!-- 筛选条件组件 -->
    <SelectConfig v-if="selectConfig && selectConfig.length > 0" :config="selectConfig" @search="handleSelectSearch"
      @reset="handleSelectReset" @export="handleExport" />

    <LuckySheetContainer ref="luckysheetRef" />
  </div>
</template>
<script lang="ts" setup>
import LuckySheetContainer from './components/luckysheet.vue'
import SelectConfig from './components/selectConfig.vue'
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import emitter from '@/utils/eventBus'
import { useRoute } from 'vue-router'
import { ReportApi } from '@/api/report/reportInfo/index'
import { validate } from 'uuid'
import { setSheetProtection } from '@/utils/luckysheet'
import { ElMessage } from 'element-plus'
import { ExcelApi } from '@/api/report/excel/index'

const route = useRoute()
const luckysheetRef = ref()
const reportCode = ref()
// 添加loading状态
const loading = ref(false)

// 定义option类型
interface LuckysheetOption {
  container: string
  lang: string
  showinfobar: boolean
  editMode: boolean
  showtoolbar: boolean
  gridKey?: string
  data?: any[]
}

// 定义所有luckySheet钩子函数
const luckysheetHooks = {
  // 鼠标点击事件
  cellMousedown: (cell: any, position: any) => {
    if (cell) {
      if (cell.isOR == 1) {
        // 只读单元格
        event.preventDefault();
        event.stopPropagation(); // 防止事件冒泡
        return;
      }
    }

  }

}

const option = ref<LuckysheetOption>({
  container: 'luckysheet',
  lang: 'zh',
  showinfobar: false, // 隐藏信息栏
  editMode: false,
  showtoolbar: false,
  showsheetbar: false, //是否显示底部sheet页按钮
  hook: luckysheetHooks,
  cellRightClickConfig: {   // 鼠标右击功能
    copy: true, // 复制
    copyAs: true, // 复制为
    rowHeight: false, // 行高
    columnWidth: false, // 列宽
    clear: false, // 清除内容
    image: false, // 插入图片
    chart: false, // 图表
    link: false, // 链接
    data: false, // 数据验证
    cellFormat: false, // 设置单元格格式
    insertColumn: false, // 插入列
    deletecolumn: false, // 删除列
    hidecolumn: false, // 隐藏列
    matrix: false, // 矩阵操作
    sort: false, // 排序
    insertRow: false, //  插入行
    deleteRow: false, //  删除行
    filter: false, //  筛选选取
    deleteSelectedColum: false, //  筛选选取
    deleteCell: false, //  删除单元格
  }
})

// 筛选条件配置
const selectConfig = ref([])

const handlePreviewData = async () => {
  // 开始加载，显示加载指示器
  loading.value = true
  // 先查询excel数据，获取查询条件
  try {
    let data = await ExcelApi.getExcelByReportCode(reportCode.value);
    // 设置筛选条件配置
    if (data.selectConfig) {
      selectConfig.value = JSON.parse(data.selectConfig)
    }

  } catch (error) {
    console.error('加载预览数据失败:', error)
    ElMessage.error('加载报表数据失败，请稍后重试')
    loading.value = false
  } finally {
    loading.value = false
  }
}

// 处理筛选条件查询
const handleSelectSearch = async (params: any) => {
  try {
    const res = (await ReportApi.getReportFillByCode(reportCode.value, 2, params)) as any
    if (res.code === 0 && res.data != null) {
      let data = res.data

      // 设置筛选条件配置
      if (data.selectConfig) {
        selectConfig.value = data.selectConfig
      }

      option.value = {
        ...option.value,
        gridKey: data.excelId,
        data: data.sheetConfigList
      }


      nextTick(() => {
        if (luckysheetRef.value) {
          luckysheetRef.value.initLuckysheet(option.value)
        } else {
          console.error('luckysheetRef未就绪')
          ElMessage.error('报表初始化失败，请刷新页面重试')
        }
        // 数据加载和初始化完成后，隐藏加载指示器
        loading.value = false
      })
    } else {
      console.error('获取数据失败:', res)
      ElMessage.error('获取报表数据失败：' + (res.msg || '未知错误'))
      loading.value = false
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载报表数据失败，请稍后重试')
    loading.value = false
  } finally {
    loading.value = false
  }
}

// 处理筛选条件重置
const handleSelectReset = () => {
  // TODO: 重置数据到初始状态
}

// 导出按钮
const handleExport = () => {
  if (luckysheetRef.value) {
    loading.value = true // 导出时显示加载中
    try {
      luckysheetRef.value.exportExcel(`${route.query.reportName || '报表导出'}.xlsx`)
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请稍后重试')
    } finally {
      loading.value = false
    }
  } else {
    ElMessage.warning('报表尚未加载完成，请稍后再试')
  }
}

onMounted(() => {
  // 获取路由参数 -- 报表编码
  reportCode.value = route.query.reportCode

  if (!reportCode.value) {
    ElMessage.warning('缺少报表编码参数')
    return
  }

  handlePreviewData()
})

onUnmounted(() => {
  // 组件卸载时移除事件监听
  emitter.off('preview-data', handlePreviewData)
})
</script>
<style scoped lang="scss">
.report-page {
  width: 100%;
  height: calc(100vh - 150px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
  position: relative; // 添加相对定位支持加载指示器

  .report-toolbar {
    padding: 16px;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: flex-start;
  }

  .test-panel {
    padding: 10px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }
}
</style>
