<template>
  <div class="svg-upload">
    <div v-if="!modelValue" class="upload-area" @click="triggerFileSelect" @dragover.prevent @drop="handleDrop">
      <input type="file" ref="fileInput" accept=".svg" style="display: none;" @change="handleFileChange" />
      <el-icon class="upload-icon">
        <upload />
      </el-icon>
      <div class="upload-text">
        <p>点击上传或拖拽SVG文件到此区域</p>
        <p class="upload-hint">支持.svg格式文件</p>
      </div>
    </div>
    <div v-else class="svg-preview">
      <div class="svg-content" v-html="modelValue"></div>
      <div class="preview-actions">
        <el-button size="small" type="primary" @click="downloadSvg">
          <el-icon>
            <download />
          </el-icon>
          下载SVG
        </el-button>
        <el-button size="small" @click="clearSvg">
          <el-icon>
            <delete />
          </el-icon>
          删除
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Delete, Download, Upload } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';

// 定义属性
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'svg-size']);

// 文件输入引用
const fileInput = ref<HTMLInputElement | null>(null);

// 触发文件选择
const triggerFileSelect = () => {
  fileInput.value?.click();
};

// 处理文件变更
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  if (files && files.length > 0) {
    loadSvgFile(files[0]);
  }
};

// 处理拖放
const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    const file = files[0];
    if (file.type === 'image/svg+xml' || file.name.endsWith('.svg')) {
      loadSvgFile(file);
    } else {
      ElMessage.error('请上传SVG格式文件');
    }
  }
};

// 加载SVG文件
const loadSvgFile = (file: File) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    const svgContent = e.target?.result as string;

    // 验证是否为有效的SVG
    if (svgContent.indexOf('<svg') >= 0) {
      // 处理SVG内容
      const processedSvg = processSvg(svgContent);
      emit('update:modelValue', processedSvg);

      // 获取SVG尺寸
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(processedSvg, 'image/svg+xml');
      const svgElement = svgDoc.querySelector('svg');

      if (svgElement) {
        const width = svgElement.getAttribute('width');
        const height = svgElement.getAttribute('height');
        const viewBox = svgElement.getAttribute('viewBox');

        let svgWidth = 0;
        let svgHeight = 0;

        if (width && height) {
          svgWidth = parseFloat(width);
          svgHeight = parseFloat(height);
        } else if (viewBox) {
          const viewBoxValues = viewBox.split(' ');
          if (viewBoxValues.length === 4) {
            svgWidth = parseFloat(viewBoxValues[2]);
            svgHeight = parseFloat(viewBoxValues[3]);
          }
        }

        if (svgWidth && svgHeight) {
          emit('svg-size', { width: svgWidth, height: svgHeight });
        }
      }

      ElMessage.success('SVG上传成功');
    } else {
      ElMessage.error('无效的SVG文件');
    }
  };

  reader.onerror = () => {
    ElMessage.error('读取文件时发生错误');
  };

  reader.readAsText(file);
};

// 处理SVG内容
const processSvg = (svgContent: string): string => {
  // 确保SVG有正确的宽高
  const parser = new DOMParser();
  const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml');
  const svgElement = svgDoc.querySelector('svg');

  if (svgElement) {
    // 确保有宽高属性
    if (!svgElement.hasAttribute('width') && !svgElement.hasAttribute('height')) {
      // 如果没有宽高但有viewBox，根据viewBox设置宽高
      const viewBox = svgElement.getAttribute('viewBox');
      if (viewBox) {
        const viewBoxValues = viewBox.split(/[\s,]+/);
        if (viewBoxValues.length === 4) {
          const width = parseFloat(viewBoxValues[2]);
          const height = parseFloat(viewBoxValues[3]);

          svgElement.setAttribute('width', `${width}`);
          svgElement.setAttribute('height', `${height}`);
        }
      } else {
        // 如果既没有宽高也没有viewBox，设置默认值
        svgElement.setAttribute('width', '800');
        svgElement.setAttribute('height', '600');
      }
    }

    // 确保有viewBox
    if (!svgElement.hasAttribute('viewBox')) {
      const width = svgElement.getAttribute('width') || '800';
      const height = svgElement.getAttribute('height') || '600';
      svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
    }

    // 确保SVG的样式适合显示
    svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');

    return new XMLSerializer().serializeToString(svgDoc);
  }

  return svgContent;
};

// 下载SVG
const downloadSvg = () => {
  if (!props.modelValue) return;

  const blob = new Blob([props.modelValue], { type: 'image/svg+xml' });
  const url = URL.createObjectURL(blob);

  const a = document.createElement('a');
  a.href = url;
  a.download = 'process_diagram.svg';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 清除SVG
const clearSvg = () => {
  emit('update:modelValue', '');
  emit('svg-size', { width: 0, height: 0 });

  if (fileInput.value) {
    fileInput.value.value = '';
  }
};
</script>

<style scoped lang="scss">
.svg-upload {
  width: 100%;
  min-height: 200px;
}

.upload-area {
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  padding: 30px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #409eff;
    background-color: #ecf5ff;
  }

  .upload-icon {
    font-size: 48px;
    color: #c0c4cc;
    margin-bottom: 10px;
  }

  .upload-text {
    color: #606266;

    p {
      margin: 5px 0;
    }

    .upload-hint {
      font-size: 12px;
      color: #909399;
    }
  }
}

.svg-preview {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
  background-color: white;

  .svg-content {
    max-height: 400px;
    overflow: auto;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;

    :deep(svg) {
      max-width: 100%;
      height: auto;
      display: block;
    }
  }

  .preview-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>