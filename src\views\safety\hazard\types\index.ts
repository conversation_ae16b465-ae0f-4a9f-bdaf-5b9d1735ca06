// 文件上传类型
export interface UploadFile {
  name: string
  url: string
  size: number
  type: string
}

// 分类树节点
export interface CategoryNode {
  id: number
  name: string
  children?: CategoryNode[]
  parentId: number | null
  level: number
  sort: number
}

// 隐患数据
export interface HazardData {
  id: number
  title: string
  description: string
  categoryId: number
  level: 'high' | 'medium' | 'low'
  status: 'pending' | 'processing' | 'completed'
  discoveryTime: string
  location: string
  reporter: string
  attachments: UploadFile[]
}

// 标准数据
export interface StandardData {
  id: number
  code: string
  name: string
  categoryId: number
  content: string
  createdBy: string
  createdTime: string
  status: 'active' | 'inactive'
  version: string
}

// 整改数据
export interface RectificationData {
  id: number
  hazardId: number
  plan: string
  responsible: string
  deadline: string
  progress: number
  status: 'pending' | 'processing' | 'completed'
  attachments: UploadFile[]
}

// 验收数据
export interface AcceptanceData {
  id: number
  hazardId: number
  result: 'pass' | 'fail'
  comments: string
  inspector: string
  inspectionTime: string
  attachments: UploadFile[]
}

// 进度数据
export interface ProgressData {
  id: number
  rectificationId: number
  progress: number
  description: string
  updateTime: string
  updatedBy: string
  attachments: UploadFile[]
}

// 分类数据
export interface CategoryData {
  id: number
  name: string
  parentId: number | null
  level: number
  sort: number
  description: string
  createdTime: string
  updatedTime: string
} 