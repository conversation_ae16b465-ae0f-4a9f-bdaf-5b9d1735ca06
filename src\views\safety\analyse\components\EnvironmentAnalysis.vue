<template>
  <div class="environment-analysis">
    <div class="filter-section">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filterForm.department" placeholder="请选择部门">
            <el-option
              v-for="item in departments"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="data-section">
      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <span>环境监测指标达标率</span>
            <el-select v-model="monitoringPeriod" placeholder="选择周期" size="small">
              <el-option label="本月" value="month" />
              <el-option label="本季度" value="quarter" />
              <el-option label="本年度" value="year" />
            </el-select>
          </div>
        </template>
        <el-table :data="monitoringData" border style="width: 100%">
          <el-table-column prop="name" label="监测指标" />
          <el-table-column prop="value" label="达标率">
            <template #default="scope">
              <el-progress :percentage="scope.row.value" :color="getProgressColor(scope.row.value)" />
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="scope.row.value >= 90 ? 'success' : scope.row.value >= 80 ? 'warning' : 'danger'">
                {{ scope.row.value >= 90 ? '良好' : scope.row.value >= 80 ? '一般' : '差' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <span>环境隐患类型分布</span>
          </div>
        </template>
        <el-table :data="hazardData" border style="width: 100%">
          <el-table-column prop="type" label="隐患类型" />
          <el-table-column prop="count" label="数量" />
          <el-table-column prop="percentage" label="占比" />
        </el-table>
      </el-card>

      <el-card>
        <template #header>
          <div class="card-header">
            <span>环境隐患整改情况</span>
          </div>
        </template>
        <el-table :data="rectificationData" border style="width: 100%">
          <el-table-column prop="month" label="月份" />
          <el-table-column prop="found" label="发现隐患数" />
          <el-table-column prop="fixed" label="已整改数" />
          <el-table-column prop="rate" label="整改率" />
        </el-table>
      </el-card>
    </div>

    <!-- 详情弹窗 -->
    <DetailDialog
      v-model:visible="detailDialogVisible"
      :type="'environment'"
      :detail-data="currentDetailData || {}"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import DetailDialog from '../dialogs/DetailDialog.vue'

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  department: ''
})

// 部门列表
const departments = [
  { label: '生产部', value: 'production' },
  { label: '安全部', value: 'safety' },
  { label: '设备部', value: 'equipment' },
  { label: '质量部', value: 'quality' }
]

const monitoringPeriod = ref('month')

// 环境监测指标达标率
const monitoringData = ref([
  { name: '噪声', value: 95 },
  { name: '粉尘', value: 92 },
  { name: '废气', value: 88 },
  { name: '废水', value: 96 },
  { name: '固废', value: 98 }
])

// 环境隐患类型分布
const hazardData = ref([
  { type: '设施损坏', count: 35, percentage: '35%' },
  { type: '管理缺陷', count: 25, percentage: '25%' },
  { type: '防护不足', count: 20, percentage: '20%' },
  { type: '标识缺失', count: 15, percentage: '15%' },
  { type: '其他', count: 5, percentage: '5%' }
])

// 环境隐患整改情况
const rectificationData = ref([
  { month: '1月', found: 45, fixed: 42, rate: '93%' },
  { month: '2月', found: 52, fixed: 48, rate: '92%' },
  { month: '3月', found: 38, fixed: 35, rate: '92%' },
  { month: '4月', found: 42, fixed: 40, rate: '95%' },
  { month: '5月', found: 35, fixed: 32, rate: '91%' },
  { month: '6月', found: 40, fixed: 38, rate: '95%' }
])

// 弹窗控制
const detailDialogVisible = ref(false)
const currentDetailData = ref(null)

// 进度条颜色
const getProgressColor = (value: number) => {
  if (value >= 90) return '#67C23A'
  if (value >= 80) return '#E6A23C'
  return '#F56C6C'
}

// 查询方法
const handleSearch = () => {
  // TODO: 实现查询逻辑
}

// 重置方法
const handleReset = () => {
  filterForm.dateRange = []
  filterForm.department = ''
}

// 详情方法
const handleDetail = (row: any) => {
  currentDetailData.value = row
  detailDialogVisible.value = true
}

const handleDetailClose = () => {
  detailDialogVisible.value = false
  currentDetailData.value = null
}
</script>

<style lang="scss" scoped>
.environment-analysis {
  padding: 20px;

  .filter-section {
    margin-bottom: 20px;
  }

  .data-section {
    .mb-20 {
      margin-bottom: 20px;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style> 