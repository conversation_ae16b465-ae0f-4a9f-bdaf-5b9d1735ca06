<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">库存查询</span>
          <div class="flex gap-2">
            <el-button @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>导出台账
            </el-button>
          </div>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-270px)] flex flex-col">
        <div class="w-full mb-2 gap-2 flex items-center flex-wrap">
          <el-form :model="searchForm" inline>
            <el-form-item label="仓库：">
              <el-select v-model="searchForm.warehouse" placeholder="请选择仓库" style="width: 200px">
                <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="物料名称：">
              <el-input v-model="searchForm.material" placeholder="请输入物料名称" />
            </el-form-item>
            <el-form-item label="物料编码：">
              <el-input v-model="searchForm.materialCode" placeholder="请输入物料编码" />
            </el-form-item>
            <el-form-item label="时间范围：">
              <el-date-picker v-model="searchForm.timeRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border height="100%">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="materialCode" label="物料编码" align="center" width="120" />
                <el-table-column prop="material" label="物料名称" align="center" min-width="120"
                  :show-overflow-tooltip="true" />
                <el-table-column prop="warehouse" label="仓库" align="center" width="120" />
                <el-table-column prop="unit" label="单位" align="center" width="80" />
                <el-table-column prop="initialStock" label="期初库存" align="center" width="100" />
                <el-table-column prop="inQuantity" label="入库数量" align="center" width="100" />
                <el-table-column prop="outQuantity" label="出库数量" align="center" width="100" />
                <el-table-column prop="currentStock" label="当前库存" align="center" width="100" />
                <el-table-column prop="lastUpdateTime" label="最后更新时间" align="center" width="160" sortable />
                <el-table-column label="操作" align="center" width="80" fixed="right">
                  <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end mt-2">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="库存详情" width="800px" :close-on-click-modal="false">
      <div class="flex flex-col gap-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">物料编码：</span>
            <span>{{ currentDetail.materialCode }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">物料名称：</span>
            <span>{{ currentDetail.material }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">仓库：</span>
            <span>{{ currentDetail.warehouse }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">单位：</span>
            <span>{{ currentDetail.unit }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">期初库存：</span>
            <span>{{ currentDetail.initialStock }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">当前库存：</span>
            <span>{{ currentDetail.currentStock }}</span>
          </div>
        </div>
        <div class="mt-4">
          <div class="text-lg font-bold mb-4">出入库记录</div>
          <el-table :data="currentDetail.records" border>
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="time" label="时间" align="center" width="160" />
            <el-table-column prop="type" label="类型" align="center" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.type === '入库' ? ('success' as const) : ('warning' as const)">
                  {{ scope.row.type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" align="center" width="100" />
            <el-table-column prop="operator" label="操作人" align="center" width="120" />
            <el-table-column prop="remark" label="备注" align="center" min-width="200" :show-overflow-tooltip="true" />
          </el-table>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Download } from '@element-plus/icons-vue'

// 定义库存记录类型接口
interface StockRecord {
  time: string
  type: '入库' | '出库'
  quantity: number
  operator: string
  remark: string
}

// 定义库存详情接口
interface StockItem {
  materialCode: string
  material: string
  warehouse: string
  unit: string
  initialStock: number
  inQuantity: number
  outQuantity: number
  currentStock: number
  lastUpdateTime: string
  records: StockRecord[]
}

// 搜索表单数据
const searchForm = reactive({
  warehouse: '',
  material: '',
  materialCode: '',
  timeRange: [] as string[]
})

// 仓库选项
const warehouseOptions = [
  { value: '1007仓库名称', label: '1007仓库名称' },
  { value: '广仓库', label: '广仓库' },
  { value: '江东水厂1号仓库', label: '江东水厂1号仓库' }
]

// 表格数据
const tableData = ref<StockItem[]>([
  {
    materialCode: 'M001',
    material: '三角带',
    warehouse: '1007仓库名称',
    unit: '个',
    initialStock: 10,
    inQuantity: 5,
    outQuantity: 3,
    currentStock: 12,
    lastUpdateTime: '2024-10-30 09:37:56',
    records: [
      {
        time: '2024-10-30 09:37:56',
        type: '入库',
        quantity: 5,
        operator: '张三',
        remark: '设备维修入库'
      },
      {
        time: '2024-10-29 14:20:30',
        type: '出库',
        quantity: 3,
        operator: '李四',
        remark: '日常维护领用'
      }
    ]
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(1)

// 详情弹窗相关
const detailDialogVisible = ref(false)
const currentDetail = ref<StockItem>({
  materialCode: '',
  material: '',
  warehouse: '',
  unit: '',
  initialStock: 0,
  inQuantity: 0,
  outQuantity: 0,
  currentStock: 0,
  lastUpdateTime: '',
  records: []
})

// 搜索方法
const handleSearch = () => {
  console.log('搜索条件：', {
    ...searchForm,
    startTime: searchForm.timeRange[0],
    endTime: searchForm.timeRange[1]
  })
}

// 重置方法
const handleReset = () => {
  searchForm.warehouse = ''
  searchForm.material = ''
  searchForm.materialCode = ''
  searchForm.timeRange = []
}

// 导出方法
const handleExport = () => {
  console.log('导出台账')
}

// 详情方法
const handleDetail = (row: StockItem) => {
  currentDetail.value = { ...row }
  detailDialogVisible.value = true
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}
</script>

<style scoped lang="scss">
:deep(.el-form) {
  .el-form-item {
    margin-bottom: 12px;
  }
}
</style>
