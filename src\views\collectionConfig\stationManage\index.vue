<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">水厂台账</span>
        </div>
      </template>

      <div class="h-[calc(100vh-370px)] w-full flex">
        <!-- 左侧树形结构 -->
        <div class="tree-container w-1/4 pr-4 h-full overflow-auto border-r border-gray-200">
          <!-- 树上方的操作按钮 -->
          <div class="mb-4">
            <el-button type="primary" @click="openDialog('create')">
              <Icon icon="ep:plus" class="mr-1" />新增水厂
            </el-button>
          </div>
          
          <el-tree
            v-loading="treeLoading"
            :data="factoryList"
            node-key="id"
            border
            ref="factoryTreeRef"
            :props="{ label: 'name', children: 'children' }"
            :highlight-current="true"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            class="factory-tree"
          >
            <template #default="{ node, data }">
              <div class="flex items-center">
                <Icon :icon="data.type === '排涝泵站' ? 'ep:office-building' : 'ep:watermelon'" class="mr-2" />
                <span>{{ node.label }}</span>
                <el-tag size="small" :type="data.isActive ? 'success' : 'danger'" class="ml-2" v-if="data.id">
                  {{ data.isActive ? '启用' : '禁用' }}
                </el-tag>
              </div>
            </template>
          </el-tree>
        </div>

        <!-- 右侧内容区 -->
        <div class="content-container w-3/4 pl-4 h-full overflow-auto">
          <!-- 统计卡片 - 放在右侧上方 -->
        <div class="mb-4">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>总水厂数</span>
                  <Icon icon="ep:office-building" class="text-[32px] text-blue-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                    <span class="text-3xl font-bold text-gray-700">{{ Number(statsData.totalFactories) || 0 }}</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>运行中水厂</span>
                  <Icon icon="ep:connection" class="text-[32px] text-green-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                    <span class="text-3xl font-bold text-gray-700">{{ Number(statsData.runningFactories) }}</span>
                    <span class="text-green-500">{{ calculatePercentage(Number(statsData.runningFactories), Number(statsData.totalFactories)) }}%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>告警水厂</span>
                  <Icon icon="ep:warning" class="text-[32px] text-red-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                    <span class="text-3xl font-bold text-gray-700">{{ Number(statsData.warningFactories) }}</span>
                    <span class="text-red-500">{{ calculatePercentage(Number(statsData.warningFactories), Number(statsData.totalFactories)) }}%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>平均处理能力</span>
                  <Icon icon="ep:data-line" class="text-[32px] text-yellow-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                    <span class="text-3xl font-bold text-gray-700">{{ Number(statsData.averageCapacity).toFixed(2) }} m³/d</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

          <div v-if="selectedFactory" class="w-full flex flex-col">
        <!-- 水厂列表 -->
        <div class="flex-1">
          <el-table
                v-loading="loading"
                :data="tableList"
            border
            style="width: 100%"
                height="calc(100vh - 470px)"
          >
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="stationCode" label="站点编码" min-width="120" />
                <el-table-column prop="name" label="站点名称" min-width="150" />
                <el-table-column prop="type" label="净水站类型" width="120" align="center" />
                <el-table-column prop="region" label="所属区域" width="120" align="center" />
            <el-table-column prop="stpUnit" label="建设/运营单位" min-width="150" align="center" />
            <el-table-column prop="stpDate" label="投运时间" width="120" align="center" />
            <el-table-column prop="desCap" label="设计能力(m³/d)" width="120" align="center" />
            <el-table-column prop="rwOut" label="出水量(m³/d)" width="120" align="center" />
                <el-table-column prop="isActive" label="是否启用" width="90" align="center">
              <template #default="{ row }">
                    <el-tag :type="row.isActive ? 'success' : 'info'">{{ row.isActive ? '是' : '否' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isWarning" label="是否告警" width="90" align="center">
              <template #default="{ row }">
                <el-tag :type="row.isWarning ? 'danger' : 'info'">{{ row.isWarning ? '是' : '否' }}</el-tag>
              </template>
            </el-table-column>
                <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="openDialog('edit', row)">编辑</el-button>
                <el-button type="primary" link @click="handleDelete(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :total="total"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
          
          <!-- 未选择水厂时的提示 -->
          <div v-else class="h-[calc(100vh-470px)] flex items-center justify-center">
            <el-empty description="请选择左侧水厂以查看详情" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="60%">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
        <el-form-item label="所属水厂" prop="factoryId" v-if="selectedFactory">
          <el-input v-model="selectedFactory.name" disabled />
        </el-form-item>
        <el-form-item label="站点编码" prop="stationCode">
          <el-input v-model="formData.stationCode" placeholder="请输入站点编码" />
        </el-form-item>
        <el-form-item label="站点名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入站点名称" />
        </el-form-item>
        <el-form-item label="净水站类型" prop="type">
          <el-select v-model="formData.type as string" placeholder="请选择净水站类型">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域" prop="region">
          <el-select v-model="formData.region as string" placeholder="请选择所属区域">
            <el-option v-for="item in areaOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="详细位置" prop="stpLoc">
          <el-input v-model="formData.stpLoc" placeholder="请输入详细位置" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number v-model="formData.longitude as number" :precision="6" :step="0.000001" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number v-model="formData.latitude as number" :precision="6" :step="0.000001" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="建设/运营单位" prop="stpUnit">
          <el-input v-model="formData.stpUnit" placeholder="请输入建设或运营单位" />
        </el-form-item>
        <el-form-item label="投运时间" prop="stpDate">
          <el-date-picker v-model="formData.stpDate as string" type="date" placeholder="请选择投运时间" style="width: 100%" />
        </el-form-item>
        <el-form-item label="服务范围描述" prop="stpRange">
          <el-input v-model="formData.stpRange" placeholder="请输入服务范围描述" />
        </el-form-item>
        <el-form-item label="处理工艺" prop="trmPro">
          <el-input v-model="formData.trmPro" placeholder="请输入处理工艺" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="设计能力" prop="desCap">
              <el-input-number v-model="formData.desCap as number" :min="0" style="width: 100%">
                <template #append>m³/d</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="规模等级" prop="stpScale">
              <el-input-number v-model="formData.stpScale as number" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出水量" prop="rwOut">
              <el-input-number v-model="formData.rwOut as number" :min="0" style="width: 100%">
                <template #append>m³/d</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="执行水质标准" prop="rwWq">
          <el-input v-model="formData.rwWq" placeholder="请输入执行水质标准" />
        </el-form-item>
        <el-form-item label="负荷信息" prop="stpLoad">
          <el-input v-model="formData.stpLoad" placeholder="请输入负荷信息" />
        </el-form-item>
        <el-form-item label="出水去向说明" prop="rwTrtm">
          <el-input v-model="formData.rwTrtm" placeholder="请输入出水去向说明" />
        </el-form-item>
        <el-form-item label="目标用途" prop="target">
          <el-input v-model="formData.target" placeholder="请输入目标用途" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="是否启用" prop="isActive">
              <el-switch v-model="formData.isActive" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否告警" prop="isWarning">
              <el-switch v-model="formData.isWarning" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="Web展示" prop="isWeb">
              <el-switch v-model="formData.isWeb" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="重点项目" prop="blStp">
              <el-switch v-model="formData.blStp" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="多个站点" prop="isMoreStation">
              <el-switch v-model="formData.isMoreStation" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="合并站" prop="isMrg">
              <el-switch v-model="formData.isMrg" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="formLoading" @click="submitForm">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 水厂详情对话框 -->
    <el-dialog v-model="detailVisible" title="水厂详情" width="80%" class="detail-dialog">
      <div v-if="detailData.id" class="space-y-6">
        <!-- 基本信息 -->
        <div class="border rounded p-4">
          <h3 class="text-lg font-bold mb-4">基本信息</h3>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="站点编码">{{ detailData.stationCode }}</el-descriptions-item>
            <el-descriptions-item label="站点名称">{{ detailData.name }}</el-descriptions-item>
            <el-descriptions-item label="净水站类型">{{ detailData.type }}</el-descriptions-item>
            <el-descriptions-item label="所属区域">{{ detailData.region }}</el-descriptions-item>
            <el-descriptions-item label="详细位置">{{ detailData.stpLoc }}</el-descriptions-item>
            <el-descriptions-item label="建设/运营单位">{{ detailData.stpUnit }}</el-descriptions-item>
            <el-descriptions-item label="经度">{{ detailData.longitude }}</el-descriptions-item>
            <el-descriptions-item label="纬度">{{ detailData.latitude }}</el-descriptions-item>
            <el-descriptions-item label="投运时间">{{ detailData.stpDate }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 性能指标 -->
        <div class="border rounded p-4">
          <h3 class="text-lg font-bold mb-4">性能指标</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="text-center">
                <div class="text-gray-500 mb-2">设计能力</div>
                <div class="text-2xl font-bold">{{ detailData.desCap }} m³/d</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="text-center">
                <div class="text-gray-500 mb-2">出水量</div>
                <div class="text-2xl font-bold">{{ detailData.rwOut }} m³/d</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="text-center">
                <div class="text-gray-500 mb-2">产能利用率</div>
                <el-progress 
                  type="dashboard" 
                  :percentage="getCapacityUtilization(detailData.rwOut, detailData.desCap)" 
                  :color="getProgressColor(getCapacityUtilization(detailData.rwOut, detailData.desCap))"
                />
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 处理工艺 -->
        <div class="border rounded p-4">
          <h3 class="text-lg font-bold mb-4">处理工艺</h3>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="处理工艺">{{ detailData.trmPro }}</el-descriptions-item>
            <el-descriptions-item label="执行水质标准">{{ detailData.rwWq }}</el-descriptions-item>
            <el-descriptions-item label="负荷信息">{{ detailData.stpLoad }}</el-descriptions-item>
            <el-descriptions-item label="出水去向说明">{{ detailData.rwTrtm }}</el-descriptions-item>
            <el-descriptions-item label="目标用途">{{ detailData.target }}</el-descriptions-item>
            <el-descriptions-item label="服务范围描述">{{ detailData.stpRange }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 项目状态 -->
        <div class="border rounded p-4">
          <h3 class="text-lg font-bold mb-4">项目状态</h3>
          <el-descriptions :column="4" border>
            <el-descriptions-item label="是否启用">
              <el-tag :type="detailData.isActive ? 'success' : 'info'">{{ detailData.isActive ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="是否告警">
              <el-tag :type="detailData.isWarning ? 'danger' : 'info'">{{ detailData.isWarning ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="是否用于Web展示">
              <el-tag :type="detailData.isWeb ? 'success' : 'info'">{{ detailData.isWeb ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="是否重点项目">
              <el-tag :type="detailData.blStp ? 'success' : 'info'">{{ detailData.blStp ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="是否多个站点">
              <el-tag :type="detailData.isMoreStation ? 'success' : 'info'">{{ detailData.isMoreStation ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="是否合并站">
              <el-tag :type="detailData.isMrg ? 'success' : 'info'">{{ detailData.isMrg ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang='ts' setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, nextTick, onMounted, computed, watch } from 'vue'
import { FactoryApi } from '@/api/report/factory'
import type { Factory } from '@/types/factory'

// 水厂树形结构相关
const factoryList = ref<Factory[]>([])
const factoryTreeRef = ref()
const selectedFactory = ref<Factory | null>(null)
const treeLoading = ref(false)

// 统计数据接口响应类型
interface StatsData {
  totalFactories: number | string;
  runningFactories: number | string;
  warningFactories: number | string;
  averageCapacity: number | string;
}

// 统计数据
const statsData = ref<StatsData>({
  totalFactories: 32,
  runningFactories: 28,
  warningFactories: 3,
  averageCapacity: 2500
})

// 计算百分比
const calculatePercentage = (value: number, total: number): number => {
  if (!total) return 0
  return Math.round((value / total) * 100)
}

// 获取水厂树
const getFactoryTree = async () => {
  treeLoading.value = true
  try {
    console.log('正在获取水厂树形数据...')
    const res = await FactoryApi.getFactoryTree({})
    console.log('获取到的水厂数据:', res)
    factoryList.value = res.data || []
  } catch (error) {
    console.error('获取水厂列表失败:', error)
    ElMessage.error('获取水厂列表失败')
  } finally {
    treeLoading.value = false
  }
}

// 获取统计数据
const getFactoryStats = async () => {
  try {
    const response = await FactoryApi.getFactoryStats()
    console.log('获取统计数据:', response)
    
    // 正确处理后端响应格式
    // 处理原始响应，适配不同的返回格式
    const res = response?.data || response
    
    if (res && typeof res === 'object') {
      // 尝试获取统计数据，支持多种格式
      const statsObj = res.data || res
      
      if (statsObj && typeof statsObj === 'object') {
        console.log('处理后的统计数据:', statsObj)
        
        statsData.value = {
          totalFactories: statsObj.totalFactories || 0,
          runningFactories: statsObj.runningFactories || 0,
          warningFactories: statsObj.warningFactories || 0,
          averageCapacity: statsObj.averageCapacity || 0
        }
      } else {
        console.warn('后端返回的数据格式异常，使用默认值')
      }
    } else {
      console.warn('后端未返回预期格式数据，使用默认值')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 保留默认值
  }
}

// 处理节点点击
const handleNodeClick = (data: Factory) => {
  selectedFactory.value = data
  // 重新加载该水厂下的点位数据
  loadFactoryDetail(data.id)
}

// 加载水厂详情数据
const loadFactoryDetail = async (factoryId?: number) => {
  if (!factoryId) return
  
  console.log('加载水厂ID为', factoryId, '的详情数据')
  loading.value = true
  
  try {
    // 调用获取水厂详情接口
    const response = await FactoryApi.getFactoryDetail(factoryId)
    console.log('获取到的水厂详情:', response)
    
    // 处理响应数据
    const res = response?.data || response
    
    if (res) {
      // 转换为表格数据展示
      tableList.value = [res as unknown as FactoryDetail]
      total.value = 1
      // 保存详情数据
      detailData.value = res as unknown as FactoryDetail
    } else {
      tableList.value = []
      total.value = 0
      ElMessage.warning('未找到水厂详情数据')
    }
  } catch (error) {
    console.error('获取水厂详情失败:', error)
    ElMessage.error('获取水厂详情失败')
    tableList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 定义响应类型
interface ResponseType<T = any> {
  list: T[]
  total?: number
}

// 定义水厂详情接口
interface FactoryDetail {
  id: number; // 主键ID
  factoryId: number | null; // 关联水厂ID（来自factory表）
  stationCode: string; // 站点编码
  name: string; // 站点名称
  type: string | null; // 净水站类型
  longitude: number | null; // 经度
  latitude: number | null; // 纬度
  region: string | null; // 所属区域
  stpLoc: string | null; // 详细位置描述
  stpUnit: string | null; // 建设或运营单位
  stpDate: string | null; // 投运时间
  stpRange: string | null; // 服务范围描述
  trmPro: string | null; // 处理工艺
  desCap: number | null; // 设计能力(m³/d)
  stpScale: number | null; // 规模等级
  rwOut: number | null; // 出水量(m³/d)
  rwWq: string | null; // 执行水质标准
  blProject: string | null; // 项目UUID
  stpLoad: string | null; // 负荷信息（如平均负荷/最大负荷）
  rwTrtm: string | null; // 出水去向说明
  isActive: boolean; // 是否启用（1是，0否）
  deptId: number | null; // 部门ID
  isWarning: boolean; // 是否告警（1是，0否）
  target: string | null; // 目标用途
  isWeb: boolean; // 是否用于Web展示（1是，0否）
  userId: string | null; // 关联用户ID
  blStp: boolean; // 是否重点项目（1是，0否）
  isMoreStation: boolean; // 是否多个站点（1是，0否）
  isMrg: boolean; // 是否合并站（1是，0否）
  
  // 以下字段可能会出现在原始数据中，但不在新接口中
  stationName?: string;
  stpType?: string;
  stpArea?: string;
  isUse?: boolean;
  creator?: string | null;
  updater?: string | null;
  createTime?: string | null;
  updateTime?: string | null;
  deleted?: boolean;
}

// 使用原始数据结构
const tableList = ref<FactoryDetail[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 存储完整数据列表，用于前端搜索
const allDataList = ref<FactoryDetail[]>([]);

// 搜索参数
const searchParams = reactive<{
  stationCode: string;
  stationName: string;
  stpArea: string;
  stpType: string;
  isUse: string | boolean;
  factoryId?: number;
}>({
  stationCode: '',
  stationName: '',
  stpArea: '',
  stpType: '',
  isUse: '',
  factoryId: undefined
});

// 选项配置
const typeOptions = [
  { label: '排涝泵站', value: '排涝泵站' },
  { label: '污水厂', value: '污水厂' },
  { label: '净水厂', value: '净水厂' }
]

const areaOptions = [
  { label: '华东', value: '华东' },
  { label: '华南', value: '华南' },
  { label: '华北', value: '华北' },
  { label: '华中', value: '华中' },
  { label: '西南', value: '西南' },
  { label: '西北', value: '西北' },
  { label: '东北', value: '东北' }
]

// 弹窗相关
const formRef = ref()
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const currentId = ref<number | null>(null)

// 详情弹窗
const detailVisible = ref(false)
const detailData = ref<FactoryDetail>({} as FactoryDetail)

// 表单数据
const formData = reactive<Partial<FactoryDetail>>({
  stationCode: '',
  name: '',
  type: '净水厂', // 设置默认类型为净水厂
  longitude: 0,
  latitude: 0,
  region: '华东', // 设置默认区域为华东
  stpLoc: '',
  stpUnit: '',
  stpDate: '',
  stpRange: '',
  trmPro: '',
  desCap: 0,
  stpScale: 0,
  rwOut: 0,
  rwWq: '',
  blProject: '',
  stpLoad: '',
  rwTrtm: '',
  isActive: false,
  isWarning: false,
  target: '',
  isWeb: false,
  userId: '',
  blStp: false,
  isMoreStation: false,
  isMrg: false
})

// 表单验证规则
const formRules = {
  stationCode: [{ required: true, message: '请输入站点编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入站点名称', trigger: 'blur' }]
}

// 统计数据计算
const runningStations = computed(() => {
  if (!tableList.value || tableList.value.length === 0) return 0;
  return tableList.value.filter(item => item && item.isActive).length;
});

const warningStations = computed(() => {
  if (!tableList.value || tableList.value.length === 0) return 0;
  return tableList.value.filter(item => item && item.isWarning).length;
});

const averageCapacity = computed(() => {
  if (!tableList.value || tableList.value.length === 0) return 0;
  const stations = tableList.value.filter(item => item && item.desCap);
  if (stations.length === 0) return 0;
  const total = stations.reduce((sum, item) => sum + (item.desCap || 0), 0);
  return Math.round(total / stations.length);
});

// 方法
const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#F56C6C'
  if (percentage >= 60) return '#E6A23C'
  return '#67C23A'
}

// 计算产能利用率
const getCapacityUtilization = (rwOut: number | null, desCap: number | null): number => {
  if (!rwOut || !desCap || desCap === 0) return 0;
  const result = Math.round((rwOut / desCap) * 100);
  return result > 100 ? 100 : result; // 确保不超过100%
};

// 搜索处理
const handleSearch = async () => {
  try {
    console.log('执行搜索操作');
    loading.value = true;
    
    // 构建参数，包含分页信息和factoryId
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      factoryId: searchParams.factoryId // 添加factoryId参数
    };
    
    console.log('后端查询参数:', params);
    
    // 如果所有字段都没有填写（除了factoryId），直接从后端获取数据
    const hasSearchCondition = ['stationCode', 'stationName', 'stpArea', 'stpType', 'isUse'].some(key => 
      searchParams[key] !== '' && searchParams[key] !== null && searchParams[key] !== undefined
    );
    
    // 直接调用API
    const result = await FactoryApi.getFactoryDetailPage(params);
    console.log('原始响应:', result);
    
    // 处理响应
    const responseData = result.data || result;
    console.log('处理后响应:', responseData);
    
    // 保存完整数据
    allDataList.value = responseData.records || [];
    
    // 如果有搜索条件，执行前端过滤
    if (hasSearchCondition) {
      console.log('前端搜索条件:', searchParams);
      
      // 前端过滤
      const filteredData = allDataList.value.filter(item => {
        // 站点编码
        if (searchParams.stationCode && !item.stationCode?.toLowerCase().includes(searchParams.stationCode.toLowerCase())) {
          return false;
        }
        
        // 站点名称
        if (searchParams.stationName && !item.stationName?.toLowerCase().includes(searchParams.stationName.toLowerCase())) {
          return false;
        }
        
        // 所属区域
        if (searchParams.stpArea && item.stpArea !== searchParams.stpArea) {
          return false;
        }
        
        // 净水站类型
        if (searchParams.stpType && item.stpType !== searchParams.stpType) {
          return false;
        }
        
        // 是否启用
        if (searchParams.isUse !== '' && searchParams.isUse !== null && searchParams.isUse !== undefined) {
          // 将字符串转换为布尔值进行比较
          const isUseBoolean = searchParams.isUse === 'true' || searchParams.isUse === true;
          if (item.isActive !== isUseBoolean) {
            return false;
          }
        }
        
        return true;
      });
      
      // 更新显示的数据和总数
      tableList.value = filteredData;
      total.value = filteredData.length;
      
      console.log('前端过滤后数据:', tableList.value);
    } else {
      // 不需要过滤时，直接使用后端返回的数据
      tableList.value = allDataList.value;
      total.value = responseData.total || 0;
    }
    
    console.log('更新后的表格数据:', tableList.value);
  } catch (error) {
    console.error('获取数据出错:', error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 分页处理 - 更新为本地分页
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  
  // 如果有搜索条件，就在前端处理分页
  const hasSearchCondition = Object.values(searchParams).some(value => value !== '' && value !== null && value !== undefined);
  if (hasSearchCondition) {
    // 留在当前页
    handleLocalPaging();
  } else {
    // 否则请求后端
    handleSearch();
  }
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  
  // 如果有搜索条件，就在前端处理分页
  const hasSearchCondition = Object.values(searchParams).some(value => value !== '' && value !== null && value !== undefined);
  if (hasSearchCondition) {
    // 留在当前页
    handleLocalPaging();
  } else {
    // 否则请求后端
    handleSearch();
  }
};

// 前端分页处理
const handleLocalPaging = () => {
  const hasSearchCondition = Object.values(searchParams).some(value => value !== '' && value !== null && value !== undefined);
  
  if (hasSearchCondition) {
    // 前端过滤
    const filteredData = allDataList.value.filter(item => {
      // 站点编码
      if (searchParams.stationCode && !item.stationCode?.toLowerCase().includes(searchParams.stationCode.toLowerCase())) {
        return false;
      }
      
      // 站点名称
      if (searchParams.stationName && !item.stationName?.toLowerCase().includes(searchParams.stationName.toLowerCase())) {
        return false;
      }
      
      // 所属区域
      if (searchParams.stpArea && item.stpArea !== searchParams.stpArea) {
        return false;
      }
      
      // 净水站类型
      if (searchParams.stpType && item.stpType !== searchParams.stpType) {
        return false;
      }
      
      // 是否启用
      if (searchParams.isUse !== '' && searchParams.isUse !== null && searchParams.isUse !== undefined) {
        // 将字符串转换为布尔值进行比较
        const isUseBoolean = searchParams.isUse === 'true' || searchParams.isUse === true;
        if (item.isActive !== isUseBoolean) {
          return false;
        }
      }
      
      return true;
    });
    
    // 计算分页
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    tableList.value = filteredData.slice(start, end);
    total.value = filteredData.length;
  }
};

// 重置搜索
const resetSearch = () => {
  // 保存当前的factoryId
  const factoryId = searchParams.factoryId;
  
  // 重置其他搜索参数
  Object.keys(searchParams).forEach(key => {
    if (key !== 'factoryId') {
      searchParams[key] = '';
    }
  });
  
  // 恢复factoryId
  searchParams.factoryId = factoryId;
  
  currentPage.value = 1;
  handleSearch();
};

// 打开弹窗
const openDialog = (type: 'create' | 'edit', row?: FactoryDetail) => {
  // 如果是新增，确保已选择水厂
  if (type === 'create' && (!selectedFactory.value || !selectedFactory.value.id)) {
    ElMessage.warning('请先选择左侧水厂');
    return;
  }

  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增水厂' : '编辑水厂'
  
  // 重置表单
  Object.keys(formData).forEach(key => {
    if (key === 'isUse') {
      formData[key] = true
    } else if (key === 'isWeb') {
      formData[key] = true
    } else if (typeof formData[key] === 'boolean') {
      formData[key] = false
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0
    } else {
      formData[key] = ''
    }
  })
  
  if (type === 'edit' && row) {
    currentId.value = row.id
    
    // 过滤掉不需要的字段
    const filterFields = ['createTime', 'updateTime', 'creator', 'updater', 'deleted'];
    
    // 填充表单
    Object.keys(formData).forEach(key => {
      // 跳过不需要的字段
      if (filterFields.includes(key)) {
        return;
      }
      
      if (row[key] !== null && row[key] !== undefined) {
        formData[key] = row[key]
      }
    })
    
    console.log('编辑表单数据:', formData);
  } else {
    currentId.value = null
  }
}

// 提交表单
const submitForm = async () => {
  formLoading.value = true
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      // 移除后端不接受的字段
      const submitData = { ...formData };
      
      // 如果有选中的水厂，设置factoryId
      if (selectedFactory.value && selectedFactory.value.id) {
        submitData.factoryId = selectedFactory.value.id;
      }
      
      // 过滤掉这些字段
      const filterFields = ['createTime', 'updateTime', 'creator', 'updater', 'deleted'];
      filterFields.forEach(field => {
        if (field in submitData) {
          delete submitData[field];
        }
      });
      
      console.log('提交数据:', submitData);
      let result;
      
      if (currentId.value) {
        // 编辑
        result = await FactoryApi.updateFactoryDetail({ ...submitData, id: currentId.value })
      } else {
        // 新增
        result = await FactoryApi.addFactoryDetail(submitData)
      }
      
      console.log('接口响应:', result);
      
      // 更详细地检查响应状态
      let success = false;
      if (result) {
        if (typeof result.code !== 'undefined') {
          // 检查code值判断成功
          success = result.code === 0;
        } else if (typeof result.success !== 'undefined') {
          // 直接使用success字段
          success = result.success === true;
        } else {
          // 假设没有错误信息表示成功
          success = !result.error && !result.msg;
        }
      }
      
      if (success) {
        ElMessage.success(currentId.value ? '修改成功' : '新增成功')
        dialogVisible.value = false
        
        // 直接更新本地数据
        if (currentId.value) {
          // 编辑模式：更新当前行数据
          const index = tableList.value.findIndex(item => item.id === currentId.value);
          if (index !== -1) {
            tableList.value[index] = { ...tableList.value[index], ...submitData, id: currentId.value };
          }
        } else {
          // 新增模式：添加到列表开头
          if (result.data) {
            tableList.value.unshift(result.data);
            total.value++;
          }
        }
      } else {
        const errorMsg = result?.msg || (typeof result?.error === 'string' ? result.error : '操作失败，请重试');
        console.error('操作失败:', errorMsg);
        ElMessage.error(errorMsg)
      }
    }
  } catch (error) {
    console.error('表单提交错误:', error)
    ElMessage.error('操作失败，请检查网络连接')
  } finally {
    formLoading.value = false
  }
} 

// 删除水厂
const handleDelete = (id: number) => {
  ElMessageBox.confirm('确认删除该水厂数据吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true;
      console.log('删除ID:', id);
      
      const result = await FactoryApi.deleteFactory(id);
      console.log('删除响应:', result);
      
      // 详细检查响应状态
      let success = false;
      if (result) {
        if (typeof result.code !== 'undefined') {
          success = result.code === 0;
        } else if (typeof result.success !== 'undefined') {
          success = result.success === true;
        } else {
          success = !result.error && !result.msg;
        }
      }
      
      if (success) {
        ElMessage.success('删除成功');
        
        // 从完整数据列表中移除该条数据
        allDataList.value = allDataList.value.filter(item => item.id !== id);
        
        // 直接从本地列表中移除该条数据
        tableList.value = tableList.value.filter(item => item.id !== id);
        
        // 调整总数
        if (total.value > 0) {
          total.value--;
        }
        
        // 检查是否有搜索条件
        const hasSearchCondition = Object.values(searchParams).some(
          value => value !== '' && value !== null && value !== undefined
        );
        
        // 如果当前页已经没有数据
        if (tableList.value.length === 0) {
          if (currentPage.value > 1) {
            // 不是第一页，则跳转到上一页
            currentPage.value--;
            
            if (hasSearchCondition) {
              // 如果有搜索条件，使用前端分页
              handleLocalPaging();
            } else {
              // 否则重新请求后端
              await handleSearch();
            }
          } else if (hasSearchCondition) {
            // 是第一页且有搜索条件，重新进行前端过滤
            handleLocalPaging();
          } else {
            // 是第一页且没有搜索条件，重新获取数据
            await handleSearch();
          }
        }
      } else {
        const errorMsg = result?.msg || (typeof result?.error === 'string' ? result.error : '删除失败，请重试');
        console.error('删除失败:', errorMsg);
        ElMessage.error(errorMsg);
        
        // 删除失败时刷新数据
        await handleSearch();
      }
    } catch (error) {
      console.error('删除出错:', error);
      ElMessage.error('删除失败，请检查网络连接');
      
      // 出错时刷新数据
      await handleSearch();
    } finally {
      loading.value = false;
    }
  }).catch(() => {
    // 用户取消，无需操作
  });
}

// 查看详情
const handleView = (row: FactoryDetail) => {
  if (row && row.id) {
    getFactoryDetailById(row.id)
  } else {
    ElMessage.warning('获取记录ID失败')
  }
}

// 获取水厂详情
const getFactoryDetailById = async (id: number) => {
  try {
    const response = await FactoryApi.getFactoryDetail(id);
    console.log('详情原始响应:', response);
    
    // 处理响应
    const resData = response.data || response;
    console.log('处理后详情:', resData);
    
    if (resData) {
      detailData.value = resData;
      detailVisible.value = true;
    } else {
      ElMessage.warning('未找到水厂详情数据');
    }
  } catch (error) {
    console.error('获取水厂详情失败:', error);
    ElMessage.error('获取水厂详情失败');
  }
};

// 导出Excel
const exportExcel = async () => {
  loading.value = true
  try {
    const params = {
      ...searchParams,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }
    await FactoryApi.exportExcel(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}

// 初始化函数
const initData = async () => {
  try {
    console.log('初始化数据');
    // 获取水厂树
    await getFactoryTree();
    // 获取统计数据
    await getFactoryStats();
    // 表格初始为空，等待用户选择水厂
    tableList.value = [];
    total.value = 0;
  } catch (error) {
    console.error('初始化数据出错:', error);
    ElMessage.error('初始化数据失败');
  }
};

// 初始化
onMounted(() => {
  console.log('组件挂载');
  initData();
});
</script>

<style scoped lang='scss'>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 16px;
  }
}

.detail-dialog {
  :deep(.el-dialog__body) {
    max-height: 70vh;
    overflow-y: auto;
  }
}

.tree-container {
  padding-right: 16px;
  min-width: 200px;
  
  .factory-tree {
    width: 100%;
    
    :deep(.el-tree-node__content) {
      height: 36px;
    }
    
    :deep(.el-tree-node) {
      margin: 4px 0;
    }
  }
}

.content-container {
  padding-left: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}
</style>
