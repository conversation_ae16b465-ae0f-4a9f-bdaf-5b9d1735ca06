<template>
  <div class="screen-manage-container">
    <!-- 左侧画面列表 -->
    <div class="screen-list">
      <div class="list-header">
        <span>画面列表</span>
        <el-button type="primary" size="small" class="new-btn" @click="triggerFileSelect">
          <i class="el-icon-plus"></i> 新建
        </el-button>
        <!-- 只保留一个隐藏的文件输入 -->
        <input type="file" ref="svgFileInput" accept=".svg" style="display:none" @change="handleSvgFileSelected" />
      </div>
      <div class="list-content">
        <template v-if="screenList.length > 0">
          <div v-for="(item, index) in filteredScreenList" :key="index" class="screen-item" :class="{
            active: currentScreen?.id === item.id,
            'has-unsaved-changes': item.isTemp && !item.id.toString().startsWith('temp_')
          }" @click="selectScreen(item)">
            <div class="item-content">
              <span>{{ item.name }}</span>
              <span class="screen-date">{{ item.updateTime }}</span>
              <span v-if="item.isTemp && !item.id.toString().startsWith('temp_')" class="unsaved-mark">已修改</span>
            </div>
            <div class="item-actions">
              <el-button type="danger" size="small" circle @click.stop="handleDeleteScreen(item)" class="delete-btn">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
          </div>

          <!-- 显示临时画面（如果有，只显示新建的临时画面） -->
          <div v-if="currentScreen?.isTemp && currentScreen.id.toString().startsWith('temp_')"
            class="screen-item temp-item active">
            <div class="item-content">
              <span>{{ currentScreen.name }}</span>
              <span class="unsaved-mark">未保存</span>
            </div>
            <div class="item-actions">
              <el-button type="danger" size="small" circle @click.stop="handleDeleteScreen(currentScreen)"
                class="delete-btn">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
          </div>
        </template>
        <div v-else class="empty-list">
          <i class="el-icon-document"></i>
          <p>暂无画面</p>
          <!-- 空状态下新建按钮也只调用 triggerFileSelect，不再重复 input -->
          <el-button type="primary" size="small" class="new-btn" @click="triggerFileSelect">
            <i class="el-icon-plus"></i> 新建画面
          </el-button>
        </div>
      </div>
    </div>

    <!-- 右侧工艺流程图展示区域 -->
    <div class="screen-display">
      <div class="display-header">
        <div class="header-title">
          <el-input v-if="currentScreen" v-model="currentScreen.name" placeholder="请输入工艺流程图名称" class="screen-name-input"
            size="small" @change="handleScreenNameChange">
            <template #prepend>
              <i class="el-icon-document"></i>
            </template>
          </el-input>
          <span v-else>工艺流程图</span>
          <span class="update-time" v-if="currentScreen?.updateTime">
            最后更新: {{ currentScreen.updateTime }}
            <span v-if="currentPoints.length > 0" class="points-info">
              监测点: {{ currentPoints.length }}个
            </span>
          </span>
        </div>
        <div class="header-actions">
          <el-button type="warning" size="small" @click="uploadSvgForCurrentScreen" class="change-btn"
            v-if="currentScreen?.svg">
            <i class="el-icon-refresh-right"></i> 更换SVG
          </el-button>
          <el-button type="primary" size="small" class="export-btn" @click="exportSVG()">
            <i class="el-icon-download"></i> 导出SVG
          </el-button>
          <el-button type="success" size="small" @click="saveScreen" class="save-btn">
            <i class="el-icon-check"></i> 保存
          </el-button>
        </div>
      </div>

      <!-- 工艺流程图展示区 -->
      <div class="display-content">
        <template v-if="currentScreen?.svg || currentScreen?.svgUrl || currentScreen?.svgContent">
          <flow-chart-display :key="currentScreen?.id"
            :svg-content="currentScreen.svg || currentScreen.svgContent || currentScreen.svgUrl"
            :screen-id="currentScreen.id" :zoom="zoom" :points="currentPoints" @update:zoom="updateZoom"
            @update:points="updatePoints" @factory-changed="handleFactoryChanged" ref="flowChartRef" />
        </template>
        <template v-else>
          <div class="empty-placeholder">
            <i class="el-icon-picture-outline"></i>
            <p>暂无工艺流程图</p>
            <p class="sub-text">请上传SVG图片</p>
            <div class="upload-actions">
              <el-button type="primary" @click="uploadSvgForCurrentScreen" class="upload-btn">
                <i class="el-icon-upload2"></i> 上传SVG
              </el-button>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, onBeforeUnmount, nextTick, watch, computed } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { getScreenList, updateScreen, createScreen, deleteScreen } from '@/api/monitor/screenManage'
import { useAppStore } from '@/store/modules/app'
import FlowChartDisplay from './components/FlowChartDisplay.vue'
import { Delete } from '@element-plus/icons-vue'

// 定义FlowChartDisplay组件的引用类型
interface FlowChartInstance {
  exportSvgWithPoints: () => string;
  [key: string]: any;
}

const appStore = useAppStore()
const zoom = ref(1) // 默认100%缩放
const svgFileInput = ref<HTMLInputElement | null>(null)
// 防抖标记，防止重复创建
let isCreatingScreen = false

// 定义位点接口
interface Point {
  id: string;
  x: number;
  y: number;
  name: string;
  type: string;
  data?: any;
  isText: boolean;
}

// 定义画面类型接口
interface Screen {
  id: string | number
  name: string
  svg?: string
  svgUrl?: string  // 新增svgUrl字段，对应后端返回的字段
  svgContent?: string  // 新增svgContent字段，对应后端可能返回的字段
  updateTime?: string
  svgWidth?: number
  svgHeight?: number
  isTemp?: boolean
  [key: string]: any
}

// 画面列表
const screenList = ref<Screen[]>([])
// 当前选中的画面
const currentScreen = ref<Screen | null>(null)
// 当前画面的位点
const currentPoints = ref<Point[]>([])

// 类型声明
type FlowChartEvents = {
  'update:svg': [svg: string],
  'update:zoom': [zoom: number],
  'update:points': [points: Point[]],
  'factory-changed': [factoryId: string | number]
}

// 获取画面列表
const fetchScreenList = async () => {
  console.log('====== 开始获取画面列表 ======');
  try {
    // 获取水厂ID
    const factoryId = appStore.getCurrentStation?.id
    if (!factoryId) {
      console.log('未选择水厂，取消获取画面列表');
      ElMessage.warning('请先选择水厂')
      return
    }

    // 记录加载前的状态
    const hasTemporaryScreen = currentScreen.value?.isTemp && currentScreen.value?.id.toString().startsWith('temp_');
    const currentScreenId = currentScreen.value?.id;

    console.log('当前画面状态:',
      currentScreen.value ? `ID:${currentScreenId}, 是否临时:${hasTemporaryScreen}` : '无选中画面');

    // 从服务器获取画面列表
    const res = await getScreenList('factory', factoryId)

    if (Array.isArray(res)) {
      console.log(`从服务器获取到${res.length}个画面`);

      // 处理返回数据中的SVG字段
      const processedScreens = res.map(screen => {
        if (!screen.svg && (screen.svgUrl || screen.svgContent)) {
          return {
            ...screen,
            svg: screen.svgContent || screen.svgUrl || ''
          };
        }
        return screen;
      });

      console.log('处理后的画面数据:', processedScreens.length);

      // 临时画面处理策略:
      // 1. 如果当前有未保存的临时画面，保留它
      // 2. 如果临时画面已保存到服务器上(ID不再以temp_开头)，则清除本地临时状态
      // 3. 确保列表中不会出现重复的画面

      // 先保存当前选中的临时画面
      const currentTempScreenToKeep = hasTemporaryScreen ? { ...currentScreen.value! } : null;

      // 更新主画面列表为服务器返回的画面
      screenList.value = [...processedScreens];

      // 检查当前选中的画面是否在返回的列表中
      const currentScreenInList = processedScreens.find(s => s.id === currentScreenId);

      if (currentScreenInList) {
        console.log('服务器返回的列表中包含当前选中的画面');
        // 如果当前画面在列表中找到，更新到最新状态
        currentScreen.value = currentScreenInList;
        // 清除isTemp标记，因为它已经是服务器上的画面了
        if (currentScreen.value?.isTemp) {
          currentScreen.value.isTemp = false;
        }
      } else if (currentTempScreenToKeep) {
        console.log('保留未保存的临时画面:', currentTempScreenToKeep.id);
        // 如果是真正的临时画面(未保存)，将其添加到列表中
        const tempAlreadyInList = screenList.value.some(s => s.id === currentTempScreenToKeep.id);
        if (!tempAlreadyInList) {
          screenList.value.push(currentTempScreenToKeep);
        }
      } else if (processedScreens.length > 0) {
        console.log('选择列表中的第一个画面');
        // 如果没有当前选中的画面，选择列表中的第一个
        selectScreen(processedScreens[0]);
      } else {
        console.log('列表为空，清空当前选中画面');
        // 列表为空，清空当前选中的画面
        currentScreen.value = null;
        currentPoints.value = [];
      }
    } else {
      console.log('服务器返回的画面列表无效');
      screenList.value = [];

      // 如果当前有未保存的临时画面，保留它
      if (hasTemporaryScreen) {
        console.log('保留未保存的临时画面');
        if (!screenList.value.some(s => s.id === currentScreenId)) {
          screenList.value = [currentScreen.value!];
        }
      } else {
        console.log('清空当前选中画面');
        currentScreen.value = null;
        currentPoints.value = [];
      }
    }

    // 打印最终状态
    console.log(`画面列表获取完成，共${screenList.value.length}个画面，其中临时画面:`,
      screenList.value.filter(s => s.isTemp).map(s => ({ id: s.id, name: s.name })));
  } catch (error) {
    console.error('获取画面列表失败:', error)
    ElMessage.error('获取画面列表失败')

    // 保留临时画面以避免数据丢失
    if (currentScreen.value?.isTemp) {
      console.log('发生错误，保留当前临时画面');
      screenList.value = [currentScreen.value];
    } else {
      screenList.value = []
      currentScreen.value = null
      currentPoints.value = []
    }
  } finally {
    console.log('====== 画面列表获取完成 ======');
  }
}

// 添加计算属性: 过滤后的屏幕列表
const filteredScreenList = computed(() => {
  return screenList.value.filter(item => !item.isTemp || !item.id.toString().startsWith('temp_'));
});

// 选择画面
const selectScreen = async (screen: Screen) => {
  console.log('====== 开始选择画面:', screen.id, screen.name, '======');

  // 检查是否选择的是临时画面
  const isSelectingTemp = screen.isTemp && screen.id.toString().startsWith('temp_');
  const isCurrentTemp = currentScreen.value?.isTemp && currentScreen.value?.id.toString().startsWith('temp_');

  // 检查当前是否有未保存的修改
  if (isCurrentTemp && !isSelectingTemp) {
    console.log('当前有未保存的临时画面，提示用户确认');
    // 弹出确认框
    try {
      await ElMessageBox.confirm(
        '当前画面尚未保存，切换将丢失未保存的更改，是否继续？',
        '提示',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
    } catch (e) {
      // 用户取消了操作
      console.log('用户取消了切换画面操作');
      return;
    }
  }

  // 如果选择的是同一个画面，不需要重新加载数据
  if (currentScreen.value?.id === screen.id) {
    console.log('选择了当前已选中的画面，不重新加载');
    return;
  }

  try {
    // 处理临时画面和已保存画面的选择逻辑
    if (isSelectingTemp) {
      console.log('选择的是临时画面，直接使用本地数据');
      // 临时画面直接使用本地数据
      currentScreen.value = screen;

      // 检查SVG是否是URL格式
      const isUrl = isSvgUrl(screen.svg);
      console.log('临时画面SVG是否为URL格式:', isUrl);

      // 如果是URL格式，尝试获取实际SVG内容
      if (isUrl && screen.svg) {
        console.log('正在从URL获取SVG内容:', screen.svg);

        try {
          // fetchSvgFromUrl函数内部已有加载提示
          const svgContent = await fetchSvgFromUrl(screen.svg);

          if (svgContent) {
            console.log('成功从URL获取SVG内容，长度:', svgContent.length);

            // 检查SVG是否包含监测点
            const hasSvgPoints = svgContent.includes('id="monitoring-points"') ||
              svgContent.includes('class="monitoring-points-layer"') ||
              svgContent.includes('data-point="true"');

            console.log('获取的SVG是否包含监测点:', hasSvgPoints);

            // 更新临时画面的SVG内容为实际内容
            screen.svg = svgContent;
            currentScreen.value.svg = svgContent;

            // 同时更新列表中的数据
            const screenIndex = screenList.value.findIndex(item => item.id === screen.id);
            if (screenIndex !== -1) {
              screenList.value[screenIndex].svg = svgContent;
            }
          } else {
            console.warn('无法从URL获取SVG内容，将使用原始URL');
            // 确保画面有有效的宽高信息，方便渲染
            if (!screen.svgWidth || !screen.svgHeight) {
              screen.svgWidth = 800; // 默认宽度
              screen.svgHeight = 600; // 默认高度
              console.log('设置URL格式SVG的默认尺寸:', screen.svgWidth, 'x', screen.svgHeight);

              // 同时更新currentScreen
              if (currentScreen.value) {
                currentScreen.value.svgWidth = screen.svgWidth;
                currentScreen.value.svgHeight = screen.svgHeight;
              }
            }
          }
        } catch (error) {
          console.error('从URL获取SVG内容失败:', error);
          ElMessage.error('加载SVG内容失败，将使用URL引用方式');
        }
      }

      // 不再自动解析SVG中的位点，直接使用当前点位数据
      console.log(`已加载临时画面"${screen.name}"，不解析SVG中的位点`);

      // 首先检查是否直接包含points数组
      if (Array.isArray(screen.points) && screen.points.length > 0) {
        console.log(`从临时画面直接包含的points数组中加载了${screen.points.length}个点位数据`);
        // 检查是否需要转换格式(根据是否有positionX字段判断)
        if (screen.points[0].positionX !== undefined) {
          // API格式，需要转换
          currentPoints.value = convertApiPointsToInternalFormat(screen.points);
          console.log('转换后的内部点位数据:', currentPoints.value);
        } else {
          // 已经是内部格式
          currentPoints.value = screen.points;
        }
      } else {
        // 尝试从extraJson中获取点位数据
        try {
          if (screen.extraJson) {
            const extraData = JSON.parse(screen.extraJson);
            if (extraData && Array.isArray(extraData.points) && extraData.points.length > 0) {
              console.log(`从临时画面的extraJson中加载了${extraData.points.length}个点位数据`);
              currentPoints.value = extraData.points;
            } else {
              console.log('临时画面的extraJson中没有点位数据或格式不正确');
              currentPoints.value = [];
            }
          } else {
            console.log('临时画面数据中没有extraJson字段');
            currentPoints.value = [];
          }
        } catch (e) {
          console.error('解析临时画面extraJson中的点位数据失败:', e);
          currentPoints.value = [];
        }
      }
    } else {
      console.log('选择的是已保存的画面，从服务器获取最新数据');
      // 从服务器获取最新数据
      const res = await getScreenList('screen', screen.id);

      if (Array.isArray(res) && res.length > 0) {
        const screenData = res[0];
        console.log('服务器返回的画面数据:', screenData);

        // 处理SVG内容，优先使用svg字段，其次使用svgUrl或svgContent
        if (!screenData.svg && (screenData.svgUrl || screenData.svgContent)) {
          console.log('使用svgUrl或svgContent替代svg字段', {
            hasSvgUrl: !!screenData.svgUrl,
            hasSvgContent: !!screenData.svgContent
          });
          screenData.svg = screenData.svgContent || screenData.svgUrl || '';
        }

        // 确保点位有效，如果没有点位数据，初始化为空数组
        if (!screenData.points) {
          screenData.points = [];
        }

        // 检查SVG是否是URL格式
        const isUrl = isSvgUrl(screenData.svg);
        console.log('服务器画面SVG是否为URL格式:', isUrl);

        // 如果是URL格式，尝试获取实际SVG内容
        if (isUrl && screenData.svg) {
          console.log('正在从URL获取SVG内容:', screenData.svg);

          try {
            // fetchSvgFromUrl函数内部已有加载提示，不需要重复显示
            const svgContent = await fetchSvgFromUrl(screenData.svg);

            if (svgContent) {
              console.log('成功从URL获取SVG内容，长度:', svgContent.length);

              // 检查SVG是否包含监测点
              const hasSvgPoints = svgContent.includes('id="monitoring-points"') ||
                svgContent.includes('class="monitoring-points-layer"') ||
                svgContent.includes('data-point="true"');

              console.log('获取的SVG是否包含监测点:', hasSvgPoints);

              // 更新画面的SVG内容为实际内容
              screenData.svg = svgContent;

              // 如果SVG中包含监测点，但extraJson中没有点位数据，尝试解析SVG中的点位
              if (hasSvgPoints && (!screenData.extraJson || !JSON.parse(screenData.extraJson || '{}').points)) {
                console.log('SVG中包含监测点，但extraJson中没有点位数据，将在加载后尝试解析SVG中的点位');
              }
            } else {
              console.warn('无法从URL获取SVG内容，将使用原始URL');
              // 保持原始URL不变
              // 确保画面有有效的宽高信息，方便渲染
              if (!screenData.svgWidth || !screenData.svgHeight) {
                screenData.svgWidth = 800; // 默认宽度
                screenData.svgHeight = 600; // 默认高度
                console.log('设置URL格式SVG的默认尺寸:', screenData.svgWidth, 'x', screenData.svgHeight);
              }
            }
          } catch (error) {
            console.error('从URL获取SVG内容失败:', error);
            ElMessage.error('加载SVG内容失败，将使用URL引用方式');
          }
        }

        currentScreen.value = screenData;

        // 清除可能被错误设置的临时标记
        if (currentScreen.value?.isTemp) {
          currentScreen.value.isTemp = false;
        }

        // 首先检查服务器是否直接返回了points数组
        if (Array.isArray(screenData.points) && screenData.points.length > 0) {
          console.log(`从服务器直接返回的points数组中加载了${screenData.points.length}个点位数据`);
          // 将API格式的点位转换为内部使用的格式
          currentPoints.value = convertApiPointsToInternalFormat(screenData.points);
          console.log('转换后的内部点位数据:', currentPoints.value);
        } else {
          // 如果服务器没有直接返回points数组，尝试从extraJson中获取点位数据
          console.log('服务器未直接返回points数组，尝试从extraJson中加载点位');

          try {
            if (screenData.extraJson) {
              const extraData = JSON.parse(screenData.extraJson);
              if (extraData && Array.isArray(extraData.points) && extraData.points.length > 0) {
                console.log(`从extraJson中加载了${extraData.points.length}个点位数据`);
                currentPoints.value = extraData.points;
              } else {
                console.log('extraJson中没有点位数据或格式不正确');
                currentPoints.value = [];
              }
            } else {
              console.log('画面数据中没有extraJson字段');
              currentPoints.value = [];
            }
          } catch (e) {
            console.error('解析extraJson中的点位数据失败:', e);
            currentPoints.value = [];
          }
        }
      } else {
        console.warn('获取画面详情失败，画面可能已被删除');
        ElMessage.warning('获取画面详情失败，画面可能已被删除');
        currentScreen.value = null;
        currentPoints.value = [];
        // 重新获取列表
        fetchScreenList();
      }
    }
  } catch (error) {
    console.error('获取画面详情失败:', error);
    ElMessage.error('获取画面详情失败');
    currentScreen.value = null;
    currentPoints.value = [];
  } finally {
    console.log('====== 画面选择完成 ======');

    // 始终使用100%缩放
    setTimeout(() => {
      console.log('画面选择完成后设置为100%缩放');
      try {
        // 检查当前SVG内容
        if (currentScreen.value?.svg) {
          console.log(`当前SVG内容长度: ${currentScreen.value.svg.length}, 是否包含<svg>标签: ${currentScreen.value.svg.includes('<svg')}`);
        } else {
          console.warn('当前画面没有SVG内容');
        }

        // 重置缩放状态，确保SVG能够正确显示
        console.log('重置缩放状态，设置为100%缩放');
        updateZoom(1.0); // 设置为100%缩放

        // 调用SVG居中函数
        if (flowChartRef.value && typeof flowChartRef.value.calculateInitialZoom === 'function') {
          setTimeout(() => {
            if (flowChartRef.value) {
              // 再次检查SVG内容
              const svgElement = document.querySelector('.svg-content svg');
              if (svgElement) {
                console.log('SVG元素已存在于DOM中，调用居中函数');
                flowChartRef.value.calculateInitialZoom();
              } else {
                console.warn('SVG元素不存在于DOM中，尝试重新设置内容');
                // 如果SVG元素不存在，尝试重新设置SVG内容
                if (currentScreen.value?.svg) {
                  console.log('尝试重新触发SVG内容更新');
                  const tempSvg = currentScreen.value.svg;
                  currentScreen.value.svg = '';
                  // 使用nextTick确保视图更新
                  nextTick(() => {
                    if (currentScreen.value) {
                      currentScreen.value.svg = tempSvg;
                      // 延迟再次调用居中函数
                      setTimeout(() => {
                        if (flowChartRef.value) {
                          flowChartRef.value.calculateInitialZoom();
                        }
                      }, 300);
                    }
                  });
                }
              }
            }
          }, 300);
        }
      } catch (e) {
        console.error('画面选择后设置缩放失败:', e);
        // 失败时也使用100%
        updateZoom(1.0);
      }
    }, 800);
  }
}

// 从URL获取SVG内容
const fetchSvgFromUrl = async (url: string): Promise<string | null> => {
  console.log('开始从URL获取SVG内容:', url);
  try {
    // 添加时间戳防止缓存
    const fetchUrl = url.includes('?') ? `${url}&_t=${Date.now()}` : `${url}?_t=${Date.now()}`;

    // 显示加载提示
    const loadingInstance = ElMessage.info({
      message: '正在获取SVG内容...',
      duration: 0,
      showClose: false
    });

    try {
      const response = await fetch(fetchUrl);

      if (!response.ok) {
        console.error('获取SVG内容失败:', response.status, response.statusText);
        ElMessage.error(`获取SVG内容失败: ${response.status} ${response.statusText}`);
        return null;
      }

      const svgText = await response.text();

      // 检查是否为有效的SVG内容
      if (!svgText) {
        console.warn('获取到的内容为空');
        ElMessage.warning('获取到的SVG内容为空');
        return null;
      }

      // 判断内容是否包含SVG标签
      if (!svgText.includes('<svg')) {
        // 检查是否可能是JSON或其他格式错误的响应
        try {
          const jsonData = JSON.parse(svgText);
          console.warn('获取到的内容是JSON而非SVG:', jsonData);
          ElMessage.warning('后端返回了JSON而非SVG格式数据');
          return null;
        } catch {
          // 不是JSON，可能是其他格式
          console.warn('获取到的内容不是有效SVG格式:', svgText.substring(0, 100));
          ElMessage.warning('获取到的内容不是有效SVG格式');
          return null;
        }
      }

      console.log('成功获取SVG内容，长度:', svgText.length);

      // 检查SVG内容是否完整
      if (svgText.length < 50) {
        console.warn('SVG内容异常短，可能不完整:', svgText);
        ElMessage.warning('获取的SVG内容过短，可能不完整');
      }

      // 如果是URL内容，确保返回的是完整SVG而不是URL
      if (svgText.trim().startsWith('http')) {
        console.warn('返回的内容是URL而非SVG:', svgText);
        ElMessage.warning('后端返回了URL而非SVG内容，将直接使用URL');
        // 返回null会让系统使用原始URL
        return null;
      }

      // 加载成功提示
      ElMessage.success('SVG内容获取成功');
      return svgText;
    } finally {
      // 关闭加载提示
      loadingInstance.close();
    }
  } catch (error) {
    console.error('获取SVG内容失败:', error);
    ElMessage.error(`获取SVG内容失败: ${error}`);
    return null;
  }
}

// 判断SVG是否是URL格式的工具函数
const isSvgUrl = (svgContent?: string) => {
  if (!svgContent) return false;
  const content = svgContent.trim();
  return content.startsWith('http://') ||
    content.startsWith('https://') ||
    content.startsWith('//') ||
    content.endsWith('.svg') ||
    content.endsWith('.ubak') ||
    content.includes('/api/');
}

// 缩放控制
const updateZoom = (newZoom: number) => {
  zoom.value = newZoom
}

// 更新位点数据
const updatePoints = (points: Point[]) => {
  console.log(`收到位点更新，共${points.length}个点位`);
  // 只在内存中保存当前点位状态，不修改Screen对象
  currentPoints.value = points;
}

// 处理画面名称变更
const handleScreenNameChange = (newName: string) => {
  if (!currentScreen.value) return

  // 更新当前画面名称
  currentScreen.value.name = newName || '新建工艺流程图'

  // 如果是已保存的画面，将其标记为需要保存
  if (!currentScreen.value.isTemp) {
    // 将已保存画面转为临时状态，等待用户点击保存按钮
    currentScreen.value.isTemp = true
    // 更新修改时间
    currentScreen.value.updateTime = new Date().toISOString()

    // 通知用户需要保存
    ElMessage.info('画面名称已更新，点击保存按钮生效')
  }

  // 同步更新左侧列表中的对应项
  const screenIndex = screenList.value.findIndex(item => item.id === currentScreen.value?.id)
  if (screenIndex !== -1) {
    // 更新列表中对应画面的名称
    screenList.value[screenIndex].name = currentScreen.value.name
    // 如果修改了标记为临时状态
    if (!screenList.value[screenIndex].isTemp && currentScreen.value.isTemp) {
      screenList.value[screenIndex].isTemp = true
    }
  }

  // 不需要刷新SVG或重新加载，保持当前缩放状态
}

// 新建按钮触发文件选择
const triggerFileSelect = () => {
  // 检查是否正在处理文件
  if (isProcessingFile) {
    console.log('文件正在处理中，忽略新建操作');
    ElMessage.info('系统正在处理另一个操作，请稍候');
    return;
  }

  // 清除延迟定时器
  if (fileProcessDebounceTimer !== null) {
    clearTimeout(fileProcessDebounceTimer);
    fileProcessDebounceTimer = null;
  }

  // 点击新建按钮时，清空当前选中的画面，强制创建新画面
  currentScreen.value = null;

  // 重置防重复创建的标志，确保能正常创建
  isCreatingNewScreen = false;

  // 触发文件选择器
  if (svgFileInput.value) {
    svgFileInput.value.value = ''; // 允许重复选择同一文件
    console.log('触发文件选择器点击');
    svgFileInput.value.click();
  }
}

// 为当前选中画面上传SVG
const uploadSvgForCurrentScreen = () => {
  // 检查是否正在处理文件
  if (isProcessingFile) {
    console.log('文件正在处理中，忽略SVG更新操作');
    ElMessage.info('系统正在处理另一个操作，请稍候');
    return;
  }

  // 确保currentScreen值不为null，表明这是为现有画面更新SVG
  if (!currentScreen.value) {
    ElMessage.warning('没有选中的画面可以更新');
    return;
  }

  if (svgFileInput.value) {
    svgFileInput.value.value = ''; // 允许重复选择同一文件
    console.log('触发SVG更新文件选择器点击');
    svgFileInput.value.click();
  }
}

// 跟踪当前上传的文件ID，避免重复处理
let currentFileUploadId = '';

// 添加全局处理锁，只允许一次处理
let isProcessingFile = false;
let fileProcessDebounceTimer: number | null = null;

// 处理SVG文件选择 - 强化防重复机制版
const handleSvgFileSelected = async (event: Event) => {
  // 清除任何挂起的文件处理定时器
  if (fileProcessDebounceTimer !== null) {
    clearTimeout(fileProcessDebounceTimer);
    fileProcessDebounceTimer = null;
  }

  // 严格控制文件处理流程，只能同时有一个处理
  if (isProcessingFile) {
    console.log('文件处理过程中，忽略新请求');
    ElMessage.warning('文件正在处理中，请稍候');
    return;
  }

  const target = event.target as HTMLInputElement;
  if (!target || !target.files || target.files.length === 0) {
    console.log('没有选择文件，取消操作');
    return;
  }

  const file = target.files[0];
  if (!file.type.includes('svg') && !file.name.toLowerCase().endsWith('.svg')) {
    ElMessage.error('请选择有效的SVG文件');
    return;
  }

  // 设置全局处理锁，阻止重复处理
  isProcessingFile = true;
  console.log('====== 开始处理SVG文件 ======');

  try {
    // 显示上传中提示
    const loadingInstance = ElMessage.info({
      message: '正在处理SVG文件...',
      duration: 0,
      showClose: false
    });

    try {
      // 使用Promise包装FileReader，便于使用async/await
      const svgContent = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = (e) => reject(new Error('读取文件失败'));
        reader.readAsText(file);
      });

      // 基本验证
      if (!svgContent || !svgContent.includes('<svg')) {
        ElMessage.error('无效的SVG文件');
        return;
      }

      // 获取文件名作为画面名称（去除扩展名）
      const fileName = file.name.replace(/\.svg$/i, '');

      // 判断当前操作类型
      const isCreatingNew = currentScreen.value === null ||
        (currentScreen.value?.isTemp && currentScreen.value?.id?.toString()?.startsWith('temp_'));

      // 处理新建场景
      if (isCreatingNew) {
        console.log('执行新建画面操作');
        createNewScreen(svgContent, fileName);
      }
      // 更新现有画面
      else if (currentScreen.value) {
        console.log('更新现有画面SVG');

        // 保存当前缩放状态
        const currentZoom = zoom.value;
        console.log('保存当前缩放比例:', currentZoom);

        // 更新SVG内容
        currentScreen.value.svg = svgContent;

        // 标记为已修改
        if (!currentScreen.value.isTemp) {
          currentScreen.value.isTemp = true;
          // 同步更新列表
          const screenIndex = screenList.value.findIndex(item =>
            item.id === currentScreen.value?.id
          );
          if (screenIndex !== -1) {
            screenList.value[screenIndex].isTemp = true;
          }
        }

        // 设置为100%缩放并居中
        setTimeout(() => {
          console.log('SVG更新后设置为100%缩放并居中');
          updateZoom(1.0);
          if (flowChartRef.value && typeof flowChartRef.value.calculateInitialZoom === 'function') {
            try {
              flowChartRef.value.calculateInitialZoom();
            } catch (e) {
              console.error('SVG更新后调用居中函数失败:', e);
              // 失败时也保持100%缩放
              updateZoom(1.0);
            }
          }
        }, 1000);

        // 确认是否清空点位
        if (currentPoints.value.length > 0) {
          const confirmReset = await ElMessageBox.confirm(
            '更换SVG会导致现有点位位置错位，是否清空现有点位？',
            '提示',
            {
              confirmButtonText: '清空点位',
              cancelButtonText: '保留点位',
              type: 'warning'
            }
          ).catch(() => false);

          if (confirmReset) {
            currentPoints.value = [];

            // 更新extraJson
            if (currentScreen.value) {
              try {
                const extraData = { points: [], containsPoints: false };
                currentScreen.value.extraJson = JSON.stringify(extraData);
              } catch (e) {
                console.error('更新extraJson失败:', e);
              }
            }
          }
        }

        if (currentScreen.value) {
          ElMessage.success(`已更新画面"${currentScreen.value.name || '未命名'}"的SVG内容`);
        }
      } else {
        // 意外情况：既不是新建也没有当前画面，创建新画面
        console.log('未找到当前画面，创建新画面');
        createNewScreen(svgContent, fileName);
      }

      // 添加延时，确保DOM完成更新
      await new Promise(resolve => setTimeout(resolve, 200));

    } catch (error) {
      console.error('处理SVG文件失败:', error);
      ElMessage.error('处理SVG文件失败');
    } finally {
      loadingInstance.close();
    }
  } finally {
    // 重置文件输入框，允许重复选择同一文件
    if (svgFileInput.value) {
      svgFileInput.value.value = '';
    }

    // 延迟解除处理锁，避免太快接收下一个请求
    setTimeout(() => {
      isProcessingFile = false;
      console.log('====== SVG文件处理完成 ======');
    }, 1000);
  }
}

// 创建临时画面的公共变量，用于阻止重复创建
let isCreatingNewScreen = false;
let lastCreatedScreenId = '';
// 上一次创建画面的时间戳，用于防止重复创建
let lastCreationTimestamp = 0;

const createNewScreen = (svgContent: string, screenName: string = '新建工艺流程图') => {
  // 防止重复创建
  if (isCreatingNewScreen) {
    console.log('已有画面正在创建中，跳过重复创建');
    return;
  }

  // 检查时间间隔，防止短时间内重复创建 
  // 仅在非首次创建时进行时间间隔检查
  const now = Date.now();
  const minInterval = 2000; // 增加到2秒
  if (lastCreationTimestamp > 0 && now - lastCreationTimestamp < minInterval) {
    console.log(`距离上次创建时间过短(${now - lastCreationTimestamp}ms)，防止重复创建`);
    return;
  }

  // 更新创建时间戳
  lastCreationTimestamp = now;

  // 锁定创建过程
  isCreatingNewScreen = true;
  console.log('正在创建新画面，已锁定创建过程');

  try {
    // 先清空当前点位数据
    currentPoints.value = []

    // 生成唯一的临时ID
    const tempId = 'temp_' + Date.now()

    // 如果和上次创建的ID相同或时间接近，说明是重复调用，直接返回
    if (tempId === lastCreatedScreenId) {
      console.log('检测到重复创建，ID与上次相同:', tempId);
      isCreatingNewScreen = false;
      return;
    }

    // 检查上一次创建的ID时间戳部分是否接近
    if (lastCreatedScreenId && lastCreatedScreenId.startsWith('temp_')) {
      const lastTimestamp = parseInt(lastCreatedScreenId.replace('temp_', ''), 10);
      const currentTimestamp = parseInt(tempId.replace('temp_', ''), 10);
      if (!isNaN(lastTimestamp) && !isNaN(currentTimestamp)) {
        const timeDiff = currentTimestamp - lastTimestamp;
        if (timeDiff > 0 && timeDiff < 3000) {  // 增加到3秒
          console.log(`检测到重复创建，时间间隔太短(${timeDiff}ms)，忽略此次创建`);
          isCreatingNewScreen = false;
          return;
        }
      }
    }

    // 记录本次创建的ID
    lastCreatedScreenId = tempId;
    console.log('设置最新创建的画面ID:', tempId);

    // 如果列表中已存在同名临时画面，先移除它
    const existingTempScreens = screenList.value.filter(
      item => item.isTemp && item.id.toString().startsWith('temp_')
    );
    if (existingTempScreens.length > 0) {
      console.log(`移除${existingTempScreens.length}个现有临时画面`);
      screenList.value = screenList.value.filter(
        item => !(item.isTemp && item.id.toString().startsWith('temp_'))
      );
    }

    const newScreen: Screen = {
      id: tempId, // 仅在前端使用的临时ID，保存时由后端生成真实ID
      name: screenName,
      svg: svgContent,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      isTemp: true
    }

    // 将新创建的临时画面设为当前选中的画面
    currentScreen.value = newScreen

    // 将临时画面添加到画面列表中，确保在左侧列表中显示
    // 检查是否已存在相同ID的画面
    if (!screenList.value.some(item => item.id === tempId)) {
      screenList.value.push(newScreen);
      console.log('创建临时画面并添加到列表:', tempId, screenList.value.length);
    } else {
      console.log('列表中已存在相同ID的画面，跳过添加:', tempId);
    }

    ElMessage.success(`已创建新画面"${screenName}"`);
  } finally {
    // 1000ms后解锁创建过程，防止短时间内重复创建
    setTimeout(() => {
      isCreatingNewScreen = false;
      console.log('创建过程已解锁');
    }, 1000);
  }
}

// 将内部使用的点位数据转换为符合后端API要求的格式
const convertPointsToApiFormat = (points: any[], screenId?: number | string): any[] => {
  return points.map(point => {
    // 提取必要信息
    const apiPoint: any = {
      name: point.name || '',
      showType: point.type || point.showType || 'circle',
      positionX: String(point.x || 0),
      positionY: String(point.y || 0),
    };

    // 设置screenId (必需字段)
    if (screenId) {
      apiPoint.screenId = screenId;
    }

    // 设置颜色，优先使用data.color，如果没有则使用point.color
    if (point.data?.color) {
      apiPoint.color = point.data.color;
    } else if (point.color) {
      apiPoint.color = point.color;
    }

    // 设置关联指标代码
    if (point.data?.indicator) {
      apiPoint.indicatorCode = point.data.indicator;
    } else if (point.indicatorCode) {
      apiPoint.indicatorCode = point.indicatorCode;
    }

    // 设置是否显示边框 - 仅对文本类型
    if (point.isText && point.data?.hasBackground !== undefined) {
      apiPoint.showDisplay = point.data.hasBackground ? 1 : 0;
    } else if (point.showDisplay !== undefined) {
      apiPoint.showDisplay = point.showDisplay;
    }

    return apiPoint;
  });
};

// 将API格式的点位数据转换为内部使用的格式
const convertApiPointsToInternalFormat = (apiPoints: any[]): any[] => {
  if (!apiPoints || !Array.isArray(apiPoints)) return [];

  return apiPoints.map(point => {
    // 基本点位信息
    const internalPoint: any = {
      id: `point_${Date.now()}_${Math.floor(Math.random() * 1000)}`, // 生成唯一ID
      name: point.name || '',
      x: Number(point.positionX) || 0,
      y: Number(point.positionY) || 0,
      type: point.showType || 'circle',
      // 根据showType确定是否为文本类型
      isText: point.showType === 'text',
      data: {}
    };

    // 填充data对象
    if (point.color) {
      internalPoint.data.color = point.color;
    }

    if (point.indicatorCode) {
      internalPoint.data.indicator = point.indicatorCode;
    }

    // 处理文本类型特有属性
    if (internalPoint.isText) {
      internalPoint.data.fontSize = 14; // 默认字体大小
      internalPoint.data.fontWeight = 'normal'; // 默认字体粗细
      internalPoint.data.hasBackground = point.showDisplay === 1;
      internalPoint.data.backgroundColor = 'rgba(255, 255, 255, 0.5)'; // 默认背景色
    }

    return internalPoint;
  });
};

// 保存画面
const saveScreen = async () => {
  if (!currentScreen.value || !currentScreen.value.svg) {
    ElMessage.warning('当前没有可保存的画面')
    return
  }

  // 显示加载提示
  const loadingInstance = ElMessage.info({
    message: '正在处理SVG内容，请稍候...',
    duration: 0,
    showClose: false
  });

  try {
    // 判断是新建还是更新
    const isNew = currentScreen.value.id.toString().startsWith('temp_')
    console.log(`准备${isNew ? '新建' : '更新'}画面:`, currentScreen.value.id, currentScreen.value.name)

    // 检查SVG是否为URL格式
    const isSvgUrlFormat = isSvgUrl(currentScreen.value.svg);
    console.log('当前SVG是否为URL格式:', isSvgUrlFormat);

    // 对任何SVG（无论是URL格式还是普通SVG），都尝试使用FlowChartDisplay组件生成包含点位的完整SVG
    let svgWithPoints = currentScreen.value.svg;

    // 确保flowChartRef可用
    if (flowChartRef.value) {
      try {
        console.log('开始生成包含监测点的SVG');
        // 导出包含监测点的完整SVG (异步处理)
        svgWithPoints = await flowChartRef.value.exportSvgWithPoints();
        console.log('已生成包含监测点的SVG，长度:', svgWithPoints.length);

        if (isSvgUrlFormat) {
          console.log('成功将URL格式SVG转换为带点位的完整SVG内容');
        }
      } catch (error) {
        console.error('生成SVG时出错:', error);
        // 失败时回退到原始内容
        console.warn('导出SVG失败，将使用原始SVG内容');
      }
    } else {
      console.warn('flowChartRef不可用，将使用原始SVG');
    }

    // 提取SVG的宽高信息
    const svgDimensions = isSvgUrlFormat
      ? { width: currentScreen.value.svgWidth || 0, height: currentScreen.value.svgHeight || 0 }
      : extractSvgDimensions(svgWithPoints);

    console.log('SVG尺寸:', svgDimensions);

    // 无需在这里转换点位数据格式，在各自分支中处理

    // 直接保存数据，不显示确认对话框
    if (isNew) {
      // 保存临时画面
      const tempScreen = { ...currentScreen.value }
      // 确保id不为空
      const tempId = tempScreen.id as string | number

      // 新建画面
      const factoryId = appStore.getCurrentStation?.id
      if (!factoryId) {
        ElMessage.warning('请先选择水厂')
        return
      }

      // 对于新建画面，先不设置screenId，创建成功后会得到新的screenId
      const apiPoints = convertPointsToApiFormat(currentPoints.value);
      console.log(`转换后的点位数据格式 (${apiPoints.length}个):`, apiPoints);

      // 准备保存数据
      const screenData = {
        name: tempScreen.name,
        factoryId,
        svgContent: svgWithPoints,      // 使用包含监测点的SVG
        svgWidth: svgDimensions.width,  // SVG宽度
        svgHeight: svgDimensions.height, // SVG高度
        points: apiPoints,              // 使用转换后的点位数组
        extraJson: JSON.stringify({
          containsPoints: true        // 标记为内部包含监测点
        })
      }

      console.log("保存的数据:", {
        name: screenData.name,
        factoryId: screenData.factoryId,
        svgType: isSvgUrlFormat ? '原始为URL，已转换为完整SVG' : 'SVG内容',
        svgLength: typeof svgWithPoints === 'string' ? svgWithPoints.length : '未知',
        pointsCount: apiPoints.length,
        containsMonitoringPoints: svgWithPoints.includes('id="monitoring-points"')
      });

      try {
        // 先清空当前画面，避免状态混淆
        currentScreen.value = null

        // 先从列表中移除临时画面，避免重复显示
        screenList.value = screenList.value.filter(item => item.id !== tempId)
        console.log('保存前已从列表中移除临时画面:', tempId)

        // 保存到服务器
        const res = await createScreen(screenData)

        if (res) {
          ElMessage.success(`成功保存画面"${tempScreen.name}"`)
          console.log('画面已成功保存，新ID:', res.id)

          // 重新获取画面列表
          await fetchScreenList()

          // 延迟一点再选择新画面，确保列表更新完成
          setTimeout(() => {
            // 选择新创建的画面
            const newScreen = screenList.value.find(item => item.id === res.id)
            if (newScreen) {
              // 确保新画面有SVG内容
              if (!newScreen.svg) {
                console.warn('新创建的画面没有SVG内容，尝试使用保存的SVG');
                newScreen.svg = svgWithPoints || screenData.svgContent;
              }

              selectScreen(newScreen).then(() => {
                // 固定使用100%缩放，不进行自适应缩放
                console.log('新画面创建后设置为100%缩放并居中');

                // 延迟处理，确保SVG内容已加载
                setTimeout(() => {
                  updateZoom(1.0);
                  if (flowChartRef.value) {
                    // 检查SVG内容是否存在
                    if (newScreen.svg && (!currentScreen.value?.svg || currentScreen.value.svg !== newScreen.svg)) {
                      console.log('手动设置SVG内容');
                      if (currentScreen.value) {
                        currentScreen.value.svg = newScreen.svg;
                      }
                    }

                    // 延迟再次检查并调用居中函数
                    setTimeout(() => {
                      if (flowChartRef.value && typeof flowChartRef.value.calculateInitialZoom === 'function') {
                        try {
                          flowChartRef.value.calculateInitialZoom();
                        } catch (e) {
                          console.error('新画面创建后调用居中函数失败:', e);
                          // 失败时也使用100%缩放
                          updateZoom(1.0);
                        }
                      }
                    }, 500);
                  }
                }, 800);
              })
            }
          }, 100)
        } else {
          ElMessage.error('保存失败，服务器未返回有效数据')

          // 如果没有有效结果，恢复临时画面避免丢失
          if (!screenList.value.some(s => s.id === tempId)) {
            // 确保tempScreen的id属性存在
            const safeScreen: Screen = {
              id: tempId,
              name: tempScreen.name || '未命名画面',
              svg: tempScreen.svg,
              updateTime: tempScreen.updateTime,
              isTemp: true
            }
            screenList.value.push(safeScreen)
            currentScreen.value = safeScreen
          }
        }
      } catch (error) {
        console.error('保存画面失败:', error)
        ElMessage.error('保存画面失败，请重试')

        // 恢复临时画面到列表中
        if (!screenList.value.some(s => s.id === tempId)) {
          // 确保tempScreen的id属性存在
          const safeScreen: Screen = {
            id: tempId,
            name: tempScreen.name || '未命名画面',
            svg: tempScreen.svg,
            updateTime: tempScreen.updateTime,
            isTemp: true
          }
          screenList.value.push(safeScreen)
          currentScreen.value = safeScreen
        }
      }
    } else if (currentScreen.value) { // 确保currentScreen.value不为null
      // 更新现有画面
      try {
        // 对于更新画面，使用当前画面ID
        const screenId = currentScreen.value.id;
        const apiPoints = convertPointsToApiFormat(currentPoints.value, screenId);
        console.log(`转换后的点位数据格式 (${apiPoints.length}个):`, apiPoints);

        // 准备更新数据
        const updateData = {
          id: screenId,
          name: currentScreen.value.name,
          svgContent: svgWithPoints,      // 使用包含监测点的SVG
          svgWidth: svgDimensions.width,  // SVG宽度
          svgHeight: svgDimensions.height, // SVG高度
          points: apiPoints,              // 使用转换后的点位数组
          extraJson: JSON.stringify({
            containsPoints: true        // 标记为内部包含监测点
          })
        }

        console.log("更新的数据:", {
          id: updateData.id,
          name: updateData.name,
          svgType: isSvgUrlFormat ? '原始为URL，已转换为完整SVG' : 'SVG内容',
          svgLength: typeof svgWithPoints === 'string' ? svgWithPoints.length : '未知',
          pointsCount: apiPoints.length,
          containsMonitoringPoints: svgWithPoints.includes('id="monitoring-points"')
        });

        await updateScreen(updateData)

        // 更新成功后将isTemp标记清除
        if (currentScreen.value) {
          currentScreen.value.isTemp = false

          // 同步更新列表中对应画面的isTemp状态
          const screenIndex = screenList.value.findIndex(item => item.id === currentScreen.value?.id)
          if (screenIndex !== -1) {
            screenList.value[screenIndex].isTemp = false
          }

          ElMessage.success(`成功更新画面"${currentScreen.value.name}"`)
        }

        // 刷新画面列表以更新其他信息
        await fetchScreenList()

        // 固定使用100%缩放，不再进行自适应缩放
        // 延迟一点再设置缩放比例，确保列表和画面已更新
        setTimeout(() => {
          console.log('保存后设置为100%缩放并居中');
          updateZoom(1.0);

          // 确保SVG内容正确设置
          if (currentScreen.value && svgWithPoints) {
            console.log('保存后确保SVG内容正确设置，长度:', svgWithPoints.length);
            // 临时清空再重新设置，确保视图更新
            const tempSvg = svgWithPoints;
            currentScreen.value.svg = '';

            // 使用nextTick确保视图更新
            nextTick(() => {
              if (currentScreen.value) {
                currentScreen.value.svg = tempSvg;

                // 延迟调用居中函数
                setTimeout(() => {
                  // 调用居中函数而非自适应缩放
                  if (flowChartRef.value && typeof flowChartRef.value.calculateInitialZoom === 'function') {
                    try {
                      // 检查SVG元素是否存在
                      const svgElement = document.querySelector('.svg-content svg');
                      if (svgElement) {
                        console.log('SVG元素已存在于DOM中，调用居中函数');
                        flowChartRef.value.calculateInitialZoom();
                      } else {
                        console.warn('SVG元素不存在于DOM中，无法调用居中函数');
                      }
                    } catch (e) {
                      console.error('调用居中函数失败:', e);
                      // 失败时也保持100%缩放
                      updateZoom(1.0);
                    }
                  }
                }, 500);
              }
            });
          } else {
            // 如果没有SVG内容，只调用居中函数
            if (flowChartRef.value && typeof flowChartRef.value.calculateInitialZoom === 'function') {
              try {
                flowChartRef.value.calculateInitialZoom();
              } catch (e) {
                console.error('调用居中函数失败:', e);
                // 失败时也保持100%缩放
                updateZoom(1.0);
              }
            }
          }
        }, 1000)
      } catch (error) {
        console.error('更新画面失败:', error)
        ElMessage.error('更新画面失败，请重试')
      }
    }
  } catch (error) {
    console.error('保存画面失败:', error)
    ElMessage.error('保存画面失败')
  } finally {
    // 关闭加载提示
    loadingInstance.close()
  }
}

// 定义一个引用变量
const flowChartRef = ref<FlowChartInstance | null>(null);

// 导出SVG
const exportSVG = () => {
  if (!currentScreen.value?.svg) {
    ElMessage.warning('当前没有可导出的SVG内容')
    return
  }

  // 生成文件名（添加时间戳）
  const timestamp = new Date().toISOString().replace(/[:-]/g, '').substring(0, 15)
  const fileName = `${currentScreen.value.name || '工艺流程图'}_${timestamp}`

  // 使用FlowChartDisplay组件的导出方法
  if (flowChartRef.value) {
    const svgWithPoints = flowChartRef.value.exportSvgWithPoints();

    // 创建下载链接
    const blob = new Blob([svgWithPoints], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${fileName}.svg`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('已成功导出SVG文件（包含监测点数据）');
  } else {
    // 降级处理：如果组件方法不可用
    console.warn('FlowChartDisplay组件实例不可用，使用原始SVG导出');

    // 获取原始SVG内容并确保包含XML声明和命名空间
    let svgContent = currentScreen.value.svg || '';

    // 确保SVG有命名空间
    if (!svgContent.includes('xmlns=')) {
      svgContent = svgContent.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
    }

    // 确保包含XML声明
    if (!svgContent.trim().startsWith('<?xml')) {
      svgContent = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n${svgContent}`;
    } else {
      // 替换现有的XML声明以确保包含standalone属性
      svgContent = svgContent.replace(/^<\?xml[^>]*\?>/, '<?xml version="1.0" encoding="UTF-8" standalone="no"?>');
    }

    // 创建下载链接
    const blob = new Blob([svgContent], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${fileName}.svg`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}



// 添加beforeunload事件处理
const handleBeforeUnload = (e: BeforeUnloadEvent) => {
  if (currentScreen.value?.isTemp) {
    // 显示确认提示
    e.preventDefault()
    e.returnValue = '您有未保存的更改，确定要离开吗？'
    return e.returnValue
  }
}

// 处理水厂变化事件
const handleFactoryChanged = async (newFactoryId: string | number) => {
  console.log('父组件接收到水厂变化事件:', newFactoryId)

  // 无论是否有未保存的画面，都清空当前状态
  console.log('水厂已切换，清空当前所有数据')

  // 清空当前选中的画面
  currentScreen.value = null

  // 清空点位数据
  currentPoints.value = []

  // 清空画面列表，稍后会重新获取
  screenList.value = []

  // 重新加载画面列表
  await fetchScreenList()
}

// 监听全局水厂变化
watch(() => appStore.getCurrentStation?.id, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    console.log('全局水厂已变化，清空当前数据并重新加载画面列表')

    // 清空当前选中的画面和临时画面
    currentScreen.value = null

    // 清空点位数据
    currentPoints.value = []

    // 清空画面列表，稍后会重新获取
    screenList.value = []

    // 重新加载画面列表
    fetchScreenList()
  }
}, { immediate: false })

onMounted(() => {
  fetchScreenList()
  // 添加beforeunload事件监听器
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onBeforeUnmount(() => {
  // 移除beforeunload事件监听器
  window.removeEventListener('beforeunload', handleBeforeUnload)
})

// 从SVG内容中提取宽高
const extractSvgDimensions = (svgContent: string): { width?: string; height?: string } => {
  try {
    const dimensions: { width?: string; height?: string } = {};

    // 提取width属性
    const widthMatch = svgContent.match(/width="([^"%]+)(%?)"/);
    if (widthMatch && !widthMatch[2]) {
      dimensions.width = widthMatch[1];
    }

    // 提取height属性
    const heightMatch = svgContent.match(/height="([^"%]+)(%?)"/);
    if (heightMatch && !heightMatch[2]) {
      dimensions.height = heightMatch[1];
    }

    // 如果没有直接的宽高，尝试从viewBox中提取
    if (!dimensions.width || !dimensions.height) {
      const viewBoxMatch = svgContent.match(/viewBox="([^"]+)"/);
      if (viewBoxMatch) {
        const viewBoxValues = viewBoxMatch[1].split(/\s+/);
        if (viewBoxValues.length === 4) {
          if (!dimensions.width) dimensions.width = viewBoxValues[2];
          if (!dimensions.height) dimensions.height = viewBoxValues[3];
        }
      }
    }

    return dimensions;
  } catch (error) {
    console.error('提取SVG尺寸失败:', error);
    return {};
  }
}

// 处理删除画面
const handleDeleteScreen = async (screen: Screen) => {
  try {
    // 弹出确认对话框
    await ElMessageBox.confirm(
      '确定要删除该画面吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 检查是否是临时画面
    const isTemp = screen.isTemp && screen.id.toString().startsWith('temp_');

    if (isTemp) {
      // 临时画面直接从本地删除
      console.log('删除临时画面:', screen.id);
      screenList.value = screenList.value.filter(item => item.id !== screen.id);

      // 如果删除的是当前选中的画面，选中第一个可用画面
      if (currentScreen.value?.id === screen.id) {
        currentScreen.value = null;
        currentPoints.value = [];

        // 如果列表中还有画面，选中第一个
        if (screenList.value.length > 0) {
          selectScreen(screenList.value[0]);
        }
      }

      ElMessage.success('临时画面已删除');
    } else {
      // 从服务器删除已保存的画面
      console.log('从服务器删除画面:', screen.id);
      await deleteScreen(screen.id);

      // 从本地列表移除
      screenList.value = screenList.value.filter(item => item.id !== screen.id);

      // 如果删除的是当前选中的画面，清空当前画面
      if (currentScreen.value?.id === screen.id) {
        currentScreen.value = null;
        currentPoints.value = [];

        // 如果列表中还有画面，选中第一个
        if (screenList.value.length > 0) {
          selectScreen(screenList.value[0]);
        }
      }

      ElMessage.success('画面已成功删除');
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消删除，不做处理
      return;
    }
    console.error('删除画面失败:', error);
    ElMessage.error('删除画面失败');
  }
}
</script>

<style lang="scss" scoped>
.screen-manage-container {
  display: flex;
  width: calc(100% - 40px);
  height: calc(100% - 40px);
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  background-color: #f5f5f5;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  .screen-list {
    width: 230px;
    background-color: white;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;

    .list-header {
      padding: 10px 16px;
      font-size: 16px;
      font-weight: 500;
      background-color: #f8f8f8;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .new-btn {
        padding: 6px 10px;
        font-size: 12px;
      }
    }

    .list-content {
      flex: 1;
      overflow-y: auto;

      .screen-item {
        padding: 10px 16px;
        cursor: pointer;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s;

        &:hover {
          background-color: #f5f5f5;
        }

        &.active {
          background-color: #ecf5ff;
          border-left: 3px solid #409EFF;
        }

        &.temp-item {
          background-color: #fff8e6;
          border-left: 3px solid #e6a23c;
        }

        &.has-unsaved-changes {
          background-color: #fef0f0;
          border-left: 3px solid #f56c6c;
        }

        .item-content {
          display: flex;
          flex-direction: column;
          flex: 1;
          overflow: hidden;

          span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .screen-date {
            font-size: 12px;
            color: #999;
            margin-top: 4px;
          }

          .unsaved-mark {
            font-size: 12px;
            color: #e6a23c;
            margin-top: 4px;
            font-weight: bold;
          }
        }

        .item-actions {
          margin-top: 4px;
          display: flex;
          justify-content: flex-end;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.3s;

          .delete-btn {
            padding: 4px;
            background-color: #f56c6c;
          }
        }

        &:hover .item-actions {
          opacity: 1;
        }
      }

      .empty-list {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        color: #909399;

        i {
          font-size: 40px;
          margin-bottom: 10px;
          color: #C0C4CC;
        }

        p {
          margin: 0;
          font-size: 14px;
        }

        .new-btn {
          margin-top: 15px;
        }
      }
    }
  }

  .screen-display {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
    margin: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .display-header {
      padding: 10px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e0e0e0;
      background-color: #f8f8f8;
      z-index: 2;
      flex-shrink: 0;

      .header-title {
        font-size: 16px;
        font-weight: 500;
        display: flex;
        align-items: center;

        .update-time {
          font-size: 12px;
          color: #666;
          margin-left: 12px;
          font-weight: normal;

          .points-info {
            margin-left: 10px;
            padding: 2px 6px;
            background-color: #ecf5ff;
            color: #409EFF;
            border-radius: 4px;
            font-size: 12px;
          }
        }

        .screen-name-input {
          width: 300px;
          margin-right: 10px;

          :deep(.el-input__inner) {
            font-weight: 500;
            font-size: 16px;
          }

          :deep(.el-input-group__prepend) {
            background-color: #f5f7fa;
            color: #409EFF;
          }

          :deep(.el-input__inner) {
            border-color: transparent;
            background-color: transparent;

            &:hover,
            &:focus {
              border-color: #DCDFE6;
              background-color: #fff;
            }
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 10px;

        .change-btn {
          background-color: #E6A23C;
        }

        .export-btn {
          background-color: #67C23A;
        }

        .save-btn {
          background-color: #67C23A;
        }
      }
    }

    .display-content {
      flex: 1;
      position: relative;
      background-color: #333333;
      display: flex;
      justify-content: center;
      align-items: center;

      .svg-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: transform 0.2s ease;
        transform-origin: center center;
        padding: 20px;
      }

      .empty-placeholder {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #aaa;

        i {
          font-size: 60px;
          margin-bottom: 20px;
        }

        p {
          margin: 0;
          font-size: 18px;

          &.sub-text {
            margin-top: 10px;
            font-size: 14px;
            color: #777;
          }
        }

        .upload-actions {
          margin-top: 20px;
          display: flex;
          gap: 10px;

          .upload-btn {
            padding: 10px 20px;

            i {
              font-size: 16px;
              margin-bottom: 0;
              margin-right: 5px;
            }
          }
        }
      }
    }
  }
}
</style>
