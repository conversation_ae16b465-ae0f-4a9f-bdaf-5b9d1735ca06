<template>
  <ContentWrap>
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center">
        <el-button type="primary" @click="router.push('/monitor/themedata')" class="mr-4">返回</el-button>
        <span class="text-lg font-bold">当前主题：{{ themeName }}</span>
      </div>
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item label="指标名称" prop="indicatorName">
          <el-input
            v-model="queryParams.indicatorName"
            class="!w-240px"
            clearable
            placeholder="请输入指标名称"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon class="mr-5px" icon="ep:search" />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
          <el-button
            plain
            type="primary"
            @click="openForm('create')"
          >
            <Icon class="mr-5px" icon="ep:plus" />
            新增
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </ContentWrap>

  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="指标编码" prop="indicatorCode" show-overflow-tooltip />
      <el-table-column align="center" label="指标名称" prop="indicatorName" show-overflow-tooltip />
      <el-table-column align="center" label="单位" prop="unit" />
      <el-table-column align="center" label="标准值" prop="standardRange" />
      <el-table-column align="center" label="默认显示" prop="defaultShow">
        <template #default="scope">
          <el-tag :type="scope.row.defaultShow === 1 ? 'success' : 'info'">
            {{ scope.row.defaultShow === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.currentPage"
      :total="total"
      @pagination="getPage"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <el-dialog
    v-model="dialogVisible"
    :title="formType === 'create' ? '新增指标' : '编辑指标'"
    width="500px"
    @close="resetDialog"
  >
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
      <el-form-item label="指标名称" prop="indicatorName">
        <el-input v-model="formData.indicatorName" placeholder="请输入指标名称" />
      </el-form-item>
      <el-form-item label="指标" prop="indicatorCode">
        <el-tree-select
          v-model="formData.indicatorCode"
          :data="factorOptions"
          placeholder="请选择指标"
          node-key="value"
          :props="{ label: 'label', children: 'children' }"
          :render-after-expand="false"
          filterable
          @change="onIndicatorChange"
          popper-class="indicator-tree-popper"
        >
          <template #default="{ data }">
            <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
              <span class="text-sm">{{ data.label }}</span>
              <span v-if="data.unit" style="color: #999; font-size: 12px; margin-left: 8px;">{{ data.unit }}</span>
            </div>
          </template>
        </el-tree-select>
      </el-form-item>
      <el-form-item label="单位" prop="unit">
        <el-input v-model="formData.unit" placeholder="请输入单位" />
      </el-form-item>
      <el-form-item label="标准值" prop="standardRange">
        <el-input v-model="formData.standardRange" placeholder="请输入标准值" />
      </el-form-item>
      <el-form-item label="默认显示" prop="defaultShow">
        <el-radio-group v-model="formData.defaultShow">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" :max="9999" style="width: 100%" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import * as ThemeApi from '@/api/monitor/themedata'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { computed, watch, ref, reactive, onMounted } from 'vue'
import { getFactorList } from '@/api/alarm'

const router = useRouter()
const themeName = ref('')

const appStore = useAppStore()
const currentFactory = computed(() => appStore.getCurrentStation)
const route = useRoute()
const themeId = computed(() => route.params.themeId)

const message = useMessage()

const loading = ref(true)
const total = ref(0)
const list = ref([])
const queryParams = reactive({
  currentPage: 1,
  pageSize: 10,
  indicatorName: '',
  themeId: themeId.value,
})
const queryFormRef = ref()

const dialogVisible = ref(false)
const formType = ref<'create' | 'update'>('create')
const formRef = ref()
const formData = reactive({
  id: undefined,
  themeId: themeId.value,
  indicatorCode: '',
  indicatorName: '',
  unit: '',
  standardRange: '',
  defaultShow: 1,
  sort: 0,
  remark: ''
})
const formRules = {
  indicatorName: [{ required: true, message: '请输入指标名称', trigger: 'blur' }],
  indicatorCode: [{ required: true, message: '请选择指标', trigger: 'change' }],
  sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  defaultShow: [{ required: true, message: '请选择是否默认显示', trigger: 'change' }],
  themeId: [{ required: true, message: '主题ID不能为空', trigger: 'blur' }]
}

const factorOptions = ref<any[]>([])

const parseNameAndUnit = (pointName) => {
  // 匹配如"加药次氯酸钠流量2L/h" => label: '加药次氯酸钠流量2', unit: 'L/h'
  const match = pointName.match(/^(.*?)(\d+)([a-zA-Z\/\d]+)$/)
  if (match) {
    return {
      label: match[1] + match[2],
      unit: match[3]
    }
  }
  return {
    label: pointName,
    unit: ''
  }
}

const convertFactorOptions = (raw) => {
  return (raw || []).map(device => ({
    label: device.deviceName,
    value: device.deviceId,
    children: (device.points || []).map(point => {
      const parsed = parseNameAndUnit(point.pointName)
      return {
        label: parsed.label,
        value: point.indicatorId,
        unit: point.unit || parsed.unit
      }
    })
  }))
}

const fetchFactorOptions = async () => {
  if (currentFactory.value?.id) {
    const res = await getFactorList(currentFactory.value.id)
    factorOptions.value = convertFactorOptions(res.data)
  }
}

const onIndicatorChange = (indicatorId: string) => {
  // 遍历 factorOptions 找到对应节点
  const findNode = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.value === indicatorId && !node.children) return node
      if (node.children) {
        const found = findNode(node.children)
        if (found) return found
      }
    }
    return null
  }
  const node = findNode(factorOptions.value)
  if (node) {
    formData.indicatorName = node.label
    formData.unit = node.unit || ''
  }
}

const getPage = async () => {
  loading.value = true
  try {
    queryParams.themeId = themeId.value
    const data = await ThemeApi.getIndicatorPage(queryParams)
    list.value = data.records || data.list || []
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.currentPage = 1
  getPage()
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const openForm = async (type: 'create' | 'update', id?: number) => {
  await fetchFactorOptions()
  formType.value = type
  if (type === 'update' && id) {
    const data = await ThemeApi.getIndicator(id)
    if (!data) {
      message.error('获取指标详情失败')
      return
    }
    formData.id = data.id
    formData.themeId = data.themeId
    formData.indicatorName = data.indicatorName
    formData.indicatorCode = String(data.indicatorCode).trim()
    formData.unit = data.unit
    formData.standardRange = data.standardRange
    formData.defaultShow = data.defaultShow
    formData.sort = data.sort
    formData.remark = data.remark
  } else {
    formData.id = undefined
    formData.themeId = themeId.value
    formData.indicatorName = ''
    formData.indicatorCode = ''
    formData.unit = ''
    formData.standardRange = ''
    formData.defaultShow = 1
    formData.sort = 0
    formData.remark = ''
  }
  dialogVisible.value = true
}

const submitForm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    try {
      formData.themeId = themeId.value
      if (formType.value === 'create') {
        await ThemeApi.addIndicator(formData)
        message.success('新增成功')
      } else {
        await ThemeApi.updateIndicator(formData)
        message.success('修改成功')
      }
      dialogVisible.value = false
      getPage()
    } catch {}
  })
}

const resetDialog = () => {
  if (formRef.value) formRef.value.resetFields()
}

const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await ThemeApi.deleteIndicator(id)
    message.success('删除成功')
    await getPage()
  } catch {}
}

const fetchThemeName = async () => {
  if (themeId.value) {
    const res = await ThemeApi.getTheme(themeId.value)
    themeName.value = res?.themeName || res?.data?.themeName || ''
  }
}

onMounted(() => {
  fetchThemeName()
})

watch(
  () => [currentFactory.value?.id || '', themeId.value || ''],
  (newArr, oldArr) => {
    const [newFactoryId, newThemeId] = newArr
    const [oldFactoryId, oldThemeId] = oldArr || []
    if (oldFactoryId && newFactoryId && newFactoryId !== oldFactoryId) {
      // 如果是水厂切换，返回主题列表页
      router.push('/monitor/themedata')
      return
    }
    if ((newFactoryId && newFactoryId !== oldFactoryId) || (newThemeId && newThemeId !== oldThemeId)) {
      queryParams.themeId = newThemeId
      getPage()
    }
  },
  { immediate: true }
)
</script>

<style>
.indicator-tree-popper .el-tree-node__content {
  display: flex !important;
  align-items: center;
}
</style> 