<template>
  <div class="process-screen-page">
    <div class="title flex items-center">
      <el-select v-model="selectedProcess" placeholder="请选择工艺段" size="small" class="ml-4">
        <el-option v-for="item in processOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <span v-if="lastUpdateTime" class="ml-2 text-xs" :class="isRealTimeUpdating ? 'text-white' : 'text-gray-400'">
        最后更新: {{ lastUpdateTime.toLocaleTimeString() }}
        <span v-if="isRealTimeUpdating && !isProcessingData" class="ml-1">(3分钟更新)</span>
        <span v-if="isProcessingData" class="ml-1">(更新中...)</span>
      </span>
      <div class="ml-auto flex items-center mr-4">
        <el-button-group>
          <el-button size="small" @click="resetZoom">
            <i class="el-icon-refresh"></i> 重置视图
          </el-button>
          <el-button size="small" @click="zoomIn">
            <i class="el-icon-zoom-in"></i> 放大
          </el-button>
          <el-button size="small" @click="zoomOut">
            <i class="el-icon-zoom-out"></i> 缩小
          </el-button>
        </el-button-group>
        <span class="ml-2 text-xs text-white">{{ Math.round(zoom * 100) }}%</span>
      </div>
    </div>

    <component :is="decoration" :reverse="true" :color="['#75dfff', '#00fbff']" style="width: 100%; height: 5px"
      :key="randDom()" ref="dvDecoration" />

    <div class="svg-main-container">
      <!-- 使用获取到的SVG内容 -->
      <div class="svg-container" @wheel="handleWheel" ref="svgContainerRef" @mousedown="startDrag" @mousemove="drag"
        @mouseup="stopDrag" @mouseleave="stopDrag">
        <!-- 加载遮罩层 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
            <span>数据加载中...</span>
          </div>
        </div>

        <!-- 当有SVG内容时显示 -->
        <div v-if="currentScreen?.svg" class="svg-wrapper">
          <div v-html="processedSvg" class="svg-content" ref="svgContentRef"></div>

          <!-- 额外渲染的HTML点位，仅当SVG不自带点位时显示 -->
          <template v-if="!svgContainsPoints && currentScreen.points && currentScreen.points.length > 0">
            <div v-for="point in currentScreen.points" :key="point.id" class="monitor-point"
              :style="getPointStyle(point)"
              @click.stop="point.indicatorCode && point.indicatorCode.trim() !== '' ? handlePointClick(point) : null">
              <!-- 点位图标或文本内容 -->
              <div class="point-content">{{ point.name }}</div>
            </div>
          </template>
        </div>

        <!-- 无SVG内容时显示提示 -->
        <div v-else class="empty-state">
          <el-empty description="请选择工艺段" />
        </div>

        <!-- 添加一个绝对定位的变换层，用于应用缩放和平移 -->
        <div class="transform-layer" :style="{
          transform: `translate(${position.x}px, ${position.y}px) scale(${zoom})`,
          transformOrigin: '0 0'
        }"></div>
      </div>

      <!-- 监测点详情弹窗 -->
      <el-dialog v-model="dialogVisible" :title="`${selectedProcessData?.name || '监测点'}详细分析`" width="65%"
        :show-close="true" :close-on-click-modal="true" @opened="handleDialogOpened" @closed="handleDialogClosed"
        destroy-on-close class="monitor-point-dialog" top="5vh">
        <el-skeleton :rows="10" animated v-if="dialogLoading" />
        <template v-else>
          <div v-if="selectedProcessData" class="point-detail-container">
            <!-- 基本信息卡片 -->
            <div class="info-card">
              <div class="card-header">
                <i class="el-icon-info"></i>
                <span>基本信息</span>
              </div>
              <div class="card-body">
                <div class="info-row">
                  <div class="info-label">指标名称</div>
                  <div class="info-value">{{ selectedProcessData.name }}</div>
                </div>
                <div class="info-row">
                  <div class="info-label">当前值/状态</div>
                  <div class="info-value" :style="{ color: getValueColor(selectedProcessData) }">
                    <span class="value-text">{{ selectedProcessData.value }}</span>
                    <span v-if="selectedProcessData.unit && selectedProcessData.pointType !== '7'" class="unit-text">{{
                      selectedProcessData.unit }}</span>
                  </div>
                </div>
                <div class="info-row">
                  <div class="info-label">更新时间</div>
                  <div class="info-value">{{ selectedProcessData.updateTime }}</div>
                </div>
              </div>
            </div>

            <!-- 历史数据图表 -->
            <div class="trend-chart-container">
              <div class="card-header">
                <i class="el-icon-data-line"></i>
                <span>历史趋势分析 (近24小时)</span>
              </div>
              <div class="chart-wrapper">
                <div id="pointDataChart"></div>
              </div>
            </div>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { DashBoardApi } from '@/api/monitor/dashboard'
import { getIndicatorDetailByCode, getScreenById, getScreenListByFactoryId, getScreenRealTimeData } from '@/api/monitor/screenManage'
import { SVG_URL } from '@/config/url'
import echarts from '@/plugins/echarts'
import { useAppStore } from '@/store/modules/app'
import { Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

const appStore = useAppStore()
const selectedProcess = ref<string | number>('')
const dialogVisible = ref(false)
const decoration = ref('dv-decoration6')
const selectedPoint = ref<any>(null) // 添加选中的点位信息

// 定义工艺段选项的类型
interface ProcessOption {
  label: string
  value: string | number
}

// 定义画面数据的类型
interface ScreenData {
  id: string | number
  name: string
  svg: string
  points: any[]
  svgWidth?: number
  svgHeight?: number
}

const processOptions = ref<ProcessOption[]>([])
const currentScreen = ref<ScreenData | null>(null)
// 标记SVG是否内部包含点位
const svgContainsPoints = ref(false)

// 定义工艺数据的类型
interface ProcessData {
  name: string;
  value: string;
  unit: string;
  standardRange: string;
  status: string;
  updateTime: string;
  pointType?: string;
  historyData?: Array<{
    time: string;
    value: number;
  }>;
}

// 定义点位详情数据类型
interface PointDetailData {
  name: string;
  value: string;
  unit: string;
  standardRange: string;
  status: string;
  updateTime: string;
  historyData: Array<{
    time: string;
    value: number;
  }>;
  pointType?: string;
  indicatorCode?: string;
}

const selectedProcessData = ref<ProcessData | PointDetailData | null>(null)
const dialogLoading = ref(false) // 添加对话框加载状态
const isLoading = ref(false) // 添加数据加载遮罩层状态

// 获取工艺段选项
const fetchProcessOptions = async () => {
  let loadingInstance: any = null;
  try {
    const factoryId = appStore.getCurrentStation?.id
    if (!factoryId) {
      console.warn('未选择水厂，无法获取工艺段选项')
      processOptions.value = []
      return
    }

    loadingInstance = ElMessage({
      message: '加载中...',
      type: 'info',
      duration: 0
    })

    const res = await getScreenListByFactoryId(factoryId)

    if (Array.isArray(res)) {
      processOptions.value = res.map(item => ({
        label: item.name,
        value: item.id
      }))

      // 自动选择第一个工艺段
      if (processOptions.value.length > 0) {
        selectedProcess.value = processOptions.value[0].value
      }
    } else {
      processOptions.value = []
      ElMessage.warning('未获取到工艺段数据')
    }
  } catch (error) {
    console.error('获取工艺段选项失败:', error)
    processOptions.value = []
    ElMessage.error('获取工艺段数据失败')
  } finally {
    // 确保无论成功还是失败，都关闭加载提示
    if (loadingInstance) {
      loadingInstance.close()
    }
  }
}

// 缩放和拖拽相关状态
const zoom = ref(1) // 缩放比例
const position = ref({ x: 0, y: 0 }) // 位置偏移
const isDragging = ref(false) // 是否正在拖拽
const dragStart = ref({ x: 0, y: 0 }) // 拖拽起始点
const dragStartPosition = ref({ x: 0, y: 0 }) // 拖拽起始位置
const svgContainerRef = ref<HTMLElement | null>(null) // SVG容器引用

// 以可视区域中心为基准点进行缩放
const zoomToCenter = (newZoom: number) => {
  if (!svgContainerRef.value || !svgContentRef.value) return;

  // 获取容器的尺寸
  const containerRect = svgContainerRef.value.getBoundingClientRect();

  // 获取内容的当前位置和尺寸
  const svgContent = svgContentRef.value;
  const svgElement = svgContent.querySelector('svg');
  if (!svgElement) return;

  // 确保正确的内容尺寸
  const svgRect = svgElement.getBoundingClientRect();

  // 计算真实的视觉中心点
  const visibleCenterX = containerRect.width / 2;
  const visibleCenterY = containerRect.height / 2;

  console.log('缩放前', {
    containerSize: { width: containerRect.width, height: containerRect.height },
    svgSize: { width: svgRect.width, height: svgRect.height },
    position: position.value,
    zoom: zoom.value
  });

  // 计算视觉中心点相对于SVG内容原点的坐标
  const oldZoom = zoom.value;
  // 中心点在缩放前的内容坐标系中的位置
  const centerInContentX = (visibleCenterX - position.value.x) / oldZoom;
  const centerInContentY = (visibleCenterY - position.value.y) / oldZoom;

  // 应用新的缩放值
  zoom.value = newZoom;

  // 重新计算位置，保持视觉中心点不变
  position.value = {
    x: visibleCenterX - (centerInContentX * newZoom),
    y: visibleCenterY - (centerInContentY * newZoom)
  };

  // 立即应用变换
  svgContent.style.transform = `translate(${position.value.x}px, ${position.value.y}px) scale(${zoom.value})`;

  console.log('中心点缩放后', {
    oldZoom,
    newZoom,
    position: position.value,
    visibleCenter: { x: visibleCenterX, y: visibleCenterY },
    centerInContent: { x: centerInContentX, y: centerInContentY }
  });
}

// 放大
const zoomIn = () => {
  const newZoom = Math.min(zoom.value * 1.2, 5);
  zoomToCenter(newZoom);
}

// 缩小
const zoomOut = () => {
  const newZoom = Math.max(zoom.value / 1.2, 0.2);
  zoomToCenter(newZoom);
}

// 重置缩放和位置
const resetZoom = () => {
  console.log('重置SVG缩放和位置')
  zoom.value = 1
  position.value = { x: 0, y: 0 }

  // 立即应用变换
  if (svgContentRef.value) {
    svgContentRef.value.style.transform = `translate(${position.value.x}px, ${position.value.y}px) scale(${zoom.value})`
  }

  // 延迟执行自动适配，确保重置后SVG能够正确居中显示
  setTimeout(() => {
    autoFitSvg()
  }, 100)
}

// 处理和计算SVG内容
const processedSvg = computed(() => {
  if (!currentScreen.value?.svg) {
    console.log('SVG内容为空，无法处理')
    return ''
  }

  // 记录处理前的SVG内容长度
  console.log(`处理SVG内容，原始长度: ${currentScreen.value.svg.length}`)

  // 修复SVG代码
  let svgContent = currentScreen.value.svg

  // 确保内容是SVG
  if (!svgContent.includes('<svg')) {
    console.error('当前内容不是有效的SVG:', svgContent.substring(0, 100))
    return `<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#ff0000" opacity="0.3" />
      <text x="400" y="300" text-anchor="middle" fill="red" font-size="24">无效的SVG内容</text>
      <text x="400" y="340" text-anchor="middle" fill="red" font-size="18">内容前100字符: ${svgContent.substring(0, 100)}</text>
    </svg>`
  }

  // 确保SVG有正确的viewBox和尺寸
  if (!svgContent.includes('viewBox') && !svgContent.includes('width') && !svgContent.includes('height')) {
    // 插入默认尺寸
    console.log('SVG缺少viewBox和尺寸，添加默认值')
    svgContent = svgContent.replace('<svg', '<svg width="1000" height="800" viewBox="0 0 1000 800"')
  }

  // 如果SVG没有尺寸但有viewBox
  if (!svgContent.includes('width') && !svgContent.includes('height') && svgContent.includes('viewBox')) {
    const viewBoxMatch = svgContent.match(/viewBox=["']([^"']*)["']/)
    if (viewBoxMatch && viewBoxMatch[1]) {
      const [, , width, height] = viewBoxMatch[1].split(/\s+/).map(Number)
      if (width && height) {
        console.log(`从viewBox提取尺寸: ${width}x${height}`)
        svgContent = svgContent.replace('<svg', `<svg width="${width}" height="${height}"`)
      }
    }
  }

  // 确保SVG响应鼠标事件，但不修改任何颜色或样式
  if (!svgContent.includes('style=')) {
    console.log('添加基础样式属性')
    svgContent = svgContent.replace('<svg', '<svg style="display:block; pointer-events:all;"')
  } else {
    // 如果已有style属性，仅添加必要的交互样式，不修改颜色或其他样式
    svgContent = svgContent.replace(/style=["']([^"']*)["']/, (match, styles) => {
      // 只添加pointer-events，不修改其他样式
      if (!styles.includes('pointer-events')) {
        return `style="pointer-events:all; ${styles}"`
      }
      return match; // 如果已有pointer-events，保持原样
    })
  }

  // 移除可能存在的XML声明和DOCTYPE，以防止渲染问题
  svgContent = svgContent.replace(/<\?xml[^>]*\?>/g, '')
  svgContent = svgContent.replace(/<!DOCTYPE[^>]*>/g, '')

  console.log(`SVG处理完成，处理后长度: ${svgContent.length}`)
  return svgContent
})

// 自动适应SVG大小，增加重试机制
const autoFitSvg = async (retryCount = 0, maxRetries = 5) => {
  if (!currentScreen.value?.svg) return

  // 等待DOM更新
  await nextTick()

  const svgContainer = svgContainerRef.value
  const svgContent = svgContentRef.value

  if (!svgContainer || !svgContent) {
    console.log('SVG容器或内容元素不存在，将重试', { retryCount, maxRetries })
    if (retryCount < maxRetries) {
      // 延迟后重试
      setTimeout(() => autoFitSvg(retryCount + 1, maxRetries), 200)
    }
    return
  }

  console.log('SVG容器和内容元素已找到', {
    containerWidth: svgContainer.offsetWidth,
    containerHeight: svgContainer.offsetHeight,
    contentWidth: svgContent.offsetWidth,
    contentHeight: svgContent.offsetHeight
  })

  const svgElement = svgContent.querySelector('svg')
  if (!svgElement) {
    console.log('未找到SVG元素，将重试', { retryCount, maxRetries })
    if (retryCount < maxRetries) {
      setTimeout(() => autoFitSvg(retryCount + 1, maxRetries), 200)
    }
    return
  }

  console.log('SVG元素已找到', {
    tagName: svgElement.tagName,
    width: svgElement.getAttribute('width'),
    height: svgElement.getAttribute('height'),
    viewBox: svgElement.getAttribute('viewBox')
  })

  // 获取SVG的尺寸，但不修改它的样式
  try {
    // 确保SVG有有效的尺寸属性，但不修改原始样式
    let svgWidth = parseInt(svgElement.getAttribute('width') || '0')
    let svgHeight = parseInt(svgElement.getAttribute('height') || '0')

    console.log('SVG原始尺寸', { svgWidth, svgHeight })

    if (svgWidth <= 10 || svgHeight <= 10) {
      // 尝试从viewBox提取尺寸
      const viewBox = svgElement.getAttribute('viewBox')
      if (viewBox) {
        const [, , vbWidth, vbHeight] = viewBox.split(/\s+/).map(Number)
        if (vbWidth && vbHeight) {
          // 只在内部使用这些值，不修改SVG
          svgWidth = vbWidth
          svgHeight = vbHeight
          console.log('从viewBox提取尺寸', { vbWidth, vbHeight })
        }
      }
    }

    // 如果仍然没有有效尺寸，使用默认值但不修改SVG
    if (svgWidth <= 10 || svgHeight <= 10) {
      svgWidth = 1000
      svgHeight = 800
      console.log('使用默认尺寸', { svgWidth, svgHeight })
    }
  } catch (error) {
    console.error('获取SVG尺寸失败:', error)
  }

  // 等待浏览器重新计算尺寸
  await nextTick()

  // 获取SVG和容器的尺寸
  const svgRect = svgElement.getBoundingClientRect()
  const containerRect = svgContainer.getBoundingClientRect()

  console.log('SVG和容器的实际尺寸', {
    svgRect: {
      width: svgRect.width,
      height: svgRect.height
    },
    containerRect: {
      width: containerRect.width,
      height: containerRect.height
    }
  })

  // 使用多种方法获取SVG尺寸
  let svgWidth = svgRect.width
  let svgHeight = svgRect.height

  // 如果getBoundingClientRect返回的尺寸不可靠，尝试其他方法
  if (svgWidth <= 10 || svgHeight <= 10) {
    svgWidth = parseInt(svgElement.getAttribute('width') || '0')
    svgHeight = parseInt(svgElement.getAttribute('height') || '0')
    console.log('从属性获取SVG尺寸', { svgWidth, svgHeight })
  }

  // 最后的安全检查
  if (svgWidth <= 10 || svgHeight <= 10) {
    svgWidth = 1000
    svgHeight = 800
    console.log('使用默认尺寸作为最终值', { svgWidth, svgHeight })

    // 如果还在重试范围内，延迟后再试一次
    if (retryCount < maxRetries) {
      setTimeout(() => autoFitSvg(retryCount + 1, maxRetries), 300)
      return
    }
  }

  const containerWidth = containerRect.width
  const containerHeight = containerRect.height

  // 计算合适的缩放比例 - 使用cover模式确保铺满可视区域
  let scaleX = containerWidth / svgWidth
  let scaleY = containerHeight / svgHeight

  console.log('计算缩放比例', { scaleX, scaleY, containerWidth, containerHeight, svgWidth, svgHeight })

  // 使用cover模式，选择较大的缩放因子，确保铺满可视区域
  let scale = Math.max(scaleX, scaleY)

  // 限制缩放范围，防止过度缩放
  scale = Math.max(0.3, Math.min(scale, 2.0))

  console.log('最终缩放比例', { scale })

  // 应用缩放
  zoom.value = scale

  // 确保居中显示
  const scaledWidth = svgWidth * scale
  const scaledHeight = svgHeight * scale

  position.value = {
    x: Math.round((containerWidth - scaledWidth) / 2),
    y: Math.round((containerHeight - scaledHeight) / 2)
  }

  // 立即应用变换
  if (svgContentRef.value) {
    svgContentRef.value.style.transform = `translate(${position.value.x}px, ${position.value.y}px) scale(${zoom.value})`

    // 确保SVG内容样式正确
    const svgElement = svgContentRef.value.querySelector('svg');
    if (svgElement) {
      // 确保SVG的变换原点设置正确
      svgContentRef.value.style.transformOrigin = '0 0';
    }
  }

  console.log('设置最终位置', { position: position.value, scaledWidth, scaledHeight, zoom: zoom.value })
}

// 监听currentScreen变化，确保SVG加载后立即适配
watch(() => currentScreen.value?.svg, (newSvg) => {
  if (newSvg) {
    console.log('SVG内容已更新，准备自动适配大小')
    // 延迟执行以确保DOM更新
    setTimeout(() => {
      console.log('第一次尝试自动适配SVG')
      autoFitSvg()
    }, 300)

    // 再次尝试适配，确保不会出现初始定位问题
    setTimeout(() => {
      console.log('第二次尝试自动适配SVG')
      autoFitSvg()
    }, 800)
  } else {
    console.log('SVG内容被清空或设置为null/undefined')
  }
})

// 处理鼠标滚轮事件 - 以鼠标位置为基准点缩放
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  if (!svgContentRef.value) return;

  // 获取鼠标相对于SVG容器的位置
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top

  // 获取当前SVG内容和缩放信息
  const svgContent = svgContentRef.value;
  const oldZoom = zoom.value;

  // 计算鼠标位置在SVG内容坐标系中的位置（缩放前）
  const mouseInContentX = (mouseX - position.value.x) / oldZoom;
  const mouseInContentY = (mouseY - position.value.y) / oldZoom;

  // 计算新的缩放比例
  const newZoom = event.deltaY < 0
    ? Math.min(oldZoom * 1.1, 5)    // 放大
    : Math.max(oldZoom / 1.1, 0.2); // 缩小

  // 应用新的缩放值
  zoom.value = newZoom;

  // 重新计算位置，保持鼠标下方的点不变
  position.value = {
    x: mouseX - (mouseInContentX * newZoom),
    y: mouseY - (mouseInContentY * newZoom)
  }

  // 立即应用变换
  svgContent.style.transform = `translate(${position.value.x}px, ${position.value.y}px) scale(${zoom.value})`;

  console.log('滚轮缩放', {
    mousePosition: { x: mouseX, y: mouseY },
    mouseInContent: { x: mouseInContentX, y: mouseInContentY },
    oldZoom,
    newZoom,
    newPosition: position.value
  });
}

// 开始拖拽
const startDrag = (event: MouseEvent) => {
  // 仅当按下左键时才开始拖拽
  if (event.button !== 0) return

  isDragging.value = true
  dragStart.value = {
    x: event.clientX,
    y: event.clientY
  }
  dragStartPosition.value = { ...position.value }

  // 更改鼠标样式
  if (svgContainerRef.value) {
    svgContainerRef.value.style.cursor = 'grabbing'
  }

  console.log('开始拖拽', { dragStart: dragStart.value, dragStartPosition: dragStartPosition.value })
}

// 拖拽中
const drag = (event: MouseEvent) => {
  if (!isDragging.value) return

  // 计算鼠标移动距离
  const dx = event.clientX - dragStart.value.x
  const dy = event.clientY - dragStart.value.y

  // 更新位置
  position.value = {
    x: dragStartPosition.value.x + dx,
    y: dragStartPosition.value.y + dy
  }

  // 应用变换到SVG包装器
  if (svgContentRef.value) {
    svgContentRef.value.style.transform = `translate(${position.value.x}px, ${position.value.y}px) scale(${zoom.value})`
  }

  console.log('拖拽中', { position: position.value, dx, dy, zoom: zoom.value })
}

// 停止拖拽
const stopDrag = () => {
  if (!isDragging.value) return

  isDragging.value = false

  // 恢复鼠标样式
  if (svgContainerRef.value) {
    svgContainerRef.value.style.cursor = 'grab'
  }

  console.log('停止拖拽', { finalPosition: position.value })
}

// 处理点击事件时，添加鼠标位置判断
const handlePointClick = (point: any) => {
  // 如果正在拖拽，不触发点击事件
  if (isDragging.value) return

  // 获取点位的ID和指标代码
  const pointId = point.id || ''
  const indicatorCode = point.indicatorCode || point.getAttribute?.('data-indicator-code') || ''

  console.log('点击点位:', pointId, '指标代码:', indicatorCode)

  // 如果没有指标代码，不打开弹窗
  if (!indicatorCode) {
    console.log(`点位 ${pointId} 没有指标代码，不打开弹窗`)
    return
  }

  // 保存选中的点位信息
  selectedPoint.value = point

  // 显示加载中
  selectedProcessData.value = null
  dialogVisible.value = true
  dialogLoading.value = true

  // 从后端获取点位详细数据，使用指标代码
  fetchPointDetail(indicatorCode)
}

// 获取点位详细数据
const fetchPointDetail = async (pointId: string) => {
  try {
    // 获取当前水厂编码
    const factoryCode = appStore.getCurrentStation?.code || ''

    if (!factoryCode || !pointId) {
      dialogLoading.value = false
      ElMessage.warning('无法获取点位数据，缺少必要参数')
      return
    }

    dialogLoading.value = true

    try {
      // 获取水厂ID
      const factoryId = appStore.getCurrentStation?.id || ''

      // 并行获取监测点基本信息和趋势数据
      const [detailData, trendResponse] = await Promise.all([
        // 获取监测点基本信息
        getIndicatorDetailByCode(pointId),
        // 获取24小时趋势数据
        DashBoardApi.singlePointTrendData24h({
          factoryCode,
          factoryId, // 添加水厂ID
          indicatorCode: pointId
        })
      ])
      console.log("trendResponse", trendResponse);

      // 解析返回数据
      const trendData = trendResponse.data || []

      if ((trendData.data) || detailData) {
        // 处理返回的数据

        const historyData = trendData || []

        // 从detailData中获取更多信息
        const indicatorInfo = detailData ? detailData : {}
        console.log("eeeeeeeeeeeeeeeeeee", indicatorInfo, detailData);


        // 构造点位数据对象
        const detailInfo: PointDetailData = {
          name: indicatorInfo.indicatorName || indicatorInfo.name || selectedPoint.value?.name || '-',
          value: formatIndicatorValue(indicatorInfo.indicatorValue, indicatorInfo.pointType),
          unit: indicatorInfo.unit || '',
          standardRange: indicatorInfo.standardRange || '-',
          status: formatPointStatus(indicatorInfo.indicatorValue, indicatorInfo.pointType),
          updateTime: indicatorInfo.updateTime || new Date().toLocaleString(),
          historyData: historyData, // 使用API返回的历史数据，可能为空数组
          pointType: indicatorInfo.pointType || '',
          indicatorCode: indicatorInfo.indicatorCode || pointId || ''
        }

        // 更新数据
        selectedProcessData.value = detailInfo
        console.log("selectedProcessData.value", selectedProcessData.value);


        // 数据加载完成后，初始化图表
        dialogLoading.value = false
        nextTick(() => {
          initChart(historyData)
        })
      } else {
        throw new Error('趋势数据接口返回数据格式不正确')
      }
    } catch (error) {
      console.error('获取监测点数据失败:', error)

      // 如果API调用失败，显示基本信息但历史数据为空
      const basicData: PointDetailData = {
        name: selectedPoint.value?.name || '监测点',
        value: '-',
        unit: '',
        standardRange: '-',
        status: '-',
        updateTime: new Date().toLocaleString(),
        historyData: [], // 空数据
        pointType: selectedPoint.value?.pointType || '',
        indicatorCode: pointId || ''
      }

      // 更新数据
      selectedProcessData.value = basicData

      // 数据加载完成后，初始化图表(显示无数据状态)
      dialogLoading.value = false
      nextTick(() => {
        initChart([]) // 传入空数组
      })
    }
  } catch (error) {
    console.error('获取点位详细数据失败:', error)
    ElMessage.error('获取点位数据失败')
    dialogLoading.value = false
  }
}

// 移除了generateMockHistoryData函数，不再使用模拟数据

// 初始化图表
const initChart = (data: Array<{ time: string, value: number }>) => {
  const chartDom = document.getElementById('pointDataChart')
  if (!chartDom) {
    console.error('未找到图表DOM元素')
    return
  }

  // 清除可能存在的旧图表实例
  echarts.dispose(chartDom)

  // 创建新图表实例
  const chart = echarts.init(chartDom)

  // 获取单位
  let unit = ''
  if (selectedProcessData.value && selectedProcessData.value.unit) {
    unit = selectedProcessData.value.unit
  }

  // 如果数据为空，显示无数据状态
  if (!data || data.length === 0) {
    chart.setOption({
      title: {
        text: '暂无历史数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#909399',
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      backgroundColor: '#fafafa',
      grid: {
        left: '5%',
        right: '5%',
        bottom: '8%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: []
      },
      yAxis: {
        type: 'value',
        name: unit,
        axisLine: {
          show: true
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series: []
    })

    // 响应窗口大小变化
    window.addEventListener('resize', () => {
      chart.resize()
    })
    return
  }

  // 对数据按时间进行排序
  const sortedData = [...data].sort((a, b) => {
    return new Date(a.time).getTime() - new Date(b.time).getTime();
  });

  // 准备数据 - 使用排序后的数据
  const xData = sortedData.map(item => item.time);

  // 处理Y轴数据，根据pointType进行转换
  let yData;
  if (selectedProcessData.value?.pointType === '7') {
    // 对于类型7（运行状态），将值转换为0或1
    yData = sortedData.map(item => {
      const val = String(item.value).toLowerCase(); // 转换为字符串进行比较
      if (val === 'true' || val === '1') {
        return 1; // 运行对应y轴上的"运行"位置(索引1)
      } else {
        return 0; // 停止对应y轴上的"停止"位置(索引0)
      }
    });
  } else {
    // 对于其他类型，直接使用数值
    yData = sortedData.map(item => item.value);
  }

  // 计算Y轴的最小值和最大值，确保有合适的边距
  // 对于类型7，使用固定的0-1范围；对于其他类型，基于数据计算范围
  const minValue = selectedProcessData.value?.pointType === '7' ?
    0 : (yData.length > 0 ? Math.floor(Math.min(...yData) * 0.8) : 0);
  const maxValue = selectedProcessData.value?.pointType === '7' ?
    1 : (yData.length > 0 ? Math.ceil(Math.max(...yData) * 1.2) : 100);

  // 图表配置
  const option = {
    backgroundColor: '#fafafa',
    legend: {
      show: true,
      top: '0%',
      right: '2%',
      textStyle: {
        color: '#606266',
        fontSize: 12
      }
    },
    // 全局图表事件配置
    silent: false,
    animation: true,
    hoverLayerThreshold: 3000,
    useUTC: false,
    blendMode: 'source-over',
    tooltip: {
      trigger: 'axis',  // 使用axis模式，鼠标在轴上任意位置都能触发
      axisPointer: {
        type: 'cross',  // 使用十字准线
        snap: true,     // 吸附到数据点
        animation: false, // 关闭动画提高性能
        lineStyle: {
          color: '#409EFF',
          opacity: 0.5
        }
      },
      confine: true,    // 限制提示框在图表区域内
      enterable: false, // 鼠标不可进入提示框
      show: true,       // 默认显示
      triggerOn: 'mousemove', // 鼠标移动触发
      hideDelay: 0,     // 立即隐藏
      formatter: function (params: any) {
        // 由于使用axis模式，params是一个数组，我们需要获取第一个元素
        const param = params[0];
        if (!param) return '';

        let valueText = param.value;
        if (typeof valueText === 'object' && valueText !== null) {
          valueText = valueText.value;  // 支持之前修改的数据结构
        }

        // 根据pointType处理不同类型的数据
        const pointType = selectedProcessData.value?.pointType
        if (pointType === '7') {
          // 运行状态类型
          const val = String(valueText).toLowerCase(); // 转换为字符串进行比较
          if (val === 'true' || val === '1') {
            valueText = '运行'
          } else if (val === 'false' || val === '0') {
            valueText = '停止'
          }
        }

        return `${param.name}<br/>${param.marker}${param.seriesName}: ${valueText}${pointType === '7' ? '' : unit}`
      },
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      textStyle: {
        color: '#303133'
      },
      padding: [8, 12]
    },
    grid: {
      left: '10%',    // 增加左侧空间，防止纵坐标被遮挡
      right: '5%',
      bottom: '10%',  // 水平标签需要合适的底部空间
      top: '10%',
      containLabel: true,
      // 确保鼠标移出图表区域时提示框消失
      tooltip: {
        confine: true  // 将提示框限制在图表区域内
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData,
      axisLabel: {
        // 根据数据量自动计算间隔，显示更多横坐标点
        interval: function (index, value) {
          // 根据图表宽度自动计算能显示的标签数量
          const maxLabels = 12; // 增加横坐标点数量
          const totalPoints = xData.length;
          const interval = Math.max(1, Math.floor(totalPoints / maxLabels));

          // 均匀分布标签
          return index % interval === 0;
        },
        rotate: 0, // 水平显示
        color: '#909399', // 使用更浅的颜色
        fontSize: 10, // 恢复标准字体大小
        margin: 12,
        align: 'center',
        hideOverlap: true, // 隐藏重叠的标签
        width: 60,  // 限制标签宽度
        fontWeight: 'normal', // 确保不加粗
        formatter: function (value) {
          // 格式化显示，只保留时分部分
          if (value.includes(' ')) {
            // 取时间部分
            const timePart = value.split(' ')[1];
            // 如果包含秒，去掉秒的部分
            if (timePart.includes(':')) {
              // 只保留时:分
              return timePart.substring(0, 5);
            }
            return timePart;
          }
          // 如果不包含空格但包含冒号，也只保留时分
          if (value.includes(':')) {
            return value.substring(0, 5);
          }
          return value;
        }
      },
      axisLine: {
        lineStyle: {
          color: '#ebeef5', // 更浅的轴线颜色
          width: 1          // 更细的轴线
        }
      },
      axisTick: {
        show: false,        // 不显示刻度线
        lineStyle: {
          color: '#ebeef5'
        }
      }
    },
    yAxis: {
      type: selectedProcessData.value?.pointType === '7' ? 'value' : 'value', // 类型7也使用数值轴，但添加自定义刻度
      name: selectedProcessData.value?.pointType === '7' ? '状态' : unit,
      min: minValue, // 使用计算后的最小值
      max: maxValue, // 使用计算后的最大值
      // 类型7时反转坐标轴，使停止在底部
      inverse: selectedProcessData.value?.pointType === '7' ? false : false,
      nameTextStyle: {
        color: '#606266',
        padding: [0, 0, 0, 10], // 增加左侧内边距
        fontSize: 10
      },
      axisLabel: {
        color: '#606266',
        fontSize: 10,
        padding: [0, 15, 0, 0], // 增加右侧内边距，防止被遮挡
        // 类型7的坐标轴标签格式化
        formatter: function (value: any) {
          if (selectedProcessData.value?.pointType === '7') {
            // 数值为0显示"停止"，数值为1显示"运行"
            if (value === 0) return '停止';
            if (value === 1) return '运行';
            return ''; // 其他值不显示
          }
          return value; // 其他类型正常显示数值
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#dcdfe6'
        }
      },
      splitLine: {
        show: selectedProcessData.value?.pointType !== '7', // 对于类型7不显示分割线
        lineStyle: {
          type: 'dashed',
          color: '#e4e7ed'
        }
      },
      // 设置刻度间隔，类型7只显示0和1两个刻度
      interval: selectedProcessData.value?.pointType === '7' ? 1 : 'auto',
      // 类型7时添加轴线设置
      axisPointer: {
        show: true,
        type: 'line'
      }
    },
    series: [
      {
        name: selectedProcessData.value?.name || '监测值',
        type: 'line',
        data: yData.map((value, index) => {
          // 确保每个数据点都有对应的时间轴信息
          return {
            value: value,
            name: xData[index]  // 将对应的时间信息关联到每个数据点
          };
        }),
        smooth: 0.4, // 稍微减小平滑系数，让曲线更准确地经过数据点
        showSymbol: true, // 显示数据点
        symbolSize: selectedProcessData.value?.pointType === '7' ? 8 : 6, // 类型7使用更大的数据点
        connectNulls: true, // 连接所有点
        // 增强提示框互动体验
        sampling: 'average', // 数据采样
        triggerLineEvent: true, // 允许触发线条事件
        lineStyle: {
          width: selectedProcessData.value?.pointType === '7' ? 2 : 3, // 运行状态使用更细的线条
          color: selectedProcessData.value?.pointType === '7' ? '#67C23A' : '#409EFF', // 状态类型使用绿色
          curveness: 0.4 // 与smooth值保持一致
        },
        markLine: selectedProcessData.value?.pointType === '7' ? {
          silent: true,
          symbol: 'none',
          lineStyle: {
            color: '#909399',
            type: 'dashed'
          },
          data: [
            { yAxis: 0.5 } // 在停止和运行之间添加参考线
          ]
        } : undefined,
        itemStyle: {
          color: selectedProcessData.value?.pointType === '7' ? '#67C23A' : '#409EFF',
          borderWidth: selectedProcessData.value?.pointType === '7' ? 1 : 2, // 运行状态使用更细的边框
          borderColor: '#ffffff' // 添加白色边框使点位更明显
        },
        emphasis: {
          focus: 'series', // 高亮整个系列
          scale: true,     // 放大强调
          itemStyle: {
            color: selectedProcessData.value?.pointType === '7' ? '#67C23A' : '#409EFF',
            borderWidth: selectedProcessData.value?.pointType === '7' ? 2 : 3,
            borderColor: '#ecf5ff',
            shadowColor: 'rgba(0, 0, 0, 0.2)',
            shadowBlur: selectedProcessData.value?.pointType === '7' ? 5 : 10
          },
          lineStyle: {
            width: 'bolder' // 加粗高亮线条
          }
        },
        areaStyle: selectedProcessData.value?.pointType === '7' ? undefined : {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.4)'
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.1)'
              }
            ]
          }
        }
      }
    ]
  }

  // 设置图表配置
  chart.setOption(option)

  // 响应窗口大小变化
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 获取画面和位点信息，添加自动适应功能
const fetchScreenData = async (screenId: string | number) => {
  let loadingInstance: any = null;
  try {
    if (!screenId) {
      console.log('未提供screenId，无法获取画面数据')
      return;
    }

    console.log(`开始获取画面数据，screenId: ${screenId}`)

    // 显示加载遮罩层
    isLoading.value = true;

    loadingInstance = ElMessage({
      message: '加载中...',
      type: 'info',
      duration: 0
    })

    // 使用getScreenList(screenId)接口获取SVG底图和初始监测点
    console.log('调用getScreenById API...')
    const res = await getScreenById(screenId)
    console.log('API返回数据:', res)

    if (res != null) {
      // 获取到的是数组，取第一个元素作为画面数据
      const screen = res
      screen.svgUrl = SVG_URL + res.svgUrl
      console.log(screen.svgUrl, "画面数据111111111111111")
      // 重置SVG点位标记
      svgContainsPoints.value = false

      // 检查是否有extraJson数据，提取containsPoints标记
      try {
        if (screen.extraJson) {
          const extraData = JSON.parse(screen.extraJson)
          if (extraData && extraData.containsPoints !== undefined) {
            svgContainsPoints.value = extraData.containsPoints
            console.log(`SVG内部包含点位标记: ${svgContainsPoints.value}`)
          }
        }
      } catch (e) {
        console.error('解析extraJson失败:', e)
      }

      // 获取SVG内容 - 可能是直接的SVG字符串或URL
      const svgContent = screen.svg || screen.svgContent || screen.svgUrl || ''
      console.log(`获取到SVG内容，长度: ${svgContent.length}`)
      console.log(`SVG内容类型: ${typeof svgContent}`)
      console.log(`SVG内容前100个字符: ${svgContent.substring(0, 100)}...`)

      // 检查是否是URL
      if (isUrlString(svgContent)) {
        try {
          console.log(`SVG内容是URL，开始从URL加载: ${svgContent}`)
          // 直接从URL加载SVG内容
          const svgData = await fetchSvgFromUrl(svgContent)
          console.log(`从URL获取到SVG内容，长度: ${svgData.length}`)

          // 将URL加载的SVG内容转为本地内嵌SVG
          currentScreen.value = {
            id: screen.id,
            name: screen.name,
            svg: svgData, // 使用从URL获取的SVG内容
            points: screen.points || [],
            svgWidth: screen.svgWidth,
            svgHeight: screen.svgHeight
          }

          console.log('已设置currentScreen.value，包含从URL加载的SVG')

          // 重置缩放和位置，准备自动适应
          resetZoom()

          // SVG加载完成后，开始实时数据更新
          startRealTimeUpdate()

          // SVG加载完成后，设置点位事件和自动适应大小
          nextTick(() => {
            // 首先设置SVG点位事件
            setupSvgPointEvents()

            // 然后重置视图确保初始状态正确
            console.log('SVG初始化完成，立即重置视图')
            resetZoom()

            // 延迟一点时间再自动适配，确保重置后的缩放和位置能被正确应用
            setTimeout(() => {
              autoFitSvg()

              // 视图适配完成后，立即获取实时数据
              console.log('SVG渲染完成，立即获取首次实时数据...')

              // 确保在获取实时数据前已保存了所有颜色信息
              saveAllOriginalColors()

              // 手动触发一次实时数据获取，强制执行
              fetchRealTimeData(true)
              // 注意：fetchRealTimeData内部会在完成时关闭isLoading
            }, 300)
          })
        } catch (error) {
          console.error('从URL加载SVG失败:', error)
          ElMessage.error('加载SVG失败，请检查URL是否正确')
          currentScreen.value = null
          isLoading.value = false // 出错时关闭加载遮罩
        }
      } else {
        console.log('SVG内容不是URL，直接使用内容')
        // 直接使用SVG内容
        currentScreen.value = {
          id: screen.id,
          name: screen.name,
          svg: svgContent,
          points: screen.points || [],
          svgWidth: screen.svgWidth,
          svgHeight: screen.svgHeight
        }

        console.log('已设置currentScreen.value，包含直接的SVG内容')

        // 重置缩放和位置，准备自动适应
        resetZoom()

        // SVG加载完成后，开始实时数据更新
        startRealTimeUpdate()

        // SVG加载完成后，设置点位事件和自动适应大小
        nextTick(() => {
          // 首先设置SVG点位事件
          setupSvgPointEvents()

          // 然后重置视图确保初始状态正确
          console.log('SVG初始化完成，立即重置视图')
          resetZoom()

          // 延迟一点时间再自动适配，确保重置后的缩放和位置能被正确应用
          setTimeout(() => {
            autoFitSvg()

            // 视图适配完成后，立即获取实时数据
            console.log('SVG渲染完成，立即获取首次实时数据...')
            // 手动触发一次实时数据获取，强制执行
            fetchRealTimeData(true)
            // 注意：fetchRealTimeData内部会在完成时关闭isLoading
          }, 300)
        })
      }
    } else {
      console.warn('API返回的数据为空')
      ElMessage.warning('获取画面信息失败，未返回有效数据')
      isLoading.value = false // 无数据时关闭加载遮罩
    }
  } catch (error) {
    console.error('获取画面信息失败:', error)
    ElMessage.error('获取画面信息失败')
    isLoading.value = false // 出错时关闭加载遮罩
  } finally {
    // 确保无论成功还是失败，都关闭加载提示
    if (loadingInstance) {
      loadingInstance.close()
    }
    // 注意：不在这里关闭isLoading，因为我们需要等待fetchRealTimeData完成
  }
}

// 判断字符串是否为URL
const isUrlString = (str: string): boolean => {
  if (!str) return false
  return str.startsWith('http://') || str.startsWith('https://')
}

// 从URL获取SVG内容
const fetchSvgFromUrl = async (url: string): Promise<string> => {
  try {
    console.log(`正在从URL加载SVG: ${url}`)

    // 处理URL - 如果url不是以http开头的完整URL，添加SVG_URL前缀
    let fullUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      fullUrl = SVG_URL + url;
      console.log(`添加SVG_URL前缀后的完整URL: ${fullUrl}`);
    }

    // 使用fetch API直接获取SVG内容
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Accept': 'image/svg+xml, text/plain, */*',
        'Cache-Control': 'no-cache'
      },
      // 添加凭证，以便在需要时能正确处理跨域认证
      // credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}, ${response.statusText}`)
    }

    // 获取SVG文本内容
    const text = await response.text()

    // 验证获取的内容是否为有效的SVG
    if (!text.includes('<svg') || !text.includes('</svg>')) {
      console.warn('获取的内容可能不是有效的SVG:', text.substring(0, 100) + '...')
    } else {
      console.log('成功获取SVG内容，长度:', text.length)
    }

    return text
  } catch (error) {
    console.error('获取SVG内容失败:', error)
    throw error
  }
}


// 处理工艺图点击事件 - 为SVG内点位添加事件处理
const setupSvgPointEvents = async () => {
  // 等待SVG渲染完成
  await nextTick()

  // 获取SVG容器
  const svgContainer = document.querySelector('.svg-content')
  if (!svgContainer) return

  // 查找SVG元素
  const svgElement = svgContainer.querySelector('svg')
  if (!svgElement) return

  console.log('设置SVG点位点击事件')

  // 查找所有带data-id属性的元素(点位)
  const pointElements = svgElement.querySelectorAll('[data-id]')
  console.log(`找到${pointElements.length}个点位`)

  // 如果SVG中有点位，设置标记为true
  if (pointElements.length > 0) {
    svgContainsPoints.value = true
    console.log('检测到SVG中有内置点位，设置svgContainsPoints=true')
  }

  // 为每个点位添加点击事件并保存原始颜色和坐标
  pointElements.forEach(element => {
    const pointId = element.getAttribute('data-id')
    const indicatorCode = element.getAttribute('data-indicator-code') // 获取指标代码
    if (!pointId) return

    // 保存原始颜色信息
    if (element.hasAttribute('fill')) {
      element.setAttribute('data-original-fill', element.getAttribute('fill') || '')
    }

    if (element.hasAttribute('stroke')) {
      element.setAttribute('data-original-stroke', element.getAttribute('stroke') || '')
    }

    // 保存原始坐标信息
    const tagName = element.tagName.toLowerCase();
    if (tagName === 'text') {
      if (element.hasAttribute('x')) {
        element.setAttribute('data-original-x', element.getAttribute('x') || '')
      }
      if (element.hasAttribute('y')) {
        element.setAttribute('data-original-y', element.getAttribute('y') || '')
      }
      if (element.hasAttribute('dx')) {
        element.setAttribute('data-original-dx', element.getAttribute('dx') || '')
      }
      if (element.hasAttribute('dy')) {
        element.setAttribute('data-original-dy', element.getAttribute('dy') || '')
      }
    } else if (tagName === 'circle') {
      if (element.hasAttribute('cx')) {
        element.setAttribute('data-original-cx', element.getAttribute('cx') || '')
      }
      if (element.hasAttribute('cy')) {
        element.setAttribute('data-original-cy', element.getAttribute('cy') || '')
      }
    } else if (tagName === 'rect' || tagName === 'polygon') {
      if (element.hasAttribute('x')) {
        element.setAttribute('data-original-x', element.getAttribute('x') || '')
      }
      if (element.hasAttribute('y')) {
        element.setAttribute('data-original-y', element.getAttribute('y') || '')
      }
    }

    if (element.hasAttribute('transform')) {
      element.setAttribute('data-original-transform', element.getAttribute('transform') || '')
    }

    // 移除旧事件监听器(防止重复绑定)
    element.removeEventListener('click', handlePointClick)

    // 检查是否有data-indicator-code属性且有值
    const hasIndicatorCode = indicatorCode && indicatorCode.trim() !== '';

    if (hasIndicatorCode) {
      // 有指标编码，添加点击事件并设置为手型鼠标
      element.addEventListener('click', (event) => {
        event.stopPropagation()
        // 创建包含所需信息的点位对象
        const pointData = {
          id: pointId,
          indicatorCode: indicatorCode, // 使用指标代码
          name: element.getAttribute('data-name') || element.textContent || `点位${pointId}`,
          element: element
        }
        handlePointClick(pointData)
      })

      // 设置为手型鼠标样式
      const el = element as HTMLElement
      el.style.cursor = 'pointer'
    } else {
      // 没有指标编码，设置为箭头鼠标样式
      const el = element as HTMLElement
      el.style.cursor = 'default'
      console.log(`点位 ${pointId} 没有data-indicator-code，使用箭头鼠标样式`)
    }
  })

  // 保存所有可能包含颜色的元素
  const colorElements = svgElement.querySelectorAll('[fill], [stroke]');
  colorElements.forEach(el => {
    // 如果元素没有data-id属性(不是监测点)，也保存其颜色
    if (!el.hasAttribute('data-id')) {
      if (el.hasAttribute('fill')) {
        el.setAttribute('data-original-fill', el.getAttribute('fill') || '')
      }

      if (el.hasAttribute('stroke')) {
        el.setAttribute('data-original-stroke', el.getAttribute('stroke') || '')
      }
    }
  });
}

// 处理外部点位样式
const getPointStyle = (point: any) => {
  // 检查是否有指标编码
  const hasIndicatorCode = point.indicatorCode && point.indicatorCode.trim() !== '';

  // 基本定位样式
  const style: any = {
    position: 'absolute',
    left: `${point.x || 0}px`,
    top: `${point.y || 0}px`,
    cursor: hasIndicatorCode ? 'pointer' : 'default' // 根据是否有指标编码设置鼠标样式
  }

  // 根据点位类型添加不同样式
  if (point.pointType === 'text') {
    style.backgroundColor = point.color || '#f5f7fa'
    style.padding = '2px 8px'
    style.borderRadius = '2px'
    style.color = point.textColor || '#303133'
    style.fontSize = `${point.fontSize || 12}px`
  } else {
    style.width = `${(point.size || 10) * 2}px`
    style.height = `${(point.size || 10) * 2}px`
    style.backgroundColor = point.color || '#409EFF'
    style.borderRadius = point.shape === 'square' ? '0' : '50%'
    style.transform = 'translate(-50%, -50%)'
  }

  return style
}

// 监听工艺段选择变化
watch(selectedProcess, (newValue) => {
  if (newValue) {
    console.log('选择的工艺段:', newValue)
    // 获取工艺段数据，fetchScreenData内部会自动启动实时数据更新
    fetchScreenData(newValue)
  } else {
    // 如果没有选择工艺段，停止实时数据更新
    stopRealTimeUpdate()
  }
})

// 组件挂载时初始化
onMounted(() => {
  fetchProcessOptions()

  // 设置初始鼠标样式
  if (svgContainerRef.value) {
    svgContainerRef.value.style.cursor = 'grab'
  }

  // 监听窗口大小变化，自动调整SVG大小
  window.addEventListener('resize', () => {
    if (currentScreen.value?.svg) {
      autoFitSvg()
    }
  })
})

// 监听水厂ID变化
watch(() => appStore.getCurrentStation?.id, (newFactoryId, oldFactoryId) => {
  if (newFactoryId && newFactoryId !== oldFactoryId) {
    console.log('水厂ID变更，重新加载数据:', newFactoryId)
    // 重置当前选中的工艺段和显示的画面
    selectedProcess.value = ''
    currentScreen.value = null
    // 获取新水厂的工艺段选项
    fetchProcessOptions()
  }
}, { immediate: true })

function randDom() {
  return Date.now()
}

// SVG内容引用
const svgContentRef = ref<HTMLElement | null>(null)

// 模拟测试相关状态
const isSimulating = ref(false)
const simulationTimer = ref<number | null>(null)
const monitoringPoints = ref<any[]>([])


// 停止模拟测试
const stopSimulation = () => {
  if (simulationTimer.value !== null) {
    clearInterval(simulationTimer.value)
    simulationTimer.value = null
  }
  isSimulating.value = false
}

// 实时数据刷新相关状态
const isRealTimeUpdating = ref(false)
const realTimeUpdateTimer = ref<number | null>(null)
const lastUpdateTime = ref(new Date())
const pointsData = ref<any[]>([])
const isProcessingData = ref(false) // 标记是否正在处理数据

// 开始实时数据更新
const startRealTimeUpdate = () => {
  if (!currentScreen.value?.id) {
    return
  }

  // 如果已经在更新中，先停止
  if (isRealTimeUpdating.value) {
    stopRealTimeUpdate()
  }

  isRealTimeUpdating.value = true

  // 更新UI状态
  console.log('启动实时数据更新，间隔3分钟')
  ElMessage({
    message: '已开启实时数据更新，间隔3分钟',
    type: 'success',
    duration: 2000
  })

  // 立即获取一次数据
  fetchRealTimeData()
}

// 停止实时数据更新
const stopRealTimeUpdate = () => {
  isRealTimeUpdating.value = false

  // 如果有定时器，清除它
  if (realTimeUpdateTimer.value !== null) {
    clearTimeout(realTimeUpdateTimer.value)
    realTimeUpdateTimer.value = null
  }

  // 输出控制台日志并显示提示
  // console.log('已停止实时数据更新')
  // ElMessage({
  //   message: '已停止实时数据更新',
  //   type: 'warning',
  //   duration: 2000
  // })
}

// 获取实时点位数据
const fetchRealTimeData = async (force: boolean = false) => {
  // 如果已经停止更新或者正在处理数据，则不再继续
  // 除非设置了force参数
  if ((!isRealTimeUpdating.value || isProcessingData.value) && !force) return

  // 标记为正在处理数据
  isProcessingData.value = true
  isLoading.value = true // 显示加载遮罩层

  try {
    // 使用当前屏幕ID请求实时数据
    const screenId = currentScreen.value?.id
    const factoryCode = appStore.getCurrentStation?.code || ''

    // 确保有水厂编码和屏幕ID
    if (!factoryCode || !screenId) {
      isProcessingData.value = false // 重置处理标志
      isLoading.value = false // 隐藏加载遮罩层
      return
    }

    console.log('获取实时数据中...')

    // 调用新接口获取实时数据
    const res = await getScreenRealTimeData({
      screenId,
      factoryCode
    })
    console.log("获取到的实时数据:", res);

    // 检查返回的数据中是否包含颜色字段
    if (res && Array.isArray(res) && res.length > 0) {
      // 打印前5个点位的数据示例
      const samplePoints = res.slice(0, 5);
      console.log("数据样本(前5个点位):", samplePoints);

      // 检查有多少点位包含颜色数据
      const pointsWithColor = res.filter(item => item.color !== undefined && item.color !== null && item.color !== '').length;
      console.log(`总共${res.length}个点位中，有${pointsWithColor}个包含颜色数据`);
    }

    if (res) {
      // 提取点位数据
      if (res && Array.isArray(res)) {
        // 更新上次更新时间
        lastUpdateTime.value = new Date()
        console.log(`获取到${res.length}个点位的实时数据`)

        // 处理返回的数据并更新到SVG中
        processRealTimeData(res)
      } else {
        console.log('获取的实时数据为空')
      }
    } else {
      console.error('获取实时数据接口返回错误:', res)
    }
  } catch (error) {
    console.error('获取实时数据失败:', error)
  } finally {
    // 数据处理完成，重置标志
    isProcessingData.value = false
    isLoading.value = false // 隐藏加载遮罩层

    // 如果仍在实时更新状态，设置三分钟后再次请求
    if (isRealTimeUpdating.value) {
      realTimeUpdateTimer.value = window.setTimeout(() => {
        fetchRealTimeData()
      }, 3 * 60 * 1000) // 3分钟 = 3 * 60 * 1000毫秒
    }
  }
}

// 添加新函数：仅更新文本内容，不修改任何其他属性
const updateTextContentOnly = (element: Element, data: any) => {
  // 检查元素是否应该显示文本（根据showType而不是tagName）
  const showType = element.getAttribute('data-show-type') || '';
  const isTextElement = element.tagName.toLowerCase() === 'text' || showType === 'text' || showType === 'number';

  if (!isTextElement) return;

  const elementId = element.getAttribute('id') || element.getAttribute('data-id') || '';
  const originalText = element.textContent || '';

  // 保存所有原始属性，特别是坐标相关的属性
  const originalAttributes: Record<string, string> = {};
  for (let i = 0; i < element.attributes.length; i++) {
    const attr = element.attributes[i];
    originalAttributes[attr.name] = attr.value;
  }

  // 特别保存坐标相关属性，优先使用之前保存的data-original-*属性
  const originalX = element.getAttribute('data-original-x') || element.getAttribute('x');
  const originalY = element.getAttribute('data-original-y') || element.getAttribute('y');
  const originalDX = element.getAttribute('data-original-dx') || element.getAttribute('dx');
  const originalDY = element.getAttribute('data-original-dy') || element.getAttribute('dy');
  const originalTransform = element.getAttribute('data-original-transform') || element.getAttribute('transform');

  // 只更新文本内容，不修改任何其他属性
  let textUpdated = false;
  let newText = '';

  // 优先使用indicatorValue
  if (data.indicatorValue !== undefined) {
    // 处理科学计数法转换为普通数值
    newText = formatNumberDisplay(data.indicatorValue);
    textUpdated = true;
    console.log(`文本点位[${elementId}]仅更新内容(indicatorValue): "${originalText}" -> "${newText}"`);
  }
  // 其次使用text
  else if (data.text !== undefined) {
    // 处理科学计数法转换为普通数值
    newText = formatNumberDisplay(data.text);
    textUpdated = true;
    console.log(`文本点位[${elementId}]仅更新内容(text): "${originalText}" -> "${newText}"`);
  }
  // 最后使用value
  else if (data.value !== undefined) {
    // 处理科学计数法转换为普通数值
    newText = formatNumberDisplay(data.value);
    textUpdated = true;
    console.log(`文本点位[${elementId}]仅更新内容(value): "${originalText}" -> "${newText}"`);
  }

  // 只有当有新内容时才更新
  if (textUpdated) {
    try {
      // 根据元素类型使用不同的更新策略
      if (element.tagName.toLowerCase() === 'text') {
        // 文本元素：标准SVG文本处理
        const textNode = Array.from(element.childNodes).find(node => node.nodeType === Node.TEXT_NODE);

        if (textNode) {
          // 如果找到文本节点，直接更新它
          textNode.nodeValue = newText;
          console.log(`通过textNode更新文本点位[${elementId}]内容: "${originalText}" -> "${newText}"`);
        } else {
          // 如果没有找到文本节点，使用textContent
          element.textContent = newText;
          console.log(`通过textContent更新文本点位[${elementId}]内容: "${originalText}" -> "${newText}"`);
        }
      } else {
        // 非文本元素但需要显示文本：查找子文本元素或创建一个
        let textSubElement = element.querySelector('text');

        if (textSubElement) {
          // 如果找到子文本元素，更新它
          textSubElement.textContent = newText;
          console.log(`通过子文本元素更新点位[${elementId}]内容: "${originalText}" -> "${newText}"`);
        } else {
          // 如果没有子文本元素，尝试使用data-text属性或创建title元素
          element.setAttribute('data-text', newText);

          // 使用data-title属性存储文本内容
          element.setAttribute('data-title', newText);

          console.log(`通过data属性更新点位[${elementId}]内容: "${originalText}" -> "${newText}"`);
        }
      }
      // 确保所有原始属性被保留，特别是坐标属性
      for (const [name, value] of Object.entries(originalAttributes)) {
        // 跳过data-开头的属性，它们是我们自己添加的
        if (name.startsWith('data-')) continue;

        // 确保属性值被正确设置回去
        if (element.getAttribute(name) !== value) {
          element.setAttribute(name, value);
        }
      }

      // 强制确保坐标属性不变
      if (originalX) element.setAttribute('x', originalX);
      if (originalY) element.setAttribute('y', originalY);
      if (originalDX) element.setAttribute('dx', originalDX);
      if (originalDY) element.setAttribute('dy', originalDY);
      if (originalTransform) element.setAttribute('transform', originalTransform);

      console.log(`保持文本点位[${elementId}]原始坐标: x=${originalX}, y=${originalY}`);
    } catch (e) {
      console.error(`设置文本内容失败:`, e);
    }
  } else {
    console.log(`文本点位[${elementId}]没有可用数据更新，保留原值: "${originalText}"`);
  }
}

// 处理实时数据
const processRealTimeData = (data: any[]) => {
  if (!svgContentRef.value || !data || data.length === 0) return

  const svgContainer = svgContentRef.value as HTMLElement
  const svgElement = svgContainer.querySelector('svg')
  if (!svgElement) return

  // 用于存储处理后的点位数据
  const processedPoints: any[] = []
  // 记录未找到的点位
  const notFoundPoints: string[] = []
  // 记录匹配的点位数量
  let matchedCount = 0
  // 记录更新的文本点位数量
  let textUpdatedCount = 0
  // 记录更新的图形点位数量
  let shapeUpdatedCount = 0

  console.log(`开始处理${data.length}个监测点数据`)

  // 处理每个点位数据
  data.forEach(item => {
    // 查找匹配的SVG元素
    let element: Element | null = null

    // 只通过data-indicator-code属性查找元素
    if (item.indicatorCode) {
      // 只使用data-indicator-code选择器
      const selector = `[data-indicator-code="${item.indicatorCode}"]`
      element = svgElement.querySelector(selector)

      if (element) {
        // 如果同时需要匹配showType，则检查元素的data-show-type属性是否与item.showType匹配
        if (item.showType) {
          const elementShowType = element.getAttribute('data-show-type')
          if (elementShowType && elementShowType !== item.showType) {
            // showType不匹配，不使用此元素
            console.log(`点位${item.indicatorCode}的data-indicator-code匹配成功，但showType不匹配: ${elementShowType} != ${item.showType}`)
            element = null
          } else {
            console.log(`点位${item.indicatorCode}通过data-indicator-code和data-show-type匹配成功`)
          }
        } else {
          console.log(`点位${item.indicatorCode}通过data-indicator-code匹配成功`)
        }
      } else {
        console.log(`未找到data-indicator-code为${item.indicatorCode}的点位元素`)
      }
    }

    // 如果找到了元素，更新它并添加到处理后的数据中
    if (element) {
      matchedCount++;

      const elementId = element.getAttribute('id') || element.getAttribute('data-id') || '';
      const originalText = element.textContent || '';
      const tagName = element.tagName.toLowerCase();
      const showType = element.getAttribute('data-show-type') || 'shape';
      console.log("pppppppppppppppppp", showType);

      // 根据showType判断是否为文本元素，而不是根据tagName
      if (showType === 'text' || showType === 'number') {
        console.log(`准备更新文本点位[${elementId}]，原始值: "${originalText}"，数据:`, {
          indicatorValue: item.indicatorValue,
          value: item.value,
          text: item.text,
          color: item.color
        });

        // 使用新函数仅更新文本内容
        updateTextContentOnly(element, item);

        // 只有当服务器明确返回颜色时才更新颜色
        if (item.color !== undefined && item.color !== null && item.color !== '') {
          element.setAttribute('fill', item.color);
          console.log(`更新文本点位[${elementId}]颜色: ${item.color}`);
        } else {
          console.log(`文本点位[${elementId}]没有新的颜色数据，保留原始颜色`);
          // 如果之前保存了原始颜色，恢复它
          const originalFill = element.getAttribute('data-original-fill');
          if (originalFill) {
            element.setAttribute('fill', originalFill);
            console.log(`恢复文本点位[${elementId}]的原始颜色: ${originalFill}`);
          }
        }

        textUpdatedCount++;
      }
      // 根据showType判断是否为图形元素，而不是根据tagName
      else if (showType === 'shape' || showType === 'status' || showType === 'circle' || showType === 'rect' || showType === 'polygon') {
        // 只有当服务器明确返回颜色时才更新颜色
        if (item.color !== undefined && item.color !== null && item.color !== '') {
          if (tagName === 'path') {
            // 对于路径，优先修改fill，如果没有fill则修改stroke
            if (element.hasAttribute('fill') && element.getAttribute('fill') !== 'none') {
              element.setAttribute('fill', item.color);
            } else if (element.hasAttribute('stroke')) {
              element.setAttribute('stroke', item.color);
            } else {
              element.setAttribute('fill', item.color);
            }
          } else {
            // 其他图形元素直接修改fill
            element.setAttribute('fill', item.color);
          }
          console.log(`更新图形点位[${elementId}]颜色: ${item.color}`);
          shapeUpdatedCount++;
        } else {
          console.log(`图形点位[${elementId}]没有新的颜色数据，保留原始颜色`);
          // 如果之前保存了原始颜色，恢复它
          const originalFill = element.getAttribute('data-original-fill');
          const originalStroke = element.getAttribute('data-original-stroke');

          if (originalFill && element.hasAttribute('fill') && element.getAttribute('fill') !== 'none') {
            element.setAttribute('fill', originalFill);
            console.log(`恢复图形点位[${elementId}]的原始fill颜色: ${originalFill}`);
          }

          if (originalStroke && element.hasAttribute('stroke')) {
            element.setAttribute('stroke', originalStroke);
            console.log(`恢复图形点位[${elementId}]的原始stroke颜色: ${originalStroke}`);
          }
        }
      }

      // 添加到处理后的点位数据中
      processedPoints.push({
        id: item.id || item.indicatorCode,
        indicatorCode: item.indicatorCode,
        value: item.value,
        indicatorValue: item.indicatorValue,
        text: item.text || item.value,
        color: item.color
      });
    } else {
      // 记录未找到的点位
      notFoundPoints.push(item.indicatorCode || item.id || '未知ID')
      console.warn(`未找到匹配的点位元素: indicatorCode=${item.indicatorCode}, id=${item.id}`)
    }
  })

  // 输出匹配统计信息
  console.log(`点位匹配统计: 总计${data.length}个, 成功匹配${matchedCount}个, 更新文本${textUpdatedCount}个, 更新图形${shapeUpdatedCount}个, 未匹配${notFoundPoints.length}个`)
  if (notFoundPoints.length > 0) {
    console.log('未匹配的点位:', notFoundPoints)
  }

  // 更新点位数据引用
  pointsData.value = processedPoints
}


// 组件卸载前清理
onBeforeUnmount(() => {
  stopSimulation()
  stopRealTimeUpdate()
  window.removeEventListener('resize', () => {
    if (currentScreen.value?.svg) {
      autoFitSvg()
    }
  })
})



// 处理科学计数法转换为普通数值显示
const formatNumberDisplay = (value: any): string => {
  if (value === undefined || value === null) return '-';

  // 转为字符串
  const strValue = String(value).trim();

  // 如果是布尔值或非数值，直接返回
  if (strValue === 'true' || strValue === 'false' || strValue === '1' || strValue === '0') {
    return strValue;
  }

  // 尝试转换为数字
  const numValue = Number(strValue);
  if (isNaN(numValue)) {
    return strValue; // 非数字，原样返回
  }

  // 检查是否是科学计数法形式的字符串
  if (/^-?\d+\.?\d*e[+-]\d+$/i.test(strValue)) {
    // 如果是科学计数法形式，直接转为普通数字显示，不做额外处理
    return numValue.toString();
  }

  // 对于普通数值，统一保留小数点后三位
  return numValue.toFixed(3);
}

// 格式化指标值显示
const formatIndicatorValue = (value: string | undefined, pointType: string | undefined): string => {
  if (value === undefined) return '-'

  // 根据点位类型处理不同的值显示
  if (pointType === '7') { // 运行状态类型
    // 处理布尔值字符串
    if (value === 'true' || value === '1') return '运行'
    if (value === 'false' || value === '0') return '停止'
  }

  // 对于数值类型，处理科学计数法
  return formatNumberDisplay(value)
}

// 格式化点位状态
const formatPointStatus = (value: string | undefined, pointType: string | undefined): string => {
  if (value === undefined) return '-'

  // 根据点位类型处理不同的状态显示
  switch (pointType) {
    case '7': // 运行状态类型
      return value === 'true' || value === '1' ? '运行中' : '停止'
    case '8': // 故障状态类型
      return value === 'true' || value === '1' ? '故障' : '正常'
    case '9': // 开关状态类型
      return value === 'true' || value === '1' ? '开启' : '关闭'
    case '10': // 在线/离线状态
      return value === 'true' || value === '1' ? '在线' : '离线'
    default:
      // 对于数值类型，检查是否超出范围
      const numValue = parseFloat(value)
      if (!isNaN(numValue)) {
        // 这里可以根据不同的指标类型设置不同的阈值判断
        return numValue > 0 ? '正常' : '异常'
      }

      // 默认返回原始值
      return value || '-'
  }
}


// 根据值获取颜色
const getValueColor = (data: any) => {
  if (!data) return '#333'

  // 优先根据状态判断颜色
  if (data.status) {
    switch (data.status) {
      case '正常': return '#67C23A'
      case '运行中': return '#67C23A'
      case '开启': return '#67C23A'
      case '在线': return '#67C23A'
      case '异常': return '#E6A23C'
      case '故障': return '#F56C6C'
      case '停止': return '#909399'
      case '关闭': return '#909399'
      case '离线': return '#909399'
      default: return '#333'
    }
  }

  // 如果没有状态，根据点位类型和值判断
  if (data.pointType && data.value) {
    switch (data.pointType) {
      case '7': // 运行状态
        return data.value === 'true' || data.value === '1' || data.value === '运行' ? '#67C23A' : '#909399'
      case '8': // 故障状态
        return data.value === 'true' || data.value === '1' || data.value === '故障' ? '#F56C6C' : '#67C23A'
      case '9': // 开关状态
        return data.value === 'true' || data.value === '1' || data.value === '开启' ? '#67C23A' : '#909399'
      case '10': // 在线/离线状态
        return data.value === 'true' || data.value === '1' || data.value === '在线' ? '#67C23A' : '#909399'
      default:
        // 数值类型
        const numValue = parseFloat(data.value)
        if (!isNaN(numValue)) {
          // 可以根据数值大小返回不同颜色
          return numValue > 0 ? '#67C23A' : '#E6A23C'
        }
    }
  }

  return '#333' // 默认颜色
}

// 对话框打开后处理函数，确保图表正确渲染
const handleDialogOpened = () => {
  if (selectedProcessData.value) {
    // 使用setTimeout确保DOM已完全渲染
    setTimeout(() => {
      const historyData = selectedProcessData.value?.historyData || []
      initChart(historyData)

      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener('resize', handleChartResize)
    }, 100)
  }
}

// 处理图表大小调整
const handleChartResize = () => {
  const chartDom = document.getElementById('pointDataChart')
  if (chartDom) {
    const chart = echarts.getInstanceByDom(chartDom)
    if (chart) {
      chart.resize()
    }
  }
}

// 对话框关闭后处理函数，清理资源
const handleDialogClosed = () => {
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleChartResize)

  // 销毁图表实例
  const chartDom = document.getElementById('pointDataChart')
  if (chartDom) {
    echarts.dispose(chartDom)
  }
}

// 保存所有SVG元素的原始颜色
const saveAllOriginalColors = () => {
  if (!svgContentRef.value) return

  const svgContainer = svgContentRef.value as HTMLElement
  const svgElement = svgContainer.querySelector('svg')
  if (!svgElement) return

  console.log('保存所有SVG元素的原始颜色信息...')

  // 查找所有具有fill或stroke属性的元素
  const colorElements = svgElement.querySelectorAll('[fill], [stroke]')
  let savedCount = 0

  colorElements.forEach(element => {
    // 保存fill属性
    if (element.hasAttribute('fill') && !element.hasAttribute('data-original-fill')) {
      const fillValue = element.getAttribute('fill')
      if (fillValue !== null && fillValue !== 'none') {
        element.setAttribute('data-original-fill', fillValue)
        savedCount++
      }
    }

    // 保存stroke属性
    if (element.hasAttribute('stroke') && !element.hasAttribute('data-original-stroke')) {
      const strokeValue = element.getAttribute('stroke')
      if (strokeValue !== null && strokeValue !== 'none') {
        element.setAttribute('data-original-stroke', strokeValue)
        savedCount++
      }
    }
  })

  console.log(`成功保存${savedCount}个元素的原始颜色信息`)
}

</script>

<style scoped lang="scss">
.process-screen-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #091637;
}

.title {
  color: #abdae9;
  padding-left: 10px;
  height: 40px;
  line-height: 40px;
  font-size: 25px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: none;
  background: linear-gradient(to right, #1e5799, #2989d8);
  border-radius: 4px;
  margin-bottom: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
  /* 防止标题被压缩 */

  .el-select {
    margin-left: 20px;
    width: 160px;
  }
}

.svg-main-container {
  position: relative;
  flex: 1;
  display: flex;
  overflow: hidden;
  padding-top: 5px;
  /* 减少顶部间距 */
  padding-bottom: 0;
  /* 移除底部间距 */
  width: 100%;
  margin-bottom: 0;
  /* 移除底部边距 */
}

.svg-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  user-select: none;
  /* 防止文本选择干扰拖拽 */
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

/* 加载遮罩层样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ffffff;
}

.loading-spinner .el-icon {
  font-size: 36px;
  margin-bottom: 10px;
  color: #409EFF;
}

.loading-spinner span {
  font-size: 14px;
}

.svg-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.svg-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
  transform-origin: 0 0;
  /* 确保变换原点在左上角 */

  :deep(svg) {
    display: block;
    overflow: visible;
    /* 允许SVG内容超出边界 */
    width: 100% !important;
    height: 100% !important;
    max-width: none;
    max-height: none;
    transform-origin: 0 0;
    /* 确保SVG变换原点在左上角 */

    // 当SVG中的点位被点击时，增加一些视觉反馈
    :deep([data-id]) {
      &[style*="cursor: pointer"]:hover {
        filter: brightness(1.2);
      }
    }
  }
}

.transform-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.empty-state {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
}

// 监测点样式
.monitor-point {
  position: absolute;
  z-index: 10;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;

  .point-content {
    font-size: 12px;
    white-space: nowrap;
  }
}

// 外部监测点样式
.monitor-point {
  position: absolute;
  z-index: 10;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;

  &[style*="cursor: pointer"]:hover {
    filter: brightness(1.2);
  }

  .point-content {
    font-size: 12px;
    white-space: nowrap;
  }
}

// 缩放控制按钮样式
.zoom-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  z-index: 20;
}

// 监测点详情弹窗样式
:deep(.monitor-point-dialog) {
  .el-dialog__header {
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    margin-right: 0;
    background: linear-gradient(to right, #ecf6ff, #ffffff);

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
    }
  }

  .el-dialog__body {
    padding: 16px;
  }

  .point-detail-container {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .info-card,
    .trend-chart-container {
      border-radius: 6px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      background-color: #fff;
      border: 1px solid #ebeef5;
      overflow: hidden;
    }

    .card-header {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      font-size: 14px;
      font-weight: 500;
      color: #303133;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }

    .card-body {
      padding: 12px;
    }

    .info-row {
      display: flex;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px dashed #ebeef5;

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .info-label {
        width: 120px;
        font-weight: 500;
        color: #606266;
        padding-right: 16px;
      }

      .info-value {
        flex: 1;
        color: #303133;

        .value-text {
          font-weight: bold;
          font-size: 16px;
          margin-right: 4px;
        }

        .unit-text {
          font-size: 12px;
          color: #909399;
        }

      }
    }

    .chart-wrapper {
      padding: 12px;
      height: 300px; // 增加图表高度

      #pointDataChart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
