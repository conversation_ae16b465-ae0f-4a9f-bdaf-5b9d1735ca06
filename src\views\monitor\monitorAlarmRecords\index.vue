<template>
  <div class="w-full h-[calc(100vh-170px)] flex flex-col">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">告警记录</span>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-260px)] flex flex-col">
        <div class="w-full h-[50px] mb-2 gap-2 flex items-center">
          <el-form :model="searchForm" inline>
            <el-form-item label="告警时间：">
              <el-date-picker v-model="searchForm.timeRange" type="datetimerange" range-separator="至"
                start-placeholder="开始时间" end-placeholder="结束时间" />
            </el-form-item>
            <el-form-item label="告警级别：">
              <el-select v-model="searchForm.level" placeholder="请选择告警级别" clearable @change="handleSearch"
                style="width: 200px">
                <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
                  <div class="flex items-center">
                    <div :class="getLevelColorClass(item.value)" class="w-3 h-3 rounded-full mr-2"></div>
                    <span>{{ item.label }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="处理状态：">
              <el-select v-model="searchForm.status" placeholder="请选择处理状态" clearable @change="handleSearch"
                style="width: 200px">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
              <el-button @click="resetForm" :icon="RefreshRight">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border v-loading="loading" max-height="580px">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="factoryName" label="水厂名称" align="center" />
                <el-table-column prop="factorName" label="告警因子" align="center" />
                <el-table-column prop="factorValue" label="实际值/实际状态" align="center">
                  <template #default="{ row }">
                    {{ row.factorType == "7" ? row.factorValue == "1" ? true : false : row.factorValue }}
                  </template>
                </el-table-column>
                <el-table-column prop="threshold" label="阈值/告警状态" align="center">
                  <template #default="{ row }">
                    {{ row.factorType == "7" ? row.threshold == "1" ? true : false : row.threshold }}
                  </template>
                </el-table-column>
                <el-table-column prop="unit" label="单位" align="center">
                  <template #default="{ row }">
                    {{ row.unit || '/' }}
                  </template>
                </el-table-column>
                <el-table-column prop="level" label="告警级别" align="center" width="100">
                  <template #default="scope">
                    <el-tag :type="getAlarmLevelType(scope.row.level)">{{ scope.row.level }}级</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="handler" label="处理人" align="center" />
                <el-table-column prop="alarmTime" label="告警时间" align="center">
                  <template #default="{ row }">
                    {{ formatTimestamp(row.alarmTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="processTime" label="处理时间" align="center">
                  <template #default="{ row }">
                    {{ formatTimestamp(row.processTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="ruleName" label="告警规则" align="center" />
                <el-table-column label="操作" align="center" width="120">
                  <template #default="{ row }">
                    <el-button type="primary" link @click="viewDetail(row)">
                      处理/详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 告警详情与处理对话框 -->
    <el-dialog v-model="detailDialogVisible" title="告警详情与处理" width="700px" :close-on-click-modal="false">
      <el-tabs v-if="currentAlarm" v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="基本信息" name="info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="水厂名称" :span="2">{{ currentAlarm.factoryName }}</el-descriptions-item>
            <el-descriptions-item label="告警因子">{{ currentAlarm.factorName }}</el-descriptions-item>
            <el-descriptions-item label="告警规则">{{ currentAlarm.ruleName }}</el-descriptions-item>
            <el-descriptions-item label="告警级别">
              <el-tag :type="getAlarmLevelType(currentAlarm.level)">{{ currentAlarm.level }}级</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="告警状态">
              <el-tag :type="getStatusTagType(currentAlarm.status)">
                {{ getStatusText(currentAlarm.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="告警时间" :span="2">{{ formatTimestamp(currentAlarm.alarmTime)
            }}</el-descriptions-item>
            <el-descriptions-item label="实际值">{{ currentAlarm.factorValue }}</el-descriptions-item>
            <el-descriptions-item label="阈值">{{ currentAlarm.threshold }}</el-descriptions-item>
            <el-descriptions-item label="单位" :span="2">{{ currentAlarm.unit || '/' }}</el-descriptions-item>
            <el-descriptions-item label="处理人" v-if="currentAlarm.handler">{{ currentAlarm.handler
            }}</el-descriptions-item>
            <el-descriptions-item label="处理时间" v-if="currentAlarm.processTime">{{
              formatTimestamp(currentAlarm.processTime)
            }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>

        <el-tab-pane label="处理流程" name="process" lazy>
          <div class="p-4">
            <!-- 先展示操作历史 -->
            <el-divider content-position="left">操作历史</el-divider>

            <div v-if="!actionHistory.length" class="text-center text-gray-400 py-2 text-sm">
              暂无操作记录
            </div>
            <div v-else class="history-scroll-container max-h-[200px] overflow-y-auto pr-2">
              <el-timeline>
                <el-timeline-item v-for="(action, index) in actionHistory" :key="index"
                  :timestamp="formatTimestamp(action.processTime)"
                  :type="action.type === 'acknowledge' ? 'warning' : 'success'">
                  <h4>{{ action.type === 'acknowledge' ? '确认告警' : '解决告警' }}</h4>
                  <p>处理人：{{ action.handler }}</p>
                  <p v-if="action.remark">备注：{{ action.remark }}</p>
                </el-timeline-item>
              </el-timeline>
            </div>

            <el-divider v-if="currentAlarm.status !== 'resolved'" content-position="left">处理操作</el-divider>

            <el-alert v-if="currentAlarm.status === 'resolved'" type="success" title="此告警已解决"
              description="已处理完毕，无需进一步操作" show-icon :closable="false" class="mt-4" />

            <el-form v-else :model="processForm" label-width="100px">
              <el-form-item label="处理操作" required>
                <el-radio-group v-model="processForm.actionType">
                  <!-- 只有未处理(active)状态的告警才能"确认"或"确认并解决" -->
                  <el-radio v-if="currentAlarm.status === 'active'" label="acknowledge">
                    确认告警
                  </el-radio>

                  <!-- 只有已确认(acknowledged)状态的告警才能"解决" -->
                  <el-radio v-if="currentAlarm.status === 'acknowledged'" label="resolve">
                    解决告警
                  </el-radio>

                  <!-- 未处理状态可以直接确认并解决 -->
                  <el-radio v-if="currentAlarm.status === 'active'" label="acknowledge_resolve">
                    确认并解决
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="处理人" required>
                <el-input v-model="processForm.handler" placeholder="请输入处理人姓名" />
              </el-form-item>

              <el-form-item
                v-if="processForm.actionType === 'resolve' || processForm.actionType === 'acknowledge_resolve'"
                label="处理方式">
                <el-radio-group v-model="processForm.resolveMethod">
                  <el-radio label="维修">维修</el-radio>
                  <el-radio label="参数调整">参数调整</el-radio>
                  <el-radio label="其他">其他</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="备注">
                <el-input v-model="processForm.remark" type="textarea" :rows="3" placeholder="请输入处理备注" />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="confirmProcess">提交处理</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { RefreshRight, Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { reactive, ref, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { getAlarmEvents, processAlarmEvent, getAlarmActionLogs } from '@/api/alarm';
import { useAppStoreWithOut } from '@/store/modules/app'
import dayjs from 'dayjs';

interface AlarmRecord {
  id: number;
  ruleId: number;
  ruleName: string;
  factoryId: string;
  factoryName: string;
  alarmTime: string;
  factorCode: string;
  factorName: string;
  factorValue: number;
  ruleType: string;
  operator: string;
  threshold: number;
  unit: string;
  expression: string;
  level: string;
  status: 'active' | 'acknowledged' | 'resolved';
  handler: string;
  processTime: string;
  notifyStatus: string;
  notifyRecords: string;
  createTime: string;
}

// 获取路由对象，用于获取水厂id
const route = useRoute();
const loading = ref(false);
// 获取应用状态
const appStore = useAppStoreWithOut()

const levelOptions = [
  { value: 'I', label: 'I级 (重要)' },
  { value: 'II', label: 'II级 (一般)' },
]

const statusOptions = [
  { value: 'active', label: '未处理' },
  { value: 'acknowledged', label: '已确认' },
  { value: 'resolved', label: '已解决' }
]

const searchForm = reactive({
  status: '',
  level: '',
  timeRange: []
})

const tableData = ref<AlarmRecord[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const detailDialogVisible = ref(false)
const processDialogVisible = ref(false)
const currentAlarm = ref<AlarmRecord | null>(null)

const processForm = reactive({
  actionType: 'acknowledge', // 默认为确认操作
  handler: '',
  remark: '',
  resolveMethod: '维修' // 默认处理方式
})

const actionHistory = ref<Array<{
  type: 'acknowledge' | 'resolve';
  processTime: string;
  handler: string;
  remark?: string;
}>>([]);

// 在script部分添加激活标签变量
const activeTab = ref('info');

// 获取告警事件分页列表
const fetchAlarmEvents = async () => {
  try {
    loading.value = true;
    // 从全局状态获取水厂信息
    const currentStation = appStore.currentStation;
    const factoryId = currentStation?.id || '';

    // 构建请求参数
    const params = {
      factoryId: factoryId,
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      status: searchForm.status || undefined,
      level: searchForm.level || undefined,
      alarmTime: searchForm.timeRange || undefined
    };

    // 使用API调用获取数据
    const response = await getAlarmEvents(params);

    // 处理响应数据
    if (response && response.data) {
      tableData.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response?.data?.message || '获取告警事件列表失败');
    }
  } catch (error) {
    console.error('获取告警事件列表失败:', error);
    ElMessage.error('获取告警事件列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 获取告警级别对应的标签类型
const getAlarmLevelType = (level: string): 'success' | 'warning' | 'info' | 'danger' | 'primary' => {
  const types: Record<string, 'success' | 'warning' | 'info' | 'danger' | 'primary'> = {
    'I': 'danger',
    'II': 'warning'
  }
  return types[level] || 'info'
}

// 获取告警级别对应的颜色类名
const getLevelColorClass = (level: string) => {
  const colors: Record<string, string> = {
    'I': 'bg-red-500',
    'II': 'bg-orange-400'
  }
  return colors[level] || 'bg-gray-400'
}

// 获取状态显示文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'active': '未处理',
    'acknowledged': '已确认',
    'resolved': '已解决'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status: string): 'success' | 'warning' | 'danger' => {
  const typeMap: Record<string, 'success' | 'warning' | 'danger'> = {
    'active': 'danger',
    'acknowledged': 'warning',
    'resolved': 'success'
  }
  return typeMap[status] || 'info'
}

const handleSearch = async () => {
  currentPage.value = 1; // 重置到第一页
  await fetchAlarmEvents();
}

const resetForm = () => {
  searchForm.status = ''
  searchForm.level = ''
  searchForm.timeRange = []
  handleSearch();
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchAlarmEvents();
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchAlarmEvents();
}

const viewDetail = (row: AlarmRecord) => {
  currentAlarm.value = row
  detailDialogVisible.value = true
  activeTab.value = 'info' // 确保默认激活基本信息标签

  // 根据当前告警状态设置默认操作类型
  if (row.status === 'active') {
    processForm.actionType = 'acknowledge' // 默认为确认操作
  } else if (row.status === 'acknowledged') {
    processForm.actionType = 'resolve' // 已确认状态，默认为解决操作
  }

  processForm.handler = ''
  processForm.remark = ''
  processForm.resolveMethod = '维修'

  // 加载操作历史
  loadActionHistory(row.id)
}

const loadActionHistory = async (alarmId: number) => {
  try {
    actionHistory.value = [];
    loading.value = true;

    const params = {
      eventId: alarmId,
      pageNo: 1,
      pageSize: 20
    };

    const response = await getAlarmActionLogs(params);

    if (response && response.data && response.data.list) {
      // 将API返回的数据转换为需要的格式
      actionHistory.value = response.data.list.map((item: any) => ({
        type: item.processStatus === 'acknowledged' ? 'acknowledge' : 'resolve',
        processTime: item.processTime,
        handler: item.handler,
        remark: item.remark || ''
      }));
    }
  } catch (error) {
    console.error('加载操作历史失败:', error)
    ElMessage.error('加载操作历史失败');
  } finally {
    loading.value = false;
  }
}

const confirmProcess = async () => {
  if (!processForm.handler) {
    return ElMessage.warning('请输入处理人姓名')
  }

  if (!currentAlarm.value) {
    return ElMessage.warning('未选择告警记录')
  }

  // 检查操作是否符合流程规则
  if (
    (currentAlarm.value.status === 'active' && processForm.actionType === 'resolve') ||
    (currentAlarm.value.status === 'acknowledged' &&
      (processForm.actionType === 'acknowledge' || processForm.actionType === 'acknowledge_resolve'))
  ) {
    return ElMessage.warning('操作不符合处理流程，请选择正确的处理方式')
  }

  try {
    // 处理"确认并解决"的情况
    if (processForm.actionType === 'acknowledge_resolve') {
      // 先进行确认操作
      const acknowledgeData = {
        eventId: currentAlarm.value.id,
        handler: processForm.handler,
        remark: processForm.remark,
        processStatus: 'acknowledged' as const
      }

      // 先确认
      await processAlarmEvent(acknowledgeData)

      // 然后解决
      const resolveData = {
        eventId: currentAlarm.value.id,
        handler: processForm.handler,
        remark: `${processForm.resolveMethod || '维修'}：${processForm.remark || ''}`,
        processStatus: 'resolved' as const
      }

      const response = await processAlarmEvent(resolveData)

      if (response) {
        // 更新本地数据
        if (currentAlarm.value) {
          currentAlarm.value.status = 'resolved'
          currentAlarm.value.handler = processForm.handler
          currentAlarm.value.processTime = Date.now().toString()

          // 更新表格中对应的数据
          const index = tableData.value.findIndex(item => item.id === currentAlarm.value?.id)
          if (index !== -1) {
            tableData.value[index] = { ...currentAlarm.value }
          }
        }

        ElMessage.success('告警已确认并解决');

        // 关闭弹窗
        detailDialogVisible.value = false;

        // 刷新列表
        await fetchAlarmEvents();
      } else {
        ElMessage.error(response?.message || '处理失败');
      }

      return;
    }

    // 处理普通的确认或解决
    const status = processForm.actionType === 'acknowledge' ? 'acknowledged' as const : 'resolved' as const

    const processData = {
      eventId: currentAlarm.value.id,
      handler: processForm.handler,
      remark: processForm.actionType === 'resolve'
        ? `${processForm.resolveMethod || '维修'}：${processForm.remark || ''}`
        : processForm.remark,
      processStatus: status
    }

    const response = await processAlarmEvent(processData)

    if (response) {
      // 更新本地数据
      if (currentAlarm.value) {
        currentAlarm.value.status = status
        currentAlarm.value.handler = processForm.handler
        currentAlarm.value.processTime = Date.now().toString()

        // 更新表格中对应的数据
        const index = tableData.value.findIndex(item => item.id === currentAlarm.value?.id)
        if (index !== -1) {
          tableData.value[index] = { ...currentAlarm.value }
        }
      }

      ElMessage.success(processForm.actionType === 'acknowledge' ? '告警确认成功' : '告警解决成功');

      // 关闭弹窗
      detailDialogVisible.value = false;

      // 刷新列表
      await fetchAlarmEvents();
    } else {
      ElMessage.error(response?.message || '处理失败');
    }
  } catch (error) {
    console.error('处理告警失败:', error);
    ElMessage.error('处理告警失败，请稍后重试');
  }
}

// 新增处理tab点击事件
const handleTabClick = (tab: any) => {
  if (tab.props.name === 'process' && currentAlarm.value) {
    loadActionHistory(currentAlarm.value.id);
  }
}

// 页面初始化时获取告警事件列表
onMounted(() => {
  fetchAlarmEvents();
});

// 监听水厂变化
watch(
  () => appStore.currentStation,
  (newVal, oldVal) => {
    if (newVal !== oldVal && newVal) {
      // 重置分页
      currentPage.value = 1;
      // 刷新数据
      fetchAlarmEvents();
    }
  }
);

// 格式化时间戳为日期时间字符串
const formatTimestamp = (timestamp: number | string): string => {
  if (!timestamp) return '';

  // 如果是数字类型的时间戳
  if (typeof timestamp === 'number') {
    return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss');
  }

  // 如果已经是格式化的字符串，直接返回
  if (typeof timestamp === 'string' && isNaN(Number(timestamp))) {
    return timestamp;
  }

  // 如果是字符串形式的数字时间戳
  return dayjs(Number(timestamp)).format('YYYY-MM-DD HH:mm:ss');
}
</script>
<style scoped lang="scss"></style>