// 引入unocss css
import '@/plugins/unocss'

// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 element-plus
import { setupElementPlus } from '@/plugins/elementPlus'

// 引入 form-create

// 引入全局样式
import '@/styles/index.scss'
import '@/styles/tailwind.css'


// 引入动画
import '@/plugins/animate.css'

// 路由
import router, { setupRouter } from '@/router'

// 指令
import { setupAuth, setupMountedFocus } from '@/directives'

import { createApp } from 'vue'

import App from './App.vue'

import Logger from '@/utils/Logger'

import './permission'

import VueDOMPurifyHTML from 'vue-dompurify-html'; // 解决v-html 的安全隐患


// 创建实例
const setupAll = async () => {
  const app = createApp(App)

  // 06-16：国际化部分，本项目暂时不需要，注释掉。
  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  setupElementPlus(app)

  // 06-16：该模块主要用于bpm中的表单创建，暂时注释掉(影响主屏加载)
  // setupFormCreate(app)

  setupRouter(app)

  // directives 指令
  setupAuth(app)
  setupMountedFocus(app)

  // 注册全局组件
  // app.component('Grid', Grid)
  // app.component('GridItem', GridItem)
  // app.component('ChartContainer', ChartContainer)
  // app.component('ChartTitle', ChartTitle)

  await router.isReady()

  app.use(VueDOMPurifyHTML)
  app.mount('#app')
}

setupAll()

Logger.prettyPrimary(`欢迎使用`, import.meta.env.VITE_APP_TITLE)
