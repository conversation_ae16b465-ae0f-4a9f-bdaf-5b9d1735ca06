<template>
  <ContentWrap>
    <!-- 审核统计部分 -->
    <el-card class="mb-20px">
      <template #header>
        <div class="flex justify-between">
          <span class="font-bold">今日审核情况</span>
        </div>
      </template>
      <div class="grid grid-cols-4 gap-20px" v-if="isAdmin">
        <!-- 未提交统计 -->
        <el-card shadow="never" class="statistic-card">
          <div class="statistic-header">
            <span class="text-lg font-bold">未提交</span>
            <div class="flex gap-2">
              <el-tag size="small" type="warning">消耗类: {{ consumptionStats.notSubmittedCount }}</el-tag>
              <el-tag size="small" type="success">水质水量: {{ qualityStats.notSubmittedCount }}</el-tag>
            </div>
          </div>
          <div class="statistic-total">
            <span class="text-2xl font-bold text-red">{{ notSubmitCount }}</span>
            <span class="text-sm text-gray-500">总数量</span>
          </div>
        </el-card>

        <!-- 已提交统计 -->
        <el-card shadow="never" class="statistic-card">
          <div class="statistic-header">
            <span class="text-lg font-bold">已提交</span>
            <div class="flex gap-2">
              <el-tag size="small" type="warning">消耗类: {{ consumptionStats.submittedCount }}</el-tag>
              <el-tag size="small" type="success">水质水量: {{ qualityStats.submittedCount }}</el-tag>
            </div>
          </div>
          <div class="statistic-total">
            <span class="text-2xl font-bold text-green">{{ submittedCount }}</span>
            <span class="text-sm text-gray-500">总数量</span>
          </div>
        </el-card>

        <!-- 未审核统计 -->
        <el-card shadow="never" class="statistic-card">
          <div class="statistic-header">
            <span class="text-lg font-bold">未审核</span>
            <div class="flex gap-2">
              <el-tag size="small" type="warning">消耗类: {{ consumptionStats.notReviewedCount }}</el-tag>
              <el-tag size="small" type="success">水质水量: {{ qualityStats.notReviewedCount }}</el-tag>
            </div>
          </div>
          <div class="statistic-total">
            <span class="text-2xl font-bold text-orange">{{ notReviewedCount }}</span>
            <span class="text-sm text-gray-500">总数量</span>
          </div>
        </el-card>

        <!-- 已审核统计 -->
        <el-card shadow="never" class="statistic-card">
          <div class="statistic-header">
            <span class="text-lg font-bold">已审核</span>
            <div class="flex gap-2">
              <el-tag size="small" type="warning">消耗类: {{ consumptionStats.reviewedCount }}</el-tag>
              <el-tag size="small" type="success">水质水量: {{ qualityStats.reviewedCount }}</el-tag>
            </div>
          </div>
          <div class="statistic-total">
            <span class="text-2xl font-bold text-blue">{{ reviewedCount }}</span>
            <span class="text-sm text-gray-500">总数量</span>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 待审核列表部分 -->
    <el-card>
      <template #header>
        <div class="flex justify-between">
          <span class="font-bold">审核列表</span>
          <div class="flex items-center gap-4">
            <el-date-picker v-model="selectedDate" type="date" placeholder="选择日期" class="w-200px"
              :disabled-date="(date) => date > new Date()" />
            <el-select v-model="queryParams.bizType" placeholder="请选择类型" clearable class="w-180px">
              <!-- <el-option label="全部" :value="undefined" /> -->
              <el-option label="水质水量" value="quality" />
              <el-option label="生产消耗" value="consumption" />
            </el-select>
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="w-180px">
              <el-option label="待审核" :value="2" />
              <el-option label="已通过" :value="3" />
              <el-option label="已驳回" :value="4" />

            </el-select>
            <el-select v-model="selectedFactory" placeholder="请选择水厂" class="w-240px" filterable clearable
              @clear="handleFactoryClear">
              <template v-for="group in groupedFactoryList" :key="'group-' + group.id">
                <el-option-group v-if="group.childrenLevel3 && group.childrenLevel3.length" :label="group.name">
                  <el-option v-for="item in group.childrenLevel3" :key="'item-' + item.id" :label="item.name"
                    :value="item.id" class="child-factory-option" />
                </el-option-group>
                <el-option v-else-if="group.level === 3" :key="'item-' + group.id" :label="group.name"
                  :value="group.id" />
              </template>
            </el-select>
          </div>
        </div>
      </template>
      <el-table v-loading="loading" :data="tableData" @selection-change="handleSelectionChange" :row-key="getRowKey"
        :expand-row-keys="expandedRows" @expand-change="handleExpand">
        <el-table-column type="selection" width="55" />
        <el-table-column type="expand" width="50">
          <template #default="{ row }">
            <div v-loading="row.loading">
              <el-table :data="row.children" border>
                <el-table-column prop="indicatorName" label="指标名称" min-width="120" />
                <el-table-column v-if="row.bizType === 'quality'" prop="standardValue" label="标准值" min-width="100">
                  <template #default="{ row: childRow }">
                    {{ childRow.standardValue ? childRow.standardValue : '/' }}
                  </template>
                </el-table-column>
                <el-table-column prop="indicatorValue" label="指标值" min-width="100" />
                <el-table-column v-if="row.bizType === 'quality'" prop="deviation" label="偏差值" min-width="100">
                  <template #default="{ row: childRow }">
                    <el-tag :type="getPercentageTagType(childRow.deviation)" size="small"
                      :class="{ 'highlight': childRow.deviation !== 0 }">
                      {{ formatDeviation(childRow.deviation) }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="reporterName" label="填报人" min-width="100" />
        <!-- <el-table-column prop="reviewerName" label="审核人" min-width="100" /> -->
        <el-table-column prop="factoryName" label="厂站名" min-width="120" />
        <el-table-column prop="bizType" label="业务类型" min-width="120">
          <template #default="{ row }">
            {{ row.bizType == "quality" ? "水质水量数据" : "生产消耗数据" }}

          </template>
        </el-table-column>
        <el-table-column prop="status" label="审核阶段" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getReviewStageTagType(row.status, row.isArchived)">
              {{ getReviewStageStatus(row.status, row.isArchived) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button v-if="row.isArchived === 1 || row.status === 3 || row.status === 4" type="primary" link
              @click="handleReview(row)">详情</el-button>
            <el-button v-if="row.isArchived === 0 && row.status === 2" type="primary" link :loading="reviewLoading"
              :disabled="reviewLoading" @click="handleReview(row)">审核</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="mt-10px flex justify-end">
        <el-pagination v-model:current-page="queryParams.pageNo" v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]" background layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 审核对话框 -->
    <el-dialog v-model="dialogVisible" title="指标审核" width="900px" destroy-on-close @close="handleDialogClose">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="填报人">{{ currentRow.reporterName }}</el-descriptions-item>
        <!-- <el-descriptions-item label="审核人">{{ currentRow.reviewerName }}</el-descriptions-item> -->
        <el-descriptions-item label="厂站名">{{ currentRow.factoryName }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">
          {{ currentRow.bizType == "quality" ? "水质水量数据" : "生产消耗数据" }}
        </el-descriptions-item>
        <el-descriptions-item label="审核阶段" :span="2">
          <el-tag :type="getReviewStageTagType(currentRow.status, currentRow.isArchived)">
            {{ getReviewStageStatus(currentRow.status, currentRow.isArchived) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 审核流程记录 -->
      <div class="mt-20px">
        <div class="font-bold mb-10px">审核流程记录</div>
        <el-timeline>
          <el-timeline-item v-for="(item, index) in reviewHistory" :key="index" :timestamp="item.time"
            :type="getTimelineItemType(item.flowStage)">
            <div class="font-bold">{{ getTimelineIteName(item.flowStage) }}</div>
            <div>操作人: {{ item.operatorName }}</div>
            <div v-if="item.comment">审核意见: {{ item.comment }}</div>
            <div>操作时间: {{ formatTime(item.operatorTime) }}</div>
          </el-timeline-item>
        </el-timeline>
      </div>


      <!-- 指标列表 -->
      <div class="mt-20px">
        <div class="font-bold mb-10px">指标列表</div>
        <el-table :data="indicatorList" border max-height="300">
          <!-- <el-table-column prop="id" label="指标ID" width="80" /> -->
          <el-table-column prop="indicatorName" label="指标名称" min-width="120" />
          <el-table-column v-if="currentRow.bizType === 'quality'" prop="standardValue" label="标准值" min-width="100">
            <template #default="{ row: childRow }">
              {{ childRow.standardValue ? childRow.standardValue : '/' }}
            </template>
          </el-table-column>
          <el-table-column prop="indicatorValue" label="指标值" min-width="100" />
          <el-table-column v-if="currentRow.bizType === 'quality'" prop="deviation" label="偏差值" min-width="100">
            <template #default="{ row: childRow }">
              <el-tag :type="getPercentageTagType(childRow.deviation)" size="small"
                :class="{ 'highlight': childRow.deviation !== 0 }">
                {{ formatDeviation(childRow.deviation) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 审核意见 -->
      <div class="mt-20px" v-if="currentRow.isArchived === 0 && currentRow.status === 2">
        <div class="font-bold mb-10px">审核意见</div>
        <el-input v-model="reviewComment" type="textarea" :rows="3" placeholder="请输入审核意见" />
      </div>

      <template #footer>
        <div class="flex justify-end">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <template v-if="currentRow.isArchived === 0 && currentRow.status === 2">
            <el-button type="warning" :loading="rejectLoading" :disabled="approveLoading || rejectLoading"
              @click="handleReject">退回</el-button>
            <el-button type="primary" :loading="approveLoading" :disabled="approveLoading || rejectLoading"
              @click="handleApprove">通过</el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, toRef } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import {
  getReviewList,
  getStatistics,
  batchApproveIndicator,
  batchRejectIndicator,
  getReviewFlowPage
} from '@/api/report/review'
import { getIndicatorStandardConfig } from '@/api/report/indicatorStandardConfig'
import { useUserStore } from '@/store/modules/user'
import { FactoryApi } from '@/api/report/factory/index'

// 用户信息
const userStore = useUserStore()
const userName = computed(() => userStore.getUser.nickname)
const userId = computed(() => userStore.getUser.id)
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 统计类型
const statisticsType = ref<'consumption' | 'quality' | undefined>(undefined)

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  bizType: undefined as 'consumption' | 'quality' | undefined,
  factoryId: computed(() => selectedFactory.value),
  reportDate: computed(() => {
    if (!selectedDate.value) return ''
    return `${selectedDate.value.getFullYear()}-${String(selectedDate.value.getMonth() + 1).padStart(2, '0')}-${String(selectedDate.value.getDate()).padStart(2, '0')}`
  }),
  isArchived: 0,
  status: undefined as number | undefined
})

// 确保选择器和统计类型保持一致
watch(() => queryParams.bizType, (newVal) => {
  if (newVal) {
    statisticsType.value = newVal
  }
}, { immediate: true })

watch(statisticsType, (newVal) => {
  if (newVal) {
    queryParams.bizType = newVal
  }
}, { immediate: true })

// 统计数据
const notSubmitCount = ref(0)
const submittedCount = ref(0)
const notReviewedCount = ref(0)
const reviewedPassCount = ref(0)
const reviewedRejectCount = ref(0)
// 待审核列表数据
const loading = ref(false)
const tableData = ref<any[]>([])
const total = ref(0)
const selectedRows = ref<any[]>([])

// 水厂相关数据
const factoryList = ref<any[]>([])
const selectedFactory = ref<number | undefined>(undefined)

// 日期筛选
const yesterday = new Date()
yesterday.setDate(yesterday.getDate() - 1)
const selectedDate = ref<Date>(yesterday)

// 审核对话框
const dialogVisible = ref(false)
const currentRow = ref<any>({})
const reviewComment = ref('')
const reviewHistory = ref<any[]>([])
const indicatorList = ref<any[]>([])

// 加载状态
const reviewLoading = ref(false)
const approveLoading = ref(false)
const rejectLoading = ref(false)

// 权限检查函数
const hasPermission = (permission: string) => {
  // 这里可以根据实际权限配置进行判断
  console.log(permission, userStore.getRoles, 'permission')
  return userStore.getRoles.includes(permission)
}

// 添加管理员权限判断
const isAdmin = computed(() => {
  return hasPermission('report_admin')
})

// 添加新的响应式变量
const consumptionStats = ref({
  notSubmittedCount: 0,
  submittedCount: 0,
  notReviewedCount: 0,
  reviewedCount: 0
})

const qualityStats = ref({
  notSubmittedCount: 0,
  submittedCount: 0,
  notReviewedCount: 0,
  reviewedCount: 0
})

const reviewedCount = computed(() => {
  return reviewedPassCount.value + reviewedRejectCount.value
})

// 防抖定时器
let statisticsTimer: ReturnType<typeof setTimeout> | null = null

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 如果未选择日期，则默认使用昨天的日期
    let date = '';
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    date = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;


    const [consumptionRes, qualityRes] = await Promise.all([
      getStatistics('consumption', date),
      getStatistics('quality', date)
    ])

    if (consumptionRes && qualityRes) {
      // 更新消耗类统计
      consumptionStats.value = {
        notSubmittedCount: consumptionRes.notSubmittedCount || 0,
        submittedCount: consumptionRes.submittedCount || 0,
        notReviewedCount: consumptionRes.notReviewedCount || 0,
        reviewedCount: (consumptionRes.reviewedPassCount || 0) + (consumptionRes.reviewedRejectCount || 0)
      }

      // 更新水质水量统计
      qualityStats.value = {
        notSubmittedCount: qualityRes.notSubmittedCount || 0,
        submittedCount: qualityRes.submittedCount || 0,
        notReviewedCount: qualityRes.notReviewedCount || 0,
        reviewedCount: (qualityRes.reviewedPassCount || 0) + (qualityRes.reviewedRejectCount || 0)
      }

      // 更新总数
      notSubmitCount.value = consumptionStats.value.notSubmittedCount + qualityStats.value.notSubmittedCount
      submittedCount.value = consumptionStats.value.submittedCount + qualityStats.value.submittedCount
      notReviewedCount.value = consumptionStats.value.notReviewedCount + qualityStats.value.notReviewedCount
      reviewedPassCount.value = (consumptionRes.reviewedPassCount || 0) + (qualityRes.reviewedPassCount || 0)
      reviewedRejectCount.value = (consumptionRes.reviewedRejectCount || 0) + (qualityRes.reviewedRejectCount || 0)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取待审核列表
const fetchReviewList = async () => {

  loading.value = true
  try {
    // 构建查询参数，不强制要求factoryId
    const params = {
      ...queryParams,
      factoryId: selectedFactory.value || undefined
    }

    const res = await getReviewList(params)
    tableData.value = res.list
    total.value = res.total
  } catch (error) {
    console.error('获取待审核列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 页码变化
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  fetchReviewList()
}

const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  fetchReviewList()
}

// 选择表格行
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 获取审核阶段标签类型
const getReviewStageTagType = (stage: number, isArchived: number) => {
  if (isArchived == 1) {
    return 'success'
  }
  switch (stage) {
    case 2:
      return 'warning'
    case 3:
      return 'success'
    case 4:
      return 'danger'
    default:
      return 'info'
  }
}

// 获取审核阶段状态值
const getReviewStageStatus = (status: number, isArchived: number) => {
  if (isArchived == 1) {
    return '已归档'
  }
  switch (status) {
    case 2:
      return '待审核'
    case 3:
      return '已通过'
    case 4:
      return '已驳回'
    default:
      return 'info'
  }
}
// 计算数值对比的差异比例
const getPercentageDiff = (standardValue: number, indicatorValue: number) => {
  if (!standardValue) return 0
  const diff = Math.abs(((indicatorValue - standardValue) / standardValue) * 100)
  return Math.min(diff, 100)
}

// 获取百分比标签类型
const getPercentageTagType = (deviation: number) => {
  if (!deviation) return 'info'
  const absDeviation = Math.abs(deviation)
  if (absDeviation > 20) return 'danger'
  if (absDeviation > 10) return 'warning'
  return 'success'
}

// 格式化偏差值
const formatDeviation = (deviation: number) => {
  if (!deviation) return '0'
  const prefix = deviation > 0 ? '+' : ''
  return `${prefix}${deviation.toFixed(2)}`
}

// 获取对比样式
const getCompareClass = (deviation: number) => {
  if (!deviation) return ''
  const absDeviation = Math.abs(deviation)
  if (absDeviation > 20) return 'text-red'
  if (absDeviation > 10) return 'text-orange'
  return 'text-green'
}

// 获取时间线项类型
const getTimelineItemType = (action: number) => {
  switch (action) {
    case 1:
      return 'primary'
    case 2:
      return 'success'
    case 3:
      return 'danger'
    default:
      return 'info'
  }
}

const getTimelineIteName = (action: number) => {
  switch (action) {
    case 1:
      return '提交'
    case 2:
      return '通过'
    case 3:
      return '驳回'
    case 4:
      return '管理员操作'
    default:
      return '其他'
  }
}

// 根据填报人以及水厂获取对应的指标列表
const getReviewListDialo = async (querydata: any) => {
  console.log(querydata, 'querydata')
  try {
    // 根据 bizType 判断是否需要获取标准配置
    if (querydata.bizType === 'quality') {
      const standardRes = await getIndicatorStandardConfig({
        factoryId: querydata.factoryId ?? null,
      })

      if (!querydata.indicatorDataDOList) {
        return []
      }

      // 将标准配置转换为 Map 以便快速查找
      const standardMap = new Map()
      if (standardRes?.length) {
        standardRes.forEach((item: any) => {
          standardMap.set(item.indicatorName, item.configJson)
        })
      }
      console.log(standardMap, 'standardMap')
      // 合并列表数据和标准值
      const mergedList = querydata.indicatorDataDOList.map((item: any) => {
        // 查找包含当前指标名称的标准配置
        let config = ''
        for (const [key, value] of standardMap.entries()) {
          if (item.indicatorName.includes(key)) {
            config = value
            break
          }
        }
        let standardValue = ''
        let deviation = 0
        let abnormal = false
        if (config && typeof config === 'object') {
          const keys = Object.keys(config)
          const indicatorValue = parseFloat(item.indicatorValue) || 0
          if (keys.includes('lowerLimit') && keys.includes('upperLimit')) {
            // 区间标准
            standardValue = `${config.lowerLimit}-${config.upperLimit}`
            const lowerLimit = parseFloat(config.lowerLimit) || 0
            const upperLimit = parseFloat(config.upperLimit) || 0
            if (indicatorValue < lowerLimit) {
              deviation = indicatorValue - lowerLimit
              abnormal = true
            } else if (indicatorValue > upperLimit) {
              deviation = indicatorValue - upperLimit
              abnormal = true
            } else {
              deviation = 0
              abnormal = false
            }
          } else if (keys.includes('standard')) {
            // 单值标准
            standardValue = config.standard || ''
            const standardValueNum = parseFloat(config.standard) || 0
            if (indicatorValue < standardValueNum) {
              deviation = indicatorValue - standardValueNum
              abnormal = true
            } else if (indicatorValue > standardValueNum) {
              deviation = indicatorValue - standardValueNum
              abnormal = true
            } else {
              deviation = 0
              abnormal = false
            }
          }
        }

        return {
          ...item,
          standardValue,
          deviation,
          abnormal
        }
      })

      return mergedList
    } else {
      // 消耗类数据直接返回原始列表
      return querydata.indicatorDataDOList || []
    }
  } catch (error) {
    console.error('获取审核列表失败:', error)
    return []
  }
}

// 在 script setup 部分添加
const expandedRows = ref<string[]>([])

// 修改 handleExpand 函数
const handleExpand = async (row: any, expanded: boolean) => {
  const rowKey = getRowKey(row)
  if (expanded) {
    // 如果当前行已经展开，先关闭它
    if (expandedRows.value.includes(rowKey)) {
      expandedRows.value = []
      row.children = []
      return
    }

    expandedRows.value = [rowKey]
    row.loading = true
    try {
      const querydata = {
        bizType: row.bizType,
        factoryId: row.factoryId,
        reportDate: row.reportDate,
        reporterId: row.reporterId,
        isArchived: row.isArchived,
        indicatorDataDOList: row.indicatorDataDOList || []
      }
      row.children = await getReviewListDialo(querydata)
    } catch (error) {
      console.error('获取指标详情失败:', error)
      ElMessage.error('获取指标详情失败')
    } finally {
      row.loading = false
    }
  } else {
    expandedRows.value = expandedRows.value.filter(key => key !== rowKey)
    row.children = []
  }
}

// 审核操作
const handleReview = async (row: any) => {
  if (reviewLoading.value) return

  reviewLoading.value = true
  try {
    currentRow.value = JSON.parse(JSON.stringify(row))
    currentRow.value.reviewerName = userName.value

    const querydata = {
      bizType: row.bizType,
      factoryId: row.factoryId,
      reportDate: row.reportDate,
      reporterId: row.reporterId,
      isArchived: row.isArchived,
      indicatorDataDOList: row.indicatorDataDOList || []
    }

    indicatorList.value = await getReviewListDialo(querydata)

    // 获取流程记录
    try {
      const flowRes = await getReviewFlowPage({
        bizType: row.bizType,
        reportDate: row.reportDate,
        factoryId: row.factoryId,
        reporterId: row.reporterId
      })
      console.log(flowRes, 'flowRes');

      reviewHistory.value = flowRes || []
    } catch (e) {
      reviewHistory.value = []
    }

    dialogVisible.value = true
    reviewComment.value = ''
  } catch (error) {
    console.error('打开审核弹窗失败:', error)
    ElMessage.error('打开审核弹窗失败')
  } finally {
    reviewLoading.value = false
  }
}

// 通过审核
const handleApprove = async () => {
  if (approveLoading.value || rejectLoading.value) return

  approveLoading.value = true
  try {
    await batchApproveIndicator({
      reporterId: currentRow.value.reporterId,
      bizType: currentRow.value.bizType,
      comment: reviewComment.value,
      factoryId: currentRow.value.factoryId,
      reportDate: currentRow.value.reportDate
    })
    ElMessage.success('审核通过成功')
    dialogVisible.value = false

    // 重置页码到第一页
    queryParams.pageNo = 1

    // 刷新所有数据
    await Promise.all([
      fetchReviewList(),
      fetchStatistics()
    ])
  } catch (error) {
    console.error('审核通过失败:', error)
    ElMessage.error('审核通过失败')
  } finally {
    approveLoading.value = false
  }
}

// 退回审核
const handleReject = async () => {
  if (approveLoading.value || rejectLoading.value) return

  if (!reviewComment.value) {
    ElMessage.warning('退回时必须填写审核意见')
    return
  }

  rejectLoading.value = true
  try {
    await batchRejectIndicator({
      reporterId: currentRow.value.reporterId,
      bizType: currentRow.value.bizType,
      comment: reviewComment.value,
      factoryId: currentRow.value.factoryId,
      reportDate: currentRow.value.reportDate
    })
    ElMessage.success('退回成功')
    dialogVisible.value = false

    // 重置页码到第一页
    queryParams.pageNo = 1

    // 刷新所有数据
    await Promise.all([
      fetchReviewList(),
      fetchStatistics()
    ])
  } catch (error) {
    console.error('退回失败:', error)
    ElMessage.error('退回失败')
  } finally {
    rejectLoading.value = false
  }
}

// 查询水厂列表
const queryFactoryList = async () => {
  try {
    const res = await FactoryApi.queryFactoryTreeByCurrentReviewer()

    if (res && res.data && res.data.length > 0) {
      factoryList.value = res.data
    }
  } catch (error) {
    console.error('获取水厂列表失败:', error)
    ElMessage.error('获取水厂列表失败')
  }
}

// 生成用于分组展示的工厂树
const groupedFactoryList = computed(() => {
  function filterTree(tree: any[]): any[] {
    return tree
      .map(node => {
        let childrenLevel3 = []
        if (node.children && node.children.length) {
          childrenLevel3 = node.children.filter(child => child.level === 3)
        }
        return {
          ...node,
          childrenLevel3
        }
      })
      .filter(node => (node.childrenLevel3 && node.childrenLevel3.length) || node.level === 3)
  }
  return filterTree(factoryList.value)
})

// 监听筛选条件变化
watch([
  selectedFactory,
  selectedDate,
  toRef(queryParams, 'status'),
  toRef(queryParams, 'bizType')
], () => {
  queryParams.pageNo = 1
  expandedRows.value = []
  fetchReviewList()
  if (isAdmin.value) {
    if (statisticsTimer) clearTimeout(statisticsTimer)
    statisticsTimer = setTimeout(() => {
      fetchStatistics()
    }, 100)
  }
})

// 初始化
onMounted(async () => {
  try {
    // 获取水厂列表
    await queryFactoryList()
    console.log(factoryList.value, 'factoryList.value', isAdmin.value);

    // 设置loading状态并调用接口
    loading.value = true
    // 直接调用接口，不依赖selectedFactory
    await fetchReviewList()
    if (isAdmin.value) {
      await fetchStatistics()
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  } finally {
    loading.value = false
  }
})

// 在文件顶部的 import 语句后添加类型定义
interface ReviewFlowParams {
  reportDate?: string
  factoryId: number
  reporterId: number
}

// 修改关闭弹窗的处理
const handleDialogClose = () => {
  dialogVisible.value = false
  reviewComment.value = ''
  approveLoading.value = false
  rejectLoading.value = false
}

// 在script setup部分添加
const getRowKey = (row: any) => {
  return row.id;
}

// 添加清除水厂的处理函数
const handleFactoryClear = () => {
  selectedFactory.value = undefined
  // 清除后刷新列表
  fetchReviewList()
  if (isAdmin.value) {
    fetchStatistics()
  }
}
</script>

<style scoped>
.text-red {
  color: #f56c6c;
}

.text-orange {
  color: #e6a23c;
}

.text-green {
  color: #67c23a;
}

:deep(.el-table__expand-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

:deep(.el-table__expand-icon .el-icon) {
  margin: 0;
  transform: rotate(0deg);
  transition: transform 0.3s ease-in-out;
}

:deep(.el-table__expand-icon--expanded .el-icon) {
  transform: rotate(90deg);
}

:deep(.el-table__expanded-cell) {
  padding: 20px !important;
}

.w-200px {
  width: 200px;
}

.w-180px {
  width: 180px !important;
}

.w-240px {
  width: 240px !important;
}

:deep(.el-select-dropdown__item) {
  height: 34px;
  padding: 0 20px;
  line-height: 34px;
}

:deep(.el-select-dropdown__item.parent-option) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item.child-option) {
  padding-left: 40px;
}

.child-factory-option {
  padding-left: 40px !important;
}

:deep(.el-select-group__title) {
  font-size: 15px;
  font-weight: bold;
}

.highlight {
  color: #000;
  background-color: #ffeb3b;
}

.mb-20px {
  margin-bottom: 20px;
}

/* 优化审核统计卡片样式 */
.el-card.mb-20px {
  background: #f8fafc;
  border: none;
  border-radius: 14px;
  box-shadow: 0 4px 24px 0 rgb(0 0 0 / 6%);
}

.el-card.mb-20px .el-card__header {
  padding: 18px 24px;
  background: #f0f4fa;
  border-radius: 14px 14px 0 0;
}

.el-card.mb-20px .el-card__body {
  padding: 28px 32px 24px;
}

.grid.grid-cols-4.gap-20px {
  gap: 32px !important;
}

.grid.grid-cols-2.gap-20px {
  gap: 32px !important;
}

.grid.grid-cols-3.gap-10px {
  gap: 20px !important;
}

.bg-gray-100 {
  background: #f4f7fb !important;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgb(0 0 0 / 3%);
}

.text-center {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.text-red,
.text-green,
.text-orange {
  display: flex;
  margin-bottom: 2px;
  font-size: 28px !important;
  font-weight: bold;
  letter-spacing: 1px;
  justify-content: center;
  align-items: center;
}

.font-bold {
  font-weight: 700 !important;
}

.p-15px {
  padding: 20px !important;
}

.mb-10px {
  margin-bottom: 14px !important;
}

.mb-5px {
  margin-bottom: 8px !important;
}

.flex.items-center.gap-4 {
  gap: 18px !important;
}

/* 优化滚动条样式 */
:deep(.el-scrollbar__bar.is-horizontal),
:deep(.el-scrollbar__bar.is-vertical) {
  background: #e0e7ef;
  border-radius: 4px;
}

.statistic-card {
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
}

.statistic-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.statistic-total {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.text-blue {
  color: #409eff;
}

.text-lg {
  font-size: 16px;
}

.text-2xl {
  font-size: 24px;
}

.text-sm {
  font-size: 14px;
}

.gap-2 {
  gap: 8px;
}
</style>
