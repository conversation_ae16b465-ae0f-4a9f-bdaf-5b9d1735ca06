<template>
  <div class="grid-item">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { GridItemProps } from '../type'

const props = defineProps<GridItemProps>()
// 水平方向上占几份
const horizontal = ref(props.horizontal)

// 垂直方向上占几份
const vertical = ref(props.vertical)
</script>

<style scoped lang="scss">
.grid-item {
  grid-column: span v-bind(horizontal);
  grid-row: span v-bind(vertical);
  overflow: hidden;
  background-color: #007aeb38;
  box-shadow: 0px 1px 3px #0068b2;
}
</style>
