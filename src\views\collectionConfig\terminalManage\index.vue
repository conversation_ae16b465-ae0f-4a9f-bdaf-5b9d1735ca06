<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full" :body-style="{ height: '100%', display: 'flex', flexDirection: 'column' }">

      <div class="h-full w-full flex flex-col">
        <!-- 统计卡片 -->
        <div class="mb-6 flex-shrink-0">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stats-card stats-card-blue">
                <div class="stats-card-content">
                  <div class="stats-card-info">
                    <div class="stats-card-title">厂站个数</div>
                    <div class="stats-card-value">{{ animatedFactoryCount }}</div>
                  </div>
                  <div class="stats-card-icon">
                    <Icon icon="ep:office-building" :size="40" />
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stats-card stats-card-green">
                <div class="stats-card-content">
                  <div class="stats-card-info">
                    <div class="stats-card-title">设备个数</div>
                    <div class="stats-card-value">{{ animatedDeviceCount }}</div>
                  </div>
                  <div class="stats-card-icon">
                    <Icon icon="ep:monitor" :size="40" />
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stats-card stats-card-orange">
                <div class="stats-card-content">
                  <div class="stats-card-info">
                    <div class="stats-card-title">点位个数</div>
                    <div class="stats-card-value">{{ animatedPointCount }}</div>
                  </div>
                  <div class="stats-card-icon">
                    <Icon icon="ep:position" :size="40" />
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 搜索表单 -->
        <div class="mb-4 flex-shrink-0">
          <el-form :inline="true" class="search-form">
            <el-form-item label="厂站">
              <el-select v-model="searchForm.factoryId" placeholder="请选择厂站" clearable style="width: 200px;">
                <el-option
                  v-for="item in groupList"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="设备编码">
              <el-input v-model="searchForm.deviceCode" placeholder="请输入设备编码" clearable style="width: 200px;" />
            </el-form-item>
            <el-form-item label="设备名称">
              <el-input v-model="searchForm.deviceName" placeholder="请输入设备名称" clearable style="width: 200px;" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 设备列表容器 -->
        <div class="flex-1 flex flex-col min-h-0">
          <!-- 表格容器 -->
          <div class="flex-1 overflow-hidden">
            <el-table
              :data="deviceList"
              border
              style="width: 100%;"
              :height="tableHeight"
              v-loading="loading"
            >
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="deviceCode" label="设备编码" min-width="50" />
              <el-table-column prop="deviceName" label="设备名称" min-width="120" />
              <el-table-column prop="factoryName" label="所属厂站" width="150" align="center" />
              <el-table-column prop="driverType" label="驱动类型" width="150" align="center" />
              <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip />
              <el-table-column label="操作" width="150" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" link @click="handleViewPoints(row)">查看点位</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页 -->
          <div class="flex-shrink-0 flex items-center justify-end mt-4 pt-4 border-t border-gray-200">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 点位列表对话框 -->
    <el-dialog
      v-model="pointDialogVisible"
      :title="`${selectedDevice?.deviceName} - 点位列表`"
      width="1000px"
      class="point-dialog"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <!-- 搜索表单 -->
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="点位编码">
              <el-input v-model="pointSearchForm.pointCode" placeholder="请输入点位编码" clearable style="width: 200px;" />
            </el-form-item>
            <el-form-item label="点位名称">
              <el-input v-model="pointSearchForm.pointName" placeholder="请输入点位名称" clearable style="width: 200px;" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handlePointSearch">查询</el-button>
              <el-button @click="resetPointSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格容器 -->
        <div class="table-container">
          <el-table
            :data="pointList"
            border
            style="width: 100%;"
            :height="400"
            v-loading="pointLoading"
          >
<!--            <el-table-column type="index" label="序号" width="60" align="center" />-->
            <el-table-column prop="pointCode" label="点位编码" min-width="50" />
            <el-table-column prop="pointName" label="点位名称" min-width="150" />
            <el-table-column prop="pointUnit" label="单位" width="80" align="center" />
<!--            <el-table-column prop="enableFlag" label="启用状态" width="100" align="center">-->
<!--              <template #default="{ row }">-->
<!--                <el-tag :type="row.enableFlag === '1' ? 'success' : 'info'">-->
<!--                  {{ row.enableFlag === '1' ? '启用' : '禁用' }}-->
<!--                </el-tag>-->
<!--              </template>-->
<!--            </el-table-column>-->
            <el-table-column prop="latestValue" label="最新值" width="120" align="center" />
            <el-table-column prop="latestTime" label="最新采集时间" width="200" align="center" />
          </el-table>
        </div>

        <!-- 点位分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pointCurrentPage"
            v-model:page-size="pointPageSize"
            :total="pointTotal"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next"
            @size-change="handlePointSizeChange"
            @current-change="handlePointCurrentChange"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { IotDeviceApi, type DeviceVO, type PointVO, type GroupVO, type StatisticsVO } from '@/api/collectionConfig/iotDevice'

// 统计数据
const statistics = ref<StatisticsVO>({
  factoryCount: 0,
  deviceCount: 0,
  pointCount: 0
})

// 厂站列表
const groupList = ref<GroupVO[]>([])

// 搜索表单
const searchForm = reactive({
  factoryId: undefined as number | undefined,
  deviceCode: '',
  deviceName: ''
})

// 设备列表数据
const deviceList = ref<DeviceVO[]>([])
const loading = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 点位相关数据
const pointList = ref<PointVO[]>([])
const pointLoading = ref(false)
const pointCurrentPage = ref(1)
const pointPageSize = ref(10)
const pointTotal = ref(0)

// 点位搜索表单
const pointSearchForm = reactive({
  pointCode: '',
  pointName: ''
})

// 对话框
const pointDialogVisible = ref(false)
const selectedDevice = ref<DeviceVO | null>(null)

// 表格高度计算
const tableHeight = ref('400px')

// 动画计数
const animatedFactoryCount = ref(0)
const animatedDeviceCount = ref(0)
const animatedPointCount = ref(0)

// 计算表格高度
const calculateTableHeight = () => {
  // 基于视窗高度计算，减去头部、统计卡片、搜索表单、分页等高度
  const windowHeight = window.innerHeight
  const headerHeight = 170 // 页面头部高度
  const statisticsHeight = 140 // 统计卡片高度
  const searchFormHeight = 80 // 搜索表单高度
  const paginationHeight = 60 // 分页高度
  const padding = 50 // 各种边距

  const calculatedHeight = windowHeight - headerHeight - statisticsHeight - searchFormHeight - paginationHeight - padding
  tableHeight.value = Math.max(calculatedHeight, 300) + 'px'
}

// 数字动画函数
const animateNumber = (target: any, endValue: number, duration = 1500) => {
  const startValue = target.value
  const startTime = Date.now()

  const animate = () => {
    const currentTime = Date.now()
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)

    // 使用缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    target.value = Math.floor(startValue + (endValue - startValue) * easeOutQuart)

    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      target.value = endValue
    }
  }

  requestAnimationFrame(animate)
}

// 获取统计数据
const getStatistics = async () => {
  try {
    const data = await IotDeviceApi.getStatistics()
    statistics.value = data

    // 启动数字动画
    animateNumber(animatedFactoryCount, data.factoryCount, 1200)
    animateNumber(animatedDeviceCount, data.deviceCount, 1500)
    animateNumber(animatedPointCount, data.pointCount, 1800)
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')

    // 即使失败也显示默认值的动画
    animateNumber(animatedFactoryCount, 5, 1200)
    animateNumber(animatedDeviceCount, 56, 1500)
    animateNumber(animatedPointCount, 1283, 1800)
  }
}

// 获取厂站列表
const getGroupList = async () => {
  try {
    const data = await IotDeviceApi.getGroupList()
    groupList.value = data
  } catch (error) {
    console.error('获取厂站列表失败:', error)
    ElMessage.error('获取厂站列表失败')
  }
}

// 获取设备列表
const getDeviceList = async () => {
  try {
    loading.value = true
    const params = {
      currentPage: currentPage.value,
      pageSize: pageSize.value,
      factoryId: searchForm.factoryId,
      deviceCode: searchForm.deviceCode || undefined,
      deviceName: searchForm.deviceName || undefined
    }
    const data = await IotDeviceApi.getDevicePage(params)
    deviceList.value = data.records
    total.value = data.total
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败')
  } finally {
    loading.value = false
  }
}

// 获取点位列表
const getPointList = async () => {
  if (!selectedDevice.value) return

  try {
    pointLoading.value = true
    const params = {
      currentPage: pointCurrentPage.value,
      pageSize: pointPageSize.value,
      deviceId: selectedDevice.value.id,
      pointCode: pointSearchForm.pointCode || undefined,
      pointName: pointSearchForm.pointName || undefined
    }
    const data = await IotDeviceApi.getPointPage(params)
    pointList.value = data.records
    pointTotal.value = data.total
  } catch (error) {
    console.error('获取点位列表失败:', error)
    ElMessage.error('获取点位列表失败')
  } finally {
    pointLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  getDeviceList()
}

const resetSearch = () => {
  searchForm.factoryId = undefined
  searchForm.deviceCode = ''
  searchForm.deviceName = ''
  handleSearch()
}

// 点位搜索处理
const handlePointSearch = () => {
  pointCurrentPage.value = 1
  getPointList()
}

const resetPointSearch = () => {
  pointSearchForm.pointCode = ''
  pointSearchForm.pointName = ''
  handlePointSearch()
}



// 查看点位
const handleViewPoints = (row: DeviceVO) => {
  selectedDevice.value = row
  pointDialogVisible.value = true
  pointCurrentPage.value = 1
  pointSearchForm.pointCode = ''
  pointSearchForm.pointName = ''
  getPointList()
}

// 设备分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getDeviceList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getDeviceList()
}

// 点位分页处理
const handlePointSizeChange = (val: number) => {
  pointPageSize.value = val
  pointCurrentPage.value = 1
  getPointList()
}

const handlePointCurrentChange = (val: number) => {
  pointCurrentPage.value = val
  getPointList()
}

// 初始化数据
onMounted(() => {
  calculateTableHeight()
  getStatistics()
  getGroupList()
  getDeviceList()

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight)

  // 添加一些测试数据以便查看效果
  if (deviceList.value.length === 0) {
    deviceList.value = Array.from({ length: 15 }, (_, index) => ({
      id: index + 1,
      deviceCode: `DEV${String(index + 1).padStart(3, '0')}`,
      deviceName: `设备${index + 1}`,
      driverType: 'Modbus TCP',
      factoryId: '1',
      factoryName: '测试水厂',
      remark: `这是设备${index + 1}的备注信息`
    }))
    total.value = 15
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})
</script>

<style scoped lang="scss">
// 统计卡片样式
.stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 20px;
  height: 120px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: 16px 16px 0 0;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  &.stats-card-blue {
    &::before {
      background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    }
    .stats-card-icon {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;
    }
  }

  &.stats-card-green {
    &::before {
      background: linear-gradient(90deg, #10b981, #059669);
    }
    .stats-card-icon {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
    }
  }

  &.stats-card-orange {
    &::before {
      background: linear-gradient(90deg, #f59e0b, #d97706);
    }
    .stats-card-icon {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
    }
  }
}

.stats-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.stats-card-info {
  flex: 1;
}

.stats-card-title {
  font-size: 15px;
  color: #6b7280;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.stats-card-value {
  font-size: 38px;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
  margin-top: 2px;
  background: linear-gradient(135deg, #1f2937, #374151);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-card-icon {
  width: 72px;
  height: 72px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
  flex-shrink: 0;
}

// 响应式设计
@media (max-width: 1200px) {
  .stats-card {
    padding: 18px;
    height: 110px;
  }

  .stats-card-title {
    font-size: 14px;
  }

  .stats-card-value {
    font-size: 34px;
  }

  .stats-card-icon {
    width: 64px;
    height: 64px;
  }
}

@media (max-width: 768px) {
  .stats-card {
    padding: 16px;
    margin-bottom: 16px;
    height: 100px;
  }

  .stats-card-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .stats-card-info {
    order: 2;
    margin-top: 6px;
  }

  .stats-card-icon {
    order: 1;
    width: 60px;
    height: 60px;
  }

  .stats-card-title {
    font-size: 13px;
    margin-bottom: 4px;
  }

  .stats-card-value {
    font-size: 28px;
  }
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

.point-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .dialog-content {
    display: flex;
    flex-direction: column;
    min-height: 500px;
    max-height: 70vh;
  }

  .table-container {
    flex: 1;
    overflow: visible;
    margin-bottom: 16px;

    :deep(.el-table) {
      .el-table__body-wrapper {
        overflow-y: auto !important;
        max-height: 400px;
      }
    }
  }

  .pagination-container {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-light);
  }
}

// 确保表格容器有正确的高度和滚动
:deep(.el-table) {
  .el-table__body-wrapper {
    overflow-y: auto !important;
    overflow-x: hidden;
  }

  .el-table__header-wrapper {
    overflow: hidden;
  }
}

// 优化卡片间距
:deep(.el-card__body) {
  padding: 20px;
}

// 优化分页样式
:deep(.el-pagination) {
  .el-pagination__total,
  .el-pagination__sizes,
  .el-pagination__jump {
    margin-right: 16px;
  }
}
</style>
