<template>
  <el-drawer v-model="visible" :title="dialogTitle" size="400px" direction="rtl" :close-on-click-modal="false"
    :append-to-body="true" @close="handleDialogClose">
    <div class="drawer-content">
      <el-form :model="formData" label-position="top" label-width="auto">
        <!-- 监测点形状 (仅非文本类型) -->
        <el-form-item v-if="!formData.isText" class="custom-form-item">
          <template #label>监测点形状</template>
          <el-select v-model="formData.type" placeholder="请选择形状" class="full-width-select">
            <el-option v-for="type in pointTypes" :key="type.value" :label="type.label" :value="type.value" />
          </el-select>
        </el-form-item>

        <!-- 监测指标 (仅非文本类型) -->
        <el-form-item v-if="!formData.isText" class="custom-form-item">
          <template #label>
            <div class="form-label-with-star">
              <span class="required-star">*</span> 监测指标
            </div>
          </template>
          <el-select v-model="selectedIndicator" placeholder="请选择监测指标" class="full-width-select">
            <el-option v-for="item in indicatorOptions" :key="item.value" :label="`${item.label}(${item.deviceName})`"
              :value="item.value" />
          </el-select>
          <div class="form-item-tip">选择监测指标，关联实时数据</div>
        </el-form-item>

        <!-- 编号 (非文本类型) -->
        <el-form-item v-if="!formData.isText" class="custom-form-item">
          <template #label>
            <div class="form-label-with-star">
              <span class="required-star">*</span> 编号
            </div>
          </template>
          <el-input v-model="formData.name" placeholder="请输入编号" />
          <div class="form-item-tip">监测点的唯一编号，用于系统识别</div>
        </el-form-item>

        <!-- 点位大小 (非文本类型) -->
        <el-form-item v-if="!formData.isText" class="custom-form-item">
          <template #label>点位大小</template>
          <div class="size-slider-container">
            <el-slider v-model="pointSize" :min="1" :max="20" :step="1" class="size-slider" />
            <div class="size-input-group">
              <el-button size="small" @click="decreasePointSize" class="size-btn">-</el-button>
              <el-input-number v-model="pointSize" :min="1" :max="20" :step="1" controls-position="right" size="small"
                class="size-input" :controls="false" />
              <el-button size="small" @click="increasePointSize" class="size-btn">+</el-button>
            </div>
          </div>
          <div class="form-item-tip">调整监测点的显示大小</div>
        </el-form-item>

        <!-- 自定义颜色 (非文本类型) -->
        <el-form-item v-if="!formData.isText" class="custom-form-item">
          <template #label>自定义颜色</template>
          <el-color-picker v-model="pointColor" />
        </el-form-item>

        <!-- 坐标位置 (非文本类型) -->
        <el-form-item v-if="!formData.isText" class="custom-form-item">
          <template #label>坐标位置</template>
          <div class="coordinates-input-group">
            <div class="coordinate-input">
              <span class="coordinate-label">X:</span>
              <el-input-number v-model="formData.x" :step="1" :precision="2" controls-position="right"
                style="width: 120px;" @input="onCoordinateUpdate" />
            </div>
            <div class="coordinate-input">
              <span class="coordinate-label">Y:</span>
              <el-input-number v-model="formData.y" :step="1" :precision="2" controls-position="right"
                style="width: 120px;" @input="onCoordinateUpdate" />
            </div>
          </div>
          <div class="form-item-tip">可直接修改坐标值，修改后将立即预览效果</div>
        </el-form-item>

        <!-- 文本样式设置 (仅文本类型) -->
        <template v-if="formData.isText">
          <!-- 文本内容 -->
          <el-form-item label="文本内容">
            <el-input v-model="formData.name" />
          </el-form-item>

          <!-- 文本监测指标（非必填） -->
          <el-form-item label="关联监测指标" class="custom-form-item">
            <el-select v-model="selectedIndicator" placeholder="可选择关联监测指标" class="full-width-select" clearable>
              <el-option v-for="item in indicatorOptions" :key="item.value" :label="`${item.label}(${item.deviceName})`"
                :value="item.value" />
            </el-select>
            <div class="form-item-tip">文本可选择关联监测指标，用于数据展示</div>
          </el-form-item>

          <!-- 文本编号 -->
          <el-form-item label="编号" class="custom-form-item">
            <el-input v-model="textPointCode" placeholder="请输入编号" />
            <div class="form-item-tip">监测点的唯一编号，用于系统识别</div>
          </el-form-item>

          <!-- 字体大小 -->
          <el-form-item label="字体大小">
            <el-input-number v-model="textFontSize" :min="10" :max="36" :step="1" />
          </el-form-item>

          <!-- 字体粗细 -->
          <el-form-item label="字体粗细">
            <el-select v-model="textFontWeight">
              <el-option label="正常" value="normal" />
              <el-option label="粗体" value="bold" />
            </el-select>
          </el-form-item>

          <!-- 字体颜色 -->
          <el-form-item label="字体颜色">
            <el-color-picker v-model="textColor" />
          </el-form-item>

          <!-- 坐标位置 -->
          <el-form-item v-if="formData.isText" label="坐标" class="custom-form-item">
            <div class="coordinates-input-group">
              <div class="coordinate-input">
                <span class="coordinate-label">X:</span>
                <el-input-number v-model="formData.x" :step="1" :precision="2" controls-position="right"
                  style="width: 120px;" @input="onCoordinateUpdate" />
              </div>
              <div class="coordinate-input">
                <span class="coordinate-label">Y:</span>
                <el-input-number v-model="formData.y" :step="1" :precision="2" controls-position="right"
                  style="width: 120px;" @input="onCoordinateUpdate" />
              </div>
            </div>
            <div class="form-item-tip">可直接修改坐标值，修改后将立即预览效果</div>
          </el-form-item>
        </template>

        <div class="drawer-footer">
          <el-button type="danger" @click="handleDelete">删除</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </el-form>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { getIndicatorList } from '@/api/monitor/screenManage'
import { useAppStore } from '@/store/modules/app'
import { getPointColorByType } from '../utils/PointsDataConverter'

// 定义位点类型
const pointTypes = [
  { label: '圆形', value: 'circle' },
  { label: '方形', value: 'square' },
  { label: '三角形', value: 'triangle' }
]

// 定义监测指标接口
interface IndicatorOption {
  label: string;
  value: string;
  deviceName?: string;
  pointCode?: string;
  data?: any;
}

// 监测指标选项数组
const indicatorOptions = ref<IndicatorOption[]>([
  { label: '加载中...', value: '' }
])

// 标记指标是否已加载
const indicatorLoaded = ref(false)

// 表单数据
const formData = ref({
  id: '',
  x: 0,
  y: 0,
  name: '',
  type: 'circle',
  showType: 'circle',
  data: {},
  isText: false
})

// 表单相关状态
const pointSize = ref(8)
const pointColor = ref('#000000')
const textFontSize = ref(13)
const textFontWeight = ref('normal')
const textColor = ref('#000000')
const textPointCode = ref('')
const selectedIndicator = ref('')

// 控制对话框的显示状态
const visible = ref(false)
const isNewPoint = ref(false)

// 获取当前水厂ID
const appStore = useAppStore()
const currentFactoryId = computed(() => appStore.getCurrentStation?.id)

// 计算对话框标题
const dialogTitle = computed(() => {
  if (isNewPoint.value) {
    return formData.value.isText ? '添加文本' : '添加监测点';
  } else {
    return formData.value.isText ? '编辑文本' : '编辑监测点';
  }
})

// 定义事件
const emit = defineEmits(['save', 'delete', 'close', 'preview'])

// 对外暴露方法
const open = (point, isNew = false) => {
  try {
    console.log("打开弹窗时的监测点数据------------", point);

    // 重置表单
    resetForm();

    // 设置新增标志
    isNewPoint.value = isNew;

    // 确保点位数据正确
    if (!point) {
      console.error("点位数据为空");
      ElMessage.error("点位数据为空，无法编辑");
      return;
    }

    // 复制点位数据到表单
    formData.value = { ...point };

    // 确保 showType 存在，如果不存在则使用 type
    if (!formData.value.showType) {
      console.log("点位缺少showType属性，使用type代替:", point.type);
      (formData.value as any).showType = point.type;
    }

    // 设置相关表单字段
    if (point.isText) {
      // 文本点位
      textFontSize.value = point.data?.pointSize || point.data?.fontSize || 14;
      textFontWeight.value = point.data?.textThickness || point.data?.fontWeight || 'normal';
      textColor.value = point.data?.color || '#000000';
      // 尝试从多个可能的字段中获取文本点位的编号
      textPointCode.value = point.data?.pointCode || point.data?.indicator || '';
      console.log("文本点位编号设置为:", textPointCode.value);
    } else {
      // 监测点
      pointSize.value = point.data?.size || 8;
      pointColor.value = point.data?.color || getPointColorByType(point.showType);
    }

    // 设置监测指标
    selectedIndicator.value = point.data?.indicatorCode || point.data?.indicator || '';
    console.log("设置监测指标编码:", selectedIndicator.value, "点位数据:", point.data);

    // 先显示对话框
    visible.value = true;
    console.log("弹窗已显示，visible =", visible.value);

    // 确保弹窗显示，并在显示后加载数据
    nextTick(() => {
      if (!visible.value) {
        console.warn("弹窗未正确显示，尝试再次打开");
        visible.value = true;
      }

      // 仅当指标数据未加载时才显示加载指示器
      if (!indicatorLoaded.value) {
        // 延迟一小段时间，确保抽屉DOM已渲染
        setTimeout(() => {
          // 找到抽屉内容区域作为加载指示器的目标
          const drawerContent = document.querySelector('.drawer-content');

          console.log("开始加载监测指标数据，目标容器:", drawerContent);

          // 创建一个更友好的加载指示器，不阻止交互
          const loadingOptions = {
            text: '加载指标数据...',
            background: 'rgba(255, 255, 255, 0.7)',
            fullscreen: false,
            lock: false
          };

          // 如果找到了具体容器，就针对容器加载
          if (drawerContent) {
            loadingOptions['target'] = drawerContent;
          }

          const loadingInstance = ElLoading.service(loadingOptions);

          // 加载监测指标列表
          fetchIndicatorList().catch(error => {
            console.error('加载指标数据失败:', error);
            ElMessage.error('加载指标数据失败，请重试');
          }).finally(() => {
            // 无论成功失败都关闭loading
            console.log("指标数据加载完成，关闭loading");
            loadingInstance.close();
          });
        }, 100);
      } else {
        console.log("指标数据已加载，无需显示加载指示器");
      }
    });
  } catch (error) {
    console.error("打开编辑弹窗失败:", error);
    ElMessage.error("打开编辑弹窗失败，请重试");

    // 确保弹窗显示，即使出错
    visible.value = true;
  }
}

// 重置表单字段
const resetForm = () => {
  // 使用类型断言避免类型错误
  formData.value = {
    id: '',
    x: 0,
    y: 0,
    name: '',
    type: 'circle',
    showType: 'circle',
    data: {},
    isText: false
  };
  pointSize.value = 8;
  pointColor.value = '#000000';
  textFontSize.value = 13;
  textFontWeight.value = 'normal';
  textColor.value = '#000000';
  textPointCode.value = '';
  selectedIndicator.value = '';
}

// 获取监测指标列表
const fetchIndicatorList = async () => {
  try {
    // 如果已经加载过并且有数据，直接返回成功
    if (indicatorLoaded.value && indicatorOptions.value.length > 1) {
      console.log("指标数据已加载，无需重复请求");
      return Promise.resolve();
    }

    // 获取当前水厂ID
    const factoryId = currentFactoryId.value;
    if (!factoryId) {
      ElMessage.warning('未选择水厂，无法获取监测指标');
      indicatorOptions.value = [{ label: '请先选择水厂', value: '' }];
      return Promise.reject('未选择水厂');
    }

    // 显示加载状态
    indicatorOptions.value = [{ label: '加载中...', value: '' }];

    // 调用API获取监测指标列表
    const res = await getIndicatorList(factoryId);

    if (res.data && Array.isArray(res.data)) {
      // 转换为下拉选项格式
      const options = res.data.map((item) => ({
        label: item.pointName || item.indicatorName || item.label,
        value: item.indicatorId || item.indicatorCode || item.value,
        deviceName: item.deviceName || '',
        pointCode: item.pointCode || '',
        data: item
      }));

      if (options.length > 0) {
        indicatorOptions.value = options;
        indicatorLoaded.value = true; // 标记为已加载
      } else {
        indicatorOptions.value = [{ label: '暂无监测指标', value: '' }];
      }
    } else {
      indicatorOptions.value = [{ label: '获取监测指标失败', value: '' }];
      return Promise.reject('接口返回数据格式错误');
    }

    return Promise.resolve();
  } catch (error) {
    console.error('获取监测指标失败:', error);
    ElMessage.error('获取监测指标列表失败');
    indicatorOptions.value = [{ label: '获取失败', value: '' }];
    return Promise.reject(error);
  }
}

// 在组件挂载时预加载指标数据
onMounted(() => {
  if (currentFactoryId.value) {
    console.log("组件挂载时预加载指标数据");
    fetchIndicatorList().catch(err => {
      console.warn("预加载指标数据失败，但不影响正常使用:", err);
    });
  }
});

// 监听水厂变化，清除已加载的指标数据
watch(() => currentFactoryId.value, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    console.log("水厂已变更，重置指标加载状态");
    indicatorLoaded.value = false;

    // 当水厂变化时，预加载新水厂的指标数据
    if (newValue) {
      console.log("水厂变化后预加载新指标数据");
      fetchIndicatorList().catch(err => {
        console.warn("预加载新水厂指标数据失败，但不影响正常使用:", err);
      });
    }
  }
});

// 监听type变化，同步更新showType
watch(() => formData.value.type, (newValue) => {
  if (newValue) {
    // 使用类型断言避免类型错误
    (formData.value as any).showType = newValue;

    // 当类型改变时，也触发预览
    previewChanges();
  }
});

// 监听表单名称变化
watch(() => formData.value.name, () => {
  previewChanges();
});

// 预览功能：生成当前表单状态的点位对象
const previewChanges = () => {
  try {
    if (!formData.value) return;

    console.log("准备生成预览数据，当前坐标：", formData.value.x, formData.value.y);
    let data = {} as PointData;

    if (formData.value.isText) {
      // 文本点位数据
      data = {
        pointSize: textFontSize.value,
        textThickness: textFontWeight.value,
        color: textColor.value,
        // 设置固定的背景和边框属性
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        borderWidth: 0,
        padding: 0,
        hasBorder: false,
        hasBackground: false,
        indicator: selectedIndicator.value || '',
        pointCode: textPointCode.value || ''
      };
    } else {
      // 监测点数据
      data = {
        size: pointSize.value,
        color: pointColor.value,
        indicator: selectedIndicator.value,
        type: formData.value.type
      };
    }

    // 确保data对象中包含type信息
    data.type = formData.value.type;

    // 构建预览点位对象
    const previewPoint = {
      ...formData.value,
      data
    } as any;

    // 确保设置showType
    previewPoint.showType = formData.value.isText ? 'text' : formData.value.type;

    console.log("发送预览事件，点位坐标：", previewPoint.x, previewPoint.y);
    // 发送预览事件
    emit('preview', previewPoint);
  } catch (error) {
    console.error('生成预览失败:', error);
  }
};

// 监听表单值变化，触发预览
watch([pointSize, pointColor, textFontSize, textFontWeight, textColor],
  (newValues, oldValues) => {
    console.log("监测到表单值变化:", {
      pointSize: newValues[0],
      pointColor: newValues[1],
      textFontSize: newValues[2],
      textFontWeight: newValues[3],
      textColor: newValues[4]
    });
    previewChanges();
  }, { deep: true });

// 监听坐标变化，触发预览
watch(() => [formData.value.x, formData.value.y], (newValues, oldValues) => {
  console.log("监测到坐标变化:", {
    x: newValues[0],
    y: newValues[1],
    oldX: oldValues ? oldValues[0] : null,
    oldY: oldValues ? oldValues[1] : null
  });
  previewChanges();
});

// 监听selectedIndicator变化，自动填充编号
watch(() => selectedIndicator.value, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 查找选中的指标信息
    const selectedOption = indicatorOptions.value.find(item => item.value === newValue);

    if (selectedOption) {
      if (!formData.value.isText) {
        // 对于非文本监测点，使用指标作为编号
        if (selectedOption.pointCode) {
          formData.value.name = selectedOption.pointCode;
        } else if (selectedOption.data?.pointCode) {
          formData.value.name = selectedOption.data.pointCode;
        } else if (formData.value.name === '' || isNewPoint.value) {
          formData.value.name = selectedOption.label;
        }
      } else {
        // 对于文本元素，使用指标相关信息填充编号字段
        if (selectedOption.pointCode) {
          textPointCode.value = selectedOption.pointCode;
        } else if (selectedOption.data?.pointCode) {
          textPointCode.value = selectedOption.data.pointCode;
        }
      }
    }
  }
});

// 增加点位大小
const increasePointSize = () => {
  if (pointSize.value < 20) {
    pointSize.value++;
  }
}

// 减小点位大小
const decreasePointSize = () => {
  if (pointSize.value > 1) {
    pointSize.value--;
  }
}

// 定义数据对象接口
interface PointData {
  pointSize?: number;
  textThickness?: string;
  fontSize?: number; // 保留向后兼容
  fontWeight?: string; // 保留向后兼容
  color?: string;
  size?: number;
  indicator?: string;
  indicatorCode?: string;
  pointCode?: string;
  type?: string;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  padding?: number;
  hasBorder?: boolean;
  hasBackground?: boolean;
  indicatorInfo?: any;
  attributes?: Record<string, string>;
}

// 保存点位
const handleSave = () => {
  try {
    // 校验必填项
    if (!formData.value.isText) {
      // 图形监测点，监测指标必填
      if (!selectedIndicator.value) {
        ElMessage.error('请选择监测指标');
        return;
      }
    }

    let data = {} as PointData;

    if (formData.value.isText) {
      // 文本点位数据
      data = {
        pointSize: textFontSize.value,
        textThickness: textFontWeight.value,
        color: textColor.value,
        // 设置固定的背景和边框属性
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        borderWidth: 0,
        padding: 0,
        hasBorder: false,
        hasBackground: false,
        indicator: selectedIndicator.value || '',
        pointCode: textPointCode.value || '',
        // 如果选择了监测指标，保存指标信息
        indicatorInfo: selectedIndicator.value ?
          indicatorOptions.value.find(item => item.value === selectedIndicator.value) : null
      };

      // 添加文本的扩展属性
      data.attributes = {
        'data-indicator-code': selectedIndicator.value || '',
        'data-indicator': selectedIndicator.value || '',
        'data-is-text': 'true',
        'data-name': formData.value.name,
        'data-point-code': textPointCode.value || '',
        'data-show-type': 'text',
        // 添加默认背景和边框属性
        'data-has-background': 'false',
        'data-has-border': 'false',
        'data-background-color': 'transparent',
        'data-border-color': 'transparent',
        'data-border-width': '0',
        'data-padding': '0'
      };
    } else {
      // 监测点数据
      data = {
        size: pointSize.value,
        color: pointColor.value,
        indicator: selectedIndicator.value,
        type: formData.value.type,
        indicatorInfo: selectedIndicator.value ?
          indicatorOptions.value.find(item => item.value === selectedIndicator.value) : null
      };

      // 添加扩展属性
      data.attributes = {
        'data-indicator-code': selectedIndicator.value,
        'data-indicator': selectedIndicator.value,
        'data-show-type': formData.value.type,
        'data-name': formData.value.name,
        'data-point-code': formData.value.name
      };
    }

    // 确保data对象中包含type信息
    data.type = formData.value.type;

    // 构建更新后的点位对象
    const updatedPoint = {
      ...formData.value,
      data
    } as any;

    // 确保设置 showType
    updatedPoint.showType = formData.value.isText ? 'text' : formData.value.type;
    console.log("保存时点位数据u-----------0", updatedPoint, isNewPoint);

    // 提交保存
    emit('save', updatedPoint, isNewPoint.value);

    // 关闭对话框
    visible.value = false;
  } catch (error: any) {
    console.error('保存点位失败:', error);
    ElMessage.error('保存失败：' + (error.message || '未知错误'));
  }
}

// 删除点位
const handleDelete = () => {
  emit('delete', formData.value);
  visible.value = false;
}

// 处理对话框关闭
const handleDialogClose = () => {
  emit('close');
}

// 预加载指标数据方法 - 供父组件调用
const preloadIndicatorData = async () => {
  console.log("预加载指标数据");
  return fetchIndicatorList();
}

// 暴露方法给父组件
defineExpose({
  open,
  preloadIndicatorData
})

// 坐标输入变化时立即更新预览
const onCoordinateUpdate = () => {
  console.log("坐标输入变化，立即触发预览更新 X:", formData.value.x, "Y:", formData.value.y);
  previewChanges();
}
</script>

<style scoped>
.drawer-content {
  padding: 0 16px;
  position: relative;
  /* 确保可以作为loading的定位父元素 */
  min-height: 300px;
  /* 确保有足够的高度显示loading */
}

.custom-form-item {
  margin-bottom: 20px;
}

.full-width-select {
  width: 100%;
}

.form-item-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.form-label-with-star {
  display: flex;
  align-items: center;
}

.required-star {
  color: #F56C6C;
  margin-right: 4px;
}

.size-slider-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.size-slider {
  flex: 1;
}

.size-input-group {
  display: flex;
  align-items: center;
}

.size-input {
  width: 70px;
  margin: 0 8px;
}

.size-btn {
  padding: 6px 12px;
}

.coordinates-input-group {
  display: flex;
  gap: 16px;
}

.coordinate-input {
  display: flex;
  align-items: center;
}

.coordinate-label {
  margin-right: 8px;
  font-weight: bold;
}

.drawer-footer {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 16px 0;
}

/* 自定义loading样式，确保不会阻挡抽屉操作 */
:deep(.el-loading-mask) {
  z-index: 2000;
  /* 确保不会覆盖整个抽屉 */
  background-color: rgba(255, 255, 255, 0.6);
  /* 半透明背景 */
}

:deep(.el-loading-spinner) {
  top: 20%;
  /* 将loading图标置于顶部 */
}

:deep(.el-loading-text) {
  color: #409EFF;
  font-weight: bold;
}
</style>

<style>
/* 自定义loading样式 - 使用全局样式以便修改element-plus组件 */
.custom-loading .el-loading-text {
  font-size: 16px;
  color: #ffffff;
  margin-top: 10px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

.custom-loading .el-loading-spinner .path {
  stroke: #409EFF;
  stroke-width: 3;
}
</style>