<template>
  <div class="w-full h-[calc(100vh-150px)] flex flex-col p-4 bg-gray-50">
    <el-card class="h-full" shadow="never">
      <div class="w-full h-full flex gap-4">
        <!-- 左侧工艺-设备树形结构 -->
        <div class="w-[280px] flex flex-col h-full">
          <el-card class="h-full flex flex-col" shadow="never">
            <div class="font-bold mb-4 text-lg">工艺设备</div>
            <el-tree ref="treeRef" v-if="treeData.length > 0" :data="treeData" :props="defaultProps" @node-click="handleNodeClick"
              :highlight-current="true" :expand-on-click-node="false" node-key="id" :current-node-key="activeNode"
              class="custom-tree flex-1" show-line :default-expanded-keys="defaultExpandedKeys">
              <template #default="{ node, data }">
                <div class="flex items-center">
                  <el-icon v-if="data.isProcess">
                    <folder-opened />
                  </el-icon>
                  <el-icon v-else>
                    <document />
                  </el-icon>
                  <span class="ml-2">{{ node.label }}</span>
                </div>
              </template>
            </el-tree>
            <el-empty v-else description="暂无数据" class="flex-grow flex items-center justify-center" />
          </el-card>
        </div>

        <!-- 右侧指标时序数据 -->
        <div class="flex-1 flex flex-col min-w-0 h-full">
          <el-card class="h-full flex flex-col" shadow="never">
            <div class="flex flex-col h-full">
              <!-- 筛选和功能按钮工具栏 -->
              <div class="flex justify-between items-center py-2 mb-4">
                <div class="flex items-center space-x-4">
                  <div class="flex items-center">
                    <span class="mr-2">采集日期:</span>
                    <div>
                      <el-date-picker v-model="collectionDate" type="date" placeholder="选择日期" :editable="false"
                        format="YYYY-MM-DD" value-format="YYYY-MM-DD" class="w-40" :disabled-date="disabledDate" />
                    </div>
                  </div>
                  <div class="flex items-center">
                    <span class="mr-2">数据频率:</span>
                    <el-radio-group v-model="dataFrequencyType">
                      <el-radio-button label="5min">5分钟</el-radio-button>
                      <el-radio-button label="1hour">1小时</el-radio-button>
                      <el-radio-button label="2hour">2小时</el-radio-button>
                    </el-radio-group>
                    <div style="margin-left: 20px;">
                      <el-button type="primary" @click="handleQuery">查询</el-button>
                      <el-button type="primary" @click="handleAnalysis">图形分析</el-button>
                      <el-button type="primary" @click="handleExport">数据导出</el-button>
                      <el-button type="primary" @click="openConfigPanel" v-hasPermi="['onlineMonitor:config']">
                        <el-icon><setting /></el-icon>
                        配置面板
                      </el-button>
                    </div>
                  </div>
                </div>

              </div>

              <!-- 指标时序数据表格 -->
              <div class="flex flex-col flex-grow relative">
                <!-- 加载状态 -->
                <div v-if="tableLoadingState.loading" class="absolute inset-0 bg-white bg-opacity-70 z-10 flex items-center justify-center">
                  <el-icon class="is-loading" :size="32"><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32zm448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32zm-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32zM195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0zm-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z"/></svg></el-icon>
                  <span class="ml-2">加载数据中...</span>
                </div>
                
                <!-- 错误状态 -->
                <div v-if="tableLoadingState.error && !tableLoadingState.loading" class="absolute inset-0 bg-white bg-opacity-70 z-10 flex flex-col items-center justify-center">
                  <el-icon class="text-danger mb-2"><warning /></el-icon>
                  <span class="text-danger">{{ tableLoadingState.errorMessage || '加载数据失败' }}</span>
                  <el-button type="primary" size="small" class="mt-2" @click="handleQuery">重试</el-button>
                </div>
                
                <div v-if="indicatorList.length > 0" class="standard-table" v-loading="tableLoding">
                  <el-table :data="indicatorList" border stripe height="100%" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" fixed="left" align="center" />
                    <el-table-column prop="indicatorName" label="指标名称" width="180" fixed="left" align="center" />
                    <el-table-column label="数据单位" width="100" fixed="left" align="center">
                      <template #default="scope">
                        {{ scope.row.unit || '/' }}
                      </template>
                    </el-table-column>
                    <el-table-column v-for="time in timeColumns" :key="time" :label="time" align="center" width="80"
                      min-width="80">
                      <template #default="scope">
                        <template v-if="getCellRenderData(scope.row, time).isTag">
                          <el-tag :type="getCellRenderData(scope.row, time).tagType">
                            {{ getCellRenderData(scope.row, time).content }}
                          </el-tag>
                        </template>
                        <span v-else>
                          {{ getCellRenderData(scope.row, time).content }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column label="数据趋势" width="120" align="center" class-name="trend-chart-cell"
                      fixed="right">
                      <template #default="scope">
                        <div :id="'trend-chart-' + scope.row.indicatorCode" style="width: 100px; height: 40px;"></div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-empty v-else-if="!tableLoadingState.loading" description="暂无指标数据" class="flex-grow flex items-center justify-center" />
                
                <!-- 在表格底部添加最后更新时间 -->
                <div v-if="tableLoadingState.lastUpdated" class="text-xs text-gray-500 mt-2 text-right">
                  最后更新: {{ new Date(tableLoadingState.lastUpdated).toLocaleString() }}
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- 图形分析对话框 -->
    <MonitorDialog ref="monitorDialogRef" :monitor-data="monitorData" />

    <!-- 配置面板对话框 -->
    <ConfigPanel ref="configPanelRef" :treeData="treeData" :currentFactory="currentFactory"
      @update:tree-data="handleUpdateTreeData" @panel-closed="handleConfigPanelClosed" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch, nextTick, onUnmounted, reactive } from 'vue'
import { FolderOpened as folderOpened, Document as document, Setting as setting, Warning as warning } from '@element-plus/icons-vue'
import { ElMessage, ElTree } from 'element-plus'
import { OnlineMonitorAPI, ProcessTreeDTO, DeviceDTO } from '@/api/monitor/onlineMonitor'
// @ts-ignore - 忽略组件默认导出检查
import MonitorDialog from './components/monitorDialog.vue'
// @ts-ignore - 忽略组件默认导出检查
import ConfigPanel from './components/ConfigPanel.vue'
import { useAppStore } from '@/store/modules/app'
import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 使用 WeakMap 存储图表的 resize 处理函数
const chartResizeHandlers = new WeakMap<echarts.EChartsType, () => void>();

// 统一的数据处理工具函数
const dataUtils = {
  // 处理指标值，统一转换为显示格式
  processIndicatorValue(value: string | boolean | number, indicatorType?: string): { 
    displayValue: string; 
    isBoolean: boolean; 
    numericValue?: number;
    tagType?: 'success' | 'danger' | 'warning' | 'info' | 'primary';
  } {
    // 根据指标类型判断是否是布尔型指标
    const isBooleanIndicator = indicatorType === '7';
    
    // 处理布尔值
    if (typeof value === 'boolean') {
      return {
        displayValue: isBooleanIndicator ? (value ? '运行' : '停止') : value ? '1' : '0',
        isBoolean: isBooleanIndicator,
        numericValue: value ? 1 : 0,
        tagType: isBooleanIndicator ? (value ? 'success' : 'danger') : undefined
      };
    }
    
    // 处理字符串类型的布尔值
    if (typeof value === 'string') {
      const lowerCaseTrimmedValue = value.toLowerCase().trim();
      if (lowerCaseTrimmedValue === 'true') {
        return {
          displayValue: isBooleanIndicator ? '运行' : '1',
          isBoolean: isBooleanIndicator,
          numericValue: 1,
          tagType: isBooleanIndicator ? 'success' : undefined
        };
      } else if (lowerCaseTrimmedValue === 'false') {
        return {
          displayValue: isBooleanIndicator ? '停止' : '0',
          isBoolean: isBooleanIndicator,
          numericValue: 0,
          tagType: isBooleanIndicator ? 'danger' : undefined
        };
      } else {
        // 尝试转换为数字
        const numValue = parseFloat(lowerCaseTrimmedValue);
        if (!isNaN(numValue)) {
          return {
            displayValue: numValue.toFixed(2),
            isBoolean: false,
            numericValue: numValue
          };
        }
      }
    }
    
    // 处理数字
    if (typeof value === 'number') {
      return {
        displayValue: value.toFixed(2),
        isBoolean: false,
        numericValue: value
      };
    }
    
    // 默认情况
    return {
      displayValue: value?.toString() || '-',
      isBoolean: false
    };
  }
};

// 接口定义
interface IndicatorDataItem {
  indicatorCode: string
  indicatorName: string
  deviceCode: string
  unit: string
  indicatorType?: string
  dataList: Array<{
    time: string
    value: string | boolean
    indicatorType?: string
  }>
}

interface CellRenderData {
  isTag: boolean;
  tagType?: 'success' | 'danger' | 'warning' | 'info' | 'primary';
  content: string;
}

interface TreeNode {
  id: string
  name: string
  isProcess: boolean
  children?: TreeNode[] // children could be empty or populated by lazy loading
  deviceCode?: string
  deviceName?: string
  hasChildren?: boolean // Added for lazy loading
}

// API响应类型
interface ApiResponse<T> {
  code: number
  data: T
  message: string
}

// 注册必要的 ECharts 组件
echarts.use([LineChart, GridComponent, TooltipComponent, CanvasRenderer])

// 树形结构数据
const treeData = ref<TreeNode[]>([])
const indicatorList = ref<IndicatorDataItem[]>([])
const timeColumns = ref<string[]>([])

// 当前激活节点
const activeNode = ref('')
const currentDevice = ref<TreeNode | null>(null)

// 默认展开的节点
const defaultExpandedKeys = ref<string[]>([])

const tableLoding = ref(false)
// 添加更详细的加载状态
const tableLoadingState = reactive({
  loading: false,
  error: false,
  errorMessage: '',
  lastUpdated: null as Date | null
})

// 类型定义
const appStore = useAppStore()
const currentFactory = computed(() => appStore.getCurrentStation)

// 配置面板引用
const configPanelRef = ref()

// 图形分析对话框
const monitorDialogRef = ref()
const selectedIndicators = ref<IndicatorDataItem[]>([])
const monitorData = ref<Array<{
  pointName: string
  value: string
  unit: string
  time: string
  status: string
}>>([])

// 树引用
const treeRef = ref<InstanceType<typeof ElTree>>()

// 趋势图表引用存储
const trendChartRefs = ref<Map<string, HTMLElement>>(new Map());

// 打开配置面板
const openConfigPanel = () => {
  configPanelRef.value?.open()
}

// 处理树形数据更新
const handleUpdateTreeData = (newTreeData: TreeNode[]) => {
  treeData.value = newTreeData

  // 如果有工艺数据，自动加载第一个工艺的设备
  if (treeData.value.length > 0) {
    const firstProcess = treeData.value[0];
    defaultExpandedKeys.value = [firstProcess.id];

    nextTick(() => {
      // 如果第一个工艺有设备，选择第一个设备
      if (firstProcess.children && firstProcess.children.length > 0) {
        const firstDevice = firstProcess.children[0];

        // 尝试清除现有选中状态，以避免父节点残留高亮
        if (treeRef.value) {
          treeRef.value.setCurrentKey(undefined);
        }

        // 手动触发handleNodeClick
        handleNodeClick(firstDevice);

        if (treeRef.value) {
          treeRef.value.setCurrentKey(firstDevice.id);
        }
      } else {
        // 如果没有设备，选中工艺
        handleNodeClick(firstProcess);
      }
    });
  }
}

// 配置面板关闭时重新获取工艺树数据
const handleConfigPanelClosed = async () => {
  console.log('配置面板关闭，重新加载工艺树');
  
  // 保存当前展开的节点
  // 使用 as any 绕过 TypeScript 类型检查，因为 getExpandedKeys 方法存在但类型定义不完整
  const currentExpandedKeys = (treeRef.value as any)?.getExpandedKeys?.() || defaultExpandedKeys.value || [];
  console.log('当前展开的节点:', currentExpandedKeys);
  
  // 保存当前选中的设备
  const currentDeviceId = currentDevice.value?.id;
  const currentProcessId = activeNode.value ? 
    treeData.value.find(p => p.children?.some(d => d.id === activeNode.value))?.id : null;
  
  // 重新获取工艺列表，传入当前展开的节点
  await getProcessList(currentExpandedKeys);
  
  // 如果有当前选中的设备，尝试重新选中
  if (currentDeviceId) {
    // 在新的树中查找该设备
    for (const process of treeData.value) {
      if (process.children) {
        const device = process.children.find(d => d.id === currentDeviceId);
        if (device) {
          console.log('重新选中设备:', device.name);
          nextTick(() => {
            // 设置当前选中的节点
            if (treeRef.value) {
              treeRef.value.setCurrentKey(device.id);
              activeNode.value = device.id;
            }
          });
          break;
        }
      }
    }
  } else if (currentProcessId) {
    // 如果没有选中设备但有选中工艺，尝试重新选中工艺
    const process = treeData.value.find(p => p.id === currentProcessId);
    if (process) {
      console.log('重新选中工艺:', process.name);
      nextTick(() => {
        if (treeRef.value) {
          treeRef.value.setCurrentKey(process.id);
        }
      });
    }
  }
}



// 检查水厂编码是否有效
const checkFactoryCode = () => {
  if (!currentFactory.value?.code) {
    // ElMessage.warning('请先选择水厂')
    return false
  }
  return true
}
watch(() => appStore.getCurrentStation, async (newStation, oldStation) => {
  if (!newStation || (oldStation && newStation.id === oldStation.id)) return
  
  // 重置数据
  treeData.value = []
  indicatorList.value = []
  timeColumns.value = []
  activeNode.value = ''
  currentDevice.value = null
  
  // 保留当前展开状态，如果是水厂变化，则清空展开状态
  const currentExpandedKeys = oldStation ? [] : (treeRef.value as any)?.getExpandedKeys?.() || [];
  defaultExpandedKeys.value = currentExpandedKeys;

  // 重新获取工艺列表，传入展开状态
  await getProcessList(currentExpandedKeys)
}, { immediate: true })

// 日期禁用函数 - 禁用今天之后的日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

// // 监听水厂变化
// watch(currentFactory, (newFactory) => {
//   if (newFactory?.code) {
//
//   }
// }, { immediate: false }) // 改为 false，避免初始化时重复调用

// Helper function to render cell content
const getCellRenderData = (rowData: IndicatorDataItem, columnTime: string): CellRenderData => {
  const dataItem = rowData.dataList.find(item => item.time.substring(11, 16) === columnTime);
  if (!dataItem) {
    return { isTag: false, content: '-' };
  }

  // 获取 indicatorType 参数
  const indicatorType = dataItem.indicatorType || rowData.indicatorType;

  const processedData = dataUtils.processIndicatorValue(dataItem.value, indicatorType);
  
  if (processedData.isBoolean) {
    return {
      isTag: true,
      tagType: processedData.tagType,
      content: processedData.displayValue
    };
  } else {
    return { 
      isTag: false, 
      content: processedData.displayValue 
    };
  }
};

// 监听 indicatorList 变化，并在 DOM 更新后渲染趋势图
watch(indicatorList, (newVal) => {
  if (newVal.length > 0) {
    // 清空旧的引用
    trendChartRefs.value = new Map();
    
    nextTick(() => {
      // 添加一个小的延迟，确保 DOM 元素完全渲染并具备尺寸
      setTimeout(() => {
        renderTrendCharts();
      }, 200); // 200毫秒延迟，可根据需要调整
    });
  }
}, { deep: false }); // 浅层监听即可，因为我们替换的是整个数组

// 树形配置
const defaultProps = {
  children: 'children',
  label: 'name',
  isLeaf: (data: TreeNode) => !data.hasChildren // Define isLeaf based on hasChildren
}

// 日期和数据频率选择
const collectionDate = ref(new Date().toISOString().split('T')[0])
const dataFrequencyType = ref<string>('2hour')

// 获取指标列表
const getIndicatorList = async (deviceCode: string) => {
  if (!checkFactoryCode()) return

  tableLoding.value = true
  tableLoadingState.loading = true;
  tableLoadingState.error = false;
  tableLoadingState.errorMessage = '';
  
  try {
    let stepValue: string
    let timeTypeValue: string

    switch (dataFrequencyType.value) {
      case '5min':
        stepValue = '5'
        timeTypeValue = 'MINUTE'
        break
      case '1hour':
        stepValue = '1'
        timeTypeValue = 'HOUR'
        break
      case '2hour':
        stepValue = '2'
        timeTypeValue = 'HOUR'
        break
      default:
        stepValue = '5'
        timeTypeValue = 'MINUTE'
    }

    const params = {
      date: collectionDate.value,
      step: stepValue,
      dateType: timeTypeValue,
      deviceCode: deviceCode,
      factoryCode: currentFactory.value.code
    }

    const res = await OnlineMonitorAPI.queryIndicatorDataListByDevice(params) as unknown as ApiResponse<IndicatorDataItem[]>
    
    if (res.code === 0 && res.data) {
      indicatorList.value = res.data
      if (res.data.length > 0) {
        // 提取时间列
        timeColumns.value = res.data[0].dataList.map(item => item.time.substring(11, 16))
        tableLoadingState.lastUpdated = new Date();
      } else {
        timeColumns.value = []
        ElMessage.warning('暂无指标数据')
      }
    } else {
      indicatorList.value = []
      timeColumns.value = []
      tableLoadingState.error = true;
      tableLoadingState.errorMessage = res.message || '获取数据失败';
      ElMessage.warning('暂无指标数据')
    }
  } catch (error) {
    console.error('获取指标列表失败:', error)
    tableLoadingState.error = true;
    tableLoadingState.errorMessage = error instanceof Error ? error.message : '未知错误';
    ElMessage.error('获取指标列表失败')
  } finally {
    tableLoding.value = false
    tableLoadingState.loading = false;
  }
}

// 获取工艺画面列表
const getProcessList = async (expandedKeys?: string[]) => {
  if (!checkFactoryCode()) return

  try {
    console.log('获取工艺树数据，水厂编码:', currentFactory.value.code);
    const res = await OnlineMonitorAPI.queryProcessDeviceTree(currentFactory.value.code) as unknown as ApiResponse<ProcessTreeDTO[]>
    if (res.code === 0 && res.data) {
      console.log('获取到工艺树数据:', res.data);
      // 转换数据结构为树形
      treeData.value = res.data.map(process => ({
        id: process.processCode,
        name: process.processName,
        isProcess: true,
        // 直接使用API返回的children数据，转换为TreeNode格式
        children: process.children.map(device => ({
          id: device.deviceCode,
          name: device.deviceName,
          isProcess: false,
          deviceCode: device.deviceCode,
          deviceName: device.deviceName,
          hasChildren: false // Devices do not have children
        })),
        hasChildren: process.children.length > 0 // 根据是否有子设备设置hasChildren
      }))

      // 设置展开节点
      if (expandedKeys && expandedKeys.length > 0) {
        // 如果传入了展开节点列表，使用它
        defaultExpandedKeys.value = expandedKeys;
        console.log('使用传入的展开节点:', defaultExpandedKeys.value);
      } else if (treeData.value.length > 0) {
        // 否则只展开第一个工艺节点
        const firstProcess = treeData.value[0];
        defaultExpandedKeys.value = [firstProcess.id];
        console.log('默认展开的节点:', defaultExpandedKeys.value);

        // 延迟执行，确保DOM已更新
        nextTick(() => {
          // 如果第一个工艺有设备，选择第一个设备
          if (firstProcess.children && firstProcess.children.length > 0) {
            const firstDevice = firstProcess.children[0];

            // 尝试清除现有选中状态，以避免父节点残留高亮
            if (treeRef.value) {
              treeRef.value.setCurrentKey(undefined);
            }

            // 手动触发handleNodeClick
            handleNodeClick(firstDevice);

            // 显式设置 el-tree 的当前选中节点
            if (treeRef.value) {
              treeRef.value.setCurrentKey(firstDevice.id);
            }
          } else {
            // 如果没有设备，选中工艺
            handleNodeClick(firstProcess);
          }
        });
      }
    } else {
      ElMessage.warning('暂无数据')
    }
  } catch (error) {
    console.error('获取工艺画面列表失败:', error)
    ElMessage.error('获取工艺画面列表失败')
  }
}

// 获取设备列表 (现在由 load 调用)
const getDeviceList = async (processCode: string) => {
  try {
    // 查找对应的工艺节点
    const process = treeData.value.find(p => p.id === processCode);
    if (process && process.children) {
      // 直接使用已加载的设备列表
      return process.children;
    } else {
      console.warn(`未找到工艺 ${processCode} 或其设备列表为空`);
      return [];
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败')
    return []
  }
}

// 懒加载节点方法
const load = async (node: any, resolve: (data: TreeNode[]) => void) => {
  if (node.level === 0) { // Root level, initial load
    resolve(treeData.value);
    return;
  }

  if (node.data.isProcess) { // If it's a process node, load its devices
    // 直接使用已加载的设备列表
    const devices = node.data.children || [];
    resolve(devices);
  } else { // If it's a device node, it has no children
    resolve([]);
  }
};

// 处理节点点击
const handleNodeClick = async (data: TreeNode) => {
  if (!data.isProcess) { // Only load indicators if it's a device node
    activeNode.value = data.id; // 更新选中设备的 ID
    currentDevice.value = data;
    await getIndicatorList(data.deviceCode!);
  } else {
    // 如果点击的是工艺节点，清除当前设备相关数据和 activeNode
    currentDevice.value = null;
    indicatorList.value = [];
    timeColumns.value = [];
    activeNode.value = ''; // 始终清除 activeNode，确保没有设备被选中
  }
}

// 处理数据查询
const handleQuery = () => {
  if (currentDevice.value) {
    getIndicatorList(currentDevice.value.deviceCode!)
  } else {
    ElMessage.warning('请先选择设备！')
  }
}

// 处理图形分析
const handleAnalysis = () => {
  const selection = indicatorList.value.filter(item =>
    selectedIndicators.value.some(selected => selected.indicatorCode === item.indicatorCode)
  )

  if (selection.length === 0) {
    ElMessage.warning('请先选择要分析的指标')
    return
  }

  // 转换数据格式
  monitorData.value = selection.flatMap(item => {
    // 获取指标类型
    const indicatorType = item.indicatorType;
    const isBooleanIndicator = indicatorType === '7';
    
    return item.dataList.map(data => {
      // 处理值的显示
      let displayValue: string;
      if (isBooleanIndicator) {
        // 布尔型指标
        const boolValue = typeof data.value === 'boolean' 
          ? data.value 
          : String(data.value).toLowerCase().trim() === 'true';
        displayValue = boolValue ? '运行' : '停止';
      } else {
        // 数值型指标
        if (typeof data.value === 'string') {
          const numValue = parseFloat(data.value);
          displayValue = !isNaN(numValue) ? numValue.toFixed(2) : data.value;
        } else if (typeof data.value === 'boolean') {
          displayValue = data.value ? '1' : '0';
        } else {
          displayValue = String(data.value);
        }
      }
      
      return {
        pointName: item.indicatorName,
        value: displayValue,
        unit: item.unit || '',
        time: data.time,
        status: 'normal'
      };
    });
  });

  // 显示对话框
  monitorDialogRef.value?.setVisible()
}

// 表格选择变化
const handleSelectionChange = (selection: IndicatorDataItem[]) => {
  selectedIndicators.value = selection
}

// 处理数据导出
const handleExport = () => {
  if (indicatorList.value.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  // 准备表头
  const headers = ['指标名称', '数据单位', ...timeColumns.value]

  // 准备数据行
  const rows = indicatorList.value.map(item => {
    const row = [item.indicatorName, item.unit]
    timeColumns.value.forEach(time => {
      const dataItem = item.dataList.find(d => d.time.substring(11, 16) === time)
      if (dataItem) {
        let exportValue: string; // 明确声明 exportValue 为字符串

        // point.value 在此处为 string | boolean
        if (typeof dataItem.value === 'string') {
          const lowerCaseTrimmedValue = dataItem.value.toLowerCase().trim();
          if (lowerCaseTrimmedValue === 'true') {
            exportValue = '运行';
          } else if (lowerCaseTrimmedValue === 'false') {
            exportValue = '停止';
          } else {
            const numValue = parseFloat(lowerCaseTrimmedValue);
            if (!isNaN(numValue)) {
              exportValue = numValue.toFixed(2); // 数字保留2位小数
            } else {
              exportValue = dataItem.value; // 非数字非布尔字符串保持原样
            }
          }
        } else if (typeof dataItem.value === 'boolean') {
          exportValue = dataItem.value ? '运行' : '停止';
        } else {
          // Fallback for other unexpected types, though dataItem.value is typically string or boolean
          exportValue = String(dataItem.value); // 转换为字符串
        }
        row.push(exportValue)
      } else {
        row.push('-') // 如果没有数据，导出为横线
      }
    })
    return row
  })

  // 生成CSV内容
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n')

  // 创建 Blob 对象，添加 BOM
  const blob = new Blob(['\ufeff' + csvContent], {
    type: 'text/csv;charset=utf-8-sig'
  })

  // 创建下载链接
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `监测数据_${collectionDate.value}_${dataFrequencyType.value}.csv`

  // 触发下载
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // 清理 URL 对象
  URL.revokeObjectURL(link.href)

  ElMessage.success('数据导出成功')
}

interface DataPoint {
  value: number;
  isBoolean: boolean;
}

const renderTrendCharts = () => {
  try {
    indicatorList.value.forEach(item => {
      // 使用表格行的索引和指标代码作为唯一标识符
      const chartId = 'trend-chart-' + item.indicatorCode;
      
      // 使用原生DOM API直接查找元素，避免使用document对象
      // 使用更安全的方式获取DOM元素
      const chartElements = window.document.querySelectorAll(`[id="${chartId}"]`);
      if (!chartElements || chartElements.length === 0) {
        console.warn(`找不到图表容器: ${chartId}`);
        return;
      }
      
      const chartDom = chartElements[0] as HTMLElement;
      
      // 检查容器尺寸
      if (chartDom.offsetWidth === 0 || chartDom.offsetHeight === 0) {
        console.warn(`图表容器 ${item.indicatorName} 尺寸为0，跳过渲染`);
        return;
      }
      
      // 存储引用以便后续使用
      trendChartRefs.value.set(item.indicatorCode, chartDom);
      
      // 检查是否已经初始化过图表，如果有则销毁重建
      const existingChart = echarts.getInstanceByDom(chartDom);
      if (existingChart) {
        existingChart.dispose();
      }
      
      const chart = echarts.init(chartDom);

      // 处理数据，区分布尔值和数值型数据
      const values: DataPoint[] = item.dataList.map(data => {
        // 获取 indicatorType 参数
        const indicatorType = data.indicatorType || item.indicatorType;
        
        const processedData = dataUtils.processIndicatorValue(data.value, indicatorType);
        return processedData.isBoolean 
          ? { value: processedData.numericValue || 0, isBoolean: true }
          : { value: processedData.numericValue || 0, isBoolean: false };
      }).filter((v): v is DataPoint => v.value !== undefined);

      const times = item.dataList.map(data => data.time.substring(11, 16));
      
      // 获取第一个数据点的 indicatorType 来确定整个序列的类型
      const seriesIndicatorType = item.dataList.length > 0 
        ? (item.dataList[0].indicatorType || item.indicatorType) 
        : undefined;
      const isBooleanSeries = seriesIndicatorType === '7';

      const option = {
        grid: {
          top: 0,
          right: 0,
          bottom: 0,
          left: 0
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params: any) => {
            const dataIndex = params[0].dataIndex
            const dataPoint = values[dataIndex]
            const time = times[dataIndex]

            // 根据数据类型和 indicatorType 显示不同的内容
            let displayValue: string;
            if (isBooleanSeries && dataPoint.isBoolean) {
              displayValue = dataPoint.value === 1 ? '运行' : '停止';
            } else {
              displayValue = dataPoint.value.toFixed(2);
            }

            return `${time}<br/>${displayValue} ${item.unit}`
          },
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#e5e7eb',
          borderWidth: 1,
          textStyle: {
            color: '#333',
            fontSize: 12
          },
          padding: [4, 8],
          position: (point: any) => {
            return [point[0], point[1] + 15];
          },
          appendToBody: true
        },
        xAxis: {
          type: 'category',
          show: false,
          boundaryGap: false,
          data: times
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [{
          data: values.map(v => v.value),
          type: 'line',
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: '#409EFF'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64,158,255,0.2)' },
              { offset: 1, color: 'rgba(64,158,255,0)' }
            ])
          }
        }]
      }

      chart.setOption(option);
      
      // 添加窗口大小变化时的重绘
      const resizeHandler = () => {
        if (chart && !chart.isDisposed()) {
          chart.resize();
        }
      };
      window.addEventListener('resize', resizeHandler);
      
      // 保存resize处理函数到WeakMap中
      chartResizeHandlers.set(chart, resizeHandler);
    });
  } catch (error) {
    console.error('渲染趋势图表时出错:', error);
  }
};

// 在组件卸载时销毁图表实例
onUnmounted(() => {
  // 使用存储的引用来获取图表实例
  trendChartRefs.value.forEach((chartDom, indicatorCode) => {
    try {
      const chart = echarts.getInstanceByDom(chartDom);
      if (chart) {
        // 移除resize事件监听
        const resizeHandler = chartResizeHandlers.get(chart);
        if (resizeHandler) {
          window.removeEventListener('resize', resizeHandler);
          chartResizeHandlers.delete(chart);
        }
        // 销毁图表实例
        chart.dispose();
      }
    } catch (error) {
      console.error(`清理图表 ${indicatorCode} 时出错:`, error);
    }
  });
  
  // 清空引用
  trendChartRefs.value.clear();
});

// 页面加载时获取数据
onMounted(() => {
  if (checkFactoryCode()) {
    getProcessList()
  }
})
</script>

<style scoped>
.custom-tree {
  height: calc(100% - 40px);
  overflow: auto;
}

:deep(.el-card__body) {
  height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

/* Ensure el-table itself behaves responsively within its scrollable container */
:deep(.el-table) {
  height: 100%;
  /* Ensure el-table takes up 100% height of its parent and handles internal scrolling */
  /* Remove flex, overflow, etc. from here */
}

/* Revert broad overflow: visible !important from general table parts. */
:deep(.el-table__header-wrapper),
:deep(.el-table__body),
:deep(.el-table__row),
:deep(.el-table__cell) {
  /* Removed explicit overflow: visible !important to allow native scrolling */
}

/* Remove direct overflow control from body-wrapper, let el-table manage it */
:deep(.el-table__body-wrapper) {
  /* Removed explicit overflow-x and overflow-y */
}

/* Set up the main scrollable container for the table area */
.flex.flex-col.flex-grow {
  /* This targets the v-loading div */
  min-height: 0;
  flex: 1;
  overflow: hidden;
  /* This should contain the table content without showing its own scrollbars */
}

.standard-table {
  height: 100%;
  /* Make it take full height of its flex parent */
  width: 100%;
  /* Make it take full width to allow el-table to detect content overflow */
  /* Removed overflow property from here */
  /* Removed flex: 1 and min-height: 0 as parent handles flex behavior */
}

/* Ensure the el-card containing the table and the root div also allow overflow */
:deep(.el-card.h-full.flex.flex-col),
.w-full.h-\[calc\(100vh-150px\)\].flex.flex-col.p-4.bg-gray-50 {
  overflow: visible !important;
}

:deep(.el-card) {
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none;
}

/* Ensure trend chart cell content visible and creates stacking context */
:deep(.el-table .trend-chart-cell) {
  overflow: visible !important;
  /* This is specific to our tooltip needs */
  min-height: 80px;
  position: relative;
  z-index: 1;
}

/* Ensure ECharts tooltip is on top */
:deep(.ec-tooltip) {
  z-index: 99999 !important;
}
</style>
