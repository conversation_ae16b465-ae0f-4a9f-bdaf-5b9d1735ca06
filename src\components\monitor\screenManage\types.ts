export interface Metric {
  id: number;
  name: string;
  unit: string;
}

export interface MetricGroup {
  id: number;
  name: string;
  metrics: Metric[];
}

export interface Position {
  x: number;
  y: number;
}

export type PointType = 'flow' | 'level' | 'ph' | 'pump' | 'gate' | string;
export type StatusType = 'running' | 'stopped' | 'alarm';
export type LayoutTemplateType = 'right' | 'left' | 'bottom' | 'top' | 'valueOnly';
export type DragTargetType = 'icon' | 'value' | 'label' | 'number';

export interface MonitorPoint {
  id: string;
  name: string;
  type: PointType;
  value?: number;
  unit?: string;
  status?: StatusType;
  position: Position;
  showDisplay?: boolean;
  displayName?: string;
  displayPosition?: Position;
  labelPosition?: Position;
  numberPosition?: Position;
  color?: string | PointColorConfig;
  displayColor?: string;
  displayBgColor?: string;
  metricId?: number;
  hasCustomValuePosition?: boolean;
  layoutTemplate?: LayoutTemplateType;
  stationId?: number;
  svgImageData?: string;
}

export interface PointColorConfig {
  normal: string;
  warning: string;
  error: string;
}

export interface LayoutOption {
  value: LayoutTemplateType;
  label: string;
  labelOffset: Position;
  numberOffset: Position;
}

export interface ColorConfig {
  running: string;
  stopped: string;
  alarm: string;
  flow: string;
  level: string;
  ph: string;
  other: string;
}

export interface Screen {
  id: string;
  name: string;
  svg: string;
  points: MonitorPoint[];
  stationId?: number;
  svgWidth?: number;
  svgHeight?: number;
  colorConfig?: ColorConfig;
}

export interface Station {
  id: number;
  name: string;
}

export interface DragTarget {
  point: MonitorPoint;
  type: DragTargetType;
}

// Common types used in monitor screen management components

export interface Point2D {
  x: number;
  y: number;
}

export type PointLayoutTemplate = 'right' | 'left' | 'top' | 'bottom' | 'value-only';

export interface MonitorPoint {
  id: string;
  name: string;
  type: PointType;
  value?: number;
  unit?: string;
  status?: StatusType;
  position: Point2D;
  showDisplay?: boolean;
  displayName?: string;
  displayPosition?: Point2D;
  labelPosition?: Point2D;
  numberPosition?: Point2D;
  color?: string;
  displayColor?: string;
  displayBgColor?: string;
  metricId?: number;
  stationId?: number;
  hasCustomValuePosition?: boolean;
  layoutTemplate?: PointLayoutTemplate;
}

export interface ColorConfig {
  running: string;
  stopped: string;
  alarm: string;
  flow: string;
  level: string;
  ph: string;
  other: string;
  [key: string]: string;
}

export interface SvgSize {
  width: number;
  height: number;
}

export interface LayoutTemplateConfig {
  id: PointLayoutTemplate;
  name: string;
  icon: string;
  labelOffset: Point2D;
  numberOffset: Point2D;
  displayOnly?: boolean;
}

// Layout templates predefined configurations
export const layoutTemplates: LayoutTemplateConfig[] = [
  {
    id: 'right',
    name: '右侧布局',
    icon: 'layout-right',
    labelOffset: { x: 50, y: -10 },
    numberOffset: { x: 50, y: 10 }
  },
  {
    id: 'left',
    name: '左侧布局',
    icon: 'layout-left',
    labelOffset: { x: -50, y: -10 },
    numberOffset: { x: -50, y: 10 }
  },
  {
    id: 'top',
    name: '顶部布局',
    icon: 'layout-top',
    labelOffset: { x: 0, y: -40 },
    numberOffset: { x: 0, y: -20 }
  },
  {
    id: 'bottom',
    name: '底部布局',
    icon: 'layout-bottom',
    labelOffset: { x: 0, y: 20 },
    numberOffset: { x: 0, y: 40 }
  },
  {
    id: 'value-only',
    name: '仅显示数值',
    icon: 'layout-value',
    labelOffset: { x: 0, y: -999 }, // Move label out of view
    numberOffset: { x: 0, y: 0 },
    displayOnly: true
  }
];

// Helper function to get a layout template by ID
export function getLayoutTemplate(id: PointLayoutTemplate): LayoutTemplateConfig {
  const template = layoutTemplates.find(t => t.id === id);
  return template || layoutTemplates[0]; // Default to first template if not found
}

// Helper function to apply a layout template to a point
export function applyLayoutTemplate(point: MonitorPoint, templateId: PointLayoutTemplate): MonitorPoint {
  const template = getLayoutTemplate(templateId);
  
  return {
    ...point,
    layoutTemplate: templateId,
    labelPosition: {
      x: template.labelOffset.x,
      y: template.labelOffset.y
    },
    numberPosition: {
      x: template.numberOffset.x,
      y: template.numberOffset.y
    },
    hasCustomValuePosition: false
  };
} 