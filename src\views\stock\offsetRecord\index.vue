<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">冲抵记录</span>
          <div class="flex gap-2">
            <el-button @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>导出
            </el-button>
          </div>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-270px)] flex flex-col">
        <div class="w-full h-[50px] mb-2 gap-2 flex items-center">
          <el-form :model="searchForm" inline>
            <el-form-item label="入库单号：">
              <el-input v-model="searchForm.stockInNo" placeholder="请输入入库单号" />
            </el-form-item>
            <el-form-item label="商品名称：">
              <el-input v-model="searchForm.productName" placeholder="请输入商品名称" />
            </el-form-item>
            <el-form-item label="操作时间：">
              <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border height="100%">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="stockInNo" label="入库单号" align="center" min-width="120" />
                <el-table-column prop="productName" label="商品名称" align="center" min-width="120" />
                <el-table-column prop="originalQuantity" label="原入库数量" align="center" width="100" />
                <el-table-column prop="offsetQuantity" label="冲抵数量" align="center" width="100" />
                <el-table-column prop="operator" label="操作人" align="center" width="120" />
                <el-table-column prop="operateTime" label="操作时间" align="center" width="160" sortable />
                <el-table-column prop="status" label="状态" align="center" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === '1' ? 'success' : 'warning'">
                      {{ row.status === '1' ? '已冲抵' : '已还原' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="150" fixed="right">
                  <template #default="scope">
                    <el-button v-if="scope.row.status === '1'" type="primary" link @click="handleRestore(scope.row)">
                      还原
                    </el-button>
                    <el-button v-if="scope.row.status === '0'" type="primary" link @click="handleOffset(scope.row)">
                      冲抵
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end mt-2">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单数据
const searchForm = reactive({
  stockInNo: '',
  productName: '',
  dateRange: []
})

// 表格数据
const tableData = ref([
  {
    stockInNo: '***********-173025227645',
    productName: '三角带',
    originalQuantity: 100,
    offsetQuantity: 50,
    operator: '中电建中南院',
    operateTime: '2024-10-30 09:37:56',
    status: '1'
  },
  {
    stockInNo: '***********-172734168203',
    productName: '水泵',
    originalQuantity: 200,
    offsetQuantity: 100,
    operator: '中电建中南院',
    operateTime: '2024-09-26 17:08:02',
    status: '0'
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(2)

// 搜索方法
const handleSearch = () => {
  console.log('搜索条件：', searchForm)
  // TODO: 调用后端API获取数据
}

// 重置方法
const handleReset = () => {
  searchForm.stockInNo = ''
  searchForm.productName = ''
  searchForm.dateRange = []
  handleSearch()
}

// 导出方法
const handleExport = () => {
  console.log('导出数据')
  // TODO: 调用导出API
}

// 冲抵操作
const handleOffset = async (row: any) => {
  try {
    await ElMessageBox.confirm('确认要对该记录进行冲抵操作吗？', '提示', {
      type: 'warning'
    })
    // TODO: 调用冲抵API
    ElMessage.success('冲抵成功')
    handleSearch()
  } catch (error) {
    console.error('冲抵操作失败:', error)
  }
}

// 还原操作
const handleRestore = async (row: any) => {
  try {
    await ElMessageBox.confirm('确认要对该记录进行还原操作吗？', '提示', {
      type: 'warning'
    })
    // TODO: 调用还原API
    ElMessage.success('还原成功')
    handleSearch()
  } catch (error) {
    console.error('还原操作失败:', error)
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}
</script>

<style scoped lang="scss">
.offset-record {
  padding: 16px;

  .search-bar {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;

    .search-input {
      width: 280px;
    }
  }

  .operation-bar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  :deep(.el-table) {
    margin-top: 20px;

    .el-button {
      padding: 0 5px;
      height: auto;
    }
  }
}
</style>
