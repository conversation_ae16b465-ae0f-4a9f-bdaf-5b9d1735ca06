import type { SheetConfig } from '@luckysheet/vue'
import { da } from 'element-plus/es/locale'

/**
 * 设置工作表保护区域
 * @param sheetIndex 工作表索引
 * @param rangeList 保护区域配置列表
 * @param options 其他保护选项
 * @param success 操作结束的回调函数
 */
export const setSheetProtection = (
  sheetIndex: number,
  rangeList: Array<{
    name: string
    password?: string
    hintText?: string
    algorithmName?: string
    saltValue?: string | null
    sqref: string
  }>,
  options: {
    selectLockedCells?: number
    selectunLockedCells?: number
    formatCells?: number
    formatColumns?: number
    formatRows?: number
    insertColumns?: number
    insertRows?: number
    insertHyperlinks?: number
    deleteColumns?: number
    deleteRows?: number
    sort?: number
    filter?: number
    usePivotTablereports?: number
    editObjects?: number
    editScenarios?: number
    sheet?: number
    hintText?: string
    algorithmName?: string
    saltValue?: string | null
  } = {},
  success?: () => void
) => {
  const defaultOptions = {
    selectLockedCells: 1,
    selectunLockedCells: 1,
    formatCells: 1,
    formatColumns: 1,
    formatRows: 1,
    insertColumns: 1,
    insertRows: 1,
    insertHyperlinks: 1,
    deleteColumns: 1,
    deleteRows: 1,
    sort: 1,
    filter: 1,
    usePivotTablereports: 1,
    editObjects: 1,
    editScenarios: 1,
    sheet: 1,
    hintText: '',
    algorithmName: 'None',
    saltValue: null
  }

  const authority = {
    ...defaultOptions,
    ...options,
    allowRangeList: rangeList.map(range => ({
      name: range.name,
      password: range.password || '',
      hintText: range.hintText || '',
      algorithmName: range.algorithmName || 'None',
      saltValue: range.saltValue || null,
      sqref: range.sqref
    }))
  }

  // 获取当前工作表配置
  const currentSheet = luckysheet.getSheet(sheetIndex)
  if (!currentSheet) {
    console.error(`工作表 ${sheetIndex} 不存在`)
    return
  }

  const data = {
    data:[{
      ...currentSheet,
      config: {
        ...currentSheet.config,
        authority
      }
    }],
    success
  };
  console.log(data);
  // 更新工作表配置
  luckysheet.updataSheet(data)
}

/**
 * 示例用法：
 * setSheetProtection(0, [
 *   {
 *     name: '保护区域1',
 *     password: '123',
 *     hintText: '请输入密码',
 *     sqref: '$A$1:$B$10'
 *   }
 * ], {
 *   selectLockedCells: 0,
 *   formatCells: 0
 * }, () => {
 *   console.log('保护区域设置完成')
 * })
 */ 