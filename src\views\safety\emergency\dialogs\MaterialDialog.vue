<template>
  <el-dialog
    :title="dialogType === 'add' ? '新增物资' : '编辑物资'"
    v-model="dialogVisible"
    width="50%"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="material-form"
    >
      <el-form-item label="物资名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入物资名称" />
      </el-form-item>

      <el-form-item label="物资类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择物资类型" style="width: 100%">
          <el-option label="消防器材" value="fire" />
          <el-option label="医疗用品" value="medical" />
          <el-option label="应急工具" value="tool" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <el-form-item label="数量" prop="quantity">
        <el-input-number v-model="form.quantity" :min="0" style="width: 100%" />
      </el-form-item>

      <el-form-item label="单位" prop="unit">
        <el-input v-model="form.unit" placeholder="请输入单位" />
      </el-form-item>

      <el-form-item label="存放位置" prop="location">
        <el-input v-model="form.location" placeholder="请输入存放位置" />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'MaterialDialog',
  props: {
    dialogType: {
      type: String,
      default: 'add'
    },
    materialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        name: '',
        type: '',
        quantity: 0,
        unit: '',
        location: '',
        status: 'normal',
        remark: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入物资名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择物资类型', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入单位', trigger: 'blur' }
        ],
        location: [
          { required: true, message: '请输入存放位置', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    materialData: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.form = { ...val }
        }
      },
      immediate: true
    }
  },
  methods: {
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()
        // 提交表单逻辑
        this.$emit('submit', this.form)
        this.dialogVisible = false
      } catch (error) {
        console.error('表单验证失败', error)
      }
    },
    open() {
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.material-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 20px;
}
</style> 