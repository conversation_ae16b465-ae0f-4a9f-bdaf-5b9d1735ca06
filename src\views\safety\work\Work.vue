<template>
  <div class="safety-work">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="安全证照" name="certificate">
        <certificate-list />
      </el-tab-pane>
      <el-tab-pane label="作业票" name="workTicket">
        <work-ticket-list />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import CertificateList from './components/CertificateList.vue'
import WorkTicketList from './components/WorkTicketList.vue'

export default {
  name: 'SafetyWork',
  components: {
    CertificateList,
    WorkTicketList
  },
  data() {
    return {
      activeTab: 'certificate'
    }
  }
}
</script>

<style lang="scss" scoped>
.safety-work {
  padding: 20px;
  
  :deep(.el-tabs__content) {
    padding: 20px;
    background-color: #fff;
  }
}
</style>
