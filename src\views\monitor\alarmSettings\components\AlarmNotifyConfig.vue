<template>
  <div class="w-full h-[calc(100vh-210px)]">
    <div class="w-full h-full flex flex-col">
      <div class="w-full h-[50px] flex p-4">
        <div class="h-full w-4/5 flex items-center gap-2">
          <el-form :inline="true" :model="formInline">
            <el-form-item label="告警级别：" class="w-[200px]">
              <el-select v-model="formInline.alarmLevel" placeholder="请选择告警级别" clearable>
                <el-option label="I级" value="I" />
                <el-option label="II级" value="II" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
              <el-button type="info" :icon="RefreshRight" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="flex-1 h-full flex justify-end items-center gap-2">
          <el-button type="primary" :icon="Plus" @click="handleAddNotify">新增配置</el-button>
        </div>
      </div>
      <div class="w-full flex-1 flex flex-col p-4">
        <div class="relative w-full h-full">
          <div class="absolute w-full h-full">
            <el-table :data="tableData" border>
              <el-table-column prop="level" label="告警级别" align="center" width="100">
                <template #default="scope">
                  <el-tag :type="getAlarmLevelType(scope.row.level)">{{ scope.row.level }}级</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="通知方式" align="center">
                <template #default="scope">
                  <div class="flex flex-wrap gap-2 justify-center">
                    <el-tag v-for="method in scope.row.notifyMethods" :key="method.type"
                      :type="method.enabled ? 'success' : 'info'" effect="plain">
                      {{ method.name }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="接收角色" align="center">
                <template #default="scope">
                  <div class="flex flex-wrap gap-1 justify-center">
                    <el-tag v-for="role in scope.row.receiverRoles" :key="role" size="small" effect="plain">
                      {{ getRoleName(role) }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" align="center">
                <template #default="scope">
                  <el-button size="small" type="primary" link
                    @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="flex-1 w-full flex items-center justify-end mt-2">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>
    <AlarmNotifyEditDialog ref="dialogRef" @save-notify="handleSaveNotify" />
  </div>
</template>
<script lang="ts" setup>
import { RefreshRight, Search, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { reactive, ref, onMounted, watch } from 'vue'
import AlarmNotifyEditDialog from './Dialog/alarmNotifyEditDialog.vue'
import { getNotificationSettings, updateNotifyConfig, getNotifyRoles } from '@/api/alarm'
import { useAppStore } from '@/store/modules/app'

// 定义props
const props = defineProps({
  isActive: {
    type: Boolean,
    default: false
  }
})

const dialogRef = ref<InstanceType<typeof AlarmNotifyEditDialog> | null>(null)

// 获取全局状态管理
const appStore = useAppStore()
// 是否正在加载数据
const isLoading = ref(false)

const formInline = reactive({
  alarmLevel: ''
})

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const tableData = ref<any[]>([])

const getAlarmLevelType = (level) => {
  const types = {
    'I': 'danger',
    'II': 'warning',
  }
  return types[level] || 'info'
}

// 处理查询按钮点击
const handleSearch = () => {
  currentPage.value = 1
  loadNotificationSettings()
}

// 角色ID到名称的映射
const roleMap = ref<Record<string | number, string>>({})

// 获取角色列表
const loadRoles = async () => {
  try {
    const response = await getNotifyRoles()
    if (response && response.data) {
      // 处理角色数据，更新表格中的角色显示
      const roles = response.data || []
      // 创建角色ID到名称的映射
      if (Array.isArray(roles)) {
        roles.forEach(role => {
          if (typeof role === 'object' && role !== null) {
            // 优先使用id字段
            const id = role.id !== undefined ? role.id : (role.value || role.name || role.code)
            const name = role.name || role.label || (typeof role.value === 'string' ? role.value : '') || (typeof role.code === 'string' ? role.code : '') || String(id || '')

            // 如果id是数字字符串，转为数字类型
            let finalId = id;
            if (typeof id === 'string' && !isNaN(Number(id)) && id.trim() !== '') {
              finalId = Number(id)
            }

            if (finalId !== undefined) {
              roleMap.value[finalId] = name
              console.log(`列表页添加角色映射: [${finalId}] -> "${name}"`)
            }
          } else if (role !== null && role !== undefined) {
            // 如果是简单值，检查是否是数字字符串
            let value = role;
            if (typeof role === 'string' && !isNaN(Number(role)) && role.trim() !== '') {
              value = Number(role);
            }

            roleMap.value[value] = String(role)
            console.log(`列表页添加简单角色: [${value}] -> "${String(role)}"`)
          }
        })
      }
      console.log('获取到角色列表:', roles)
      console.log('角色映射:', roleMap.value)
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
  }
}

// 获取通知设置列表数据
const loadNotificationSettings = async () => {
  // 如果当前正在加载，直接返回
  if (isLoading.value) return

  try {
    isLoading.value = true
    // 告警通知设置不需要水厂ID参数

    // 同时获取角色列表数据
    await loadRoles()

    // 调用接口获取数据
    const response = await getNotificationSettings({
      page: currentPage.value,
      pageSize: pageSize.value,
      level: formInline.alarmLevel || undefined
    })

    if (response && response.data) {
      // 处理API返回的数据，解析notifyMethods字段
      const list = response.data.list || [];
      tableData.value = list.map(item => {
        // 如果notifyMethods是字符串，则尝试解析为JSON对象
        if (item.notifyMethods && typeof item.notifyMethods === 'string') {
          try {
            const methodsObj = JSON.parse(item.notifyMethods);
            // 转换为组件期望的格式
            item.notifyMethods = [
              { type: 'sms', name: '短信', enabled: !!methodsObj.sms },
              { type: 'internal', name: '站内信', enabled: !!methodsObj.internal }
            ];
          } catch (e) {
            console.error('解析notifyMethods失败:', e);
            item.notifyMethods = [
              { type: 'sms', name: '短信', enabled: false },
              { type: 'internal', name: '站内信', enabled: false }
            ];
          }
        }

        // 处理接收角色字段
        if (item.receiverRoles && typeof item.receiverRoles === 'string') {
          item.receiverRoles = item.receiverRoles.split(',')
            .map(role => {
              const trimmed = role.trim();
              // 尝试将字符串转换为数字（如果它是一个有效的数字）
              const num = Number(trimmed);
              return !isNaN(num) && trimmed !== '' ? num : trimmed;
            });
        }
        console.log('处理后的角色:', item.receiverRoles);

        return item;
      });
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('获取告警通知设置失败:', error)
    ElMessage.error('获取告警通知设置失败')
  } finally {
    isLoading.value = false
  }
}

// 获取角色名称的方法
const getRoleName = (roleId: string | number): string => {
  return roleMap.value[roleId] || String(roleId)
}

// 监听组件是否激活
watch(() => props.isActive, (newActive) => {
  if (newActive && !isLoading.value) {
    loadNotificationSettings()
  }
})

// 监听水厂变化，重新加载数据
watch(() => appStore.currentStation, () => {
  if (props.isActive && !isLoading.value) {
    handleSearch()
  }
}, { deep: true })

// 组件挂载时，如果是活跃状态则加载数据
onMounted(() => {
  if (props.isActive) {
    loadNotificationSettings()
  }
})

// 处理重置按钮点击
const resetForm = () => {
  formInline.alarmLevel = ''
  handleSearch()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  console.log('每页显示:', pageSize.value)
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  console.log('当前页:', currentPage.value)
}

async function handleEdit(index, row) {
  // 先获取角色列表
  await loadRoles()

  console.log('编辑时传入的角色数据:', row.receiverRoles)

  // 打开编辑对话框
  dialogRef.value?.openDialog(row)
}

function handleSaveNotify(notify) {
  if (notify.id) {
    // 编辑模式 - 更新现有记录
    const index = tableData.value.findIndex(item => item.id === notify.id)
    if (index !== -1) {
      tableData.value[index] = notify
      ElMessage.success('通知设置更新成功')
    }
  } else {
    // 新增模式 - 添加到列表
    // 生成临时ID (实际应该由后端生成)
    notify.id = Date.now().toString()
    tableData.value.push(notify)
    ElMessage.success('通知配置添加成功')
  }
  // 刷新数据
  loadNotificationSettings()
}

function handleAddNotify() {
  // 先获取角色列表
  loadRoles()

  // 创建一个新的通知配置对象
  const newNotify = {
    id: '', // 新建时ID为空
    level: '', // 新建时初始化为空
    factoryId: appStore.currentStation?.id, // 从当前选中水厂获取ID
    notifyMethods: [
      { type: 'sms', name: '短信', enabled: false },
      { type: 'internal', name: '站内信', enabled: false }
    ],
    receiverRoles: []
  }
  // 打开对话框并传入新对象
  dialogRef.value?.openDialog(newNotify)
}
</script>
<style scoped lang="scss"></style>
