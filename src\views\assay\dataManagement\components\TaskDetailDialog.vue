<template>
  <el-dialog v-model="visible" title="检测任务详情" width="70%" class="task-detail-dialog">
    <div v-if="currentTask" class="task-detail-content">
      <!-- 基本信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">基本信息</span>
            <el-tag :type="getStatusType(currentTask.status)" size="small">
              {{ getStatusText(currentTask.status) }}
            </el-tag>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>任务编号：</label>
              <span>{{ currentTask.taskCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>样品编号：</label>
              <span>{{ currentTask.sampleCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>优先级：</label>
              <el-tag :type="getPriorityType(currentTask.priority)" size="small">
                {{ getPriorityText(currentTask.priority) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 16px;">
          <el-col :span="8">
            <div class="detail-item">
              <label>分配人员：</label>
              <span>{{ currentTask.assignedTo || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>截止日期：</label>
              <span>{{ currentTask.dueDate || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ currentTask.createTime }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 16px;" v-if="currentTask.progress !== undefined">
          <el-col :span="24">
            <div class="detail-item">
              <label>完成进度：</label>
              <el-progress :percentage="currentTask.progress" :color="getProgressColor(currentTask.progress)" />
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 检测项目 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
        <template #header>
          <span class="card-title">检测项目</span>
        </template>
        <el-table :data="currentTask.testItems" border class="task-detail-table">
          <el-table-column prop="name" label="项目名称" min-width="120" />
          <el-table-column prop="method" label="检测方法" min-width="150" show-overflow-tooltip />
          <el-table-column prop="unit" label="单位" width="80" align="center" />
          <el-table-column prop="standardValue" label="标准值" width="120" align="center" />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getItemStatusType(row.status)" size="small">
                {{ getItemStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 执行记录 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
        <template #header>
          <span class="card-title">执行记录</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="record in taskExecutionRecords"
            :key="record.id"
            :timestamp="record.time"
            :type="record.type"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ record.action }}</div>
              <div class="timeline-desc" v-if="record.description">{{ record.description }}</div>
              <div class="timeline-operator" v-if="record.operator">操作人：{{ record.operator }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 备注信息 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;" v-if="currentTask.specialRequirements">
        <template #header>
          <span class="card-title">特殊要求</span>
        </template>
        <p class="remark-content">{{ currentTask.specialRequirements }}</p>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button v-if="currentTask?.status === 'pending'" type="primary" @click="handleAcceptTask">
          接受任务
        </el-button>
        <el-button v-if="currentTask?.status === 'assigned'" type="success" @click="handleStartTask">
          开始执行
        </el-button>
        <el-button v-if="currentTask?.status === 'processing'" type="warning" @click="handleCompleteTask">
          完成任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'TaskDetailDialog' })

// 弹窗显示状态
const visible = ref(false)

// 当前任务
const currentTask = ref<any>(null)

// 任务执行记录
const taskExecutionRecords = computed(() => {
  if (!currentTask.value) return []
  
  const records = []
  
  // 创建记录
  records.push({
    id: 1,
    action: '任务创建',
    description: '检测任务已创建',
    operator: '系统',
    time: currentTask.value.createTime,
    type: 'primary'
  })
  
  // 接受记录
  if (currentTask.value.acceptTime) {
    records.push({
      id: 2,
      action: '任务接受',
      description: '检测人员已接受任务',
      operator: currentTask.value.assignedTo,
      time: currentTask.value.acceptTime,
      type: 'success'
    })
  }
  
  // 开始记录
  if (currentTask.value.startTime) {
    records.push({
      id: 3,
      action: '开始执行',
      description: '开始进行检测工作',
      operator: currentTask.value.assignedTo,
      time: currentTask.value.startTime,
      type: 'warning'
    })
  }
  
  // 完成记录
  if (currentTask.value.completeTime) {
    records.push({
      id: 4,
      action: '任务完成',
      description: '检测任务已完成',
      operator: currentTask.value.assignedTo,
      time: currentTask.value.completeTime,
      type: 'success'
    })
  }
  
  return records
})

// 打开弹窗
const open = (task: any) => {
  currentTask.value = {
    ...task,
    testItems: task.testItems?.map((item: string) => ({
      name: item,
      method: getTestMethod(item),
      unit: getTestUnit(item),
      standardValue: getStandardValue(item),
      status: 'pending'
    })) || []
  }
  visible.value = true
}

// 获取检测方法
const getTestMethod = (item: string) => {
  const methods: Record<string, string> = {
    'COD': '重铬酸钾法',
    'BOD5': '稀释接种法',
    '氨氮': '纳氏试剂比色法',
    '总磷': '钼酸铵分光光度法',
    '总氮': '碱性过硫酸钾消解法',
    'pH值': '玻璃电极法',
    '悬浮物': '重量法',
    '污泥浓度': '重量法',
    '污泥沉降比': '静置沉降法',
    '铜': '原子吸收分光光度法',
    '锌': '原子吸收分光光度法'
  }
  return methods[item] || '待确定'
}

// 获取检测单位
const getTestUnit = (item: string) => {
  const units: Record<string, string> = {
    'COD': 'mg/L',
    'BOD5': 'mg/L',
    '氨氮': 'mg/L',
    '总磷': 'mg/L',
    '总氮': 'mg/L',
    'pH值': '无量纲',
    '悬浮物': 'mg/L',
    '污泥浓度': 'mg/L',
    '污泥沉降比': '%',
    '铜': 'mg/L',
    '锌': 'mg/L'
  }
  return units[item] || '-'
}

// 获取标准值
const getStandardValue = (item: string) => {
  const standards: Record<string, string> = {
    'COD': '≤50',
    'BOD5': '≤10',
    '氨氮': '≤5',
    '总磷': '≤0.5',
    '总氮': '≤15',
    'pH值': '6-9',
    '悬浮物': '≤10',
    '污泥浓度': '2000-4000',
    '污泥沉降比': '15-30',
    '铜': '≤0.5',
    '锌': '≤1.0'
  }
  return standards[item] || '-'
}

// 状态相关方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'assigned': 'warning',
    'processing': 'primary',
    'completed': 'success'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待分配',
    'assigned': '已分配',
    'processing': '进行中',
    'completed': '已完成'
  }
  return textMap[status] || status
}

const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    'urgent': 'danger',
    'high': 'warning',
    'normal': 'primary',
    'low': 'info'
  }
  return typeMap[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    'urgent': '紧急',
    'high': '高',
    'normal': '普通',
    'low': '低'
  }
  return textMap[priority] || priority
}

const getItemStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success'
  }
  return typeMap[status] || 'info'
}

const getItemStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待检测',
    'processing': '检测中',
    'completed': '已完成'
  }
  return textMap[status] || status
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 任务操作
const handleAcceptTask = () => {
  ElMessage.success('任务已接受')
  visible.value = false
}

const handleStartTask = () => {
  ElMessage.success('任务已开始')
  visible.value = false
}

const handleCompleteTask = () => {
  ElMessage.success('任务已完成')
  visible.value = false
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.task-detail-dialog {
  .task-detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .detail-card {
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-weight: 600;
      color: #303133;
    }
  }

  .detail-item {
    margin-bottom: 12px;

    label {
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
    }

    span {
      color: #303133;
    }
  }

  .task-detail-table {
    margin-top: 16px;
  }

  .timeline-content {
    .timeline-title {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }

    .timeline-desc {
      color: #606266;
      font-size: 14px;
      margin-bottom: 4px;
    }

    .timeline-operator {
      color: #909399;
      font-size: 12px;
    }
  }

  .remark-content {
    color: #606266;
    line-height: 1.6;
    margin: 0;
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
