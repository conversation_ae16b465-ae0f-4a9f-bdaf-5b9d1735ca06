<template>
  <div class="investment-analysis">
    <div class="filter-section">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filterForm.department" placeholder="请选择部门">
            <el-option
              v-for="item in departments"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="data-section">
      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <span>安全投入概览</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="总投入金额">¥ {{ overview.totalAmount }}</el-descriptions-item>
          <el-descriptions-item label="同比增长">{{ overview.yearGrowth }}</el-descriptions-item>
          <el-descriptions-item label="环比增长">{{ overview.monthGrowth }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="date" label="日期" width="180" />
        <el-table-column prop="department" label="部门" width="180" />
        <el-table-column prop="category" label="投入类别" />
        <el-table-column prop="amount" label="投入金额" />
        <el-table-column prop="status" label="使用状态" />
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <DetailDialog
      v-model:visible="detailDialogVisible"
      :type="'investment'"
      :detail-data="currentDetailData || {}"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import DetailDialog from '../dialogs/DetailDialog.vue'

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  department: ''
})

// 部门列表
const departments = [
  { label: '生产部', value: 'production' },
  { label: '安全部', value: 'safety' },
  { label: '设备部', value: 'equipment' },
  { label: '质量部', value: 'quality' }
]

// 概览数据
const overview = reactive({
  totalAmount: '1,256,800',
  yearGrowth: '+15.6%',
  monthGrowth: '+2.3%'
})

// 表格数据
const tableData = ref([
  {
    date: '2023-10-15',
    department: '生产部',
    category: '安全设备',
    amount: '¥ 45,600',
    status: '已使用'
  },
  {
    date: '2023-10-10',
    department: '安全部',
    category: '培训费用',
    amount: '¥ 23,800',
    status: '已使用'
  },
  {
    date: '2023-09-28',
    department: '设备部',
    category: '维护保养',
    amount: '¥ 58,200',
    status: '已使用'
  },
  {
    date: '2023-09-15',
    department: '质量部',
    category: '检测设备',
    amount: '¥ 86,500',
    status: '使用中'
  },
  {
    date: '2023-09-05',
    department: '安全部',
    category: '应急物资',
    amount: '¥ 32,400',
    status: '已使用'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 弹窗控制
const detailDialogVisible = ref(false)
const currentDetailData = ref(null)

// 查询方法
const handleSearch = () => {
  // TODO: 实现查询逻辑
}

// 重置方法
const handleReset = () => {
  filterForm.dateRange = []
  filterForm.department = ''
}

// 分页方法
const handleSizeChange = (size: number) => {
  pageSize.value = size
  // TODO: 重新加载数据
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  // TODO: 重新加载数据
}

// 详情方法
const handleDetail = (row: any) => {
  currentDetailData.value = row
  detailDialogVisible.value = true
}

const handleDetailClose = () => {
  detailDialogVisible.value = false
  currentDetailData.value = null
}
</script>

<style lang="scss" scoped>
.investment-analysis {
  padding: 20px;

  .filter-section {
    margin-bottom: 20px;
  }

  .data-section {
    .mb-20 {
      margin-bottom: 20px;
    }
  }

  .pagination-section {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>