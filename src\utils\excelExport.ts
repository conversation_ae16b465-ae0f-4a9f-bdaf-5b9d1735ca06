import { Workbook } from 'exceljs'
import { saveAs } from 'file-saver'

// 类型定义
interface CellStyle {
  ff?: string
  fs?: number
  fc?: string
  bg?: string
  ht?: string
  vt?: string
  bl?: number
  it?: boolean
  un?: boolean
  st?: boolean
  wrap?: boolean
  format?: string
  bd?: {
    top?: { style?: string; color?: string }
    left?: { style?: string; color?: string }
    bottom?: { style?: string; color?: string }
    right?: { style?: string; color?: string }
  }
}

interface CellValue {
  v?: string | number
  m?: string
  ff?: string
  ct?: {
    t: string
    fa: string
  }
  mc?: {
    r: number
    c: number
    rs: number
    cs: number
  }
  bl?: number
  ht?: string
  fc?: string
  fs?: number
  vt?: string
  tb?: string
  bg?: string
}

interface CellData {
  r: number
  c: number
  v?: CellValue
  start_r?: number
  start_c?: number
  end_r?: number
  end_c?: number
}

interface SheetConfig {
  columnlen?: Record<string, number>
  rowlen?: Record<string, number>
  merges?: Array<{
    r: number
    c: number
    rs: number
    cs: number
  }>
}

interface SheetData {
  name: string
  celldata: CellData[]
  config?: SheetConfig
}

/**
 * 导出Excel文件
 * @param sheets 工作表数据
 * @param fileName 文件名，默认为'导出数据.xlsx'
 */
export const exportExcel = async (sheets: SheetData[], fileName: string = '导出数据.xlsx') => {
  try {
    const workbook = new Workbook()
    
    sheets.forEach(sheet => {
      const worksheet = workbook.addWorksheet(sheet.name)
      
      // 设置列宽
      if (sheet.config?.columnlen) {
        Object.entries(sheet.config.columnlen).forEach(([col, width]) => {
          const colIndex = parseInt(col)
          worksheet.getColumn(colIndex + 1).width = width / 7
        })
      }
      
      // 设置行高
      if (sheet.config?.rowlen) {
        Object.entries(sheet.config.rowlen).forEach(([row, height]) => {
          const rowIndex = parseInt(row)
          worksheet.getRow(rowIndex + 1).height = height / 1.5
        })
      }

      // 收集所有需要合并的单元格
      const mergeCells: Set<string> = new Set()
      
      // 填充数据
      if (sheet.celldata) {
        sheet.celldata.forEach(cell => {
          const { r, c, v } = cell
          const cellValue = v?.v || v?.m || ''
          const worksheetCell = worksheet.getCell(r + 1, c + 1)
          
          // 设置单元格值
          if (v?.ct?.t === 'n') {
            // 数字类型
            worksheetCell.value = Number(cellValue)
            worksheetCell.numFmt = v.ct.fa || 'General'
          } else {
            // 文本类型
            worksheetCell.value = cellValue
          }
          
          // 设置样式
          if (v) {
            const cell = worksheetCell
            
            // 设置字体
            const font: any = {}
            if (v.ff) font.name = v.ff
            if (v.fs) font.size = v.fs
            if (v.fc) font.color = { argb: v.fc.replace('#', '') }
            if (v.bl) font.bold = true
            if (Object.keys(font).length > 0) cell.font = font
            
            // 设置背景色
            if (v.bg) {
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: v.bg.replace('#', '') }
              }
            }
            
            // 设置对齐方式
            const alignment: any = {}
            
            // 处理水平对齐
            if (v.ht) {
              switch(v.ht) {
                case '0':
                  alignment.horizontal = 'left'
                  break
                case '1':
                  alignment.horizontal = 'center'
                  break
                case '2':
                  alignment.horizontal = 'right'
                  break
                default:
                  alignment.horizontal = 'left'
              }
            }
            
            // 处理垂直对齐
            if (v.vt) {
              switch(v.vt) {
                case '0':
                  alignment.vertical = 'top'
                  break
                case '1':
                  alignment.vertical = 'middle'
                  break
                case '2':
                  alignment.vertical = 'bottom'
                  break
                default:
                  alignment.vertical = 'middle'
              }
            }

            // 处理tb属性（居中对齐）
            if (v.tb) {
              switch(v.tb) {
                case '1':
                  alignment.horizontal = 'center'
                  alignment.vertical = 'middle'
                  break
                case '2':
                  alignment.vertical = 'middle'
                  break
              }
            }
            
            if (Object.keys(alignment).length > 0) {
              cell.alignment = alignment
            }

            // 收集合并单元格信息
            if (v.mc) {
              const mergeKey = `${v.mc.r + 1}:${v.mc.c + 1}:${v.mc.r + v.mc.rs}:${v.mc.c + v.mc.cs}`
              mergeCells.add(mergeKey)
            }
          }

          // 为所有单元格添加默认边框
          worksheetCell.border = {
            top: { style: 'thin', color: { argb: '000000' } },
            left: { style: 'thin', color: { argb: '000000' } },
            bottom: { style: 'thin', color: { argb: '000000' } },
            right: { style: 'thin', color: { argb: '000000' } }
          }
        })
      }

      // 处理合并单元格
      mergeCells.forEach(mergeKey => {
        const [startRow, startCol, endRow, endCol] = mergeKey.split(':').map(Number)
        try {
          worksheet.mergeCells(startRow, startCol, endRow, endCol)
        } catch (error) {
          console.warn(`跳过重复的合并单元格: ${mergeKey}`)
        }
      })
    })
    
    // 导出文件
    const buffer = await workbook.xlsx.writeBuffer()
    saveAs(new Blob([buffer]), fileName)
  } catch (error) {
    console.error('导出Excel失败:', error)
    throw error
  }
} 