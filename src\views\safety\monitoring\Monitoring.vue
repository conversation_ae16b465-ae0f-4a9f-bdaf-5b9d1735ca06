<template>
  <div class="app-container">
    <!-- 操作栏 -->
    <ContentWrap>
      <div class="mb-10px flex justify-between">
        <div class="flex items-center">
          <el-button type="primary" @click="handleCreate">
            <Icon icon="ep:plus" class="mr-5px" /> 新增监测设备
          </el-button>
          <el-button type="success" @click="handleRefresh">
            <Icon icon="ep:refresh" class="mr-5px" /> 刷新
          </el-button>
        </div>
        <div class="flex items-center">
          <el-button @click="exportExcel">
            <Icon icon="ep:download" class="mr-5px" /> 导出
          </el-button>
        </div>
      </div>
      <!-- 搜索表单 -->
      <el-form ref="searchFormRef" :model="searchForm" inline label-width="68px"
        class="bg-[var(--el-fill-color-light)] flex flex-wrap items-center justify-start px-20px py-16px mb-10px">
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="searchForm.deviceType" placeholder="请选择设备类型" clearable class="!w-240px">
            <el-option v-for="item in deviceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable class="!w-240px">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="位置" prop="location">
          <el-input v-model="searchForm.location" placeholder="请输入设备位置" clearable class="!w-240px" />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="searchForm.deviceName" placeholder="请输入设备名称" clearable class="!w-240px" />
        </el-form-item>
        <el-form-item label="检测日期" prop="inspectionDateRange">
          <el-date-picker v-model="searchForm.inspectionDateRange" type="daterange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" class="!w-240px" />
        </el-form-item>
        <el-form-item class="!ml-auto">
          <el-button type="primary" @click="handleSearch">
            <Icon icon="ep:search" class="mr-5px" /> 搜索
          </el-button>
          <el-button @click="resetSearch">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 设备列表 -->
    <ContentWrap>
      <el-card shadow="never">
        <div class="flex h-full flex-col">
          <el-table v-loading="loading" :data="deviceList" border style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="deviceCode" label="设备编号" min-width="120" align="center" show-overflow-tooltip />
            <el-table-column prop="deviceName" label="设备名称" min-width="120" align="center" show-overflow-tooltip />
            <el-table-column prop="deviceType" label="设备类型" min-width="120" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.deviceType === '流量计'" type="success">{{ row.deviceType }}</el-tag>
                <el-tag v-else-if="row.deviceType === '压力表'" type="info">{{ row.deviceType }}</el-tag>
                <el-tag v-else-if="row.deviceType === '防雷设备'" type="warning">{{ row.deviceType }}</el-tag>
                <el-tag v-else-if="row.deviceType === '起重设备'" type="danger">{{ row.deviceType }}</el-tag>
                <el-tag v-else type="primary">{{ row.deviceType }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="specifications" label="规格型号" min-width="120" align="center" show-overflow-tooltip />
            <el-table-column prop="location" label="安装位置" min-width="120" align="center" show-overflow-tooltip />
            <el-table-column prop="lastInspectionDate" label="上次检测日期" min-width="120" align="center" />
            <el-table-column prop="nextInspectionDate" label="下次检测日期" min-width="120" align="center" />
            <el-table-column prop="inspectionCycle" label="检测周期" min-width="80" align="center" />
            <el-table-column prop="status" label="状态" min-width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.status === 'normal'" type="success">正常</el-tag>
                <el-tag v-else-if="row.status === 'comingDue'" type="warning">即将到期</el-tag>
                <el-tag v-else-if="row.status === 'expired'" type="danger">已过期</el-tag>
                <el-tag v-else-if="row.status === 'maintenance'" type="info">维护中</el-tag>
                <el-tag v-else>{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleView(row)">查看</el-button>
                <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
                <el-button link type="primary" @click="handleInspection(row)">检测</el-button>
                <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="mt-10px flex justify-end">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </div>
      </el-card>
    </ContentWrap>

    <!-- 弹窗组件 -->
    <DeviceFormDialog ref="deviceFormDialogRef" @success="getDeviceList" />
    <DeviceViewDialog ref="deviceViewDialogRef" />
    <InspectionDialog ref="inspectionDialogRef" @success="getDeviceList" />
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
// 导入弹窗组件
import DeviceFormDialog from './dialogs/DeviceFormDialog.vue'
import DeviceViewDialog from './dialogs/DeviceViewDialog.vue'
import InspectionDialog from './dialogs/InspectionDialog.vue'

defineOptions({ name: 'SafetyMonitoring' })

// 表单引用
const searchFormRef = ref()
const deviceFormDialogRef = ref()
const deviceViewDialogRef = ref()
const inspectionDialogRef = ref()

// 搜索表单
const searchForm = reactive({
  deviceType: '',
  status: '',
  location: '',
  deviceName: '',
  inspectionDateRange: []
})

// 设备类型选项
const deviceTypeOptions = [
  { label: '流量计', value: '流量计' },
  { label: '压力表', value: '压力表' },
  { label: '防雷设备', value: '防雷设备' },
  { label: '预防性试验', value: '预防性试验' },
  { label: '起重设备', value: '起重设备' },
  { label: '压力容器', value: '压力容器' },
  { label: '安全工器具', value: '安全工器具' },
  { label: '常规检测', value: '常规检测' },
  { label: '非常规检测', value: '非常规检测' }
]

// 状态选项
const statusOptions = [
  { label: '正常', value: 'normal' },
  { label: '即将到期', value: 'comingDue' },
  { label: '已过期', value: 'expired' },
  { label: '维护中', value: 'maintenance' }
]

// 设备列表数据
const loading = ref(false)
const deviceList = ref([
  {
    id: 1,
    deviceCode: 'FLM-2024001',
    deviceName: '超声波流量计',
    deviceType: '流量计',
    specifications: 'DN100',
    location: '1号车间进水管道',
    lastInspectionDate: '2024-01-15',
    nextInspectionDate: '2024-07-15',
    inspectionCycle: '6个月',
    status: 'normal',
    manufacturer: '西门子',
    installationDate: '2023-05-10',
    responsiblePerson: '张三',
    remarks: '正常运行'
  },
  {
    id: 2,
    deviceCode: 'PG-2024001',
    deviceName: '数字压力表',
    deviceType: '压力表',
    specifications: '0-1.6MPa',
    location: '锅炉房',
    lastInspectionDate: '2024-02-20',
    nextInspectionDate: '2024-05-20',
    inspectionCycle: '3个月',
    status: 'comingDue',
    manufacturer: '上海自动化仪表',
    installationDate: '2023-10-15',
    responsiblePerson: '李四',
    remarks: '需注意压力波动'
  },
  {
    id: 3,
    deviceCode: 'LP-2024001',
    deviceName: '避雷针',
    deviceType: '防雷设备',
    specifications: 'FDY-12',
    location: '主厂房楼顶',
    lastInspectionDate: '2023-10-10',
    nextInspectionDate: '2024-04-10',
    inspectionCycle: '6个月',
    status: 'expired',
    manufacturer: '安科防雷',
    installationDate: '2022-08-01',
    responsiblePerson: '王五',
    remarks: '需尽快安排检测'
  },
  {
    id: 4,
    deviceCode: 'PT-2024001',
    deviceName: '绝缘电阻测试',
    deviceType: '预防性试验',
    specifications: '不适用',
    location: '配电室',
    lastInspectionDate: '2024-03-15',
    nextInspectionDate: '2024-09-15',
    inspectionCycle: '6个月',
    status: 'normal',
    manufacturer: '不适用',
    installationDate: '不适用',
    responsiblePerson: '赵六',
    remarks: '按照电力安全规程执行'
  },
  {
    id: 5,
    deviceCode: 'L*********',
    deviceName: '5吨桥式起重机',
    deviceType: '起重设备',
    specifications: '5t',
    location: '装配车间',
    lastInspectionDate: '2023-12-10',
    nextInspectionDate: '2024-06-10',
    inspectionCycle: '6个月',
    status: 'comingDue',
    manufacturer: '华中起重',
    installationDate: '2022-05-10',
    responsiblePerson: '钱七',
    remarks: '运行正常，需定期检查钢丝绳'
  },
  {
    id: 6,
    deviceCode: 'PV-2024001',
    deviceName: '储气罐',
    deviceType: '压力容器',
    specifications: '1.0m³/1.0MPa',
    location: '空压机房',
    lastInspectionDate: '2023-09-15',
    nextInspectionDate: '2024-03-15',
    inspectionCycle: '6个月',
    status: 'expired',
    manufacturer: '合肥通用机械',
    installationDate: '2021-06-15',
    responsiblePerson: '孙八',
    remarks: '需尽快安排检验'
  },
  {
    id: 7,
    deviceCode: 'ST-2024001',
    deviceName: '绝缘手套',
    deviceType: '安全工器具',
    specifications: '12kV',
    location: '配电室工具柜',
    lastInspectionDate: '2024-01-20',
    nextInspectionDate: '2024-04-20',
    inspectionCycle: '3个月',
    status: 'comingDue',
    manufacturer: '安全工具厂',
    installationDate: '2023-10-20',
    responsiblePerson: '周九',
    remarks: '使用前需检查'
  },
  {
    id: 8,
    deviceCode: 'D*********',
    deviceName: '气体检测仪',
    deviceType: '常规检测',
    specifications: 'GC310',
    location: '化学品库',
    lastInspectionDate: '2024-02-01',
    nextInspectionDate: '2024-05-01',
    inspectionCycle: '3个月',
    status: 'normal',
    manufacturer: '德尔格',
    installationDate: '2023-07-15',
    responsiblePerson: '吴十',
    remarks: '检测CO, O2, CH4等'
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)
const handleSizeChange = (val: number) => {
  pageSize.value = val
  getDeviceList()
}
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getDeviceList()
}

// 选中数据
const selectedRows = ref([])
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 获取设备列表
const getDeviceList = async () => {
  loading.value = true
  try {
    // 模拟后端请求
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 在真实项目中，此处会发送searchForm中的条件到后端进行筛选
    // 这里可以添加模拟的本地筛选逻辑，但在实际应用中应该由后端完成

  } catch (error) {
    console.error('获取设备列表失败', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getDeviceList()
}

// 重置搜索
const resetSearch = () => {
  searchFormRef.value?.resetFields()
  currentPage.value = 1
  getDeviceList()
}

// 新增设备
const handleCreate = () => {
  deviceFormDialogRef.value?.open()
}

// 查看设备
const handleView = (row) => {
  deviceViewDialogRef.value?.open(row)
}

// 编辑设备
const handleEdit = (row) => {
  deviceFormDialogRef.value?.open(row)
}

// 设备检测
const handleInspection = (row) => {
  inspectionDialogRef.value?.open(row)
}

// 删除设备
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该设备？删除后无法恢复！', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500))
      // await deleteDeviceApi(row.id)
      ElMessage.success('删除成功')
      getDeviceList()
    } catch (error) {
      console.error('删除失败', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => { })
}

// 刷新
const handleRefresh = () => {
  getDeviceList()
}

// 导出Excel
const exportExcel = () => {
  ElMessage.success('导出成功')
}

// 初始化
onMounted(() => {
  getDeviceList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
}
</style>
