<template>
  <div class="assess-scheme-container">
    <el-card class="assess-scheme-card">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold text-lg">评估方案管理</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleAdd">
              <el-icon>
                <Plus />
              </el-icon>
              新建方案
            </el-button>
          </div>
        </div>
      </template>

      <div class="table-container">
        <!-- 搜索表单 -->
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="关键词">
              <el-input v-model="searchForm.keyword" placeholder="请输入方案名称" clearable style="width: 15rem"
                @keyup.enter="handleSearch" />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 7.5rem">
                <el-option label="启用中" value="启用中" />
                <el-option label="已失效" value="已失效" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon>
                  <Search />
                </el-icon>
                查询
              </el-button>
              <el-button @click="resetSearch">
                <el-icon>
                  <Refresh />
                </el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="table-wrapper">
          <el-table ref="tableRef" v-loading="loading" :data="paginatedTableData" border class="scheme-table"
            :max-height="tableMaxHeight">
            <el-table-column prop="name" label="方案名称" show-overflow-tooltip min-width="150" />
            <el-table-column prop="cycle" label="评估周期" align="center" min-width="100" />
            <el-table-column prop="startDate" label="启用时间" align="center" min-width="120" />
            <el-table-column prop="endDate" label="失效时间" align="center" min-width="120" />
            <el-table-column prop="status" label="状态" align="center" min-width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="方案说明" show-overflow-tooltip min-width="200">
              <template #default="{ row }">
                <span>{{ row.description || '无说明' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" min-width="200">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleEdit(row)">
                  编辑
                </el-button>
                <el-button type="info" link @click="handleDetail(row)">查看详情</el-button>

                <el-button type="danger" link @click="handleDelete(row)">
                  删除
                </el-button>
                <el-button v-if="row.status === '已失效'" type="success" link @click="handleEnable(row)">
                  启用
                </el-button>
                <el-button v-if="row.status === '启用中'" type="warning" link @click="handleDisable(row)">
                  失效
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="pagination-wrapper">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>
  </div>

  <!-- 方案编辑弹窗 -->
  <SchemeFormDialog v-model="dialogVisible" :dialog-type="dialogType" :form-data="formData"
    :indicator-library="indicatorLibrary" @submit="handleFormSubmit" @cancel="handleFormCancel" />

  <!-- 方案详情弹窗 -->
  <SchemeDetailDialog v-model="detailDialogVisible" :scheme-data="selectedItem" @edit="handleEditFromDetail" />
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import { SchemeFormDialog, SchemeDetailDialog } from './components'
import type { SchemeItem, SchemeFormData, Indicator } from './components'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 加载状态
const loading = ref(false)

// 表格最大高度
const tableMaxHeight = ref(600)

// 计算表格最大高度
const calculateTableHeight = () => {
  nextTick(() => {
    // 获取视窗高度
    const windowHeight = window.innerHeight
    // 预留给头部、搜索区域、分页等其他元素的高度
    const reservedHeight = 300 // 包括页面头部、卡片头部、搜索表单、分页等
    // 计算表格可用的最大高度
    const maxHeight = windowHeight - reservedHeight
    // 设置最小高度为400px，最大高度不超过计算值
    tableMaxHeight.value = Math.max(400, Math.min(maxHeight, 800))
  })
}



// 表格数据
const tableData = ref<SchemeItem[]>([
  {
    id: 'scheme_001',
    name: '月度评估方案',
    status: '启用中',
    cycle: '月度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '用于考核运营质量和效率的综合评估方案',
    projects: [
      {
        id: 'proj_001',
        name: '能耗指标',
        weight: 50,
        scoreType: '加权平均',
        fullScore: 100,
        indicators: [
          {
            id: 'ind_001',
            name: '单位能耗',
            weight: 70,
            formula: '总能耗 / 产出量',
            unit: 'kWh/m³',
            cycle: '月度',
            description: '单位处理水量的能源消耗'
          },
          {
            id: 'ind_002',
            name: '功率因数',
            weight: 30,
            formula: '有功功率 / 视在功率',
            unit: '无',
            cycle: '月度',
            description: '电力系统效率指标'
          }
        ]
      },
      {
        id: 'proj_002',
        name: '运行效率',
        weight: 30,
        scoreType: '打分制',
        fullScore: 100,
        indicators: [
          {
            id: 'ind_003',
            name: '设备运行率',
            formula: '运行时间 / 总时间 × 100',
            unit: '%',
            cycle: '周',
            description: '设备正常运行时间占比'
          }
        ]
      },
      {
        id: 'proj_003',
        name: '水质达标',
        weight: 20,
        scoreType: '打分制',
        fullScore: 100,
        indicators: [
          {
            id: 'ind_004',
            name: '出水水质达标率',
            formula: '达标次数 / 总检测次数 × 100',
            unit: '%',
            cycle: '天',
            description: '出水水质检测达标次数占比'
          }
        ]
      }
    ],
    createTime: '2024-01-01 10:00:00',
    creator: '张三'
  },
  {
    id: 'scheme_002',
    name: '季度评估方案',
    status: '启用中',
    cycle: '季度',
    startDate: '2024-04-01',
    endDate: '2024-12-31',
    description: '季度综合评估方案',
    projects: [],
    createTime: '2024-03-15 14:30:00',
    creator: '李四'
  },
  {
    id: 'scheme_003',
    name: '年度评估方案',
    status: '已失效',
    cycle: '年度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '年度评估方案',
    projects: [
      {
        id: 'proj_004',
        name: '管网完整性',
        weight: 60,
        scoreType: '加权平均',
        fullScore: 100,
        indicators: [
          {
            id: 'ind_005',
            name: '漏损率',
            weight: 100,
            formula: '(进水流量 - 出水流量) / 进水流量 × 100',
            unit: '%',
            cycle: '月度',
            description: '管网漏损水量占比'
          }
        ]
      },
      {
        id: 'proj_005',
        name: '供水质量',
        weight: 40,
        scoreType: '打分制',
        fullScore: 100,
        indicators: [
          {
            id: 'ind_006',
            name: '水压合格率',
            formula: '合格水压次数 / 总检测次数 × 100',
            unit: '%',
            cycle: '季度',
            description: '管网水压检测合格率'
          }
        ]
      }
    ],
    createTime: '2024-02-20 09:15:00',
    creator: '王五'
  },
  {
    id: 'scheme_004',
    name: '水质监测评估方案',
    status: '启用中',
    cycle: '天',
    startDate: '2024-02-01',
    endDate: '2024-12-31',
    description: '专注于水质指标的每日评估方案',
    projects: [],
    createTime: '2024-02-01 09:00:00',
    creator: '赵六'
  },
  {
    id: 'scheme_005',
    name: '设备运维评估方案',
    status: '启用中',
    cycle: '周',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '设备运行维护效果的每周评估',
    projects: [],
    createTime: '2024-01-15 14:30:00',
    creator: '孙七'
  },
  {
    id: 'scheme_006',
    name: '能耗管理评估方案',
    status: '已失效',
    cycle: '时',
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    description: '能源消耗管理的小时级评估方案',
    projects: [],
    createTime: '2023-01-01 10:00:00',
    creator: '周八'
  },
  {
    id: 'scheme_007',
    name: '安全生产评估方案',
    status: '启用中',
    cycle: '年度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '安全生产管理的年度综合评估',
    projects: [],
    createTime: '2024-01-01 08:00:00',
    creator: '吴九'
  },
  {
    id: 'scheme_008',
    name: '环保达标评估方案',
    status: '启用中',
    cycle: '季度',
    startDate: '2024-03-01',
    endDate: '2024-12-31',
    description: '环保指标达标情况的季度评估',
    projects: [],
    createTime: '2024-03-01 11:20:00',
    creator: '郑十'
  },
  {
    id: 'scheme_009',
    name: '成本控制评估方案',
    status: '已失效',
    cycle: '季度',
    startDate: '2023-04-01',
    endDate: '2023-12-31',
    description: '运营成本控制效果的季度评估',
    projects: [],
    createTime: '2023-04-01 15:45:00',
    creator: '冯十一'
  },
  {
    id: 'scheme_010',
    name: '客户服务评估方案',
    status: '启用中',
    cycle: '月度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '客户服务质量的月度评估方案',
    projects: [],
    createTime: '2024-01-10 13:15:00',
    creator: '陈十二'
  },
  {
    id: 'scheme_011',
    name: '技术创新评估方案',
    status: '启用中',
    cycle: '年度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '技术创新能力的年度评估',
    projects: [],
    createTime: '2024-01-05 16:30:00',
    creator: '蒋十三'
  },
  {
    id: 'scheme_012',
    name: '人员培训评估方案',
    status: '已失效',
    cycle: '年度',
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    description: '员工培训效果的年度评估',
    projects: [],
    createTime: '2023-01-01 12:00:00',
    creator: '韩十四'
  },
  {
    id: 'scheme_013',
    name: '应急响应评估方案',
    status: '启用中',
    cycle: '季度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '应急响应能力的季度评估',
    projects: [],
    createTime: '2024-01-20 10:45:00',
    creator: '杨十五'
  },
  {
    id: 'scheme_014',
    name: '信息化建设评估方案',
    status: '启用中',
    cycle: '年度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '信息化建设进展的年度评估',
    projects: [],
    createTime: '2024-02-15 14:20:00',
    creator: '朱十六'
  },
  {
    id: 'scheme_015',
    name: '供应链管理评估方案',
    status: '已失效',
    cycle: '月度',
    startDate: '2023-06-01',
    endDate: '2023-12-31',
    description: '供应链管理效率的月度评估',
    projects: [],
    createTime: '2023-06-01 09:30:00',
    creator: '秦十七'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 过滤后的表格数据
const filteredTableData = computed(() => {
  let data = tableData.value

  // 关键词搜索
  if (searchForm.keyword) {
    const keyword = searchForm.keyword.toLowerCase()
    data = data.filter(item =>
      item.name.toLowerCase().includes(keyword)
    )
  }

  // 状态筛选
  if (searchForm.status) {
    data = data.filter(item => item.status === searchForm.status)
  }



  return data
})

// 分页后的表格数据
const paginatedTableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTableData.value.slice(start, end)
})

// 总数
const total = computed(() => filteredTableData.value.length)

// 对话框相关
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const selectedItem = ref<SchemeItem | null>(null)



// 表格引用
const tableRef = ref()

// 表单数据
const formData = ref<SchemeFormData>({
  name: '',
  cycle: '月度',
  startDate: '',
  endDate: '',
  description: '',
  projects: []
})

// Mock 指标库数据
const indicatorLibrary = ref<Indicator[]>([
  // 能耗类指标
  {
    id: 'ind_001',
    name: '单位能耗',
    formula: '总能耗 / 产出量',
    unit: 'kWh/m³',
    cycle: '月度',
    description: '单位处理水量的能源消耗'
  },
  {
    id: 'ind_002',
    name: '功率因数',
    formula: '有功功率 / 视在功率',
    unit: '无',
    cycle: '月度',
    description: '电力系统效率指标'
  },
  {
    id: 'ind_013',
    name: '电能利用率',
    formula: '有效电能 / 总电能消耗 × 100',
    unit: '%',
    cycle: '月度',
    description: '电能有效利用程度'
  },
  {
    id: 'ind_014',
    name: '峰谷电价比',
    formula: '峰时电费 / 谷时电费',
    unit: '无',
    cycle: '月度',
    description: '峰谷电价使用优化指标'
  },


])

// 工具方法
const getStatusType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '草稿': 'info',
    '启用中': 'success',
    '已失效': 'danger'
  }
  return typeMap[status] || 'info'
}

// 搜索和重置
const handleSearch = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 500)
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  handleSearch()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 表单操作
const handleAdd = () => {
  dialogType.value = 'add'
  formData.value = {
    name: '',
    cycle: '月度',
    startDate: '',
    endDate: '',
    description: '',
    projects: []
  }
  dialogVisible.value = true
}

const handleEdit = (row: SchemeItem) => {
  dialogType.value = 'edit'
  selectedItem.value = row
  formData.value = {
    ...row,
    projects: row.projects.map(project => ({
      ...project,
      indicators: project.indicators.map(indicator => ({ ...indicator }))
    }))
  }
  dialogVisible.value = true
}

const handleDetail = (row: SchemeItem) => {
  selectedItem.value = row
  detailDialogVisible.value = true
}

const handleEditFromDetail = () => {
  detailDialogVisible.value = false
  if (selectedItem.value) {
    handleEdit(selectedItem.value)
  }
}



const handleDelete = (row: SchemeItem) => {
  ElMessageBox.confirm('确定要删除该方案吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      tableData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  })
}

const handleEnable = (row: SchemeItem) => {
  // 检查是否有其他启用中的方案
  const activeSchemes = tableData.value.filter(item =>
    item.status === '启用中' &&
    item.cycle === row.cycle &&
    item.id !== row.id
  )

  if (activeSchemes.length > 0) {
    ElMessageBox.confirm(
      `检测到已有${activeSchemes.length}个同类型的启用方案，启用此方案将自动失效其他方案。是否继续？`,
      '确认启用',
      {
        confirmButtonText: '确定启用',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      // 失效其他方案
      activeSchemes.forEach(scheme => {
        scheme.status = '已失效'
      })
      // 启用当前方案
      row.status = '启用中'
      ElMessage.success('方案启用成功')
    })
  } else {
    row.status = '启用中'
    ElMessage.success('方案启用成功')
  }
}

const handleDisable = (row: SchemeItem) => {
  ElMessageBox.confirm('确定要失效该方案吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = '已失效'
    ElMessage.success('方案已失效')
  })
}



// 新的事件处理方法
const handleFormSubmit = (data: SchemeFormData) => {
  if (dialogType.value === 'add') {
    // 新增方案
    const newScheme: SchemeItem = {
      id: Date.now().toString(),
      name: data.name,
      status: '启用中',
      cycle: data.cycle,
      startDate: data.startDate,
      endDate: data.endDate,
      description: data.description,
      projects: data.projects,
      createTime: new Date().toLocaleString(),
      creator: '当前用户'
    }
    tableData.value.unshift(newScheme)
    ElMessage.success('方案创建成功')
  } else {
    // 编辑方案
    const index = tableData.value.findIndex(item => item.id === selectedItem.value?.id)
    if (index !== -1) {
      tableData.value[index] = {
        ...tableData.value[index],
        ...data
      } as SchemeItem
      ElMessage.success('方案保存成功')
    }
  }

  dialogVisible.value = false
}

const handleFormCancel = () => {
  dialogVisible.value = false
  formData.value = {
    name: '',
    cycle: '月度',
    startDate: '',
    endDate: '',
    description: '',
    projects: []
  }
}

// 页面初始化
onMounted(() => {
  // 计算表格高度
  calculateTableHeight()

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight)
})

// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})
</script>

<style scoped lang="scss">
// 主容器样式
.assess-scheme-container {
  width: 100%;
  height: calc(100vh - 8rem);
  min-height: 40rem;
}

.assess-scheme-card {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.el-card__body) {
    flex: 1;
    padding: 1rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
}

.table-container {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: 25rem;
  overflow: hidden;
}

.table-wrapper {
  flex: 1;
  min-height: 20rem;
  overflow: hidden;

  .scheme-table {
    width: 100%;
  }
}

.pagination-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 0.0625rem solid #e4e7ed;
}

// 搜索表单样式
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

// 按钮图标样式优化
.el-button {
  .el-icon {
    margin-right: 0.25rem;
  }
}

// 响应式样式
@media (max-width: 75rem) {
  .assess-scheme-container {
    height: calc(100vh - 8rem);
  }

  .table-container {
    height: calc(100vh - 12rem);
  }
}

@media (max-width: 48rem) {
  .assess-scheme-container {
    height: calc(100vh - 6rem);
  }

  .table-container {
    height: calc(100vh - 10rem);
  }

  .pagination-wrapper {
    justify-content: center;
    margin-top: 0.75rem;
    padding-top: 0.75rem;
  }
}
</style>
