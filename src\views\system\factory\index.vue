<template>
  <div class="factory-page">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="厂站名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入厂站名称" clearable />
        </el-form-item>
        <el-form-item label="厂站类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择厂站类型" clearable style="width: 200px">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-select v-model="queryParams.isActive" placeholder="请选择状态" clearable style="width: 200px">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="primary" @click="handleAdd">新增厂站</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧厂站树 -->
      <el-card class="tree-card">
        <template #header>
          <div class="card-header">
            <span>厂站树</span>
            <div class="header-actions">
<!--              <el-button type="primary" link @click="toggleExpandAll">-->
<!--                {{ isExpandAll ? '折叠全部' : '展开全部' }}-->
<!--              </el-button>-->
            </div>
          </div>
        </template>

        <el-tree
          ref="treeRef"
          v-loading="treeLoading"
          :data="factoryTree"
          :props="treeProps"
          node-key="id"
          :expand-on-click-node="false"
          :default-expand-all="false"
          @node-click="handleNodeClick"
          class="factory-tree"
        >
          <template #default="{ data }">
            <div class="tree-node">
              <div class="node-content">
                <el-icon class="node-icon"><OfficeBuilding /></el-icon>
                <span class="node-label">{{ data.name }}</span>
<!--                <el-tag v-if="data.type" size="small" type="info" class="node-type">{{ data.type }}</el-tag>-->
<!--                <el-tag :type="data.isActive ? 'success' : 'danger'" size="small" class="node-status">-->
<!--                  {{ data.isActive ? '启用' : '禁用' }}-->
<!--                </el-tag>-->
              </div>
              <div class="node-actions" v-if="data.id">
                <el-button type="primary" link size="small" @click.stop="handleEdit(data)">编辑</el-button>
                <el-button type="danger" link size="small" @click.stop="handleDelete(data)">删除</el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </el-card>

      <!-- 右侧角色配置 -->
      <el-card class="role-card scrollable-role-card" v-if="selectedFactory">
        <template #header>
          <div class="card-header">
            <span class="module-title">{{ selectedFactory.name }} - 角色配置</span>
          </div>
        </template>
        <el-skeleton v-if="treeLoading" rows="6" animated style="padding: 32px 0;" />
        <el-collapse v-else v-model="activeModules">
          <el-collapse-item v-for="module in moduleConfigs" :key="module.code" :title="module.name" :name="module.code">
            <div class="role-section" v-for="role in module.roles" :key="role.code">
              <div class="role-header">
                <span class="role-name">{{ role.name }}</span>
                <el-button type="primary" link size="small" @click="handleAddUserRole(module, role)">添加</el-button>
              </div>
              <div class="user-list user-list-flex">
                <template v-if="userListLoadingMap[`${module.code}-${role.code}`]">
                  <el-icon class="is-loading"><Loading /></el-icon> 加载中...
                </template>
                <template v-else-if="userListCache[`${module.code}-${role.code}`] && userListCache[`${module.code}-${role.code}`].length">
                  <div class="user-chip" v-for="user in userListCache[`${module.code}-${role.code}`]" :key="user.id">
                    <el-avatar :size="24" v-if="user.avatar" :src="user.avatar" />
                    <el-avatar :size="24" v-else>{{ user.nickname?.charAt(0) || 'U' }}</el-avatar>
                    <span class="user-chip-name">{{ user.nickname || user.username }}</span>
                    <el-button type="danger" link size="small" class="user-chip-remove" @click="handleRemoveUserRole(user, module, role)">移除</el-button>
                  </div>
                </template>
                <span v-else class="empty-users">暂无用户</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-card>

      <!-- 未选择厂站时的提示 -->
      <el-card class="empty-card" v-else>
        <el-empty description="请选择左侧厂站查看角色配置" />
      </el-card>
    </div>

    <!-- 厂站新增/编辑对话框 -->
    <el-dialog :title="factoryDialogTitle" v-model="factoryDialogVisible" width="650px" :close-on-click-modal="false">
      <el-form ref="factoryFormRef" :model="factoryForm" :rules="factoryRules" label-width="100px">
        <el-form-item label="厂站名称" prop="name">
          <el-input v-model="factoryForm.name" placeholder="请输入厂站名称" />
        </el-form-item>
        <el-form-item label="父级厂站" prop="parentId">
          <el-select v-model="factoryForm.parentId" placeholder="请选择父级厂站" clearable style="width: 100%">
            <el-option v-for="item in availableParentFactories" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="厂站类型" prop="type">
          <el-select v-model="factoryForm.type" placeholder="请选择厂站类型" style="width: 100%">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="厂站级别" prop="level">
          <el-select v-model="factoryForm.level" placeholder="请选择厂站级别" style="width: 100%">
            <el-option label="集团级" :value="1" />
            <el-option label="区域级" :value="2" />
            <el-option label="单厂级" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="factoryForm.orderNum" :min="0" :max="9999" controls-position="right" style="width: 100%" />
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-switch v-model="factoryForm.isActive" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="factoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleFactorySubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 人员选择器 -->
    <UserSelectForm ref="userSelectFormRef" @confirm="handleUserSelectConfirm" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { OfficeBuilding, Loading } from '@element-plus/icons-vue'
import { SystemFactoryApi, SystemFactoryUserRoleApi } from '@/api/system/factory'
import type {
  FactorySaveReqVO,
  FactoryTreeVO,
  FactoryUserRoleInsertReqVO,
  FactoryUserRoleSearchReqVO,
  FactoryUserRoleInfoRespVO
} from '@/api/system/factory'
import * as UserApi from '@/api/system/user'
import UserSelectForm from '@/components/UserSelectForm/index.vue'

defineOptions({ name: 'Factory' })

// 厂站类型选项
const typeOptions = [
  { label: '地表水', value: '地表水' },
  { label: '地下水', value: '地下水' },
  { label: '净水厂', value: '净水厂' },
  { label: '污水厂', value: '污水厂' },
  { label: '排涝泵站', value: '排涝泵站' }
]

// 模块角色配置
const moduleConfigs = [
  {
    code: 'system',
    name: '系统模块',
    roles: [
      { code: 'select', name: '全局厂站选择' }
    ]
  },
  {
    code: 'report',
    name: '报表模块',
    roles: [
      { code: 'filler', name: '填报人' },
      { code: 'auditor', name: '审核人' }
    ]
  }

]

// 查询参数
const queryParams = reactive({
  name: '',
  type: '',
  isActive: undefined as boolean | undefined
})

// 树形数据
const treeLoading = ref(false)
const factoryTree = ref<FactoryTreeVO[]>([])
const treeRef = ref()
const isExpandAll = ref(false)
const treeProps = {
  children: 'children',
  label: 'name'
}

// 选中的厂站
const selectedFactory = ref<FactoryTreeVO | null>(null)

// 用户角色关联数据
const userRoleList = ref<FactoryUserRoleInfoRespVO[]>([])

// 厂站表单相关
const factoryDialogVisible = ref(false)
const factoryDialogTitle = ref('')
const factoryForm = reactive<FactorySaveReqVO>({
  name: '',
  type: '',
  level: 3,
  parentId: undefined,
  orderNum: 0,
  isActive: true
})

const factoryRules = {
  name: [
    { required: true, message: '请输入厂站名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择厂站类型', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择厂站级别', trigger: 'change' }
  ]
}

const factoryFormRef = ref()

// 用户选择相关
const currentModule = ref<any>(null)
const currentRole = ref<any>(null)

// 用户列表缓存
const userListCache = ref<Record<string, UserApi.UserVO[]>>({})

// 人员选择器ref
const userSelectFormRef = ref()

// 用户列表加载状态
const userListLoadingMap = ref<Record<string, boolean>>({})

// 扁平化厂站列表（用于父级选择）
const flattenFactoryList = computed(() => {
  const result: FactoryTreeVO[] = []

  const flatten = (nodes: FactoryTreeVO[]) => {
    nodes.forEach(node => {
      const { children, ...rest } = node
      result.push({ ...rest, children: [] })
      if (children && children.length > 0) {
        flatten(children)
      }
    })
  }

  flatten(factoryTree.value)
  return result
})

// 可选的父级厂站（排除自己及其子厂站）
const availableParentFactories = computed(() => {
  if (!factoryForm.id) {
    return flattenFactoryList.value
  }

  return flattenFactoryList.value.filter(factory => {
    if (factory.id === factoryForm.id) return false
    // 这里可以添加更复杂的子厂站排除逻辑
    return true
  })
})

// 折叠面板展开项
const activeModules = ref<string[]>([])

// 获取厂站树
const getFactoryTree = async () => {
  treeLoading.value = true
  try {
    const params = {
      name: queryParams.name,
      type: queryParams.type,
      isActive: queryParams.isActive?.toString()
    }
    const res = await SystemFactoryApi.getFactoryTree(params)
    if (res.code === 0) {
      factoryTree.value = res.data || []
    } else {
      ElMessage.error(res.msg || '获取厂站树失败')
    }
  } catch (error) {
    console.error('获取厂站树失败:', error)
    ElMessage.error('获取厂站树失败')
  } finally {
    treeLoading.value = false
  }
}

// 获取用户角色关联列表
const getUserRoleList = async (factoryId: number, onlyInit = false, moduleCode?: string) => {
  try {
    if (onlyInit) {
      userRoleList.value = []
      userListCache.value = {}
      userListLoadingMap.value = {}
      return
    }
    if (!moduleCode) {
      // 查全部
      for (const module of moduleConfigs) {
        for (const role of module.roles) {
          const cacheKey = `${module.code}-${role.code}`
          userListLoadingMap.value[cacheKey] = true
        }
      }
      const res = await SystemFactoryUserRoleApi.listByFactoryIdAndModuleCode({ factoryId })
      userRoleList.value = res.data || []
      await updateUserListCache()
      for (const module of moduleConfigs) {
        for (const role of module.roles) {
          const cacheKey = `${module.code}-${role.code}`
          userListLoadingMap.value[cacheKey] = false
        }
      }
    } else {
      // 查单模块
      for (const role of moduleConfigs.find(m => m.code === moduleCode)?.roles || []) {
        const cacheKey = `${moduleCode}-${role.code}`
        userListLoadingMap.value[cacheKey] = true
      }
      const res = await SystemFactoryUserRoleApi.listByFactoryIdAndModuleCode({ factoryId, moduleCode })
      const newList = res.data || []
      userRoleList.value = userRoleList.value.filter(item => item.moduleCode !== moduleCode).concat(newList)
      await updateUserListCache()
      for (const role of moduleConfigs.find(m => m.code === moduleCode)?.roles || []) {
        const cacheKey = `${moduleCode}-${role.code}`
        userListLoadingMap.value[cacheKey] = false
      }
    }
  } catch (error) {
    ElMessage.error('获取用户角色关联失败')
  }
}

// 更新用户列表缓存
const updateUserListCache = async () => {
  try {
    // 获取所有用户列表
    const allUsers = await UserApi.getSimpleUserList()

    // 为每个模块和角色组合缓存用户信息
    for (const module of moduleConfigs) {
      for (const role of module.roles) {
        const userRoleIds = userRoleList.value
          .filter(item => item.moduleCode === module.code && item.roleCode === role.code)
          .map(item => item.userId)

        const cacheKey = `${module.code}-${role.code}`
        userListCache.value[cacheKey] = allUsers.filter(user => userRoleIds.includes(user.id))
      }
    }
  } catch (error) {
    console.error('更新用户列表缓存失败:', error)
  }
}

// 获取部门名称
const getDeptName = (deptId: number) => {
  const deptMap: Record<number, string> = {
    1: '技术部',
    2: '运营部',
    3: '管理部'
  }
  return deptMap[deptId] || '未知部门'
}

// 查询
const handleQuery = () => {
  getFactoryTree()
}

// 重置查询
const resetQuery = () => {
  queryParams.name = ''
  queryParams.type = ''
  queryParams.isActive = undefined
  handleQuery()
}

// 展开/折叠全部
const toggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value
  if (treeRef.value) {
    if (isExpandAll.value) {
      expandAllNodes()
    } else {
      collapseAllNodes()
    }
  }
}

// 展开所有节点
const expandAllNodes = () => {
  const expandedKeys = getAllKeys(factoryTree.value)
  expandedKeys.forEach(key => {
    treeRef.value.setExpanded(key, true)
  })
}

// 折叠所有节点
const collapseAllNodes = () => {
  const expandedKeys = getAllKeys(factoryTree.value)
  expandedKeys.forEach(key => {
    treeRef.value.setExpanded(key, false)
  })
}

// 获取所有节点的ID
const getAllKeys = (data: any[]): number[] => {
  const keys: number[] = []
  const getKeys = (items: any[]) => {
    items.forEach(item => {
      keys.push(item.id)
      if (item.children && item.children.length > 0) {
        getKeys(item.children)
      }
    })
  }
  getKeys(data)
  return keys
}

// 节点点击
const handleNodeClick = async (data: FactoryTreeVO) => {
  selectedFactory.value = data
  treeLoading.value = true
  activeModules.value = [] // 默认全部折叠
  // 只查基本信息，不查所有用户
  await getUserRoleList(data.id, true)
  treeLoading.value = false
}

// 新增厂站
const handleAdd = () => {
  factoryDialogTitle.value = '新增厂站'
  factoryForm.id = undefined
  factoryForm.name = ''
  factoryForm.type = ''
  factoryForm.level = 3
  factoryForm.parentId = undefined
  factoryForm.orderNum = 0
  factoryForm.isActive = true
  factoryDialogVisible.value = true
}

// 编辑厂站
const handleEdit = async (data: FactoryTreeVO) => {
  try {
    const res = await SystemFactoryApi.getFactory(data.id)
    if (res.code === 0) {
      Object.assign(factoryForm, res.data)
      factoryDialogTitle.value = '编辑厂站'
      factoryDialogVisible.value = true
    } else {
      ElMessage.error(res.msg || '获取厂站详情失败')
    }
  } catch (error) {
    console.error('获取厂站详情失败:', error)
    ElMessage.error('获取厂站详情失败')
  }
}

// 删除厂站
const handleDelete = (data: FactoryTreeVO) => {
  ElMessageBox.confirm(`确认删除厂站"${data.name}"吗？`, '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      const res = await SystemFactoryApi.deleteFactory(data.id)
      if (res.code === 0) {
        ElMessage.success('删除成功')
        getFactoryTree()
        if (selectedFactory.value?.id === data.id) {
          selectedFactory.value = null
        }
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

// 提交厂站表单
const handleFactorySubmit = async () => {
  if (!factoryFormRef.value) return

  try {
    await factoryFormRef.value.validate()
    const api = factoryForm.id ? SystemFactoryApi.updateFactory : SystemFactoryApi.createFactory
    const res = await api(factoryForm)

    if (res.code === 0) {
      ElMessage.success(factoryForm.id ? '修改成功' : '新增成功')
      factoryDialogVisible.value = false
      getFactoryTree()
    } else {
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

// 添加用户角色
const handleAddUserRole = async (module: any, role?: any) => {
  if (!selectedFactory.value) {
    ElMessage.warning('请先选择厂站')
    return
  }

  currentModule.value = module
  currentRole.value = role || module.roles[0]

  // 获取当前角色已选择的用户
  const cacheKey = `${module.code}-${currentRole.value.code}`
  const currentUsers = userListCache.value[cacheKey] || []

  // 打开人员选择器
  userSelectFormRef.value?.open(0, currentUsers)
}

// 移除用户角色
const handleRemoveUserRole = async (user: any, module: any, role: any) => {
  ElMessageBox.confirm(`确认移除用户"${user.nickname}"的${module.name}-${role.name}角色吗？`, '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      // 这里需要调用删除接口
      const userRole = userRoleList.value.find(item =>
        item.userId === user.id &&
        item.moduleCode === module.code &&
        item.roleCode === role.code
      )

      if (userRole) {
        const res = await SystemFactoryUserRoleApi.deleteFactoryUserRole(userRole.id)
        if (res.code === 0) {
          ElMessage.success('移除成功')
          await getUserRoleList(selectedFactory.value!.id)
        } else {
          ElMessage.error(res.msg || '移除失败')
        }
      }
    } catch (error) {
      console.error('移除失败:', error)
      ElMessage.error('移除失败')
    }
  })
}

// 用户选择确认
const handleUserSelectConfirm = async (type: number, selectedUsers: UserApi.UserVO[]) => {
  if (!selectedFactory.value || !currentModule.value || !currentRole.value) {
    return
  }

  try {
    // 获取当前角色已有的用户关联
    const cacheKey = `${currentModule.value.code}-${currentRole.value.code}`
    const currentUserIds = userListCache.value[cacheKey]?.map(user => user.id) || []
    const newUserIds = selectedUsers.map(user => user.id)

    // 找出需要新增的用户
    const usersToAdd = selectedUsers.filter(user => !currentUserIds.includes(user.id))

    // 找出需要删除的用户
    const usersToRemove = userListCache.value[cacheKey]?.filter(user => !newUserIds.includes(user.id)) || []

    // 批量添加新用户
    if (usersToAdd.length > 0) {
      const addPromises = usersToAdd.map(user =>
        SystemFactoryUserRoleApi.addFactoryUserRole({
          factoryId: selectedFactory.value!.id,
          userId: user.id,
          moduleCode: currentModule.value.code,
          roleCode: currentRole.value.code
        })
      )
      await Promise.all(addPromises)
    }

    // 批量删除移除的用户
    if (usersToRemove.length > 0) {
      const removePromises = usersToRemove.map(user => {
        const userRole = userRoleList.value.find(item =>
          item.userId === user.id &&
          item.moduleCode === currentModule.value.code &&
          item.roleCode === currentRole.value.code
        )
        return userRole ? SystemFactoryUserRoleApi.deleteFactoryUserRole(userRole.id) : Promise.resolve()
      })
      await Promise.all(removePromises)
    }

    ElMessage.success('用户配置更新成功')

    // 重新获取用户角色关联并更新缓存
    await getUserRoleList(selectedFactory.value.id)
  } catch (error) {
    console.error('更新用户配置失败:', error)
    ElMessage.error('更新用户配置失败')
  }
}

// 监听折叠面板展开，展开时查该模块
watch(activeModules, async (val, oldVal) => {
  if (!selectedFactory.value) return
  const opened = val.filter(v => !oldVal.includes(v))
  if (opened.length) {
    await getUserRoleList(selectedFactory.value.id, false, opened[0])
  }
})

onMounted(() => {
  getFactoryTree()
})
</script>

<style scoped lang="scss">
.factory-page {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .search-card {
    margin-bottom: 20px;
    flex-shrink: 0;
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;

    .tree-card {
      width: 400px;
      flex-shrink: 0;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .factory-tree {
        .tree-node {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          padding: 4px 0;

          .node-content {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;

            .node-icon {
              color: #409eff;
            }

            .node-label {
              font-weight: 500;
            }

            .node-type {
              font-size: 12px;
            }

            .node-status {
              font-size: 12px;
            }
          }

          .node-actions {
            opacity: 0;
            transition: opacity 0.2s;
          }

          &:hover .node-actions {
            opacity: 1;
          }
        }
      }
    }

    .role-card {
      flex: 1;
      min-width: 0;

      .module-roles {
        .module-section {
          margin-bottom: 24px;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          overflow: hidden;

          .module-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f5f7fa;
            border-bottom: 1px solid #e4e7ed;

            h4 {
              margin: 0;
              color: #303133;
              font-size: 16px;
            }
          }

          .role-section {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .role-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .role-name {
                font-weight: 500;
                color: #606266;
              }
            }

            .user-list {
              .empty-users {
                color: #909399;
                text-align: center;
                padding: 20px;
                background-color: #fafafa;
                border-radius: 4px;
              }

              .user-items {
                .user-item {
                  display: flex;
                  align-items: center;
                  gap: 12px;
                  padding: 8px 12px;
                  border-radius: 4px;
                  margin-bottom: 8px;
                  background-color: #f8f9fa;
                  border: 1px solid #e9ecef;

                  .user-name {
                    flex: 1;
                    font-weight: 500;
                  }
                }
              }
            }
          }
        }
      }
    }

    .empty-card {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .scrollable-role-card {
    max-height: 80vh;
    overflow-y: auto;
  }
}
.module-title {
  font-size: 18px;
  font-weight: bold;
  color: #222;
}
.role-header {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 8px;
  color: #2d8cf0;
}
.role-name {
  font-weight: bold;
  font-size: 15px;
  margin-right: 12px;
}
.user-list-flex {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}
.user-chip {
  display: flex;
  align-items: center;
  background: #f4f8fb;
  border-radius: 16px;
  padding: 2px 10px 2px 4px;
  margin-bottom: 4px;
  font-size: 14px;
  color: #333;
}
.user-chip-name {
  margin: 0 4px 0 4px;
  font-weight: 500;
}
.user-chip-remove {
  margin-left: 2px;
  color: #f56c6c;
  font-size: 13px;
}
.empty-users {
  color: #bbb;
  font-size: 14px;
  margin: 4px 0;
}
</style>
