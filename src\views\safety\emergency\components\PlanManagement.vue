<template>
  <div class="plan-management">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="预案名称">
          <el-input v-model="searchForm.name" placeholder="请输入预案名称" />
        </el-form-item>
        <el-form-item label="适用范围">
          <el-select v-model="searchForm.scope" placeholder="请选择适用范围">
            <el-option label="全公司" value="company" />
            <el-option label="部门" value="department" />
            <el-option label="工作区域" value="workspace" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleAdd">新增预案</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="name" label="预案名称" />
      <el-table-column prop="scope" label="适用范围" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="updateTime" label="更新时间" />
      <el-table-column label="操作" width="280">
        <template #default="scope">
          <el-button type="primary" link @click="handleView(scope.row)">查看</el-button>
          <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="primary" link @click="handleDownload(scope.row)">下载</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 弹窗 -->
    <plan-dialog
      v-model="dialogVisible"
      :dialog-type="dialogType"
      :plan-data="currentPlan"
      @submit="handleDialogSubmit"
    />
  </div>
</template>

<script>
import PlanDialog from '../dialogs/PlanDialog.vue'

export default {
  name: 'PlanManagement',
  components: {
    PlanDialog
  },
  data() {
    return {
      searchForm: {
        name: '',
        scope: ''
      },
      tableData: [
        {
          id: 1,
          name: '火灾应急预案',
          scope: 'company',
          createTime: '2024-03-15 10:00:00',
          updateTime: '2024-03-15 10:00:00',
          process: '1. 发现火情立即报警\n2. 组织人员疏散\n3. 启动消防设施\n4. 通知相关部门',
          description: '适用于公司范围内的火灾事故应急处置'
        },
        {
          id: 2,
          name: '危险品泄露应急预案',
          scope: 'department',
          createTime: '2024-03-14 14:30:00',
          updateTime: '2024-03-14 16:45:00',
          process: '1. 确认泄露源\n2. 隔离危险区域\n3. 穿戴防护装备\n4. 进行泄露处置',
          description: '适用于化学品仓库的泄露事故处置'
        },
        {
          id: 3,
          name: '自然灾害应急预案',
          scope: 'company',
          createTime: '2024-03-13 09:15:00',
          updateTime: '2024-03-13 11:20:00',
          process: '1. 监测预警\n2. 人员转移\n3. 物资调配\n4. 灾后恢复',
          description: '适用于地震、台风等自然灾害的应急处置'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 3,
      dialogVisible: false,
      dialogType: 'add',
      currentPlan: {}
    }
  },
  methods: {
    handleSearch() {
      // 实现搜索逻辑
      console.log('搜索条件：', this.searchForm)
    },
    resetSearch() {
      this.searchForm = {
        name: '',
        scope: ''
      }
    },
    handleAdd() {
      this.dialogType = 'add'
      this.currentPlan = {}
      this.dialogVisible = true
    },
    handleView(row) {
      this.dialogType = 'view'
      this.currentPlan = { ...row }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentPlan = { ...row }
      this.dialogVisible = true
    },
    handleDownload(row) {
      // 下载预案
      console.log('下载预案：', row)
    },
    handleDelete(row) {
      // 删除预案
      this.$confirm('确定要删除该预案吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('删除预案：', row)
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    fetchData() {
      // 获取表格数据
      console.log('获取数据，页码：', this.currentPage, '每页条数：', this.pageSize)
    },
    handleDialogSubmit(formData) {
      // 处理弹窗提交
      console.log('提交数据：', formData)
      if (this.dialogType === 'add') {
        this.$message.success('新增成功')
      } else if (this.dialogType === 'edit') {
        this.$message.success('修改成功')
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.plan-management {
  .search-area {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 