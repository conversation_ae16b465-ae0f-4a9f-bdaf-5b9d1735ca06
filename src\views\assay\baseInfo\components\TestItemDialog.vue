<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="800px" :close-on-click-modal="false">
    <!-- 步骤指示器 -->
    <div class="step-container">
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="基本信息" description="项目名称、代码等基础信息" />
        <el-step title="标准配置" description="检测标准、阈值等配置" />
        <el-step title="检测设置" description="仪器、频率等检测参数" />
        <el-step title="确认提交" description="检查信息并提交" />
      </el-steps>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <el-form
        ref="formRef"
        v-loading="formLoading"
        :model="formData"
        :rules="getCurrentStepRules()"
        label-width="120px"
        class="step-form"
      >
        <!-- 第一步：基本信息 -->
        <div v-show="currentStep === 0" class="step-content">
          <div class="step-header">
            <h3>基本信息</h3>
            <p class="step-description">请填写检测项目的基本信息</p>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="项目名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入项目名称"
                  :prefix-icon="Document"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目代码" prop="code">
                <el-input
                  v-model="formData.code"
                  placeholder="请输入项目代码"
                  :prefix-icon="Key"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="检测方法" prop="method">
                <el-input
                  v-model="formData.method"
                  placeholder="请输入检测方法"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位" prop="unit">
                <el-input
                  v-model="formData.unit"
                  placeholder="如：mg/L、%等"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="项目描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请简要描述该检测项目的用途和意义"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 第二步：标准配置 -->
        <div v-show="currentStep === 1" class="step-content">
          <div class="step-header">
            <h3>标准配置</h3>
            <p class="step-description">配置检测标准和阈值参数</p>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="标准类型" prop="standardType">
                <el-select v-model="formData.standardType" placeholder="请选择标准类型" style="width: 100%">
                  <el-option label="国家标准" value="national">
                    <div class="option-item">
                      <span>国家标准</span>
                      <el-tag size="small" type="danger">GB</el-tag>
                    </div>
                  </el-option>
                  <el-option label="行业标准" value="industry">
                    <div class="option-item">
                      <span>行业标准</span>
                      <el-tag size="small" type="warning">HJ</el-tag>
                    </div>
                  </el-option>
                  <el-option label="地方标准" value="local">
                    <div class="option-item">
                      <span>地方标准</span>
                      <el-tag size="small" type="info">DB</el-tag>
                    </div>
                  </el-option>
                  <el-option label="企业标准" value="enterprise">
                    <div class="option-item">
                      <span>企业标准</span>
                      <el-tag size="small">Q</el-tag>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标准编号" prop="standardCode">
                <el-input
                  v-model="formData.standardCode"
                  placeholder="如：GB3838-2002"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="标准值范围" prop="standard">
                <el-input
                  v-model="formData.standard"
                  placeholder="如：≤10 mg/L"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报警阈值" prop="alarmThreshold">
                <el-input
                  v-model="formData.alarmThreshold"
                  placeholder="超过此值将报警"
                  clearable
                />
                <div class="field-tip">
                  <el-icon><InfoFilled /></el-icon>
                  <span>建议设置为标准值的80%-90%</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="精度要求" prop="precision">
            <el-input
              v-model="formData.precision"
              placeholder="如：0.01"
              style="width: 200px"
              clearable
            />
            <span class="input-suffix">{{ formData.unit || '单位' }}</span>
          </el-form-item>
        </div>

        <!-- 第三步：检测设置 -->
        <div v-show="currentStep === 2" class="step-content">
          <div class="step-header">
            <h3>检测设置</h3>
            <p class="step-description">配置检测仪器、频率等参数</p>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="采样频率" prop="frequency">
                <el-select v-model="formData.frequency" placeholder="请选择采样频率" style="width: 100%">
                  <el-option label="每日" value="daily">
                    <div class="frequency-option">
                      <span>每日</span>
                      <el-tag size="small" type="danger">高频</el-tag>
                    </div>
                  </el-option>
                  <el-option label="每周" value="weekly">
                    <div class="frequency-option">
                      <span>每周</span>
                      <el-tag size="small" type="warning">中频</el-tag>
                    </div>
                  </el-option>
                  <el-option label="每月" value="monthly">
                    <div class="frequency-option">
                      <span>每月</span>
                      <el-tag size="small" type="info">低频</el-tag>
                    </div>
                  </el-option>
                  <el-option label="季度" value="quarterly">
                    <div class="frequency-option">
                      <span>季度</span>
                      <el-tag size="small">极低频</el-tag>
                    </div>
                  </el-option>
                  <el-option label="不定期" value="irregular">
                    <div class="frequency-option">
                      <span>不定期</span>
                      <el-tag size="small" type="success">按需</el-tag>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据来源" prop="dataSource">
                <el-radio-group v-model="formData.dataSource">
                  <el-radio label="manual">
                    <el-icon><Edit /></el-icon>人工录入
                  </el-radio>
                  <el-radio label="auto">
                    <el-icon><Monitor /></el-icon>自动采集
                  </el-radio>
                  <el-radio label="both">
                    <el-icon><Connection /></el-icon>两者结合
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="检测仪器" prop="equipment">
            <el-select
              v-model="formData.equipment"
              multiple
              filterable
              allow-create
              placeholder="请选择或输入检测仪器"
              style="width: 100%"
            >
              <el-option-group label="常用仪器">
                <el-option label="COD分析仪" value="cod_analyzer">
                  <div class="equipment-option">
                    <span>COD分析仪</span>
                    <el-tag size="small" type="warning">化学需氧量</el-tag>
                  </div>
                </el-option>
                <el-option label="BOD测定仪" value="bod_analyzer">
                  <div class="equipment-option">
                    <span>BOD测定仪</span>
                    <el-tag size="small" type="info">生化需氧量</el-tag>
                  </div>
                </el-option>
                <el-option label="pH计" value="ph_meter">
                  <div class="equipment-option">
                    <span>pH计</span>
                    <el-tag size="small" type="success">酸碱度</el-tag>
                  </div>
                </el-option>
              </el-option-group>
              <el-option-group label="精密仪器">
                <el-option label="分光光度计" value="spectrophotometer">
                  <div class="equipment-option">
                    <span>分光光度计</span>
                    <el-tag size="small" type="danger">光谱分析</el-tag>
                  </div>
                </el-option>
                <el-option label="气相色谱仪" value="gc">
                  <div class="equipment-option">
                    <span>气相色谱仪</span>
                    <el-tag size="small" type="primary">色谱分析</el-tag>
                  </div>
                </el-option>
              </el-option-group>
            </el-select>
            <div class="field-tip">
              <el-icon><InfoFilled /></el-icon>
              <span>可多选，支持输入自定义仪器名称</span>
            </div>
          </el-form-item>

          <el-form-item label="关联计划" prop="relatedPlans">
            <el-checkbox-group v-model="formData.relatedPlans">
              <el-checkbox label="regularPlan">
                <div class="plan-option">
                  <el-icon><Calendar /></el-icon>
                  <span>常规检测计划</span>
                </div>
              </el-checkbox>
              <el-checkbox label="temporaryPlan">
                <div class="plan-option">
                  <el-icon><Clock /></el-icon>
                  <span>临时检测计划</span>
                </div>
              </el-checkbox>
              <el-checkbox label="monitoringPlan">
                <div class="plan-option">
                  <el-icon><View /></el-icon>
                  <span>在线监测</span>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="formData.status">
                  <el-radio :label="0">
                    <el-icon><Check /></el-icon>启用
                  </el-radio>
                  <el-radio :label="1">
                    <el-icon><Close /></el-icon>停用
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 第四步：确认提交 -->
        <div v-show="currentStep === 3" class="step-content">
          <div class="step-header">
            <h3>确认信息</h3>
            <p class="step-description">请确认以下信息无误后提交</p>
          </div>

          <div class="confirmation-content">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="项目名称">{{ formData.name || '-' }}</el-descriptions-item>
              <el-descriptions-item label="项目代码">{{ formData.code || '-' }}</el-descriptions-item>
              <el-descriptions-item label="检测方法">{{ formData.method || '-' }}</el-descriptions-item>
              <el-descriptions-item label="单位">{{ formData.unit || '-' }}</el-descriptions-item>
              <el-descriptions-item label="标准类型">{{ getStandardTypeLabel(formData.standardType) }}</el-descriptions-item>
              <el-descriptions-item label="标准编号">{{ formData.standardCode || '-' }}</el-descriptions-item>
              <el-descriptions-item label="标准值范围">{{ formData.standard || '-' }}</el-descriptions-item>
              <el-descriptions-item label="报警阈值">{{ formData.alarmThreshold || '-' }}</el-descriptions-item>
              <el-descriptions-item label="精度要求">{{ formData.precision || '-' }}</el-descriptions-item>
              <el-descriptions-item label="采样频率">{{ getFrequencyLabel(formData.frequency) }}</el-descriptions-item>
              <el-descriptions-item label="数据来源">{{ getDataSourceLabel(formData.dataSource) }}</el-descriptions-item>
              <el-descriptions-item label="状态">{{ formData.status === 0 ? '启用' : '停用' }}</el-descriptions-item>
              <el-descriptions-item label="检测仪器" :span="2">
                <el-tag v-for="item in formData.equipment" :key="item" size="small" class="mr-1">{{ item }}</el-tag>
                <span v-if="!formData.equipment || formData.equipment.length === 0">-</span>
              </el-descriptions-item>
              <el-descriptions-item label="关联计划" :span="2">
                <el-tag v-for="plan in formData.relatedPlans" :key="plan" size="small" type="success" class="mr-1">
                  {{ getPlanLabel(plan) }}
                </el-tag>
                <span v-if="!formData.relatedPlans || formData.relatedPlans.length === 0">-</span>
              </el-descriptions-item>
              <el-descriptions-item label="项目描述" :span="2">{{ formData.description || '-' }}</el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ formData.remark || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
        <el-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="currentStep === 3" :loading="formLoading" type="primary" @click="submitForm">
          <el-icon><Check /></el-icon>确认提交
        </el-button>
      </div>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules, FormInstance } from 'element-plus'
import {
  Document, Key, InfoFilled, Edit, Monitor, Connection,
  Calendar, Clock, View, Check, Close
} from '@element-plus/icons-vue'

defineOptions({ name: 'TestItemDialog' })

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')
const currentStep = ref(0)

// 表单数据
const formData = ref({
  id: undefined,
  name: '',
  code: '',
  method: '',
  standardType: '',
  standardCode: '',
  standard: '',
  alarmThreshold: '',
  unit: '',
  precision: '',
  frequency: '',
  equipment: [],
  dataSource: 'manual',
  relatedPlans: [],
  status: 0,
  remark: '',
  description: ''
})

// 分步骤表单校验规则
const stepRules = {
  // 第一步：基本信息
  step0: {
    name: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
    code: [{ required: true, message: '项目代码不能为空', trigger: 'blur' }],
    method: [{ required: true, message: '检测方法不能为空', trigger: 'blur' }],
    unit: [{ required: true, message: '单位不能为空', trigger: 'blur' }]
  },
  // 第二步：标准配置
  step1: {
    standardType: [{ required: true, message: '标准类型不能为空', trigger: 'change' }],
    standard: [{ required: true, message: '标准值范围不能为空', trigger: 'blur' }],
    precision: [{ required: true, message: '精度要求不能为空', trigger: 'blur' }]
  },
  // 第三步：检测设置
  step2: {
    frequency: [{ required: true, message: '采样频率不能为空', trigger: 'change' }],
    dataSource: [{ required: true, message: '数据来源不能为空', trigger: 'change' }]
  },
  // 第四步：确认提交（无额外验证）
  step3: {}
}

// 获取当前步骤的验证规则
const getCurrentStepRules = () => {
  return stepRules[`step${currentStep.value}` as keyof typeof stepRules] || {}
}

const formRef = ref()
const emit = defineEmits(['success'])

// 步骤导航方法
const nextStep = async () => {
  // 验证当前步骤
  if (formRef.value) {
    try {
      await formRef.value.validate()
      if (currentStep.value < 3) {
        currentStep.value++
      }
    } catch (error) {
      ElMessage.warning('请完善当前步骤的必填信息')
    }
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 标签转换方法
const getStandardTypeLabel = (type: string) => {
  const map: Record<string, string> = {
    national: '国家标准',
    industry: '行业标准',
    local: '地方标准',
    enterprise: '企业标准'
  }
  return map[type] || type
}

const getFrequencyLabel = (frequency: string) => {
  const map: Record<string, string> = {
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    quarterly: '季度',
    irregular: '不定期'
  }
  return map[frequency] || frequency
}

const getDataSourceLabel = (source: string) => {
  const map: Record<string, string> = {
    manual: '人工录入',
    auto: '自动采集',
    both: '两者结合'
  }
  return map[source] || source
}

const getPlanLabel = (plan: string) => {
  const map: Record<string, string> = {
    regularPlan: '常规检测计划',
    temporaryPlan: '临时检测计划',
    monitoringPlan: '在线监测'
  }
  return map[plan] || plan
}

// 打开对话框
const open = async (type: string, data?: any) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增检测项目' : '编辑检测项目'
  formType.value = type
  currentStep.value = 0 // 重置步骤
  resetForm()

  // 如果是编辑模式，设置表单数据
  if (type === 'update' && data) {
    // 深拷贝，避免直接修改原始数据
    formData.value = JSON.parse(JSON.stringify(data))
    // 确保数组类型的字段有默认值
    if (!formData.value.equipment) formData.value.equipment = []
    if (!formData.value.relatedPlans) formData.value.relatedPlans = []
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据流转到检测项目库
    console.log('检测项目数据入库:', formData.value)
    // 如果有选择关联计划，则模拟数据流转到计划制定模块
    if (formData.value.relatedPlans && formData.value.relatedPlans.length > 0) {
      console.log('数据流转到计划制定模块:', formData.value.relatedPlans)
    }
    
    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    code: '',
    method: '',
    standardType: '',
    standardCode: '',
    standard: '',
    alarmThreshold: '',
    unit: '',
    precision: '',
    frequency: '',
    equipment: [],
    dataSource: 'manual',
    relatedPlans: [],
    status: 0,
    remark: '',
    description: ''
  }
  currentStep.value = 0
  formRef.value?.resetFields()
}
</script>

<style scoped>
/* 步骤容器样式 */
.step-container {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
}

/* 表单容器样式 */
.form-container {
  min-height: 400px;
  padding: 0 20px;
}

.step-form {
  max-width: 100%;
}

/* 步骤内容样式 */
.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 步骤标题样式 */
.step-header {
  margin-bottom: 24px;
  text-align: center;
}

.step-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.step-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 选项样式 */
.option-item,
.frequency-option,
.equipment-option,
.plan-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.plan-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 字段提示样式 */
.field-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.input-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

/* 确认页面样式 */
.confirmation-content {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

/* 底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-container {
    padding: 0 10px;
  }

  .step-container {
    margin-bottom: 20px;
    padding: 15px;
  }

  .step-header h3 {
    font-size: 18px;
  }
}

/* 表单项间距优化 */
.el-form-item {
  margin-bottom: 20px;
}

/* 标签样式 */
.mr-1 {
  margin-right: 4px;
}

/* 选择器选项组样式 */
:deep(.el-select-group__title) {
  font-weight: 600;
  color: #606266;
}

/* 步骤指示器样式优化 */
:deep(.el-steps) {
  .el-step__title {
    font-size: 14px;
    font-weight: 500;
  }

  .el-step__description {
    font-size: 12px;
    color: #909399;
  }
}
</style>