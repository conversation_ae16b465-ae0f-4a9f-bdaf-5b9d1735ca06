<template>
  <div class="violation-container">
    <el-card>
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="处罚标准" name="standard">
          <violation-standard />
        </el-tab-pane>
        <el-tab-pane label="违章记录" name="record">
          <violation-record />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import ViolationStandard from './components/ViolationStandard.vue'
import ViolationRecord from './components/ViolationRecord.vue'

export default {
  name: 'Violation',
  components: {
    ViolationStandard,
    ViolationRecord
  },
  data() {
    return {
      activeTab: 'standard'
    }
  }
}
</script>

<style lang="scss" scoped>
.violation-container {
  padding: 20px;
  
  :deep(.el-tabs__content) {
    padding: 20px;
  }
}
</style>
