<template>
  <Dialog v-model="dialogVisible" title="处理结果详情" width="800px">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="样品编号">{{ formData.sampleCode }}</el-descriptions-item>
      <el-descriptions-item label="检测项目">{{ formData.testItem }}</el-descriptions-item>
      <el-descriptions-item label="采样点">{{ formData.samplingPoint }}</el-descriptions-item>
      <el-descriptions-item label="检测值">
        <span :class="{ 'text-danger': formData.isExceeded, 'text-warning': formData.isWarning }">
          {{ formData.testValue }} {{ formData.unit }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="异常类型">
        <el-tag :type="getExceptionTypeTagType(formData.exceptionType)">
          {{ getExceptionTypeLabel(formData.exceptionType) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="处理状态">
        <el-tag :type="getHandleStatusTagType(formData.handleStatus)">
          {{ getHandleStatusLabel(formData.handleStatus) }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="center">处理记录</el-divider>
    
    <el-timeline>
      <el-timeline-item
        v-for="(record, index) in handleRecords"
        :key="index"
        :timestamp="record.timestamp"
        :type="getTimelineType(record.type)"
      >
        <el-card>
          <h4>{{ record.title }}</h4>
          <p>{{ record.description }}</p>
          <p class="text-sm text-gray-500">处理人：{{ record.handler }}</p>
        </el-card>
      </el-timeline-item>
    </el-timeline>
    
    <el-divider content-position="center">处理结果</el-divider>
    
    <el-descriptions :column="1" border>
      <el-descriptions-item label="最终结果">
        <el-tag :type="formData.handleStatus === 'completed' ? 'success' : 'warning'">
          {{ formData.handleStatus === 'completed' ? '已完成处理' : '处理中' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="处理方案">
        {{ formData.handlePlan || '暂无' }}
      </el-descriptions-item>
      <el-descriptions-item label="处理结果">
        {{ formData.handleResult || '暂无' }}
      </el-descriptions-item>
      <el-descriptions-item label="后续措施">
        {{ formData.followUpMeasures || '暂无' }}
      </el-descriptions-item>
      <el-descriptions-item label="完成时间">
        {{ formData.completedTime || '未完成' }}
      </el-descriptions-item>
      <el-descriptions-item label="处理人">
        {{ formData.handler || '暂无' }}
      </el-descriptions-item>
    </el-descriptions>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Dialog } from '@/components/Dialog'

defineOptions({ name: 'HandleResultDialog' })

// 对话框显示状态
const dialogVisible = ref(false)

// 表单数据
const formData = reactive({
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  testValue: '',
  unit: '',
  exceptionType: '',
  handleStatus: '',
  isExceeded: false,
  isWarning: false,
  handlePlan: '',
  handleResult: '',
  followUpMeasures: '',
  completedTime: '',
  handler: ''
})

// 处理记录
const handleRecords = ref([
  {
    timestamp: '2023-07-20 09:00:00',
    type: 'primary',
    title: '异常发现',
    description: '系统自动检测到数据异常，已标记为待处理状态',
    handler: '系统'
  },
  {
    timestamp: '2023-07-20 10:30:00',
    type: 'warning',
    title: '开始处理',
    description: '质量管理员开始处理该异常数据，制定处理方案',
    handler: '张三'
  },
  {
    timestamp: '2023-07-20 14:00:00',
    type: 'success',
    title: '处理完成',
    description: '异常数据处理完成，已采取相应措施',
    handler: '张三'
  }
])

// 打开对话框
const open = (data: any) => {
  Object.assign(formData, data)
  
  // 根据数据生成处理记录（实际项目中应该从API获取）
  generateHandleRecords(data)
  
  dialogVisible.value = true
}

// 生成处理记录
const generateHandleRecords = (data: any) => {
  const records = [
    {
      timestamp: data.createTime || '2023-07-20 09:00:00',
      type: 'primary',
      title: '异常发现',
      description: `检测到${data.exceptionType === 'exceed' ? '超标' : '异常'}数据：${data.testItem} = ${data.testValue}${data.unit}`,
      handler: '系统'
    }
  ]
  
  if (data.handleStatus === 'processing' || data.handleStatus === 'completed') {
    records.push({
      timestamp: data.handleStartTime || '2023-07-20 10:30:00',
      type: 'warning',
      title: '开始处理',
      description: '质量管理员开始处理该异常数据，制定处理方案',
      handler: data.handler || '张三'
    })
  }
  
  if (data.handleStatus === 'completed') {
    records.push({
      timestamp: data.completedTime || '2023-07-20 14:00:00',
      type: 'success',
      title: '处理完成',
      description: '异常数据处理完成，已采取相应措施',
      handler: data.handler || '张三'
    })
  }
  
  handleRecords.value = records
}

// 获取异常类型标签类型
const getExceptionTypeTagType = (type: string) => {
  const typeMap = {
    'exceed': 'danger',
    'instrument': 'warning',
    'operation': 'info',
    'sample': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取异常类型标签文本
const getExceptionTypeLabel = (type: string) => {
  const labelMap = {
    'exceed': '超标异常',
    'instrument': '仪器故障',
    'operation': '操作失误',
    'sample': '样品异常'
  }
  return labelMap[type] || '未知异常'
}

// 获取处理状态标签类型
const getHandleStatusTagType = (status: string) => {
  const statusMap = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success'
  }
  return statusMap[status] || 'info'
}

// 获取处理状态标签文本
const getHandleStatusLabel = (status: string) => {
  const labelMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已处理'
  }
  return labelMap[status] || '未知状态'
}

// 获取时间线类型
const getTimelineType = (type: string) => {
  return type
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.text-danger {
  color: #f56c6c;
}

.text-warning {
  color: #e6a23c;
}

.text-sm {
  font-size: 0.875rem;
}

.text-gray-500 {
  color: #6b7280;
}
</style>
