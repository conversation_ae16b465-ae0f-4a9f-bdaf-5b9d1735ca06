<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="数据分析"
      width="70%"
      destroy-on-close
      @closed="analysisChart = null"
    >
      <div class="flex flex-col gap-4">
        <div class="flex justify-between items-center">
          <div class="text-base text-gray-600">
            分析时间：{{ displayDate }}
          </div>
          <el-button type="primary" @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
        <div ref="analysisChart" class="h-[500px]" id="analysisChart"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, ref, watch } from 'vue'
import { useECharts } from '@/hooks/web/useEChart'
import { Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { log } from 'console'

interface MonitorData {
  pointName: string
  value: string
  unit: string
  time: string
  status: string
  indicatorType?: string
}

const dialogVisible = ref(false)
const analysisChart = ref<HTMLDivElement | null>(null)

const props = defineProps({
  monitorData: {
    type: Array as () => MonitorData[],
    default: () => []
  }
})

// 按监测点分组数据
const groupedData = computed(() => {
  const groups = new Map<string, MonitorData[]>()
  props.monitorData.forEach(item => {
    if (!groups.has(item.pointName)) {
      groups.set(item.pointName, [])
    }
    groups.get(item.pointName)?.push(item)
  })
  return groups
})

// 获取所有时间点
const xAxisData = computed(() => {
  const times = new Set<string>()
  props.monitorData.forEach(item => times.add(item.time))
  return Array.from(times).sort()
})

// 生成系列数据
const series = computed(() => {
  console.log('重新计算系列数据，组数:', groupedData.value.size);
  
  return Array.from(groupedData.value.entries()).map(([pointName, data], index) => {
    try {
      // 检查该数据系列是否为布尔型指标
      // 通过检查第一个数据点的值来确定是否为布尔类型
      const firstPointValue = data[0]?.value;
      const isBooleanIndicator = typeof firstPointValue === 'string' && 
        (firstPointValue.toLowerCase().trim() === 'true' || firstPointValue.toLowerCase().trim() === 'false');
      console.log(`isBooleanIndicator ${isBooleanIndicator}`);
      console.log(`处理系列 ${index}: ${pointName}, 是否布尔型:${isBooleanIndicator}, 数据点数:${data.length}`);
      
      const values = xAxisData.value.map(time => {
        const point = data.find(item => item.time === time);
        if (!point) return null;

        console.log(`point.value ${point.value}`);

        // 根据指标类型处理值
        if (isBooleanIndicator) {
          // 布尔型指标
          if (typeof point.value === 'string') {
            console.log("point is string");
            const lowerCaseTrimmedValue = point.value.toLowerCase().trim();
            if (lowerCaseTrimmedValue === 'true') {
              return 1;
            } else if (lowerCaseTrimmedValue === 'false') {
              return 0;
            }
          } else if (typeof point.value === 'boolean') {
            console.log("point is boolean");
            
            return point.value ? 1 : 0;
          }
        } else {
          // 数值型指标
          if (typeof point.value === 'string') {
            // 尝试转换为数值
            const num = parseFloat(point.value);
            return isNaN(num) ? null : num;
          }
        }
        
        // 如果无法处理，返回null
        return null;
      });
      
      return {
        name: pointName,
        type: 'line',
        data: values,
        smooth: true,
        symbolSize: 6,
        showSymbol: false,
        // 存储附加信息，用于tooltip显示
        isBooleanIndicator: isBooleanIndicator,
        unit: data[0]?.unit || '',
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        }
      }
    } catch (error) {
      console.error(`处理系列 ${pointName} 时出错:`, error);
      // 返回一个空系列，避免整个图表失败
      return {
        name: pointName,
        type: 'line',
        data: [],
        smooth: true,
        symbolSize: 6,
        showSymbol: false,
        isBooleanIndicator: false,
        unit: '',
      }
    } 
  });
});


// 提取显示日期信息
const displayDate = computed(() => {
  if (props.monitorData.length === 0) return '';
  const firstDate = props.monitorData[0].time.split(' ')[0];
  const lastDate = props.monitorData[props.monitorData.length - 1].time.split(' ')[0];
  return firstDate === lastDate ? firstDate : `${firstDate} 至 ${lastDate}`;
})

const { setOption, onResize, dispose } = useECharts(analysisChart)

// 生成图表配置
function createOption() {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  
  // 收集所有不同的单位
  const uniqueUnits = new Set<string>();
  series.value.forEach(s => {
    if (s.unit) uniqueUnits.add(s.unit);
  });
  
  // 生成多个Y轴
  const yAxis: any[] = [];
  Array.from(uniqueUnits).forEach((unit, index) => {
    yAxis.push({
      type: 'value',
      name: unit,
      nameLocation: 'middle',
      nameGap: 40,
      position: index === 0 ? 'left' : 'right',
      offset: index > 1 ? (index - 1) * 80 : 0,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    });
  });
  
  // 如果没有单位，添加一个默认Y轴
  if (yAxis.length === 0) {
    yAxis.push({
      type: 'value',
      name: '',
      nameLocation: 'middle',
      nameGap: 40,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    });
  }
  
  // 为每个系列分配对应的Y轴索引
  const seriesWithYAxisIndex = series.value.map((item, index) => {
    const yAxisIndex = item.unit ? Array.from(uniqueUnits).indexOf(item.unit) : 0;
    
    return {
      ...item,
      yAxisIndex: yAxisIndex,
      itemStyle: {
        color: colors[index % colors.length]
      },
      lineStyle: {
        width: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: colors[index % colors.length] + '80'
            },
            {
              offset: 1,
              color: colors[index % colors.length] + '10'
            }
          ]
        }
      }
    };
  });
  
  return {
    title: {
      text: '监测数据趋势分析',
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any[]) {
        if (!params || params.length === 0) return '';
        
        let result = params[0].axisValue + '<br/>';
        
        params.forEach(param => {
          // 获取系列索引，用于从原始 series 数据中获取附加信息
          const seriesIndex = param.seriesIndex;
          const originalSeries = series.value[seriesIndex];
          
          // 安全地获取是否为布尔型指标和单位
          const isBooleanIndicator = originalSeries?.isBooleanIndicator || false;
          const unit = originalSeries?.unit || '';
          console.log('param.value', param.value);
          // 检查是否有有效数据
          if (param.value === null || typeof param.value === 'undefined' || isNaN(param.value)) {
            result += `${param.marker}${param.seriesName}: - (无数据)<br/>`;
          } else {
            let displayValue = param.value;
            // 根据指标类型显示不同的值
            if (isBooleanIndicator) {
              if (displayValue === 1) {
                displayValue = '运行';
              } else if (displayValue === 0) {
                displayValue = '停止';
              }
            }
            result += `${param.marker}${param.seriesName}: ${displayValue} ${unit}<br/>`;
          }
        });
        
        return result;
      }
    },
    legend: {
      type: 'scroll',
      bottom: 0,
      data: Array.from(groupedData.value.keys())
    },
    toolbox: {
      show: true,
      feature: {
        saveAsImage: {
          title: '保存为图片'
        },
        // dataZoom: {
        //   title: {
        //     zoom: '区域缩放',
        //     back: '还原缩放'
        //   }
        // }
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        type: 'slider',
        start: 0,
        end: 100
      }
    ],
    grid: {
      top: '15%',
      bottom: '15%',
      left: '5%',
      right: yAxis.length > 1 ? '5%' : '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData.value,
      axisLabel: {
        rotate: 30,
        formatter: function(value: string) {
          return value.split(' ')[1]
        },
        margin: 14
      },
      boundaryGap: false
    },
    yAxis: yAxis,
    series: seriesWithYAxisIndex
  }
}

// 导出数据
function exportData() {
  const headers = ['时间', ...Array.from(groupedData.value.keys())]
  const rows = xAxisData.value.map(time => {
    const row = [time]
    Array.from(groupedData.value.entries()).forEach(([_, data]) => {
      const point = data.find(item => item.time === time)
      if (point) {
        // 检查是否为布尔型指标
        const isBooleanIndicator = typeof point.value === 'string' && 
          (point.value.toLowerCase().trim() === 'true' || point.value.toLowerCase().trim() === 'false');
        
        let exportValue: string; // 明确声明 exportValue 为字符串，因为最终会导出为 CSV

        if (isBooleanIndicator) {
          // 布尔型指标
          const lowerCaseTrimmedValue = point.value.toLowerCase().trim();
          if (lowerCaseTrimmedValue === 'true') {
            exportValue = '运行';
          } else if (lowerCaseTrimmedValue === 'false') {
            exportValue = '停止';
          } else {
            exportValue = point.value; // 未知值，保留原始字符串
          }
        } else {
          // 数值型指标
          const numValue = parseFloat(point.value);
          if (!isNaN(numValue)) {
            exportValue = numValue.toFixed(2); // 确保在数字上调用 toFixed
          } else {
            exportValue = point.value; // 保留原始字符串
          }
        }
        
        row.push(exportValue)
      } else {
        row.push('') // 如果没有数据，导出为空
      }
    })
    return row
  })

  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n')

  // 创建 Blob 对象，添加 BOM
  const blob = new Blob(['\ufeff' + csvContent], {
    type: 'text/csv;charset=utf-8-sig'
  })

  // 创建下载链接
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `监测数据_${displayDate.value}.csv`

  // 触发下载
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // 清理 URL 对象
  URL.revokeObjectURL(link.href)

  ElMessage.success('数据导出成功')
}

function initChart() {
  nextTick(() => {
    if (analysisChart.value) {
      try {
        console.log('初始化图表，数据点数量:', props.monitorData.length);
        console.log('时间轴数据:', xAxisData.value.length);
        console.log('系列数据:', series.value);
        
        const option = createOption();
        console.log('图表配置生成完成');
        
        setOption(option);
        console.log('图表配置已应用');
      } catch (error) {
        console.error('初始化图表时出错:', error);
      }
    } else {
      console.warn('图表容器不存在');
    }
  });
}

function setVisible() {
  dialogVisible.value = !dialogVisible.value
}

defineExpose({ setVisible })

watch(dialogVisible, (newVal) => {
  if (newVal) {
    console.log('对话框显示，准备初始化图表');
    initChart();
    nextTick(() => {
      console.log('执行图表重绘');
      onResize();
    });
  } else {
    console.log('对话框关闭，销毁图表');
    dispose();
  }
})
</script>
