<template>
<!--  <ContentWrap>-->

<!--&lt;!&ndash;    <el-button type="success" plain @click="handleExport" :loading="exportLoading">&ndash;&gt;-->
<!--&lt;!&ndash;      <Icon icon="ep:download" class="icon-margin" /> 导出&ndash;&gt;-->
<!--&lt;!&ndash;    </el-button>&ndash;&gt;-->
<!--  </ContentWrap>-->
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="search-form"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="报表名称" prop="reportName">
        <el-input
          v-model="queryParams.reportName"
          placeholder="请输入报表名称"
          clearable
          @keyup.enter="handleQuery"
          class="search-input"
        />
      </el-form-item>
      <el-form-item label="报表编码" prop="reportCode">
        <el-input
          v-model="queryParams.reportCode"
          placeholder="请输入报表编码"
          clearable
          @keyup.enter="handleQuery"
          class="search-input"
        />
      </el-form-item>
      <!-- <el-form-item label="分组" prop="reportGroup">
        <el-input
          v-model="queryParams.reportGroup"
          placeholder="请输入分组"
          clearable
          @keyup.enter="handleQuery"
          class="search-input"
        />
      </el-form-item> -->
      <!-- <el-form-item label="报表类型" prop="reportType">
        <el-select
          v-model="queryParams.reportType"
          placeholder="请选择报表类型"
          clearable
          class="search-input"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="报表状态" prop="enableFlag">
        <el-select
          v-model="queryParams.enableFlag"
          placeholder="请选择报表状态"
          clearable
          class="search-input"
        >
          <el-option
            v-for="item in enableFlagOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item label="创建时间" prop="createTime">-->
<!--        <el-date-picker-->
<!--          v-model="queryParams.createTime"-->
<!--          value-format="YYYY-MM-DD HH:mm:ss"-->
<!--          type="daterange"-->
<!--          start-placeholder="开始日期"-->
<!--          end-placeholder="结束日期"-->
<!--          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"-->
<!--          class="date-picker"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button @click="handleQuery"
          ><Icon icon="ep:search" class="icon-margin" /> 搜索</el-button
        >
        <el-button @click="resetQuery"
          ><Icon icon="ep:refresh" class="icon-margin" /> 重置</el-button
        >
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="icon-margin" /> 新增
        </el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['report:report:create']"
        >
          <Icon icon="ep:plus" class="icon-margin" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['report:report:export']"
        >
          <Icon icon="ep:download" class="icon-margin" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="名称" align="center" prop="reportName" />
      <el-table-column label="报表编码" align="center" prop="reportCode" />
      <!-- <el-table-column label="分组" align="center" prop="reportGroup" /> -->
      <el-table-column label="报表类型" align="center" prop="reportType">
        <template #default="scope">
          <el-tag v-if="Number(scope.row.reportType) === 1" type="info">普通报表</el-tag>
          <el-tag v-else-if="Number(scope.row.reportType) === 2" type="warning">填报报表</el-tag>
          <el-tag v-else-if="Number(scope.row.reportType) === 3" type="success">协同报表</el-tag>
          <span v-else>{{ scope.row.reportType }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="报表缩略图" align="center" prop="reportImage" /> -->
      <el-table-column label="报表描述" align="center" prop="reportDesc" />
<!--      <el-table-column label="报表作者" align="center" prop="reportAuthor" />-->
      <!-- <el-table-column label="报表下载次数" align="center" prop="downloadCount" /> -->
      <el-table-column label="报表状态" align="center" prop="enableFlag">
        <template #default="scope">
          <el-tag v-if="scope.row.enableFlag === 1" type="success">已启用</el-tag>
          <el-tag v-else type="danger">已禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['report:report:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['report:report:delete']"
          >
            删除
          </el-button> -->
          <div class="template-btn">
            <el-button link type="primary" @click="openForm('update', scope.row.reportCode)">
              编辑
            </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.reportCode)"> 删除 </el-button>
            <el-dropdown trigger="click">
              <span class="more-btn"> 更多 </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleDesign(scope.row)">设计</el-dropdown-item>
                  <el-dropdown-item @click="handleEdit(scope.row)">编辑</el-dropdown-item>
                  <el-dropdown-item @click="handlePreview(scope.row)">预览</el-dropdown-item>
                  <el-dropdown-item @click="handleShow(scope.row)">展示</el-dropdown-item>
                  <!--                  <el-dropdown-item @click="handleShare(scope.row)">分享</el-dropdown-item>-->
<!--                  <el-dropdown-item @click="handleCopy(scope.row)">复制</el-dropdown-item>-->
                  <el-dropdown-item @click="handleDelete(scope.row.id)">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ReportForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ReportApi, ReportVO } from '@/api/report/reportInfo/index'
import ReportForm from './components/ReportForm.vue'
import router from '@/router'
import { useReportStore } from '@/stores/reportStore'
const store = useReportStore()

/** 报表信息 列表 */
defineOptions({ name: 'Report' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ReportVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  reportName: undefined,
  reportCode: undefined,
  reportGroup: undefined,
  reportType: undefined,
  reportImage: undefined,
  reportDesc: undefined,
  reportAuthor: undefined,
  downloadCount: undefined,
  enableFlag: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const enableFlagOptions = ref([
  { label: '已启用', value: 1 },
  { label: '已禁用', value: 0 }
])

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ReportApi.getReportPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, reportCode?: string) => {
  formRef.value.open(type, reportCode)
}

/** 删除按钮操作 */
const handleDelete = async (reportCode: string) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ReportApi.deleteReportByReportCode(reportCode)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}


/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ReportApi.exportReport(queryParams)
    download.excel(data, '报表信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 预览按钮操作 */
const handlePreview = (row: ReportVO) => {
  router.push({
    path: '/report/reportPreview',
    query: {
      reportCode: row.reportCode,
      timestamp: new Date().getTime()
    }
  })
}

/** 设计按钮操作 */
const handleDesign = async (row: ReportVO) => {
  try {
    // const res = await ReportApi.getReportByCode(row.reportCode)
    // store.setReportData(res)
    router.push({
      path: '/report/reportDesign',
      query: {
        reportCode: row.reportCode,
        reportName: row.reportName,
        timestamp: new Date().getTime()
      }
    })
  } catch (error: unknown) {
    const err = error as Error
    console.error('Error in handleDesign:', {
      errorName: err.name,
      errorMessage: err.message,
      errorStack: err.stack
    })
    message.error('获取报表数据失败：' + err.message)
  }
}

/** 展示按钮操作 */
const handleShow = async (row) => {
    router.push({
      path: '/report/reportShow',
      query: {
        reportCode: row.reportCode,
        reportName: row.reportName,
        timestamp: new Date().getTime()
      }
    }).then(() => {
      // 跳转完成后强制刷新页面
      // window.location.reload();
    }).catch((error) => {
      console.error('Error during navigation:', error);
    });
    // const url = `/reportShow?reportCode=${encodeURIComponent(row.reportCode)}&reportName=${encodeURIComponent(row.reportName)}`;
    // window.open(url, '_blank'); // 在新标签页中打开
};

/** 分享按钮操作 */
const handleShare = (row: ReportVO) => {
  console.log('🚀 ~ handleShare ~ row:', row)
}

/** 复制按钮操作 */
const handleCopy = async (row: ReportVO) => {
  console.log('🚀 ~ handleCopy ~ row:', row)
  try {
    // 复制的二次确认
    await message.confirm('确认要复制该报表吗？')
    // TODO: 实现复制功能
    message.success('复制功能开发中')
  } catch {}
}

/** 编辑按钮操作 */
const handleEdit = (row: ReportVO) => {
  const { reportCode, reportName } = row
  router.push({
    path: '/report/reportFill',
    query: {
      reportCode,
      reportName,
      timestamp: new Date().getTime()
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.search-form {
  margin-bottom: -15px;
}

.search-input {
  width: 240px !important;
}

.date-picker {
  width: 220px !important;
}

.icon-margin {
  margin-right: 5px;
}

.template-btn {
  display: flex;
  justify-content: space-evenly;
  align-items: center;

  .more-btn {
    cursor: pointer;
    color: #409eff;

    &:hover {
      color: #40a0ff9d;
    }
  }
}
</style>
