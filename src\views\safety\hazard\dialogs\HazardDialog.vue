<template>
  <el-dialog
    :title="props.type === 'add' ? '新增隐患' : '编辑隐患'"
    v-model="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="hazard-form"
    >
      <el-form-item label="隐患名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入隐患名称" />
      </el-form-item>

      <el-form-item label="隐患类型" prop="category">
        <el-cascader
          v-model="formData.category"
          :options="categoryOptions"
          :props="{ checkStrictly: true }"
          placeholder="请选择隐患类型"
        />
      </el-form-item>

      <el-form-item label="危险等级" prop="severity">
        <el-select v-model="formData.severity" placeholder="请选择危险等级">
          <el-option label="高危" value="high" />
          <el-option label="中危" value="medium" />
          <el-option label="低危" value="low" />
        </el-select>
      </el-form-item>

      <el-form-item label="责任部门" prop="department">
        <el-select v-model="formData.department" placeholder="请选择责任部门">
          <el-option label="生产部" value="production" />
          <el-option label="安全部" value="safety" />
          <el-option label="设备部" value="equipment" />
        </el-select>
      </el-form-item>

      <el-form-item label="责任人" prop="responsible">
        <el-input v-model="formData.responsible" placeholder="请输入责任人" />
      </el-form-item>

      <el-form-item label="隐患描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入隐患描述"
        />
      </el-form-item>

      <el-form-item label="整改措施" prop="measures">
        <div class="measures-list">
          <div
            v-for="(measure, index) in formData.measures"
            :key="index"
            class="measure-item"
          >
            <el-input v-model="formData.measures[index]" placeholder="请输入整改措施" />
            <el-button
              type="danger"
              link
              @click="removeMeasure(index)"
              :disabled="formData.measures.length === 1"
            >
              删除
            </el-button>
          </div>
        </div>
        <el-button type="primary" link @click="addMeasure">添加措施</el-button>
      </el-form-item>

      <el-form-item label="整改期限" prop="deadline">
        <el-date-picker
          v-model="formData.deadline"
          type="datetime"
          placeholder="请选择整改期限"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="附件" prop="attachments">
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="formData.attachments"
          multiple
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持jpg、png、pdf格式文件
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import type { HazardData, UploadFile } from '../types'

const props = defineProps({
  type: {
    type: String as () => 'add' | 'edit',
    required: true
  },
  data: {
    type: Object as () => Partial<HazardData>,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive<HazardData>({
  code: '',
  name: '',
  category: [],
  severity: '',
  department: '',
  responsible: '',
  description: '',
  measures: [''],
  deadline: '',
  attachments: []
})

const categoryOptions = [
  {
    value: 'equipment',
    label: '设备类',
    children: [
      { value: 'mechanical', label: '机械设备' },
      { value: 'electrical', label: '电气设备' },
      { value: 'special', label: '特种设备' }
    ]
  },
  {
    value: 'environment',
    label: '环境类',
    children: [
      { value: 'workplace', label: '作业环境' },
      { value: 'fire', label: '消防安全' },
      { value: 'health', label: '职业卫生' }
    ]
  }
]

const rules: FormRules = {
  name: [{ required: true, message: '请输入隐患名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择隐患类型', trigger: 'change' }],
  severity: [{ required: true, message: '请选择危险等级', trigger: 'change' }],
  department: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
  responsible: [{ required: true, message: '请输入责任人', trigger: 'blur' }],
  description: [{ required: true, message: '请输入隐患描述', trigger: 'blur' }],
  measures: [{ required: true, message: '请输入至少一条整改措施', trigger: 'change' }],
  deadline: [{ required: true, message: '请选择整改期限', trigger: 'change' }]
}

const addMeasure = () => {
  formData.measures.push('')
}

const removeMeasure = (index: number) => {
  formData.measures.splice(index, 1)
}

const handleFileChange = (file: UploadFile, fileList: UploadFile[]) => {
  formData.attachments = fileList
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      // TODO: 实现提交逻辑
      ElMessage.success('提交成功')
      dialogVisible.value = false
      emit('success', formData)
    }
  })
}

const handleClosed = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  Object.assign(formData, {
    name: '',
    category: [],
    severity: '',
    department: '',
    responsible: '',
    description: '',
    measures: [''],
    deadline: '',
    attachments: []
  })
}

// 暴露方法给父组件
defineExpose({
  open: () => {
    dialogVisible.value = true
    if (props.type === 'edit' && props.data) {
      Object.assign(formData, props.data)
    }
  }
})
</script>

<style lang="scss" scoped>
.hazard-form {
  .measures-list {
    .measure-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .el-input {
        margin-right: 10px;
      }
    }
  }
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

:deep(.el-upload-list) {
  width: 360px;
}
</style> 