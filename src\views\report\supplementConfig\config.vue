<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="filter-container">
      <div class="filter-item">
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item label="配置名称" prop="name" style="width: 280px;">
            <el-input v-model="queryParams.name" placeholder="请输入配置名称" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="报表类型" prop="reportType" style="width: 280px;">
            <el-select v-model="queryParams.reportType" placeholder="请选择报表类型" clearable>
              <el-option label="生产消耗数据" value="consumption" />
              <el-option label="水质数据" value="quality" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="enabled" style="width: 280px;">
            <el-select v-model="queryParams.enabled" placeholder="请选择状态" clearable>
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="mt-4">
      <template #header>
        <div class="card-header">
          <div class="title-container">
            <span class="font-bold">补录配置列表</span>
            <el-tooltip content="每种报表类型只能有一个启用状态的补录配置" placement="top" effect="light">
              <el-icon class="hint-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
          <el-button type="primary" @click="handleAdd">
            <el-icon class="mr-1">
              <Plus />
            </el-icon>新增补录配置
          </el-button>
        </div>
      </template>

      <!-- 表格区域 -->
      <el-table v-loading="loading" :data="configList" border style="width: 100%">
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="配置名称" prop="name" width="120" show-overflow-tooltip />
        <el-table-column label="报表类型" prop="reportType" width="120" align="center">
          <template #default="scope">
            {{ getReportTypeLabel(scope.row.reportType) }}
          </template>
        </el-table-column>
        <el-table-column label="补录数据时间" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatDate(scope.row.dataStartDate) }} 至 {{ formatDate(scope.row.dataEndDate) }}
          </template>
        </el-table-column>
        <el-table-column label="允许补录时间" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatDateTime(scope.row.operationStartTime) }} 至 {{ formatDateTime(scope.row.operationEndTime) }}
          </template>
        </el-table-column>

        <el-table-column label="更新人" prop="updater" width="100" align="center" show-overflow-tooltip />
        <el-table-column label="更新时间" prop="updateTime" width="160" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ formatDateTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="creator" width="100" align="center" show-overflow-tooltip />
        <el-table-column label="创建时间" prop="createTime" width="160" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="状态" prop="enabled" width="80" align="center">
          <template #default="scope">
            <el-switch v-model="scope.row.enabled" @change="handleStatusChange(scope.row)" :active-value="true"
              :inactive-value="false" active-color="#13ce66" inactive-color="#ff4949" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="queryParams.pageNo" v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="660px" destroy-on-close
      :close-on-click-modal="false">
      <el-form ref="configFormRef" :model="configForm" :rules="formRules" label-width="140px">
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="configForm.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="报表类型" prop="reportType" required>
          <el-select v-model="configForm.reportType" placeholder="请选择报表类型" style="width: 100%"
            @change="validateReportType">
            <el-option label="生产消耗数据" value="consumption" />
            <el-option label="水质数据" value="quality" />
          </el-select>
        </el-form-item>
        <el-form-item label="允许补录时间段" prop="operationTimeRange">
          <el-date-picker v-model="timeRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
            end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="补录数据时间段" prop="dataDateRange">
          <el-date-picker v-model="dataTimeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%" />
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-switch v-model="configForm.enabled" :active-value="true" :inactive-value="false" active-text="启用"
            inactive-text="禁用" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="configForm.remark" type="textarea" placeholder="请输入备注信息" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { deleteConfig, getConfigPage, setConfig, updateConfig } from '@/api/report/supplementConfig'
import { DataSupplementConfigPageReqVO, DataSupplementConfigRespVO, SupplementConfigReq } from '@/api/report/supplementConfig/types'
import { Plus, QuestionFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'

// 查询参数
const queryParams = reactive<DataSupplementConfigPageReqVO>({
  pageNo: 1,
  pageSize: 10,
  name: '',
  reportType: '',
  enabled: undefined
})

// 表格数据
const configList = ref<DataSupplementConfigRespVO[]>([])
const total = ref(0)
const loading = ref(false)

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = computed(() => isEdit.value ? '编辑补录配置' : '新增补录配置')
const isEdit = ref(false)
const configFormRef = ref<FormInstance | null>(null)
const configForm = reactive<SupplementConfigReq>({
  id: undefined,
  name: '',
  reportType: '',
  operationStartTime: undefined,
  operationEndTime: undefined,
  dataStartDate: '',
  dataEndDate: '',
  period: 'DAY',
  enabled: true,
  remark: ''
})

// 时间范围选择器 - 使用any类型避免类型错误
const timeRange = ref<any>(null)
const dataTimeRange = ref<any>(null)

// 表单校验规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  reportType: [
    { required: true, message: '请选择报表类型', trigger: 'change' }
  ]
})

// 监听时间范围变化，同步到表单
watch(timeRange, (val) => {
  if (val && val.length === 2) {
    // 转换为时间戳
    configForm.operationStartTime = new Date(val[0]).getTime()
    configForm.operationEndTime = new Date(val[1]).getTime()
  } else {
    configForm.operationStartTime = undefined
    configForm.operationEndTime = undefined
  }
})

// 监听数据时间范围变化，同步到表单
watch(dataTimeRange, (val) => {
  if (val && val.length === 2) {
    configForm.dataStartDate = val[0]
    configForm.dataEndDate = val[1]
  } else {
    configForm.dataStartDate = ''
    configForm.dataEndDate = ''
  }
})

// 初始化
onMounted(() => {
  getConfigList()
})

// 获取配置列表
const getConfigList = async () => {
  loading.value = true
  try {
    const res = await getConfigPage(queryParams)
    // 根据axios封装，res已经是PageResult<DataSupplementConfigRespVO>类型了
    configList.value = res?.list || []
    total.value = res?.total || 0
  } catch (error) {
    console.error('获取配置列表失败', error)
    ElMessage.error('获取配置列表失败')
  } finally {
    loading.value = false
  }
}

// 查询操作
const handleQuery = () => {
  queryParams.pageNo = 1
  getConfigList()
}

// 重置查询
const resetQuery = () => {
  queryParams.name = ''
  queryParams.reportType = ''
  queryParams.enabled = undefined
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  getConfigList()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNo = page
  getConfigList()
}

// 校验报表类型是否已存在
const validateReportType = async (value: string) => {
  if (!value) return

  // 如果是编辑模式，并且报表类型没有改变，则不需要校验
  if (isEdit.value && configForm.id !== undefined && value === configForm.reportType) {
    return
  }

  // 检查是否已存在相同报表类型的配置
  const existingConfig = configList.value.find(item =>
    item.reportType === value
  )

  if (existingConfig) {
    ElMessage.warning(`已存在${getReportTypeLabel(value)}补录配置，每种报表类型只能有一个补录配置`)
    configForm.reportType = ''
    return
  }
}

// 处理新增
const handleAdd = () => {
  isEdit.value = false
  resetForm()

  // 检查是否已存在每种报表类型的配置
  const existingReportTypes = new Set(configList.value.map(item => item.reportType))

  // 如果所有报表类型都已存在配置，提示用户
  if (existingReportTypes.size >= 2 &&
    existingReportTypes.has('consumption') &&
    existingReportTypes.has('quality')) {
    ElMessage.warning('所有报表类型都已存在配置，请编辑现有配置或删除后重新创建')
    return
  }

  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row: DataSupplementConfigRespVO) => {
  isEdit.value = true
  resetForm()

  // 填充表单数据
  Object.assign(configForm, row)

  // 设置时间范围
  if (row.operationStartTime && row.operationEndTime) {
    timeRange.value = [
      formatDateTime(row.operationStartTime),
      formatDateTime(row.operationEndTime)
    ]
  }

  // 设置数据时间范围
  if (row.dataStartDate && row.dataEndDate) {
    dataTimeRange.value = [row.dataStartDate, row.dataEndDate]
  }

  dialogVisible.value = true
}

// 处理删除
const handleDelete = (row: DataSupplementConfigRespVO) => {
  ElMessageBox.confirm(`确定要删除配置 "${row.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteConfig(row.id)
      if (res) {
        ElMessage.success('删除成功')
        getConfigList()
      } else {
        ElMessage.error('删除失败')
      }
    } catch (error) {
      console.error('删除失败', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => { })
}

// 处理状态变更
const handleStatusChange = async (row: DataSupplementConfigRespVO) => {
  // 如果是启用状态，需要检查是否已存在相同报表类型的启用配置
  if (row.enabled) {
    const existingConfig = configList.value.find(item =>
      item.reportType === row.reportType &&
      item.enabled === true &&
      item.id !== row.id
    )

    if (existingConfig) {
      row.enabled = false // 恢复状态
      ElMessage.warning(`已存在启用状态的${getReportTypeLabel(row.reportType)}补录配置，每种报表类型只能有一个启用的补录配置`)
      return
    }
  }

  try {
    // 构建更新请求对象
    const updateReq: SupplementConfigReq = {
      id: row.id,
      name: row.name,
      reportType: row.reportType,
      operationStartTime: row.operationStartTime,
      operationEndTime: row.operationEndTime,
      dataStartDate: row.dataStartDate,
      dataEndDate: row.dataEndDate,
      period: row.period,
      enabled: row.enabled,
      remark: row.remark
    }

    const res = await updateConfig(updateReq)
    if (res) {
      ElMessage.success(`${row.enabled ? '启用' : '禁用'}成功`)
    } else {
      // 恢复状态
      row.enabled = !row.enabled
      ElMessage.error(`${!row.enabled ? '启用' : '禁用'}失败`)
    }
  } catch (error) {
    // 恢复状态
    row.enabled = !row.enabled
    console.error('更新状态失败', error)
    ElMessage.error(`${!row.enabled ? '启用' : '禁用'}失败`)
  }
}

// 提交表单
const submitForm = async () => {
  if (!configFormRef.value) return

  // 验证时间范围
  if (!timeRange.value || timeRange.value.length !== 2) {
    ElMessage.warning('请选择允许补录时间范围')
    return
  }

  // 验证数据时间范围
  if (!dataTimeRange.value || dataTimeRange.value.length !== 2) {
    ElMessage.warning('请选择补录数据时间范围')
    return
  }

  // 验证报表类型
  if (!configForm.reportType) {
    ElMessage.warning('请选择报表类型')
    return
  }

  // 检查是否已存在相同报表类型的配置
  if (!isEdit.value) {
    const existingConfig = configList.value.find(item =>
      item.reportType === configForm.reportType
    )

    if (existingConfig) {
      ElMessage.warning(`已存在${getReportTypeLabel(configForm.reportType)}补录配置，每种报表类型只能有一个补录配置`)
      return
    }
  }

  // 检查是否已存在相同报表类型的启用配置
  if (configForm.enabled) {
    const existingConfig = configList.value.find(item =>
      item.reportType === configForm.reportType &&
      item.enabled === true &&
      item.id !== configForm.id
    )

    if (existingConfig) {
      ElMessage.warning(`已存在启用状态的${getReportTypeLabel(configForm.reportType)}补录配置，每种报表类型只能有一个启用的补录配置`)
      return
    }
  }

  await configFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        let res
        if (isEdit.value) {
          res = await updateConfig(configForm)
        } else {
          res = await setConfig(configForm)
        }

        if (res) {
          ElMessage.success(`${isEdit.value ? '修改' : '新增'}成功`)
          dialogVisible.value = false
          getConfigList()
        } else {
          ElMessage.error(`${isEdit.value ? '修改' : '新增'}失败`)
        }
      } catch (error) {
        console.error(`${isEdit.value ? '修改' : '新增'}失败`, error)
        ElMessage.error(`${isEdit.value ? '修改' : '新增'}失败`)
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (configFormRef.value) {
    configFormRef.value.resetFields()
  }

  Object.assign(configForm, {
    id: undefined,
    name: '',
    reportType: '',
    operationStartTime: undefined,
    operationEndTime: undefined,
    dataStartDate: '',
    dataEndDate: '',
    period: 'DAY',
    enabled: true,
    remark: ''
  })

  timeRange.value = null
  dataTimeRange.value = null
}

// 格式化日期时间
const formatDateTime = (timestamp: number | string) => {
  if (!timestamp) return '--'

  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 格式化日期（不含时间）
const formatDate = (dateStr: string) => {
  if (!dateStr) return '--'
  return dateStr
}

// 获取周期标签
const getPeriodLabel = (period: string) => {
  const periodMap: Record<string, string> = {
    'MINUTE': '分钟',
    'HOUR': '小时',
    'DAY': '天',
    'WEEK': '周',
    'MONTH': '月',
    'YEAR': '年'
  }
  return periodMap[period] || period
}

// 获取报表类型标签
const getReportTypeLabel = (reportType: string) => {
  const reportTypeMap: Record<string, string> = {
    'consumption': '生产消耗数据',
    'quality': '水质数据'
  }
  return reportTypeMap[reportType] || reportType || '--'
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hint-icon {
  font-size: 16px;
  color: #909399;
  cursor: help;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

.position-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.position-group {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
}

.form-label {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.coordinate-badge {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.position-inputs {
  display: flex;
  gap: 10px;
}

.axis-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.axis-label {
  width: 20px;
  margin-right: 8px;
}
</style>
