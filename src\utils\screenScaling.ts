/**
 * 屏幕分辨率自适应缩放工具
 * 根据不同分辨率自动调整页面缩放比例，优化在4K等高分辨率屏幕上的显示
 */

// 存储上次检测到的屏幕信息
let lastScreenInfo = {
  width: window.screen.width,
  height: window.screen.height,
  systemResolution: 0
};

// 存储上次检测到的视口尺寸
let lastViewportWidth = window.innerWidth;
let lastViewportHeight = window.innerHeight;

// 检测4K屏幕并应用缩放
export function applyScreenScaling() {
  // 获取屏幕分辨率
  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;
  
  // 获取系统和浏览器的缩放信息
  const { zoom, browserZoom, systemZoom } = getAllZoom();
  
  // 计算实际系统分辨率
  const systemResolution = Math.round(screenWidth * systemZoom);
  
  console.log('检测到系统分辨率', systemResolution, '屏幕分辨率', screenWidth, '总缩放倍数', zoom);

  // 更新上次屏幕信息
  lastScreenInfo = {
    width: screenWidth,
    height: screenHeight,
    systemResolution
  };

  // 目标元素
  const appElement = document.getElementById('app');
  if (!appElement) return;
  
  // 默认缩放比例
  let scaleRatio = 1;
  
  // 获取实际可见视口尺寸（考虑浏览器窗口大小）
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  // 只对4K及以上分辨率进行缩放，其他分辨率保持100%
  if (systemResolution >= 3800 || viewportWidth >= 3000) {
    scaleRatio = 1.75; // 4K屏幕下缩放到175%
    console.log('检测到4K或更高分辨率屏幕，应用175%缩放');
  } else {
    // 其他分辨率统一使用100%缩放
    scaleRatio = 1;
    console.log('非4K屏幕，保持100%缩放');
  }
  
  // 应用缩放
  applyScaling(appElement, scaleRatio);
}

// 应用缩放到指定元素
export function applyScaling(element: HTMLElement, scale: number) {
  if (!element) return;
  
  // 获取视口宽度
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  // 检查是否已经存在包装器
  let wrapperDiv = document.getElementById('app-scale-wrapper');
  
  // 如果不存在包装器，创建一个新的
  if (!wrapperDiv) {
    wrapperDiv = document.createElement('div');
    wrapperDiv.id = 'app-scale-wrapper';
    wrapperDiv.style.position = 'fixed'; // 使用fixed定位，避免滚动问题
    wrapperDiv.style.top = '0';
    wrapperDiv.style.left = '0';
    wrapperDiv.style.width = '100vw';
    wrapperDiv.style.height = '100vh';
    wrapperDiv.style.overflow = 'auto'; // 允许滚动
    wrapperDiv.style.display = 'flex';
    wrapperDiv.style.justifyContent = 'center';
    wrapperDiv.style.alignItems = 'flex-start';
    wrapperDiv.style.zIndex = '1'; // 确保wrapper在最上层
    
    // 将app元素移动到包装容器中
    const parent = element.parentNode;
    if (parent) {
      parent.insertBefore(wrapperDiv, element);
      wrapperDiv.appendChild(element);
    }
  }
  
  // 设置app元素样式，添加过渡效果实现平滑缩放
  element.style.transition = 'transform 0.3s ease, width 0.3s ease, height 0.3s ease';
  element.style.transformOrigin = 'top center';
  element.style.transform = `scale(${scale})`;
  
  // 计算缩放后需要的宽度
  const scaledWidth = viewportWidth / scale;
  
  // 应用样式 - 确保内容填满整个视口宽度
  element.style.width = `${scaledWidth}px`;
  element.style.maxWidth = `${scaledWidth}px`;
  
  // 调整元素样式，确保内容完全可见
  if (scale !== 1) {
    // 对于所有非1的缩放，都调整高度
    const scaledHeight = viewportHeight / scale;
    element.style.minHeight = `${scaledHeight}px`;
    element.style.height = `${scaledHeight}px`; // 明确设置高度
    
    // 确保父容器样式正确
    if (wrapperDiv) {
      // 设置父容器样式，确保没有留白
      wrapperDiv.style.padding = '0';
      
      // 对于小于1的缩放（小屏幕），特别处理以消除留白
      if (scale < 1) {
        // 确保父容器宽度恰好为视口宽度
        wrapperDiv.style.width = '100vw';
        wrapperDiv.style.justifyContent = 'flex-start';
        wrapperDiv.style.alignItems = 'flex-start';
        element.style.marginLeft = '0';
        element.style.marginRight = '0';
      } else {
        // 大于1的缩放，保持居中
        wrapperDiv.style.justifyContent = 'center';
        wrapperDiv.style.alignItems = 'flex-start'; // 确保顶部对齐
      }
    }
    
    // 添加一些额外的样式以确保内容居中
    element.style.position = 'relative';
  } else {
    // 当缩放比例为1时，重置高度
    element.style.height = '100%';
    element.style.minHeight = '100%';
    
    if (wrapperDiv) {
      wrapperDiv.style.alignItems = 'flex-start';
    }
  }
  
  // 保存当前缩放比例，以便其他组件使用
  window.__SCREEN_SCALE__ = scale;
  
  // 移除旧的resize监听器（如果存在）
  window.removeEventListener('resize', handleResize);
  
  // 添加窗口大小变化监听，以便在调整窗口大小时重新计算
  window.addEventListener('resize', handleResize);
  
  // 触发自定义事件，通知UI组件缩放已更改
  window.dispatchEvent(new CustomEvent('screen-scaling-auto-adjust'));
}

/**
 * 手动设置缩放比例
 * @param scale 缩放比例，例如1.75表示175%
 */
export function setManualScale(scale: number) {
  if (scale <= 0) return;
  
  const appElement = document.getElementById('app');
  if (!appElement) return;
  
  // 应用新的缩放比例
  applyScaling(appElement, scale);
  
  // 在控制台输出信息
  console.log(`手动设置缩放比例为: ${scale} (${Math.round(scale * 100)}%)`);
  
  // 返回当前缩放比例
  return scale;
}

/**
 * 获取当前缩放比例
 */
export function getCurrentScale(): number {
  return window.__SCREEN_SCALE__ || 1;
}

/**
 * 重置缩放比例为1（100%）
 */
export function resetScale(): number {
  const appElement = document.getElementById('app');
  if (appElement) {
    applyScaling(appElement, 1);
  }
  return 1;
}

// 初始化屏幕检测
export function initScreenDetection() {
  const { systemResolution } = getAllZoom();
  lastScreenInfo = {
    width: window.screen.width,
    height: window.screen.height,
    systemResolution
  };
  
  // 设置定期检测
  setInterval(checkScreenChange, 2000); // 每2秒检查一次
  
  // 额外监听可能触发屏幕变化的事件
  window.addEventListener('resize', debounce(checkScreenChange, 500));
}

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: number | null = null;
  return function(...args: any[]) {
    if (timeout) clearTimeout(timeout);
    timeout = window.setTimeout(() => {
      func(...args);
      timeout = null;
    }, wait);
  };
}

// 检查屏幕是否发生变化
function checkScreenChange() {
  const currentWidth = window.screen.width;
  const currentHeight = window.screen.height;
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const { systemResolution } = getAllZoom();
  
  // 检查屏幕尺寸或分辨率是否有明显变化
  if (
    Math.abs(currentWidth - lastScreenInfo.width) > 50 || 
    Math.abs(currentHeight - lastScreenInfo.height) > 50 ||
    Math.abs(systemResolution - lastScreenInfo.systemResolution) > 100 ||
    Math.abs(viewportWidth - lastViewportWidth) > 100 ||
    Math.abs(viewportHeight - lastViewportHeight) > 100
  ) {
    console.log('检测到屏幕或窗口变化:', {
      old: {
        screen: { width: lastScreenInfo.width, height: lastScreenInfo.height },
        viewport: { width: lastViewportWidth, height: lastViewportHeight },
        systemResolution: lastScreenInfo.systemResolution
      },
      new: { 
        screen: { width: currentWidth, height: currentHeight },
        viewport: { width: viewportWidth, height: viewportHeight },
        systemResolution 
      }
    });
    
    // 更新存储的屏幕信息
    lastScreenInfo = {
      width: currentWidth,
      height: currentHeight,
      systemResolution
    };
    
    // 更新视口尺寸
    lastViewportWidth = viewportWidth;
    lastViewportHeight = viewportHeight;
    
    // 平滑应用新的缩放比例
    smoothScaleTransition();
  }
}

// 平滑过渡到新的缩放比例
function smoothScaleTransition() {
  // 获取当前缩放比例
  const currentScale = getCurrentScale();
  
  // 计算目标缩放比例
  const targetScale = calculateTargetScale();
  
  // 如果缩放比例相近，不需要动画
  if (Math.abs(currentScale - targetScale) < 0.05) {
    const appElement = document.getElementById('app');
    if (appElement) {
      applyScaling(appElement, targetScale);
    }
    return;
  }
  
  // 否则执行平滑过渡
  animateScaling(currentScale, targetScale);
}

// 根据屏幕分辨率计算目标缩放比例
function calculateTargetScale() {
  const { systemResolution } = getAllZoom();
  const viewportWidth = window.innerWidth;
  
  // 只对4K及以上分辨率进行缩放
  if (systemResolution >= 3800 || viewportWidth >= 3000) {
    return 1.75; // 4K屏幕
  } else {
    return 1.0; // 其他屏幕保持100%
  }
}

// 平滑动画过渡到新的缩放比例
function animateScaling(startScale: number, targetScale: number) {
  const appElement = document.getElementById('app');
  if (!appElement) return;
  
  const duration = 300; // 动画持续时间(毫秒)
  const startTime = performance.now();
  
  // 创建动画帧
  function animate(currentTime: number) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // 使用easeInOutQuad缓动函数使动画更平滑
    const easeProgress = progress < 0.5 
      ? 2 * progress * progress 
      : 1 - Math.pow(-2 * progress + 2, 2) / 2;
    
    // 计算当前动画帧的缩放值
    const currentAnimScale = startScale + (targetScale - startScale) * easeProgress;
    
    // 应用当前缩放
    const currentAppElement = document.getElementById('app');
    if (currentAppElement) {
      applyScaling(currentAppElement, currentAnimScale);
    }
    
    // 如果动画未完成，继续下一帧
    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  }
  
  // 开始动画
  requestAnimationFrame(animate);
}

// 处理窗口大小变化
function handleResize() {
  const wrapper = document.getElementById('app-scale-wrapper');
  const element = wrapper?.firstElementChild as HTMLElement;
  
  if (wrapper && element) {
    const newWidth = window.innerWidth;
    const newHeight = window.innerHeight;
    const scale = window.__SCREEN_SCALE__ || 1;
    
    // 更新包装器尺寸
    wrapper.style.width = '100vw';
    wrapper.style.height = '100vh';
    
    // 更新元素尺寸
    const scaledWidth = newWidth / scale;
    element.style.width = `${scaledWidth}px`;
    element.style.maxWidth = `${scaledWidth}px`;
    
    if (scale !== 1) {
      // 调整高度
      const scaledHeight = newHeight / scale;
      element.style.minHeight = `${scaledHeight}px`;
      element.style.height = `${scaledHeight}px`; // 明确设置高度
      
      // 小于1的缩放，特殊处理留白问题
      if (scale < 1) {
        wrapper.style.justifyContent = 'flex-start';
        wrapper.style.alignItems = 'flex-start';
        element.style.marginLeft = '0';
        element.style.marginRight = '0';
      } else {
        wrapper.style.justifyContent = 'center';
        wrapper.style.alignItems = 'flex-start'; // 确保顶部对齐
      }
    } else {
      // 缩放为1时，重置高度
      element.style.height = '100%';
      element.style.minHeight = '100%';
      wrapper.style.alignItems = 'flex-start';
    }
    
    // 额外检查是否需要修复布局
    fixLayoutIfNeeded(wrapper, element, scale);
  }
}

// 修复特殊情况下的布局问题
function fixLayoutIfNeeded(wrapper: HTMLElement, element: HTMLElement, scale: number) {
  // 检查是否存在水平留白
  const wrapperWidth = wrapper.clientWidth;
  const elementWidth = element.offsetWidth * scale;
  
  // 检查是否存在垂直留白
  const wrapperHeight = wrapper.clientHeight;
  const elementHeight = element.offsetHeight * scale;
  
  // 如果元素宽度比容器小，可能出现水平留白
  if (elementWidth < wrapperWidth && scale <= 1) {
    console.log('检测到可能的水平留白，尝试修复...');
    
    // 强制设置宽度为100%
    const newWidth = wrapperWidth / scale;
    element.style.width = `${newWidth}px`;
    element.style.maxWidth = `${newWidth}px`;
    element.style.marginLeft = '0';
    
    // 修改容器对齐方式
    wrapper.style.justifyContent = 'flex-start';
    wrapper.style.alignItems = 'flex-start';
  }
  
  // 如果元素高度比容器小，可能出现垂直留白
  if (elementHeight < wrapperHeight) {
    console.log('检测到可能的垂直留白，尝试修复...');
    
    if (scale !== 1) {
      // 对于缩放不为1的情况，设置元素高度为容器高度的缩放比例
      const newHeight = wrapperHeight / scale;
      element.style.height = `${newHeight}px`;
      element.style.minHeight = `${newHeight}px`;
    } else {
      // 对于缩放为1的情况，直接设置100%高度
      element.style.height = '100%';
      element.style.minHeight = '100%';
    }
    
    // 确保顶部对齐
    wrapper.style.alignItems = 'flex-start';
  }
}

// 修复当前布局问题
export function fixCurrentLayout() {
  const wrapper = document.getElementById('app-scale-wrapper');
  const element = wrapper?.firstElementChild as HTMLElement;
  
  if (wrapper && element) {
    const scale = window.__SCREEN_SCALE__ || 1;
    
    // 获取视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // 更新包装器尺寸
    wrapper.style.width = '100vw';
    wrapper.style.height = '100vh';
    wrapper.style.overflow = 'auto';
    wrapper.style.padding = '0';
    
    // 更新元素尺寸
    const scaledWidth = viewportWidth / scale;
    const scaledHeight = viewportHeight / scale;
    
    element.style.width = `${scaledWidth}px`;
    element.style.maxWidth = `${scaledWidth}px`;
    
    // 设置高度，确保没有底部留白
    element.style.height = `${scaledHeight}px`;
    element.style.minHeight = `${scaledHeight}px`;
    
    // 确保顶部对齐
    wrapper.style.alignItems = 'flex-start';
    
    // 根据缩放比例设置不同的对齐方式
    if (scale < 1) {
      wrapper.style.justifyContent = 'flex-start';
      element.style.marginLeft = '0';
      element.style.marginRight = '0';
    } else {
      wrapper.style.justifyContent = 'center';
    }
    
    // 应用额外的修复
    fixLayoutIfNeeded(wrapper, element, scale);
    
    console.log('已修复当前布局问题，缩放比例:', scale);
  }
}

// 获取缩放倍数（1*系统缩放倍数*浏览器缩放倍数）
function getZoom() {
  let zoom = 1;
  const ua = navigator.userAgent.toLowerCase();

  if (window.devicePixelRatio !== undefined) {
    zoom = window.devicePixelRatio;
  } else if (ua.indexOf('msie') !== -1) {
    // IE浏览器特定属性，使用类型断言处理
    const screenAny = window.screen as any;
    if (screenAny.deviceXDPI && screenAny.logicalXDPI) {
      zoom = screenAny.deviceXDPI / screenAny.logicalXDPI;
    }
  } else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {
    zoom = window.outerWidth / window.innerWidth;
  }
  return getDecimal(zoom);
}

// 保留两位小数
const getDecimal = (num: number) => {
  return Math.round(num * 100) / 100;
};

// 获取所有缩放相关的数据
export function getAllZoom() {
  // 总缩放倍数
  const zoom = getZoom();
  // 屏幕分辨率
  const screenResolution = window.screen.width;
  // 获取浏览器内部宽度
  const browserWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  // 浏览器缩放倍数
  // 浏览器外部宽度不受浏览器缩放影响，浏览器内部宽度受影响,所以根据这个可以计算出浏览器缩放倍数
  const browserZoom = getDecimal(window.outerWidth / browserWidth);
  // 系统缩放倍数
  const systemZoom = getDecimal(zoom / browserZoom);
  // 系统分辨率
  const systemResolution = Math.round(screenResolution * systemZoom);

  return {
    zoom,
    browserZoom,
    systemZoom,
    systemResolution
  };
}

// 为window添加__SCREEN_SCALE__属性
declare global {
  interface Window {
    __SCREEN_SCALE__: number;
  }
}

// 强制重新检测并应用缩放
export function forceRescale() {
  console.log('强制重新检测屏幕尺寸并应用缩放');
  // 重置上次检测信息，确保检测逻辑认为发生了变化
  lastScreenInfo = {
    width: 0,
    height: 0,
    systemResolution: 0
  };
  lastViewportWidth = 0;
  lastViewportHeight = 0;
  
  // 延迟执行以确保状态重置完成
  setTimeout(() => {
    applyScreenScaling();
    // 触发自定义事件，通知UI组件缩放已更改
    window.dispatchEvent(new CustomEvent('screen-scaling-auto-adjust'));
  }, 100);
} 