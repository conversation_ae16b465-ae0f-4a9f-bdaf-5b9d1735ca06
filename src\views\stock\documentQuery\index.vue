<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">单据查询</span>
          <div class="flex gap-2">
            <el-button @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>导出
            </el-button>
          </div>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-270px)] flex flex-col">
        <div class="w-full mb-2 gap-2 flex items-center flex-wrap">
          <el-form :model="searchForm" inline>
            <el-form-item label="单据类型：">
              <el-select v-model="searchForm.documentType" placeholder="请选择单据类型" style="width: 200px">
                <el-option v-for="item in documentTypeOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="单据编号：">
              <el-input v-model="searchForm.documentNo" placeholder="请输入单据编号" />
            </el-form-item>
            <el-form-item label="物料名称：">
              <el-input v-model="searchForm.material" placeholder="请输入物料名称" />
            </el-form-item>
            <el-form-item label="用途：">
              <el-input v-model="searchForm.purpose" placeholder="请输入用途" />
            </el-form-item>
            <el-form-item label="申请时间：">
              <el-date-picker v-model="searchForm.timeRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border height="100%">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="documentNo" label="单据编号" align="center" min-width="120" />
                <el-table-column prop="documentType" label="单据类型" align="center" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.documentType === '入库单'
                      ? ('success' as const)
                      : ('warning' as const)
                      ">
                      {{ scope.row.documentType }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="material" label="物料名称" align="center" min-width="120"
                  :show-overflow-tooltip="true" />
                <el-table-column prop="quantity" label="数量" align="center" width="80" />
                <el-table-column prop="purpose" label="用途" align="center" min-width="120"
                  :show-overflow-tooltip="true" />
                <el-table-column prop="warehouse" label="仓库" align="center" width="120" />
                <el-table-column prop="applicant" label="申请人" align="center" width="100" />
                <el-table-column prop="applyTime" label="申请时间" align="center" width="160" sortable />
                <el-table-column prop="status" label="状态" align="center" width="100">
                  <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                      {{ scope.row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="80" fixed="right">
                  <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end mt-2">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="单据详情" width="600px" :close-on-click-modal="false">
      <div class="flex flex-col gap-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">单据编号：</span>
            <span>{{ currentDetail.documentNo }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">单据类型：</span>
            <el-tag :type="currentDetail.documentType === '入库单'
              ? ('success' as const)
              : ('warning' as const)
              ">
              {{ currentDetail.documentType }}
            </el-tag>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">物料名称：</span>
            <span>{{ currentDetail.material }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">数量：</span>
            <span>{{ currentDetail.quantity }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">用途：</span>
            <span>{{ currentDetail.purpose }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">仓库：</span>
            <span>{{ currentDetail.warehouse }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">申请人：</span>
            <span>{{ currentDetail.applicant }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">申请时间：</span>
            <span>{{ currentDetail.applyTime }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-[100px] text-gray-500">状态：</span>
            <el-tag :type="getStatusType(currentDetail.status)">
              {{ currentDetail.status }}
            </el-tag>
          </div>
        </div>
        <div class="flex items-start">
          <span class="w-[100px] text-gray-500">备注：</span>
          <div class="flex-1 bg-gray-50 p-3 rounded">
            {{ currentDetail.remark || '暂无备注' }}
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Download } from '@element-plus/icons-vue'

// 定义单据类型接口
interface DocumentItem {
  documentNo: string
  documentType: string
  material: string
  quantity: number
  purpose: string
  warehouse: string
  applicant: string
  applyTime: string
  status: string
  remark?: string
}

// 搜索表单数据
const searchForm = reactive({
  documentType: '',
  documentNo: '',
  material: '',
  purpose: '',
  timeRange: [] as string[]
})

// 单据类型选项
const documentTypeOptions = [
  { value: '入库单', label: '入库单' },
  { value: '领用单', label: '领用单' }
]

// 表格数据
const tableData = ref<DocumentItem[]>([
  {
    documentNo: 'IN-20241030-001',
    documentType: '入库单',
    material: '三角带',
    quantity: 2,
    purpose: '设备维修',
    warehouse: '1007仓库名称',
    applicant: '张三',
    applyTime: '2024-10-30 09:37:56',
    status: '已完成'
  },
  {
    documentNo: 'OUT-20241030-001',
    documentType: '领用单',
    material: '水泵',
    quantity: 1,
    purpose: '日常维护',
    warehouse: '广仓库',
    applicant: '李四',
    applyTime: '2024-10-30 10:37:56',
    status: '待确认'
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(2)

// 获取状态标签类型
const getStatusType = (status: string) => {
  const statusMap = {
    待确认: 'warning',
    已完成: 'success',
    已取消: 'info'
  } as const
  return statusMap[status as keyof typeof statusMap] || 'info'
}

// 搜索方法
const handleSearch = () => {
  console.log('搜索条件：', {
    ...searchForm,
    startTime: searchForm.timeRange[0],
    endTime: searchForm.timeRange[1]
  })
}

// 重置方法
const handleReset = () => {
  searchForm.documentType = ''
  searchForm.documentNo = ''
  searchForm.material = ''
  searchForm.purpose = ''
  searchForm.timeRange = []
}

// 导出方法
const handleExport = () => {
  console.log('导出数据')
}

// 详情弹窗相关
const detailDialogVisible = ref(false)
const currentDetail = ref<DocumentItem>({
  documentNo: '',
  documentType: '',
  material: '',
  quantity: 0,
  purpose: '',
  warehouse: '',
  applicant: '',
  applyTime: '',
  status: '',
  remark: ''
})

// 详情方法
const handleDetail = (row: DocumentItem) => {
  currentDetail.value = { ...row }
  detailDialogVisible.value = true
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}
</script>

<style scoped lang="scss">
:deep(.el-form) {
  .el-form-item {
    margin-bottom: 12px;
  }
}
</style>
