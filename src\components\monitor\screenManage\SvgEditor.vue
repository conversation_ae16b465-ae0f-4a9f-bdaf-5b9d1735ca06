<template>
  <div class="svg-editor" ref="editorContainer">
    <div class="editor-toolbar">
      <div class="editor-tools">
        <el-button-group>
          <el-tooltip content="添加测点" placement="top">
            <el-button :type="editorMode === 'add' ? 'primary' : ''" size="small" @click="toggleMode('add')">
              <el-icon>
                <plus />
              </el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="编辑测点" placement="top">
            <el-button :type="editorMode === 'edit' ? 'primary' : ''" size="small" @click="toggleMode('edit')">
              <el-icon>
                <edit />
              </el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除测点" placement="top">
            <el-button :type="editorMode === 'delete' ? 'danger' : ''" size="small" @click="toggleMode('delete')">
              <el-icon>
                <delete />
              </el-icon>
            </el-button>
          </el-tooltip>
        </el-button-group>
      </div>

      <div class="editor-actions">
        <el-button-group>
          <el-tooltip content="放大" placement="top">
            <el-button size="small" @click="zoomIn" :disabled="zoomLevel >= 2.0">
              <el-icon><zoom-in /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="缩小" placement="top">
            <el-button size="small" @click="zoomOut" :disabled="zoomLevel <= 0.5">
              <el-icon><zoom-out /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="重置缩放" placement="top">
            <el-button size="small" @click="resetZoom">
              <el-icon>
                <refresh />
              </el-icon>
            </el-button>
          </el-tooltip>
        </el-button-group>
        <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
      </div>
    </div>

    <div class="editor-content" ref="editorContent" @wheel="handleWheel" @mousemove="handleMouseMove"
      @mouseup="handleMouseUp">
      <!-- 内容容器 -->
      <div class="content-wrapper" :style="{ transform: `scale(${zoomLevel})` }">
        <!-- SVG内容 -->
        <div class="svg-container" @click="handleSvgClick" v-if="svgContent">
          <div v-html="svgContent" ref="svgWrapper"></div>
        </div>

        <!-- 测点层 -->
        <div class="points-layer" v-if="svgContent">
          <div v-for="point in workingPoints" :key="point.id" class="monitor-point"
            :class="{ active: selectedPoint?.id === point.id }"
            :style="{ left: `${point.position.x}px`, top: `${point.position.y}px` }">

            <!-- 图标部分 -->
            <div class="point-icon"
              :class="[point.type, { dragging: dragTarget?.point.id === point.id && dragTarget?.type === 'icon' }]"
              @mousedown.ctrl.prevent.stop="startDrag($event, point, 'icon')" @click.stop="handlePointClick(point)">
              <el-icon>
                <component :is="getIconByType(point.type)" />
              </el-icon>
            </div>

            <!-- 显示值部分 -->
            <div v-if="point.showDisplay !== false" class="point-value"
              :class="{ dragging: dragTarget?.point.id === point.id && dragTarget?.type === 'value' }"
              :style="getValueStyle(point)" @mousedown.ctrl.prevent.stop="startDrag($event, point, 'value')">

              <div v-if="point.layoutTemplate !== 'valueOnly'" class="value-label"
                :class="{ dragging: dragTarget?.point.id === point.id && dragTarget?.type === 'label' }"
                :style="getLabelStyle(point)" @mousedown.ctrl.prevent.stop="startDrag($event, point, 'label')">
                {{ point.displayName || point.name }}
              </div>

              <div class="value-number"
                :class="{ dragging: dragTarget?.point.id === point.id && dragTarget?.type === 'number' }"
                :style="getNumberStyle(point)" @mousedown.ctrl.prevent.stop="startDrag($event, point, 'number')">
                {{ formatValue(point.value) }} {{ point.unit || '' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 提示层 -->
      <div class="drag-tip" v-if="isDragging">
        <p>
          正在拖动:
          <span v-if="dragTarget?.type === 'icon'" class="tip-icon">图标</span>
          <span v-if="dragTarget?.type === 'value'" class="tip-value">数值框</span>
          <span v-if="dragTarget?.type === 'label'" class="tip-label">标签</span>
          <span v-if="dragTarget?.type === 'number'" class="tip-number">数值</span>
        </p>
        <p>按住Ctrl并拖动鼠标调整位置</p>
      </div>

      <!-- 操作提示 -->
      <div class="help-tip">Ctrl+拖动可调整位置 | 鼠标滚轮+Ctrl缩放</div>
    </div>

    <!-- 配置对话框 -->
    <point-config-dialog v-model:visible="dialogVisible" :point-data="editingPoint" :metric-options="metricOptions"
      :station-options="stationOptions" @save="handlePointSave" />
  </div>
</template>

<script lang="ts" setup>
import { Delete, Edit, Plus, Refresh, ZoomIn, ZoomOut } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import PointConfigDialog from './PointConfigDialog.vue';
import { DragTarget, DragTargetType, MonitorPoint } from './types';

// 编辑器模式类型
type EditorMode = 'add' | 'edit' | 'delete' | 'none';

// 定义属性
const props = defineProps({
  svgContent: {
    type: String,
    default: ''
  },
  points: {
    type: Array as () => MonitorPoint[],
    default: () => []
  },
  metricOptions: {
    type: Array,
    default: () => []
  },
  stationOptions: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['update:points', 'save']);

// 引用
const editorContainer = ref<HTMLElement | null>(null);
const editorContent = ref<HTMLElement | null>(null);
const svgWrapper = ref<HTMLElement | null>(null);

// 编辑器状态
const editorMode = ref<EditorMode>('edit');
const zoomLevel = ref(1.0);
const dialogVisible = ref(false);
const selectedPoint = ref<MonitorPoint | null>(null);
const editingPoint = ref<MonitorPoint | null>(null);
const isDragging = ref(false);
const dragStart = reactive({ x: 0, y: 0 });
const dragTarget = ref<DragTarget | null>(null);
const mousePos = reactive({ x: 0, y: 0 });

// 工作点数组
const workingPoints = ref<MonitorPoint[]>([]);

// 监听points变化
watch(() => props.points, (newVal) => {
  workingPoints.value = JSON.parse(JSON.stringify(newVal || []));
}, { immediate: true, deep: true });

// 切换编辑器模式
const toggleMode = (mode: EditorMode) => {
  if (editorMode.value === mode) {
    editorMode.value = 'none';
  } else {
    editorMode.value = mode;
  }

  // 重置选中状态
  selectedPoint.value = null;
};

// 获取测点类型对应的图标
const getIconByType = (type: string) => {
  const iconMap: Record<string, string> = {
    'flow': 'WaterMeter',
    'level': 'Odometer',
    'ph': 'Cpu',
    'pump': 'SwitchButton',
    'gate': 'Switch'
  };

  return iconMap[type] || 'Monitor';
};

// 格式化数值
const formatValue = (value?: number) => {
  if (value === undefined || value === null) return '--';
  return value.toFixed(2);
};

// 获取数值样式
const getValueStyle = (point: MonitorPoint) => {
  const basePosition = point.displayPosition || { x: 0, y: 0 };

  return {
    left: `${basePosition.x}px`,
    top: `${basePosition.y}px`,
    backgroundColor: point.displayBgColor || 'rgba(255, 255, 255, 0.7)',
    color: point.displayColor || '#303133'
  };
};

// 获取标签样式
const getLabelStyle = (point: MonitorPoint) => {
  const labelPos = point.labelPosition || { x: 0, y: 0 };

  return {
    left: `${labelPos.x}px`,
    top: `${labelPos.y}px`
  };
};

// 获取数值样式
const getNumberStyle = (point: MonitorPoint) => {
  const numberPos = point.numberPosition || { x: 0, y: 0 };

  return {
    left: `${numberPos.x}px`,
    top: `${numberPos.y}px`
  };
};

// 放大
const zoomIn = () => {
  zoomLevel.value = Math.min(2.0, zoomLevel.value + 0.1);
};

// 缩小
const zoomOut = () => {
  zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1);
};

// 重置缩放
const resetZoom = () => {
  zoomLevel.value = 1.0;
};

// 鼠标滚轮缩放
const handleWheel = (event: WheelEvent) => {
  if (event.ctrlKey) {
    event.preventDefault();
    event.stopPropagation();

    const delta = event.deltaY || event.detail || (event as any).wheelDelta;
    if (delta < 0) {
      // 向上滚动，放大
      zoomLevel.value = Math.min(2.0, zoomLevel.value + 0.1);
    } else {
      // 向下滚动，缩小
      zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1);
    }
  }
};

// 处理SVG点击
const handleSvgClick = (event: MouseEvent) => {
  // 如果正在拖动，不处理点击
  if (isDragging.value) return;

  // 只在添加模式下处理
  if (editorMode.value !== 'add') return;

  // 获取点击位置相对于SVG的坐标
  if (!svgWrapper.value) return;

  const rect = svgWrapper.value.getBoundingClientRect();
  const x = (event.clientX - rect.left) / zoomLevel.value;
  const y = (event.clientY - rect.top) / zoomLevel.value;

  // 创建新测点
  const newPointId = `point_${Date.now()}`;
  editingPoint.value = {
    id: newPointId,
    name: '新测点',
    type: 'flow',
    position: { x, y },
    showDisplay: true,
    displayPosition: { x: x + 50, y },
    labelPosition: { x: 15, y: 0 },
    numberPosition: { x: 50, y: 0 },
    layoutTemplate: 'right'
  };

  // 打开配置对话框
  dialogVisible.value = true;
};

// 处理测点点击
const handlePointClick = (point: MonitorPoint) => {
  // 如果正在拖动，不处理点击
  if (isDragging.value) return;

  if (editorMode.value === 'edit') {
    selectedPoint.value = point;
    editingPoint.value = JSON.parse(JSON.stringify(point));
    dialogVisible.value = true;
  } else if (editorMode.value === 'delete') {
    deletePoint(point);
  }
};

// 删除测点
const deletePoint = (point: MonitorPoint) => {
  const index = workingPoints.value.findIndex(p => p.id === point.id);
  if (index !== -1) {
    workingPoints.value.splice(index, 1);
    emit('update:points', workingPoints.value);
    ElMessage.success('测点已删除');
  }
};

// 处理测点保存
const handlePointSave = (point: MonitorPoint) => {
  // 确保point对象包含所有必要的属性
  if (!point.position) {
    point.position = { x: 0, y: 0 };
  }

  if (!point.labelPosition) {
    point.labelPosition = { x: 15, y: 0 };
  }

  if (!point.numberPosition) {
    point.numberPosition = { x: 50, y: 0 };
  }

  if (!point.displayPosition) {
    // 根据布局模板计算显示位置
    point.displayPosition = {
      x: point.position.x + 50,
      y: point.position.y
    };
  }

  const index = workingPoints.value.findIndex(p => p.id === point.id);

  if (index !== -1) {
    // 更新现有测点
    workingPoints.value[index] = JSON.parse(JSON.stringify(point));
  } else {
    // 添加新测点
    workingPoints.value.push(JSON.parse(JSON.stringify(point)));
  }

  // 更新父组件中的点位数据
  emit('update:points', JSON.parse(JSON.stringify(workingPoints.value)));
  ElMessage.success(index !== -1 ? '测点已更新' : '测点已添加');
};

// 保存所有测点
const savePoints = () => {
  emit('save', workingPoints.value);
  ElMessage.success('配置已保存');
};

// 开始拖动
const startDrag = (event: MouseEvent, point: MonitorPoint, type: DragTargetType) => {
  isDragging.value = true;

  // 记录起始位置
  dragStart.x = event.clientX;
  dragStart.y = event.clientY;

  // 记录拖动目标
  dragTarget.value = { point, type };

  // 选中当前测点
  selectedPoint.value = point;

  // 防止事件冒泡和默认行为
  event.stopPropagation();
  event.preventDefault();
};

// 处理鼠标移动
const handleMouseMove = (event: MouseEvent) => {
  mousePos.x = event.clientX;
  mousePos.y = event.clientY;

  if (!isDragging.value || !dragTarget.value) return;

  // 计算移动距离
  const deltaX = (event.clientX - dragStart.x) / zoomLevel.value;
  const deltaY = (event.clientY - dragStart.y) / zoomLevel.value;

  // 更新点位置
  const point = workingPoints.value.find(p => p.id === dragTarget.value!.point.id);
  if (!point) return;

  switch (dragTarget.value.type) {
    case 'icon':
      // 移动图标位置
      point.position.x += deltaX;
      point.position.y += deltaY;

      // 如果显示位置未设置，一起移动
      if (point.displayPosition) {
        point.displayPosition.x += deltaX;
        point.displayPosition.y += deltaY;
      }
      break;

    case 'value':
      // 移动值框位置
      if (!point.displayPosition) {
        point.displayPosition = { x: 0, y: 0 };
      }
      point.displayPosition.x += deltaX;
      point.displayPosition.y += deltaY;
      point.hasCustomValuePosition = true;
      break;

    case 'label':
      // 移动标签位置
      if (!point.labelPosition) {
        point.labelPosition = { x: 0, y: 0 };
      }
      point.labelPosition.x += deltaX;
      point.labelPosition.y += deltaY;
      break;

    case 'number':
      // 移动数值位置
      if (!point.numberPosition) {
        point.numberPosition = { x: 0, y: 0 };
      }
      point.numberPosition.x += deltaX;
      point.numberPosition.y += deltaY;
      break;
  }

  // 更新拖动起始位置
  dragStart.x = event.clientX;
  dragStart.y = event.clientY;

  // 通知父组件
  emit('update:points', workingPoints.value);
};

// 处理鼠标松开
const handleMouseUp = () => {
  if (isDragging.value) {
    isDragging.value = false;
    dragTarget.value = null;
  }
};

// 组件挂载时
onMounted(() => {
  // 添加鼠标滚轮事件，防止在滚动时触发页面缩放
  if (editorContent.value) {
    editorContent.value.addEventListener('wheel', handleWheel, { passive: false });
    editorContent.value.addEventListener('mousewheel', handleWheel as any, { passive: false });
    editorContent.value.addEventListener('DOMMouseScroll', handleWheel as any, { passive: false });
  }

  // 添加全局鼠标事件，确保拖动正常结束
  document.addEventListener('mouseup', handleMouseUp);
});

// 组件卸载时
onUnmounted(() => {
  if (editorContent.value) {
    editorContent.value.removeEventListener('wheel', handleWheel);
    editorContent.value.removeEventListener('mousewheel', handleWheel as any);
    editorContent.value.removeEventListener('DOMMouseScroll', handleWheel as any);
  }
  document.removeEventListener('mouseup', handleMouseUp);
});
</script>

<style scoped lang="scss">
.svg-editor {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #dcdfe6;
  border-radius: 0;
}

.editor-toolbar {
  padding: 2px 6px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f7fa;
  height: 32px;

  .editor-tools,
  .editor-actions {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .zoom-level {
    font-size: 12px;
    color: #606266;
    width: 40px;
    text-align: center;
  }
}

.editor-content {
  flex: 1;
  overflow: auto;
  position: relative;
  background-color: #f5f7fa;
  padding: 0;
}

.content-wrapper {
  position: relative;
  transform-origin: top left;
  transition: transform 0.2s ease;
  display: inline-block;
}

.svg-container {
  position: relative;
  background-color: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  :deep(svg) {
    display: block;
    max-width: 100%;
    height: auto;
  }
}

.points-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.monitor-point {
  position: absolute;
  transform: translate(-50%, -50%);

  &.active {
    z-index: 10;
  }
}

.point-icon {
  width: 28px;
  height: 28px;
  background-color: #f5f7fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &.dragging {
    border: 2px solid #409eff;
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
  }

  &.flow {
    color: #409eff;
  }

  &.level {
    color: #67c23a;
  }

  &.ph {
    color: #e6a23c;
  }

  &.pump,
  &.gate {
    color: #f56c6c;
  }

  &.running {
    background-color: #67C23A;
    color: white;
  }

  &.stopped {
    background-color: #909399;
    color: white;
  }

  &.alarm {
    background-color: #F56C6C;
    color: white;
  }

  :deep(.el-icon) {
    font-size: 16px;
  }
}

.point-value {
  position: absolute;
  min-width: 80px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  pointer-events: auto;
  cursor: move;
  transform: translate(-50%, -50%);
  transition: all 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &.dragging {
    border: 1px solid #409eff;
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
  }
}

.value-label,
.value-number {
  position: relative;
  transition: all 0.3s;
  cursor: move;

  &.dragging {
    color: #409eff;
    font-weight: bold;
  }
}

.value-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.value-number {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.drag-tip {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;

  p {
    margin: 5px 0;
  }

  .tip-icon {
    color: #409eff;
    font-weight: bold;
  }

  .tip-value {
    color: #67c23a;
    font-weight: bold;
  }

  .tip-label {
    color: #e6a23c;
    font-weight: bold;
  }

  .tip-number {
    color: #f56c6c;
    font-weight: bold;
  }
}

.help-tip {
  position: fixed;
  bottom: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 99;
}
</style>