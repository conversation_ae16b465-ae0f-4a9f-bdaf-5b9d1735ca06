<template>
  <div class="w-full h-[calc(100vh-210px)]">
    <div class="w-full h-full flex flex-col">
      <div class="w-full h-[50px] flex p-4">
        <div class="h-full w-4/5 flex items-center gap-2">
          <el-form :inline="true" :model="formInline" class="flex items-center gap-2">
            <el-form-item label="规则名称" class="w-[300px]">
              <el-input v-model="formInline.name" placeholder="请输入规则名称" clearable />
            </el-form-item>
            <el-form-item label="告警级别" class="w-[200px]">
              <el-select v-model="formInline.level" placeholder="请选择告警级别" clearable>
                <el-option label="I级" value="I" />
                <el-option label="II级" value="II" />
              </el-select>
            </el-form-item>
            <el-form-item label="规则类型" class="w-[200px]">
              <el-select v-model="formInline.ruleType" placeholder="请选择规则类型" clearable>
                <el-option label="单因子" value="single" />
                <el-option label="组合因子" value="combination" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
              <el-button type="info" :icon="RefreshRight" @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="flex-1 h-full flex justify-end items-center gap-2">
          <el-button type="primary" :icon="Plus" @click="handleAddRule">新增规则</el-button>
        </div>
      </div>
      <div class="w-full flex-1 flex flex-col p-4">
        <div class="relative w-full h-full">
          <div class="absolute w-full h-full">
            <el-table :data="tableData" border height="100%">
              <el-table-column prop="name" label="规则名称" align="center" />
              <el-table-column prop="ruleType" label="规则类型" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.ruleType === 'single' ? 'success' : 'warning'">
                    {{ scope.row.ruleType === 'single' ? '单因子' : '组合因子' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="监测因子表达式" align="center">
                <template #default="scope">
                  <el-tooltip :content="scope.row.expression" placement="top" effect="light">
                    <span class="cursor-pointer text-blue-500">
                      {{ truncateText(scope.row.expression, 30) }}
                    </span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="level" label="告警级别" align="center" width="100">
                <template #default="scope">
                  <el-tag :type="getAlarmLevelType(scope.row.level)">{{ scope.row.level }}级</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="状态" align="center" width="100">
                <template #default="scope">
                  <el-switch v-model="scope.row.enabled" @change="(val) => handleStatusChange(scope.row, val)" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" align="center">
                <template #default="scope">
                  <el-button size="small" type="primary" link
                    @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" link
                    @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="flex-1 w-full flex items-center justify-end mt-2">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>
    <AlarmRuleDialog ref="dialogRef" @save-rule="handleSaveRule" />
  </div>
</template>
<script lang="ts" setup>
import { Plus, RefreshRight, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reactive, ref, onMounted, watch } from 'vue'
import AlarmRuleDialog from './Dialog/AlarmRuleDialog.vue'
import { getAlarmRules, deleteAlarmRule, saveAlarmRule } from '@/api/alarm'
import { useAppStore } from '@/store/modules/app'

// 定义props
const props = defineProps({
  isActive: {
    type: Boolean,
    default: false
  }
})

const dialogRef = ref<InstanceType<typeof AlarmRuleDialog> | null>(null)

const formInline = reactive({
  name: '',
  level: '',
  ruleType: ''
})

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

const tableData = ref([])

// 获取全局状态管理
const appStore = useAppStore()
// 是否正在加载数据
const isLoading = ref(false)

const getOperatorSymbol = (operator) => {
  const symbols = {
    '>': '>',
    '<': '<',
    '=': '=',
    '>=': '≥',
    '<=': '≤'
  }
  return symbols[operator] || operator
}

const getAlarmLevelType = (level) => {
  const types = {
    'I': 'danger',
    'II': 'warning',
    'III': 'info'
  }
  return types[level] || 'info'
}

const truncateText = (text, maxLength) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const handleAddRule = () => {
  dialogRef.value?.openDialog()
}

const handleEdit = (index: number, row: any) => {
  // 复制一份数据避免直接修改原始数据
  const editData = JSON.parse(JSON.stringify(row))
  
  // 在编辑前打印原始数据，便于调试
  console.log('编辑前的原始数据:', editData)

  // 处理单因子规则
  if (editData.ruleType === 'single') {
    // 如果缺少factor字段，尝试从表达式中提取
    if (!editData.factor && editData.expression) {
      // 从表达式中提取监测因子名称
      const match = editData.expression.match(/^([^><!=]+)[><!=]+.*$/)
      if (match && match[1]) {
        editData.factor = match[1].trim()
      }
    }
    
    // 如果缺少operator字段，尝试从表达式中提取
    if (!editData.operator && editData.expression) {
      const match = editData.expression.match(/[^><!=]+([><!=]+)[^><!=]+/)
      if (match && match[1]) {
        editData.operator = match[1].trim()
        // 确保等于操作符是==
        if (editData.operator === '=') {
          editData.operator = '=='
        }
      }
    }
    
    // 如果缺少threshold字段，尝试从表达式中提取
    if (!editData.threshold && editData.expression) {
      const match = editData.expression.match(/[^><!=]+[><!=]+(.+)$/)
      if (match && match[1]) {
        editData.threshold = match[1].trim()
      }
    }

    // 如果仍然没有factor，使用规则名称作为备选
    if (!editData.factor) {
      editData.factor = editData.name
    }
  }

  console.log('编辑数据:', editData)
  dialogRef.value?.openDialog(editData)
}

const handleDelete = (index: number, row: any) => {
  ElMessageBox.confirm(
    `确定要删除告警规则"${row.name}"吗？`,
    '删除提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        // 调用删除API，传递id参数
        await deleteAlarmRule(row.id)

        ElMessage({
          type: 'success',
          message: '删除成功',
        })

        // 重新加载数据
        loadAlarmRules()
      } catch (error) {
        console.error('删除规则失败:', error)
        ElMessage.error('删除失败，请重试')
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除',
      })
    })
}

const handleStatusChange = async (row, val) => {
  try {
    // 复制完整的行数据并更新启用状态
    const updateData = { ...row, enabled: val }

    // 调用接口更新规则状态
    await saveAlarmRule(updateData)

    const status = val ? '启用' : '禁用'
    ElMessage.success(`规则"${row.name}"已${status}`)
  } catch (error) {
    console.error('更新规则状态失败:', error)
    ElMessage.error('操作失败，请重试')
    // 回滚UI状态
    row.enabled = !val
  }
}

// 处理查询按钮点击
const handleSearch = () => {
  currentPage.value = 1
  loadAlarmRules()
}

// 获取告警规则列表数据
const loadAlarmRules = async () => {
  // 如果当前正在加载，直接返回
  if (isLoading.value) return

  try {
    isLoading.value = true
    // 获取当前选中水厂ID
    const factoryId = appStore.currentStation?.id

    // 调用接口获取数据
    const response: any = await getAlarmRules({
      page: currentPage.value,
      pageSize: pageSize.value,
      factoryId: factoryId,
      name: formInline.name || undefined,
      level: formInline.level || undefined,
      ruleType: formInline.ruleType || undefined
    })

    console.log('获取告警规则列表数据:', response)

    if (response && response.list) {
      // 直接返回 {list:[...], total:数值}
      tableData.value = response.list || []
      total.value = response.total || 0
    } else if (response && response.data) {
      // 返回在data字段中
      tableData.value = response.data.list || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('获取告警规则失败:', error)
    ElMessage.error('获取告警规则失败')
  } finally {
    isLoading.value = false
  }
}

// 监听组件是否激活
watch(() => props.isActive, (newActive) => {
  if (newActive && !isLoading.value) {
    loadAlarmRules()
  }
})

// 监听水厂变化，重新加载数据
watch(() => appStore.currentStation, () => {
  if (props.isActive && !isLoading.value) {
    handleSearch()
  }
}, { deep: true })

// 组件挂载时，如果是活跃状态则加载数据
onMounted(() => {
  if (props.isActive) {
    loadAlarmRules()
  }
})

// 处理重置按钮点击
const resetForm = () => {
  formInline.name = ''
  formInline.level = ''
  formInline.ruleType = ''
  handleSearch()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadAlarmRules()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadAlarmRules()
}

const handleSaveRule = (rule: any) => {
  // 保存成功后重新加载数据
  loadAlarmRules()
}
</script>
<style scoped lang="scss"></style>
