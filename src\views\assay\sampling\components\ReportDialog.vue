<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="报告名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入报告名称" />
      </el-form-item>
      <el-form-item label="报告编号" prop="code">
        <el-input v-model="formData.code" placeholder="请输入报告编号" />
      </el-form-item>
      <el-form-item label="报告类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择报告类型">
          <el-option label="水质报告" value="water" />
          <el-option label="污泥报告" value="sludge" />
          <el-option label="气体报告" value="gas" />
        </el-select>
      </el-form-item>
      
      <!-- 关联样品选择区域 -->
      <el-divider content-position="left">样品关联</el-divider>
      <el-form-item label="关联样品" prop="sampleIds">
        <el-select 
          v-model="formData.sampleIds" 
          multiple 
          placeholder="请选择关联样品"
          :popper-class="'sample-select-dropdown'"
        >
          <el-option 
            v-for="item in sampleOptions" 
            :key="item.id" 
            :label="item.code" 
            :value="item.id" 
          >
            <div class="flex flex-col">
              <span class="font-bold">{{ item.code }}</span>
              <span class="text-xs text-gray-500">{{ item.samplingPoint }} / {{ item.samplingDate }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item v-if="formData.sampleIds.length > 0" label="样品详情">
        <div class="bg-gray-50 p-2 rounded border">
          <div v-for="id in formData.sampleIds" :key="id" class="mb-2 last:mb-0">
            <div class="flex justify-between">
              <span class="font-bold">{{ getSampleById(id)?.code }}</span>
              <el-tag size="small" :type="getSampleStatusType(getSampleById(id)?.status)">
                {{ getSampleStatusText(getSampleById(id)?.status) }}
              </el-tag>
            </div>
            <div class="text-sm text-gray-600">采样点: {{ getSampleById(id)?.samplingPoint }}</div>
            <div class="text-sm text-gray-600">采样人: {{ getSampleById(id)?.samplingPerson }}</div>
            <div class="text-sm text-gray-600">采样日期: {{ getSampleById(id)?.samplingDate }}</div>
          </div>
        </div>
      </el-form-item>

      <!-- 报告基本信息 -->
      <el-divider content-position="left">报告信息</el-divider>
      <el-form-item label="报告日期" prop="reportDate">
        <el-date-picker v-model="formData.reportDate" type="date" placeholder="请选择报告日期" />
      </el-form-item>
      
      <el-form-item label="检测实验室" prop="laboratoryId">
        <el-select v-model="formData.laboratoryId" placeholder="请选择检测实验室">
          <el-option v-for="item in laboratoryOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="实验室负责人" prop="labManager">
        <el-input v-model="formData.labManager" placeholder="请输入实验室负责人" />
      </el-form-item>
      
      <el-form-item label="报告描述" prop="description">
        <el-input v-model="formData.description" type="textarea" placeholder="请输入报告描述" />
      </el-form-item>

      <!-- 权限设置区域 -->
      <el-divider content-position="left">权限设置</el-divider>
      <el-form-item label="查看权限" prop="viewPermission">
        <el-cascader
          v-model="formData.viewPermission"
          :options="permissionOptions"
          :props="{ multiple: true }"
          placeholder="请设置查看权限"
        />
      </el-form-item>
      
      <el-form-item label="下载权限" prop="downloadPermission">
        <el-cascader
          v-model="formData.downloadPermission"
          :options="permissionOptions"
          :props="{ multiple: true }"
          placeholder="请设置下载权限"
        />
      </el-form-item>
      
      <el-form-item>
        <el-checkbox v-model="formData.notifyStakeholders">通知相关人员</el-checkbox>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'
import { Dialog } from '../../../../components/Dialog'

defineOptions({ name: 'ReportDialog' })

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')

interface SampleOption {
  id: number;
  code: string;
  samplingPoint: string;
  samplingDate: string;
  samplingPerson: string;
  status: string;
}

interface LaboratoryOption {
  id: number;
  name: string;
}

// 样品选项（模拟数据）
const sampleOptions: SampleOption[] = [
  { id: 1, code: 'SP20230715001', samplingPoint: '进水总口', samplingDate: '2023-07-15', samplingPerson: '张三', status: 'completed' },
  { id: 2, code: 'SP20230716001', samplingPoint: '出水总口', samplingDate: '2023-07-16', samplingPerson: '李四', status: 'completed' },
  { id: 3, code: 'SP20230717001', samplingPoint: '生化池', samplingDate: '2023-07-17', samplingPerson: '王五', status: 'testing' },
  { id: 4, code: 'SP20230718001', samplingPoint: '出水总口', samplingDate: '2023-07-18', samplingPerson: '赵六', status: 'pending' },
  { id: 5, code: 'SP20230720001', samplingPoint: '进水总口', samplingDate: '2023-07-20', samplingPerson: '钱七', status: 'pending' }
]

// 实验室选项（模拟数据）
const laboratoryOptions: LaboratoryOption[] = [
  { id: 1, name: '中心化验室' },
  { id: 2, name: '微生物实验室' },
  { id: 3, name: '理化实验室' },
  { id: 4, name: '第三方检测机构' }
]

// 权限选项（模拟数据）
const permissionOptions = [
  {
    value: 'lab',
    label: '化验室',
    children: [
      { value: 'lab-technician', label: '化验员' },
      { value: 'lab-supervisor', label: '化验主管' }
    ]
  },
  {
    value: 'admin',
    label: '管理部门',
    children: [
      { value: 'plant-manager', label: '厂长' },
      { value: 'environment-officer', label: '环保专员' }
    ]
  },
  {
    value: 'external',
    label: '外部单位',
    children: [
      { value: 'environment-agency', label: '环保局' },
      { value: 'water-authority', label: '水务局' }
    ]
  }
]

// 根据样品ID获取样品信息
const getSampleById = (id: number): SampleOption | undefined => {
  return sampleOptions.find(sample => sample.id === id)
}

// 获取样品状态对应的标签类型
const getSampleStatusType = (status?: string): "primary" | "success" | "warning" | "info" | "danger" => {
  switch (status) {
    case 'pending':
      return 'info'
    case 'testing':
      return 'warning'
    case 'completed':
      return 'success'
    default:
      return 'info'
  }
}

// 获取样品状态对应的文本
const getSampleStatusText = (status?: string): string => {
  switch (status) {
    case 'pending':
      return '待送检'
    case 'testing':
      return '检测中'
    case 'completed':
      return '已检测'
    default:
      return '未知状态'
  }
}

// 表单数据
const formData = ref({
  id: undefined as number | undefined,
  name: '',
  code: '',
  type: '',
  sampleIds: [] as number[],
  description: '',
  viewPermission: [] as any[],
  downloadPermission: [] as any[],
  notifyStakeholders: false,
  remark: '',
  fileUrl: '',
  reportDate: '',
  laboratoryId: undefined as number | undefined,
  labManager: ''
})

// 表单校验规则
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '报告名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '报告编号不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '报告类型不能为空', trigger: 'change' }]
})

const formRef = ref()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (type: string, data?: any) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增送检报告' : '编辑送检报告'
  formType.value = type
  resetForm()
  
  // 如果是编辑模式，设置表单数据
  if (type === 'update' && data) {
    // 深拷贝，避免直接修改原始数据
    formData.value = JSON.parse(JSON.stringify(data))
    
    // 如果没有sampleIds，设置默认空数组
    if (!formData.value.sampleIds) formData.value.sampleIds = []
    
    // 如果没有viewPermission或downloadPermission，设置默认空数组
    if (!formData.value.viewPermission) formData.value.viewPermission = []
    if (!formData.value.downloadPermission) formData.value.downloadPermission = []
    
    // 设置默认当前日期
    if (!formData.value.reportDate) {
      formData.value.reportDate = new Date().toISOString().split('T')[0]
    }
  } else {
    // 创建模式也设置当前日期
    formData.value.reportDate = new Date().toISOString().split('T')[0]
    
    // 设置默认查看权限为化验室所有人员
    formData.value.viewPermission = [['lab', 'lab-technician'], ['lab', 'lab-supervisor']]
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 如果设置了通知相关人员，模拟发送通知
    if (formData.value.notifyStakeholders) {
      await new Promise(resolve => setTimeout(resolve, 300))
      ElMessage.success('已通知相关人员')
    }
    
    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    code: '',
    type: '',
    sampleIds: [],
    description: '',
    viewPermission: [],
    downloadPermission: [],
    notifyStakeholders: false,
    remark: '',
    fileUrl: '',
    reportDate: '',
    laboratoryId: undefined,
    labManager: ''
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.sample-select-dropdown {
  min-width: 400px;
}
</style>