<template>
  <div class="process-flow-container">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>检测数据管理流程</span>
        </div>
      </template>
      
      <div ref="chartRef" class="chart-container"></div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, onUnmounted, defineComponent } from 'vue'
import * as echarts from 'echarts'

const ProcessFlow = defineComponent({
  name: 'ProcessFlow',
  setup() {
    const chartRef = ref<HTMLDivElement>()
    let chart: echarts.ECharts | null = null
    
    onMounted(() => {
      if (!chartRef.value) return
      
      chart = echarts.init(chartRef.value)
      renderChart()
      
      window.addEventListener('resize', handleResize)
    })
    
    onUnmounted(() => {
      if (chart) {
        chart.dispose()
        chart = null
      }
      
      window.removeEventListener('resize', handleResize)
    })
    
    const handleResize = () => {
      chart?.resize()
    }
    
    const renderChart = () => {
      // 使用普通图表代替Sankey图表，避免DAG限制
      const option = {
        tooltip: {},
        legend: [{
          data: ['流程节点']
        }],
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut' as any,
        series: [
          {
            type: 'graph',
            layout: 'none',
            symbolSize: 50,
            roam: true,
            label: {
              show: true
            },
            edgeSymbol: ['circle', 'arrow'],
            edgeSymbolSize: [4, 10],
            edgeLabel: {
              fontSize: 12
            },
            // 使用手动布局可以避免自动布局算法的问题
            data: [
              {
                name: '待检测样品',
                x: 100,
                y: 300,
                itemStyle: {
                  color: '#bbdefb'
                }
              },
              {
                name: '数据录入',
                x: 250,
                y: 300,
                itemStyle: {
                  color: '#bbdefb'
                }
              },
              {
                name: '数据校验',
                x: 400,
                y: 300,
                itemStyle: {
                  color: '#bbdefb'
                }
              },
              {
                name: '数据异常',
                x: 400,
                y: 450,
                itemStyle: {
                  color: '#ffcdd2'
                }
              },
              {
                name: '待审核数据',
                x: 550,
                y: 300,
                itemStyle: {
                  color: '#bbdefb'
                }
              },
              {
                name: '已驳回数据',
                x: 550,
                y: 450,
                itemStyle: {
                  color: '#ffcdd2'
                }
              },
              {
                name: '已通过数据',
                x: 700,
                y: 300,
                itemStyle: {
                  color: '#c8e6c9'
                }
              },
              {
                name: '复检数据',
                x: 550,
                y: 150,
                itemStyle: {
                  color: '#ffe0b2'
                }
              },
              {
                name: '数据入库',
                x: 850,
                y: 300,
                itemStyle: {
                  color: '#c8e6c9'
                }
              },
              {
                name: '数据查询',
                x: 1000,
                y: 200,
                itemStyle: {
                  color: '#e1bee7'
                }
              },
              {
                name: '数据分析',
                x: 1000,
                y: 400,
                itemStyle: {
                  color: '#e1bee7'
                }
              }
            ],
            // 定义边，这里可以有环
            links: [
              {
                source: '待检测样品',
                target: '数据录入',
                lineStyle: {
                  width: 3,
                  curveness: 0.2
                }
              },
              {
                source: '数据录入',
                target: '数据校验',
                lineStyle: {
                  width: 3,
                  curveness: 0.2
                }
              },
              {
                source: '数据校验',
                target: '数据异常',
                lineStyle: {
                  width: 2,
                  curveness: 0.2
                }
              },
              {
                source: '数据校验',
                target: '待审核数据',
                lineStyle: {
                  width: 3,
                  curveness: 0.2
                }
              },
              {
                source: '数据异常',
                target: '复检数据',
                lineStyle: {
                  width: 2,
                  curveness: 0.4
                }
              },
              {
                source: '待审核数据',
                target: '已驳回数据',
                lineStyle: {
                  width: 2,
                  curveness: 0.2
                }
              },
              {
                source: '待审核数据',
                target: '已通过数据',
                lineStyle: {
                  width: 3,
                  curveness: 0.2
                }
              },
              {
                source: '已驳回数据',
                target: '数据录入',
                lineStyle: {
                  width: 2,
                  curveness: 0.5,
                  color: '#ff6b6b'
                }
              },
              {
                source: '复检数据',
                target: '待审核数据',
                lineStyle: {
                  width: 2,
                  curveness: 0.4
                }
              },
              {
                source: '已通过数据',
                target: '数据入库',
                lineStyle: {
                  width: 3,
                  curveness: 0.2
                }
              },
              {
                source: '数据入库',
                target: '数据查询',
                lineStyle: {
                  width: 2,
                  curveness: 0.2
                }
              },
              {
                source: '数据入库',
                target: '数据分析',
                lineStyle: {
                  width: 2,
                  curveness: 0.2
                }
              }
            ],
            lineStyle: {
              opacity: 0.9,
              width: 2,
              curveness: 0
            }
          }
        ]
      }
      
      chart?.setOption(option)
    }
    
    return {
      chartRef
    }
  }
})

export default ProcessFlow
</script>

<style scoped>
.process-flow-container {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  font-weight: 500;
}

.chart-container {
  height: 28rem;
  width: 100%;
}
</style> 