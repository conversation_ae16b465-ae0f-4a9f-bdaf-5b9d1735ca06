<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">评估知识库</span>
          <el-button type="primary" @click="handleAdd">添加规则</el-button>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex flex-col">
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="评估指标" style="width: 200px;">
              <el-select v-model="searchForm.index" placeholder="请选择评估指标" clearable>
                <el-option v-for="item in indexOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="评估方案" style="width: 200px;">
              <el-select v-model="searchForm.scheme" placeholder="请选择评估方案" clearable>
                <el-option v-for="item in schemeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" style="width: 200px;">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="启用" value="active" />
                <el-option label="禁用" value="inactive" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="relative w-full h-full">
          <div class="absolute w-full h-full">
            <el-table :data="tableData" border style="width: 100%">
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="index" label="评估指标" min-width="150" />
              <el-table-column prop="scheme" label="评估方案" min-width="150" />
              <el-table-column prop="condition" label="评估条件" min-width="200" show-overflow-tooltip />
              <el-table-column prop="conclusion" label="评估结论" min-width="200" show-overflow-tooltip />
              <el-table-column prop="suggestion" label="改进建议" min-width="200" show-overflow-tooltip />
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-switch v-model="row.status" :active-value="'active'" :inactive-value="'inactive'"
                    @change="handleStatusChange(row)" />
                </template>
              </el-table-column>
              <el-table-column prop="creator" label="创建人" width="100" align="center" />
              <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
              <el-table-column label="操作" width="200" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                  <el-button type="primary" link @click="handlePreview(row)">预览</el-button>
                  <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>

    </el-card>

    <!-- 规则编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '添加规则' : '编辑规则'" width="800px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="评估指标" prop="index">
          <el-select v-model="form.index" placeholder="请选择评估指标">
            <el-option v-for="item in indexOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="评估方案" prop="scheme">
          <el-select v-model="form.scheme" placeholder="请选择评估方案">
            <el-option v-for="item in schemeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="评估条件" prop="condition">
          <div class="border rounded p-4">
            <div class="mb-4">
              <el-button type="primary" link @click="addCondition">添加条件</el-button>
            </div>
            <div class="space-y-4">
              <div v-for="(item, index) in form.condition" :key="index" class="border rounded p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="font-bold">条件 {{ index + 1 }}</span>
                  <el-button type="danger" link @click="removeCondition(index)">删除</el-button>
                </div>
                <div class="grid grid-cols-3 gap-4">
                  <el-select v-model="item.operator" placeholder="请选择运算符">
                    <el-option label="大于" value=">" />
                    <el-option label="大于等于" value=">=" />
                    <el-option label="等于" value="=" />
                    <el-option label="小于等于" value="<=" />
                    <el-option label="小于" value="<" />
                    <el-option label="范围" value="range" />
                  </el-select>
                  <el-input v-if="item.operator !== 'range'" v-model="item.value" placeholder="请输入值" />
                  <template v-else>
                    <el-input v-model="item.min" placeholder="最小值" />
                    <el-input v-model="item.max" placeholder="最大值" />
                  </template>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="评估结论" prop="conclusion">
          <el-input v-model="form.conclusion" type="textarea" :rows="3" placeholder="请输入评估结论" />
        </el-form-item>
        <el-form-item label="改进建议" prop="suggestion">
          <el-input v-model="form.suggestion" type="textarea" :rows="3" placeholder="请输入改进建议" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 规则预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="规则预览" width="800px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="评估指标">{{ selectedItem?.index }}</el-descriptions-item>
        <el-descriptions-item label="评估方案">{{ selectedItem?.scheme }}</el-descriptions-item>
        <el-descriptions-item label="评估条件">
          <div v-for="(item, index) in selectedItem?.condition" :key="index" class="mb-2">
            <span v-if="item.operator !== 'range'">
              当指标值 {{ item.operator }} {{ item.value }}
            </span>
            <span v-else>
              当指标值在 {{ item.min }} 到 {{ item.max }} 之间
            </span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="评估结论">{{ selectedItem?.conclusion }}</el-descriptions-item>
        <el-descriptions-item label="改进建议">{{ selectedItem?.suggestion }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  index: '',
  scheme: '',
  status: ''
})

// 评估指标选项
const indexOptions = [
  { label: '功率因数', value: 'powerFactor' },
  { label: '负载率', value: 'loadRate' },
  { label: '温度', value: 'temperature' },
  { label: '振动', value: 'vibration' },
  { label: '噪声', value: 'noise' }
]

// 评估方案选项
const schemeOptions = [
  { label: '电力系统运行评估方案', value: 'power' },
  { label: '水处理系统评估方案', value: 'water' },
  { label: '环境监测评估方案', value: 'environment' }
]

// 表格数据
const tableData = ref([
  {
    id: '1',
    index: '功率因数',
    scheme: '电力系统运行评估方案',
    condition: [
      { operator: '<', value: '0.9' }
    ],
    conclusion: '功率因数偏低，影响电能质量',
    suggestion: '建议安装无功补偿装置，提高功率因数',
    status: 'active',
    creator: '张三',
    createTime: '2024-04-21 10:00:00'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框
const dialogVisible = ref(false)
const previewDialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref<FormInstance>()
const selectedItem = ref<any>(null)

// 表单数据
const form = reactive({
  index: '',
  scheme: '',
  condition: [
    {
      operator: '',
      value: '',
      min: '',
      max: ''
    }
  ],
  conclusion: '',
  suggestion: ''
})

// 表单验证规则
const rules = {
  index: [{ required: true, message: '请选择评估指标', trigger: 'change' }],
  scheme: [{ required: true, message: '请选择评估方案', trigger: 'change' }],
  condition: [{ required: true, message: '请设置评估条件', trigger: 'change' }],
  conclusion: [{ required: true, message: '请输入评估结论', trigger: 'blur' }],
  suggestion: [{ required: true, message: '请输入改进建议', trigger: 'blur' }]
}

// 方法
const addCondition = () => {
  form.condition.push({
    operator: '',
    value: '',
    min: '',
    max: ''
  })
}

const removeCondition = (index: number) => {
  form.condition.splice(index, 1)
}

const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', searchForm)
}

const resetSearch = () => {
  searchForm.index = ''
  searchForm.scheme = ''
  searchForm.status = ''
}

const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
  Object.assign(form, {
    index: '',
    scheme: '',
    condition: [
      {
        operator: '',
        value: '',
        min: '',
        max: ''
      }
    ],
    conclusion: '',
    suggestion: ''
  })
}

const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  Object.assign(form, row)
}

const handlePreview = (row: any) => {
  selectedItem.value = row
  previewDialogVisible.value = true
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实现删除逻辑
    ElMessage.success('删除成功')
  })
}

const handleStatusChange = (row: any) => {
  // 实现状态变更逻辑
  ElMessage.success(`规则已${row.status === 'active' ? '启用' : '禁用'}`)
}

const handleSave = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      // 实现保存逻辑
      console.log('表单数据:', form)
      dialogVisible.value = false
      ElMessage.success(dialogType.value === 'add' ? '添加成功' : '编辑成功')
    }
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
