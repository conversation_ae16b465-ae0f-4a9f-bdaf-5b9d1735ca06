// Define Fn type locally if it's not available from #/types
type Fn = () => void;

import { useTagsViewStoreWithOut } from '@/store/modules/tagsView';
import { computed, nextTick, unref } from 'vue';
import { RouteLocationNormalizedLoaded, useRouter } from 'vue-router';

export const useTagsView = () => {
  const tagsViewStore = useTagsViewStoreWithOut()

  const { currentRoute, replace, push } = useRouter()

  const selectedTag = computed(() => tagsViewStore.getSelectedTag)

  const closeAll = (callback?: Fn) => {
    tagsViewStore.delAllViews()
    callback?.()
  }

  const closeLeft = (callback?: Fn) => {
    tagsViewStore.delLeftViews(unref(selectedTag) as RouteLocationNormalizedLoaded)
    callback?.()
  }

  const closeRight = (callback?: Fn) => {
    tagsViewStore.delRightViews(unref(selectedTag) as RouteLocationNormalizedLoaded)
    callback?.()
  }

  const closeOther = (callback?: Fn) => {
    tagsViewStore.delOthersViews(unref(selectedTag) as RouteLocationNormalizedLoaded)
    callback?.()
  }

  const closeCurrent = (view?: RouteLocationNormalizedLoaded, callback?: Fn) => {
    if (view?.meta?.affix) return
    tagsViewStore.delView(view || unref(currentRoute))

    callback?.()
  }

  const refreshPage = async (view?: RouteLocationNormalizedLoaded, callback?: Fn) => {
    try {
      tagsViewStore.delCachedView()
      const { path, query } = view || unref(currentRoute)
      await nextTick()
      replace({
        path: '/redirect' + path,
        query: query
      }).catch(err => {
        console.error('Navigation error:', err)
      })
      if (callback) callback()
    } catch (error) {
      console.error('Error refreshing page:', error)
    }
  }

  const setTitle = (title: string, path?: string) => {
    tagsViewStore.setTitle(title, path)
  }

  return {
    closeAll,
    closeLeft,
    closeRight,
    closeOther,
    closeCurrent,
    refreshPage,
    setTitle
  }
}
