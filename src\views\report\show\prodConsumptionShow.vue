<template>
  <div class="app-container" v-loading="isLoading" element-loading-text="数据加载中，请稍候...">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="header-content">
            <div class="header-left">
              <div class="header-title">生产消耗数据展示</div>
            </div>
            <div class="header-right">
              <el-select v-model="selectedFactory" multiple collapse-tags collapse-tags-tooltip placeholder="请选择查询水厂范围"
                class="control-item" @change="handleFactoryChange">
                <el-option v-for="item in allFactories" :key="item.id" :label="item.name" :value="item.id"
                  :style="{ paddingLeft: 20 + (item.level - 1) * 20 + 'px' }" />
              </el-select>
              <el-select v-model="selectedIndicators" multiple collapse-tags collapse-tags-tooltip placeholder="指标筛选"
                class="control-item control-item-large" clearable @clear="handleClearIndicators">
                <el-option-group v-for="group in indicatorGroups" :key="group.value" :label="group.label">
                  <template v-for="item in group.options" :key="item.value">
                    <el-option :label="item.label" :value="item.value" class="parent-option">
                      {{ item.label }}
                    </el-option>
                    <el-option v-for="child in item.children" :key="child.value"
                      :label="`${item.label} - ${child.label}`" :value="child.value" class="child-option">
                      <span class="sub-item-label">{{ child.label }}</span>
                    </el-option>
                  </template>
                </el-option-group>
              </el-select>
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" class="control-item date-picker"
                @change="handleDateRangeChange" />
              <el-button type="success" @click="handleExport">导出</el-button>
            </div>
          </div>
        </div>
      </template>

      <el-table :data="filteredTableData" border style="width: 100%" :header-cell-style="headerCellStyle"
        :cell-style="cellStyle" :span-method="objectSpanMethod" size="small">
        <!-- 项目列 -->
        <el-table-column prop="group" label="项目" align="center" width="120" fixed="left" />

        <!-- 指标名称列 -->
        <el-table-column prop="name" label="指标名称" align="center" width="120" fixed="left">
          <template #default="scope">
            <template v-if="scope.row.group === '产量'">
              {{ scope.row.name }}
            </template>
            <template v-else>
              {{ scope.row.name }}
            </template>
          </template>
        </el-table-column>

        <!-- 指标内容列 -->
        <el-table-column prop="subName" label="指标内容" align="center" width="120" fixed="left">
          <template #default="scope">
            {{ scope.row.subName }}
            <template v-if="scope.row.unit">
              ({{ scope.row.unit }})
            </template>
          </template>
        </el-table-column>

        <!-- 水厂列（按父子关系组织） -->
        <template v-for="parentFactory in displayFactories" :key="parentFactory.id">
          <!-- 父水厂列 -->
          <el-table-column :label="parentFactory.name" align="center">
            <!-- 如果没有子水厂，直接显示本日和累计 -->
            <template v-if="!factoryParentChildMap.get(parentFactory.id)?.children.length">
              <el-table-column :label="'本日'" :prop="`factory${parentFactory.id}Current`" width="120" align="center">
                <template #default="scope">
                  {{ getFactoryValue(scope.row, parentFactory.id, 'current') }}
                </template>
              </el-table-column>
              <el-table-column :label="'累计'" :prop="`factory${parentFactory.id}Total`" width="120" align="center">
                <template #default="scope">
                  {{ getFactoryValue(scope.row, parentFactory.id, 'total') }}
                </template>
              </el-table-column>
            </template>

            <!-- 如果有子水厂，先显示子水厂，再显示父水厂自己的数据 -->
            <template v-else>
              <!-- 先显示子水厂 -->
              <template v-for="childFactory in getDisplayChildFactories(parentFactory.id)" :key="childFactory.id">
                <el-table-column :label="childFactory.name" align="center">
                  <el-table-column :label="'本日'" :prop="`factory${childFactory.id}Current`" width="120" align="center">
                    <template #default="scope">
                      {{ getFactoryValue(scope.row, childFactory.id, 'current') }}
                    </template>
                  </el-table-column>
                  <el-table-column :label="'累计'" :prop="`factory${childFactory.id}Total`" width="120" align="center">
                    <template #default="scope">
                      {{ getFactoryValue(scope.row, childFactory.id, 'total') }}
                    </template>
                  </el-table-column>
                </el-table-column>
              </template>

              <!-- 最后显示父水厂自己的数据 -->
              <el-table-column :label="'本日'" :prop="`factory${parentFactory.id}Current`" width="120" align="center">
                <template #default="scope">
                  {{ getFactoryValue(scope.row, parentFactory.id, 'current') }}
                </template>
              </el-table-column>
              <el-table-column :label="'累计'" :prop="`factory${parentFactory.id}Total`" width="120" align="center">
                <template #default="scope">
                  {{ getFactoryValue(scope.row, parentFactory.id, 'total') }}
                </template>
              </el-table-column>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { FactoryApi } from '@/api/report/factory/index'
import { prodConsumptionDataAPI } from '@/api/report/prodConsumptionData/index'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { computed, onMounted, ref, watch } from 'vue'

interface Factory {
  id: number
  name: string
  level?: number
  children?: Factory[]
}

interface IndicatorOption {
  label: string
  value: string
  children?: IndicatorOption[]
}

interface IndicatorGroup {
  label: string
  value: string
  options: IndicatorOption[]
}

// 加载状态
const isLoading = ref(false)

// 当前选中的时间区间（默认本月1号~昨天）
const getDefaultDateRange = () => {
  const today = dayjs()

  // 如果今天是月初1号，返回上个月的日期范围
  if (today.date() === 1) {
    const lastMonth = today.subtract(1, 'month')
    const start = lastMonth.startOf('month').format('YYYY-MM-DD')
    const end = lastMonth.endOf('month').format('YYYY-MM-DD')
    return [start, end]
  }

  // 否则返回本月1号到昨天
  const start = today.startOf('month').format('YYYY-MM-DD')
  const end = today.subtract(1, 'day').format('YYYY-MM-DD')
  return [start, end]
}
const dateRange = ref<[string, string]>(getDefaultDateRange())

// 当前选中的水厂
const selectedFactory = ref<number[]>([])

const oldSelec = ref<number[]>([])

// 选中的指标
const selectedIndicators = ref<string[]>([])

// 水厂列表
const factoryList = ref<Factory[]>([])

// 提取所有水厂（包括子水厂）
const allFactories = computed(() => {
  const result: { id: number; name: string; level: number }[] = []

  function extractFactories(factories: Factory[], level = 1) {
    factories.forEach(factory => {
      result.push({ id: factory.id, name: factory.name, level })
      if (factory.children && factory.children.length) {
        extractFactories(factory.children, level + 1)
      }
    })
  }

  extractFactories(factoryList.value)
  return result
})

// 构建父子工厂映射关系
const factoryParentChildMap = computed(() => {
  const map = new Map<number, { parent: Factory | null, children: Factory[] }>();

  // 初始化映射
  allFactories.value.forEach(factory => {
    map.set(factory.id, { parent: null, children: [] });
  });

  // 建立父子关系
  function buildRelationship(factories: Factory[], parent: Factory | null = null) {
    factories.forEach(factory => {
      // 设置父厂
      if (parent && map.has(factory.id)) {
        map.get(factory.id)!.parent = parent;
      }

      // 如果有子厂，则添加到父厂的children中
      if (parent && map.has(parent.id)) {
        map.get(parent.id)!.children.push(factory);
      }

      // 递归处理子厂
      if (factory.children && factory.children.length) {
        buildRelationship(factory.children, factory);
      }
    });
  }

  buildRelationship(factoryList.value);
  return map;
});

// 获取根工厂（没有父工厂的工厂）
const rootFactories = computed(() => {
  return allFactories.value.filter(factory => {
    return !factoryParentChildMap.value.get(factory.id)?.parent;
  });
});

// 计算要显示的水厂列表 - 根据展示需求调整
const displayFactories = computed(() => {
  // 如果没有选中任何水厂，显示所有水厂
  if (selectedFactory.value.length === 0) {
    return rootFactories.value; // 仅返回根工厂用于表头渲染
  }
  // 否则只显示选中的根工厂
  return rootFactories.value.filter(factory =>
    selectedFactory.value.includes(factory.id)
  );
});

// 获取应该显示的子级水厂列表
const getDisplayChildFactories = (parentId: number) => {
  const children = factoryParentChildMap.value.get(parentId)?.children || [];

  // 如果没有选中任何水厂，显示所有子级水厂
  if (selectedFactory.value.length === 0) {
    return children;
  }

  // 如果选中了父级水厂，显示所有子级水厂
  if (selectedFactory.value.includes(parentId)) {
    // 检查是否有选中的子级水厂
    const hasSelectedChild = children.some(child => selectedFactory.value.includes(child.id));

    // 如果有选中的子级水厂，只显示选中的子级水厂
    if (hasSelectedChild) {
      return children.filter(child => selectedFactory.value.includes(child.id));
    }
    // 否则显示所有子级水厂
    return children;
  }

  // 如果没有选中父级水厂，但选中了该父级下的某些子级水厂，只显示选中的子级
  const selectedChildren = children.filter(child => selectedFactory.value.includes(child.id));
  if (selectedChildren.length > 0) {
    return selectedChildren;
  }

  // 其他情况下不显示子级水厂
  return [];
};

// 获取工厂的所有子工厂（包括自身）
const getFactoryWithChildren = (factoryId: number) => {
  const result: number[] = [factoryId];
  const children = factoryParentChildMap.value.get(factoryId)?.children || [];

  function collectChildrenIds(factories: Factory[]) {
    factories.forEach(factory => {
      result.push(factory.id);
      const subChildren = factoryParentChildMap.value.get(factory.id)?.children || [];
      if (subChildren.length > 0) {
        collectChildrenIds(subChildren);
      }
    });
  }

  collectChildrenIds(children);
  return result;
};

// 指标分组数据
const indicatorGroups = computed<IndicatorGroup[]>(() => {
  // 使用嵌套Map来保持强绑定关系
  const groups = new Map<string, Map<string, string[]>>()

  // 首先构建完整的层级结构
  tableData.value.forEach(row => {
    if (!groups.has(row.group)) {
      groups.set(row.group, new Map())
    }
    const groupMap = groups.get(row.group)!
    if (!groupMap.has(row.name)) {
      groupMap.set(row.name, [])
    }
    if (!groupMap.get(row.name)!.includes(row.subName)) {
      groupMap.get(row.name)!.push(row.subName)
    }
  })

  // 转换为选择器需要的结构
  return Array.from(groups.entries()).map(([groupLabel, nameMap]) => ({
    label: groupLabel,
    value: groupLabel,
    options: Array.from(nameMap.entries()).map(([name, subNames]) => ({
      label: name,
      value: `${groupLabel}/${name}`,
      children: subNames.map(subName => ({
        label: subName,
        value: `${groupLabel}/${name}/${subName}`
      }))
    }))
  }))
})

// 表格数据
const tableData = ref([
  // 产量
  { group: '产量', name: '水量', subName: '进水水量', unit: '万m³', field: 'inFlowVol' },
  { group: '产量', name: '水量', subName: '出水水量', unit: '万m³', field: 'treatVol' },

  // 能耗
  { group: '能耗', name: '电量', subName: '电量', unit: 'KWh', field: 'elecCons' },
  { group: '能耗', name: '电量', subName: '电单耗', unit: 'KWh/Km³', field: 'elecSingleCons' },

  // 原材料消耗
  { group: '原材料消耗', name: '碳源', subName: '用量', unit: 'Kg', field: 'carbonUsage' },
  { group: '原材料消耗', name: '碳源', subName: '单耗', unit: 'Kg/Km³', field: 'carbonSingleCons' },
  { group: '原材料消耗', name: '次氯酸钠', subName: '用量', unit: 'Kg', field: 'sodiumHypoUsage' },
  { group: '原材料消耗', name: '次氯酸钠', subName: '单耗', unit: 'Kg/Km³', field: 'sodiumHypoSingleCons' },
  { group: '原材料消耗', name: '聚合氯化铝(PAC)', subName: '用量', unit: 'Kg', field: 'pacUsage' },
  { group: '原材料消耗', name: '聚合氯化铝(PAC)', subName: '单耗', unit: 'Kg/Km³', field: 'pacSingleCons' },
  { group: '原材料消耗', name: '聚合硫酸铁', subName: '用量', unit: 'Kg', field: 'ferricSulfUsage' },
  { group: '原材料消耗', name: '聚合硫酸铁', subName: '单耗', unit: 'Kg/Km³', field: 'ferricSulfSingleCons' },
  { group: '原材料消耗', name: '氢氧化钠', subName: '用量', unit: 'Kg', field: 'naohUsage' },
  { group: '原材料消耗', name: '氢氧化钠', subName: '单耗', unit: 'Kg/Km³', field: 'naohSingleCons' },
  { group: '原材料消耗', name: '固体聚丙烯酰胺(阴离子)', subName: '用量', unit: 'Kg', field: 'anPamUsage' },
  { group: '原材料消耗', name: '固体聚丙烯酰胺(阴离子)', subName: '单耗', unit: 'Kg/Km³', field: 'anPamSingleCons' },
  { group: '原材料消耗', name: '固体聚丙烯酰胺(阳离子)', subName: '用量', unit: 'Kg', field: 'catPamUsage' },
  { group: '原材料消耗', name: '固体聚丙烯酰胺(阳离子)', subName: '单耗', unit: 'Kg/Km³', field: 'catPamSingleCons' },

  // 污泥
  { group: '污泥', name: '污泥产量', subName: '60%污泥产量', unit: '吨(t)', field: 'sludge60Prod' },
  { group: '污泥', name: '污泥产量', subName: '80%污泥产量', unit: '吨(t)', field: 'sludge80Prod' },
  { group: '污泥', name: '污泥产量', subName: '绝干污泥产量', unit: '吨(t)', field: 'drySludgeProd' },
  { group: '污泥', name: '产泥率', subName: '湿泥', unit: 't/万m³', field: 'sludgeRate' },
  { group: '污泥', name: '固体聚丙烯酰胺(阳离子)', subName: '用量', unit: 'Kg', field: 'catPamSludgeUsage' },
  { group: '污泥', name: '固体聚丙烯酰胺(阳离子)', subName: '单耗', unit: 'Kg/Km³', field: 'catPamSludgeSingleCons' },
  { group: '污泥', name: '聚合氯化铝(PAC)', subName: '用量', unit: 'Kg', field: 'pacSludgeUsage' },
  { group: '污泥', name: '聚合氯化铝(PAC)', subName: '单耗', unit: 'Kg/Km³', field: 'pacSludgeSingleCons' },
  { group: '污泥', name: '液体铁盐(kg)', subName: '用量', unit: 'Kg', field: 'liqIronSaltUsage' },
  { group: '污泥', name: '液体铁盐(kg)', subName: '单耗', unit: 'Kg/Km³', field: 'liqIronSaltSingleCons' },
  { group: '污泥', name: '石灰', subName: '用量', unit: 'Kg', field: 'limeUsage' },
  { group: '污泥', name: '石灰', subName: '单耗', unit: 'Kg/Km³', field: 'limeSingleCons' }
])

// 过滤后的表格数据
const filteredTableData = computed(() => {
  if (!selectedIndicators.value.length) {
    return tableData.value
  }

  // 解析选中的指标
  const selectedGroups = new Set<string>()
  const selectedNames = new Set<string>()
  const selectedFullPaths = new Set<string>()

  selectedIndicators.value.forEach(path => {
    const [group, name, subName] = path.split('/')
    if (group && !name && !subName) {
      selectedGroups.add(group)
    } else if (group && name && !subName) {
      selectedNames.add(`${group}/${name}`)
    } else if (group && name && subName) {
      selectedFullPaths.add(path)
    }
  })

  return tableData.value.filter(row => {
    const groupPath = row.group
    const namePath = `${row.group}/${row.name}`
    const fullPath = `${row.group}/${row.name}/${row.subName}`

    return selectedGroups.has(row.group) ||
      selectedNames.has(namePath) ||
      selectedFullPaths.has(fullPath)
  })
})

// 表头样式
const headerCellStyle = {
  backgroundColor: '#bde7f9',
  color: '#606266',
  padding: '8px 0',
  textAlign: 'center' as const
}

// 单元格样式
const cellStyle = ({ column }) => {
  const isFixedColumn = column.fixed === 'left'
  return {
    padding: '2px 0',
    backgroundColor: isFixedColumn ? '#bde7f9' : '#ffffff'
  }
}

// 合并单元格方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  const data = filteredTableData.value

  // 合并"项目"列（group）
  if (column.property === 'group') {
    if (rowIndex === 0 || data[rowIndex - 1].group !== row.group) {
      let count = 1
      for (let i = rowIndex + 1; i < data.length; i++) {
        if (data[i].group === row.group) count++
        else break
      }
      return { rowspan: count, colspan: 1 }
    }
    return { rowspan: 0, colspan: 0 }
  }

  // 合并"指标名称"列（name）
  if (column.property === 'name') {
    if (rowIndex === 0 || data[rowIndex - 1].name !== row.name || data[rowIndex - 1].group !== row.group) {
      let count = 1
      for (let i = rowIndex + 1; i < data.length; i++) {
        if (data[i].name === row.name && data[i].group === row.group) count++
        else break
      }
      return { rowspan: count, colspan: 1 }
    }
    return { rowspan: 0, colspan: 0 }
  }

  return { rowspan: 1, colspan: 1 }
}


const specifiedSubNames = ['单耗', '电单耗', '湿泥'];

// 获取水厂数据值
const getFactoryValue = (row: any, factoryId: number, type: 'current' | 'total') => {
  // 定义需要判断的 subName 值数组
  if (type === 'total' && specifiedSubNames.includes(row.subName)) {
    return '/';
  }

  const field = type === 'current' ? `factory${factoryId}Current` : `factory${factoryId}Total`
  const value = row[field] || ''

  // 对进水水量和出水水量进行除以10的处理
  if (value && (row.field === 'inFlowVol' || row.field === 'treatVol')) {
    return (Number(value) / 10).toFixed(4)
  }

  return value
}

// 处理导出
const handleExport = async () => {
  try {
    isLoading.value = true

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('生产消耗数据')

    // 设置列宽 - 前三列是固定的
    const baseColumns = [
      { header: '项目', key: 'group', width: 15 },
      { header: '指标名称', key: 'name', width: 20 },
      { header: '指标内容', key: 'subName', width: 20 }
    ]

    // 统计所有需要显示的水厂（父子结构）
    const factories = displayFactories.value
    let factoryColumnKeys: { id: number, isParent: boolean }[] = []

    // 重新生成三行动态表头内容
    const headerRow1: string[] = []
    const headerRow2: string[] = []
    const headerRow3: string[] = []
    // 添加固定列的表头
    for (let i = 0; i < baseColumns.length; i++) {
      headerRow1.push(baseColumns[i].header)
      headerRow2.push('')
      headerRow3.push('')
    }

    // 统一处理表头内容和factoryColumnKeys，确保完全一致的顺序
    // 初始列索引
    let colIdx = baseColumns.length + 1
    // 遍历所有父水厂
    factories.forEach(parent => {
      const children = factoryParentChildMap.value.get(parent.id)?.children || []

      if (children.length === 0) {
        // 没有子级水厂：直接添加一个水厂
        factoryColumnKeys.push({ id: parent.id, isParent: true })

        // 表头：合并第1-2行显示水厂名称，第3行显示"本日"和"累计"
        headerRow1.push(parent.name, '')
        headerRow2.push('', '')
        headerRow3.push('本日', '累计')
        colIdx += 2
      } else {
        // 使用与表格相同的筛选逻辑获取应显示的子级水厂
        const displayedChildren = getDisplayChildFactories(parent.id)

        // 有子级水厂：第1行父水厂名称横跨所有列，第2行子水厂名称+父水厂自己
        const totalChildCols = displayedChildren.length * 2 + 2  // +2是父水厂自己的列

        // 第1行：父水厂名称横跨所有列
        headerRow1.push(parent.name)
        for (let i = 1; i < totalChildCols; i++) {
          headerRow1.push('')
        }

        // 第2-3行：先添加所有子水厂
        displayedChildren.forEach(child => {
          // 数据列表中添加子水厂
          factoryColumnKeys.push({ id: child.id, isParent: false })

          // 表头中添加子水厂
          headerRow2.push(child.name, '')
          headerRow3.push('本日', '累计')
          colIdx += 2
        })

        // 第2-3行：再添加父水厂自己
        factoryColumnKeys.push({ id: parent.id, isParent: true })
        headerRow2.push(parent.name, '')
        headerRow3.push('本日', '累计')
        colIdx += 2
      }
    })

    // 仅设置列宽，不设置header，避免覆盖我们自定义的表头
    const columnCount = baseColumns.length + factoryColumnKeys.length * 2
    const allColumns: { key: string; width: number }[] = []
    for (let i = 0; i < columnCount; i++) {
      if (i < baseColumns.length) {
        allColumns.push({ key: baseColumns[i].key, width: baseColumns[i].width })
      } else {
        allColumns.push({ key: `col${i}`, width: 15 })
      }
    }
    worksheet.columns = allColumns
    // 1. 先添加表头行
    worksheet.addRow(headerRow1)
    worksheet.addRow(headerRow2)
    worksheet.addRow(headerRow3)

    // 2. 设置表头样式
    for (let i = 1; i <= 3; i++) {
      const row = worksheet.getRow(i)
      row.height = 30
      row.font = { bold: true, size: 12 }
      row.alignment = { vertical: 'middle', horizontal: 'center' }
      row.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFBDE7F9' }
      }
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    }

    // 3. 合并单元格
    const mergedCellTracker = new Set<string>() // 跟踪已合并单元格

    // 合并固定列
    for (let i = 1; i <= baseColumns.length; i++) {
      try {
        worksheet.mergeCells(1, i, 3, i)
        for (let row = 1; row <= 3; row++) {
          mergedCellTracker.add(`${row},${i}`)
        }
      } catch (e) {
        console.warn('合并固定列失败:', e)
      }
    }

    // 合并水厂列
    colIdx = baseColumns.length + 1
    factories.forEach(parent => {
      const children = factoryParentChildMap.value.get(parent.id)?.children || []

      if (children.length === 0) {
        // 合并单元格：水厂名称合并两行两列
        try {
          const cellKey1 = `1,${colIdx}`
          const cellKey2 = `2,${colIdx}`
          const cellKey3 = `1,${colIdx + 1}`
          const cellKey4 = `2,${colIdx + 1}`

          if (!mergedCellTracker.has(cellKey1) && !mergedCellTracker.has(cellKey2) &&
            !mergedCellTracker.has(cellKey3) && !mergedCellTracker.has(cellKey4)) {
            worksheet.mergeCells(1, colIdx, 2, colIdx + 1)
            mergedCellTracker.add(cellKey1)
            mergedCellTracker.add(cellKey2)
            mergedCellTracker.add(cellKey3)
            mergedCellTracker.add(cellKey4)
          }
        } catch (e) {
          console.warn('合并水厂名称失败:', e)
        }

        colIdx += 2
      } else {
        // 使用与表格相同的筛选逻辑获取应显示的子级水厂
        const displayedChildren = getDisplayChildFactories(parent.id)

        // 有子级
        const totalCols = displayedChildren.length * 2 + 2  // 修改：+2 是父水厂自己的列
        const startCol = colIdx
        const endCol = startCol + totalCols - 1  // 包括父水厂自己的列

        // 合并第1行父水厂名称
        try {
          let canMergeParent = true
          for (let col = startCol; col <= endCol; col++) {
            if (mergedCellTracker.has(`1,${col}`)) {
              canMergeParent = false
              break
            }
          }

          if (canMergeParent) {
            worksheet.mergeCells(1, startCol, 1, endCol)
            for (let col = startCol; col <= endCol; col++) {
              mergedCellTracker.add(`1,${col}`)
            }
          }
        } catch (e) {
          console.warn('合并父水厂名称失败:', e)
        }

        // 合并第2行子水厂名称
        let childCol = colIdx
        displayedChildren.forEach(child => {
          try {
            const cellKey1 = `2,${childCol}`
            const cellKey2 = `2,${childCol + 1}`

            if (!mergedCellTracker.has(cellKey1) && !mergedCellTracker.has(cellKey2)) {
              worksheet.mergeCells(2, childCol, 2, childCol + 1)
              mergedCellTracker.add(cellKey1)
              mergedCellTracker.add(cellKey2)
            }
          } catch (e) {
            console.warn('合并子水厂名称失败:', e)
          }

          childCol += 2
        })

        // 添加：合并父工厂自己的名称
        try {
          const parentColStart = childCol
          const cellKey1 = `2,${parentColStart}`
          const cellKey2 = `2,${parentColStart + 1}`

          if (!mergedCellTracker.has(cellKey1) && !mergedCellTracker.has(cellKey2)) {
            worksheet.mergeCells(2, parentColStart, 2, parentColStart + 1)
            mergedCellTracker.add(cellKey1)
            mergedCellTracker.add(cellKey2)
          }
        } catch (e) {
          console.warn('合并父水厂自己名称失败:', e)
        }

        colIdx += totalCols  // 修改：更新colIdx以包含父工厂自己的列
      }
    })

    // 填充数据
    // 预处理：对产量和能耗进行特殊处理
    // 1. 产量水量：直接显示水量，不显示"进水水量"和"出水水量"
    // 2. 能耗电量：保持正常显示，通过合并单元格处理
    const processedData: any[] = []

    // 按组和名称分组
    const dataGroups: Record<string, Record<string, any[]>> = {}
    filteredTableData.value.forEach(row => {
      if (!dataGroups[row.group]) {
        dataGroups[row.group] = {}
      }
      if (!dataGroups[row.group][row.name]) {
        dataGroups[row.group][row.name] = []
      }
      dataGroups[row.group][row.name].push(row)
    })

    // 处理每个分组
    for (const group in dataGroups) {
      for (const name in dataGroups[group]) {
        const rows = dataGroups[group][name]

        if (group === '产量' && name === '水量') {
          // 产量-水量特殊处理：为进水水量和出水水量分别添加一行
          rows.forEach((row, index) => {
            const newRow = {
              group: index === 0 ? row.group : '', // 仅第一行显示组名
              name: '水量',
              subName: row.subName + (row.unit ? ` (${row.unit})` : ''), // 正确显示"进水水量"或"出水水量"
              originalRow: row,  // 保留原始数据用于获取水厂值
              isWaterRow: true,  // 标记为水量相关行
              shouldMergeNameColumn: true  // 标记需要合并指标名称列
            }
            processedData.push(newRow)
          })
        } else {
          // 其他指标正常处理
          rows.forEach((row, index) => {
            const newRow = {
              group: index === 0 ? row.group : '', // 仅第一行显示组名
              name: row.name,
              subName: row.subName + (row.unit ? ` (${row.unit})` : ''),
              originalRow: row  // 保留原始数据用于获取水厂值
            }
            processedData.push(newRow)
          })
        }
      }
    }

    // 添加数据行
    processedData.forEach(row => {
      const rowData = [
        row.group,
        row.name,
        row.subName
      ]
      factoryColumnKeys.forEach(f => {
        rowData.push(getFactoryValue(row.originalRow, f.id, 'current'))
        rowData.push(getFactoryValue(row.originalRow, f.id, 'total'))
      })
      worksheet.addRow(rowData)
    })

    // 数据行样式
    for (let i = 4; i <= processedData.length + 3; i++) {
      const row = worksheet.getRow(i)
      row.height = 25
      row.alignment = { vertical: 'middle', horizontal: 'center' }
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    }

    // 合并数据行单元格 - 先收集所有相同名称的行，按名称组织
    const nameToRows: Record<string, number[]> = {}
    const groupStartRows: Record<string, number> = {}
    let currentGroup = ''

    // 1. 收集所有指标名称的行号和项目分组信息
    processedData.forEach((row, index) => {
      const rowNumber = index + 4

      // 收集项目列开始行
      if (row.group && row.group !== currentGroup) {
        currentGroup = row.group
        groupStartRows[currentGroup] = rowNumber
      }

      // 收集指标名称的所有行
      if (!nameToRows[row.name]) {
        nameToRows[row.name] = []
      }
      nameToRows[row.name].push(rowNumber)
    })

    // 2. 合并项目列（A列）
    const groups = Object.keys(groupStartRows)
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i]
      const startRow = groupStartRows[group]
      const endRow = i === groups.length - 1
        ? processedData.length + 3
        : groupStartRows[groups[i + 1]] - 1

      try {
        worksheet.mergeCells(`A${startRow}:A${endRow}`)
      } catch (e) {
        console.warn(`合并项目 ${group} 失败:`, e)
      }
    }

    // 3. 合并指标名称列（B列）- 关键改进：按连续范围分组进行合并
    for (const name in nameToRows) {
      const rows = nameToRows[name].sort((a, b) => a - b) // 确保行号有序

      // 按连续范围分组
      const ranges: number[][] = []
      let currentRange: number[] = [rows[0]]

      for (let i = 1; i < rows.length; i++) {
        if (rows[i] === rows[i - 1] + 1) {
          // 连续的行
          currentRange.push(rows[i])
        } else {
          // 不连续，开始新范围
          ranges.push([...currentRange])
          currentRange = [rows[i]]
        }
      }

      // 添加最后一个范围
      if (currentRange.length > 0) {
        ranges.push(currentRange)
      }

      // 对每个连续范围进行合并
      ranges.forEach(range => {
        if (range.length > 1) {
          try {
            worksheet.mergeCells(`B${range[0]}:B${range[range.length - 1]}`)
          } catch (e) {
            console.warn(`合并指标名称 ${name} 失败:`, e)
          }
        }
      })
    }

    // 生成文件名
    const start = dateRange.value[0]
    const end = dateRange.value[1]
    const fileName = `生产消耗数据_${start}_${end}.xlsx`
    const buffer = await workbook.xlsx.writeBuffer()
    saveAs(new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), fileName)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    isLoading.value = false
  }
}

// 处理水厂选择变化
const handleFactoryChange = (value: number[]) => {
  // 处理父子水厂联动选择
  const newSelection = new Set<number>() // 使用Set避免重复

  // 记录已处理的水厂ID，避免重复处理
  const processedIds = new Set<number>()

  // 遍历当前选择的水厂ID
  for (const factoryId of value) {
    if (processedIds.has(factoryId)) continue
    processedIds.add(factoryId)

    // 添加当前选择的水厂
    newSelection.add(factoryId)

    // 获取当前水厂的父子关系信息
    const factoryInfo = factoryParentChildMap.value.get(factoryId)
    if (!factoryInfo) continue

    // 如果选择的是父级水厂，添加所有子级水厂
    const children = factoryInfo.children || []
    if (children.length > 0) {
      // 这是一个父级水厂，添加其所有子级
      for (const child of children) {
        if (!processedIds.has(child.id)) {
          processedIds.add(child.id)
          newSelection.add(child.id)
        }
      }
    }

    // 如果选择的是子级水厂，只添加其父级，不添加其他子级
    const parent = factoryInfo.parent
    if (parent && !processedIds.has(parent.id)) {
      processedIds.add(parent.id)
      newSelection.add(parent.id)
    }
  }

  // 检查是否有移除的水厂
  const removedItems = oldSelec.value.filter(id => !value.includes(id))

  // 处理移除的项
  for (const removedId of removedItems) {
    // 如果移除的是父级水厂，移除其所有子级
    const factoryInfo = factoryParentChildMap.value.get(removedId)
    if (!factoryInfo) continue

    const children = factoryInfo.children || []
    if (children.length > 0) {
      // 这是一个父级水厂，移除其所有子级
      for (const child of children) {
        newSelection.delete(child.id)
      }
    }

    // 如果移除的是子级水厂，检查是否需要移除父级
    const parent = factoryInfo.parent
    if (parent) {
      // 检查是否有其他同级水厂仍被选中
      const siblings = factoryParentChildMap.value.get(parent.id)?.children || []
      const hasSelectedSibling = siblings.some(sibling =>
        sibling.id !== removedId && newSelection.has(sibling.id)
      )

      // 如果没有其他子级被选中，移除父级
      if (!hasSelectedSibling) {
        newSelection.delete(parent.id)
      }
    }
  }

  // 更新选择结果
  selectedFactory.value = Array.from(newSelection)
  oldSelec.value = [...selectedFactory.value]
}

// 处理月份变化
const handleDateRangeChange = (val: [string, string]) => {
  // 这里可以根据 dateRange 重新请求数据
  // 例如 loadData(val[0], val[1])
  // loadData()
}

// 处理清空选择
const handleClearIndicators = () => {
  selectedIndicators.value = []
  loadData()
}

// 获取水厂列表
const getFactoryList = async () => {
  try {
    const res = await FactoryApi.queryAllFactoryTreeAndLevelOne()
    // getOriginal直接返回了axios的响应，需要从data中获取数据
    if (res && res.code === 0) {
      factoryList.value = res.data || []
    } else {
      ElMessage.error('获取水厂列表失败')
    }
  } catch (error) {
    console.error('获取水厂列表失败:', error)
    ElMessage.error('获取水厂列表失败')
  }
}

// 加载数据
const loadData = async () => {
  if (!dateRange.value[0] || !dateRange.value[1]) return

  isLoading.value = true
  try {
    // 不再清空水厂列表，保留从getFactoryList获取的数据
    tableData.value = tableData.value.map(row => {
      const rowData = { ...row }
      // 清空所有水厂的数据
      allFactories.value.forEach(factory => {
        rowData[`factory${factory.id}Current`] = ''
        rowData[`factory${factory.id}Total`] = ''
      })
      return rowData
    })

    const params = {
      startDate: dateRange.value[0],
      endDate: dateRange.value[1]
    }

    const result = await prodConsumptionDataAPI.queryProductCollectData(params)

    // 处理getOriginal返回的axios响应
    if (!result) {
      ElMessage.error('加载数据失败')
      return
    }

    const data = result || []

    // 为每个指标添加水厂数据
    tableData.value = tableData.value.map(row => {
      const rowData = { ...row }
      // 为每个水厂添加数据字段
      allFactories.value.forEach(factory => {
        const factoryData = data.find(d => d.factoryId === factory.id)

        if (factoryData) {
          // 使用day数据作为current，month数据作为total
          rowData[`factory${factory.id}Current`] = factoryData.day[row.field] || ''
          rowData[`factory${factory.id}Total`] = factoryData.month[row.field] || ''
        } else {
          rowData[`factory${factory.id}Current`] = ''
          rowData[`factory${factory.id}Total`] = ''
        }
      })
      return rowData
    })
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  isLoading.value = true;
  await getFactoryList();
  await loadData();
  isLoading.value = false;
})

// 监听月份变化
watch(dateRange, () => {
  loadData()
})
</script>

<style lang="scss" scoped>
.app-container {
  // padding: 20px;

  .card-header {
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        .header-title {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
        }
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 16px;

        .control-item {
          width: 200px;
        }

        .control-item-large {
          width: 300px;
        }

        .date-picker {
          width: 220px;
        }
      }
    }
  }
}

.report-container {
  height: calc(100vh - 84px);
  padding: 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      margin-right: auto;
    }

    .controls {
      display: flex;
      align-items: center;
      gap: 16px;

      .factory-select {
        width: 200px;
      }

      .indicator-select {
        width: 300px;
      }

      .month-picker {
        width: 200px;
      }
    }
  }

  .table-container {
    flex: 1;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .el-table {
      flex: 1;
      overflow: auto;

      // 设置固定列的背景色
      :deep(.el-table__fixed) {
        .el-table__cell {
          background-color: #f1f1f5;
        }
      }

      // 设置表头样式
      :deep(.el-table__header-wrapper) {
        .el-table__cell {
          background-color: #f1f1f5;
          color: #303133;
          font-weight: bold;
          height: 40px;
          padding: 8px;
          text-align: center;
        }
      }

      // 设置单元格样式
      :deep(.el-table__body-wrapper) {
        .el-table__cell {
          padding: 8px;
          text-align: center;
          height: 40px;
        }
      }

      // 设置滚动条样式
      :deep(.el-scrollbar__wrap) {
        overflow-x: auto;
        overflow-y: hidden;
      }

      // 设置表格边框
      :deep(.el-table__cell) {
        border-right: 1px solid #EBEEF5;
        border-bottom: 1px solid #EBEEF5;
      }
    }
  }
}

// 设置加载状态样式
.loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.parent-option {
  font-weight: bold;
  background-color: #f5f7fa;
}

.child-option {
  padding-left: 20px !important;
}

.sub-item-label {
  padding-left: 8px;
  color: #606266;
}

:deep(.el-select-dropdown__item) {
  padding: 0 20px;
  height: 34px;
  line-height: 34px;
}

:deep(.el-select-dropdown__item.parent-option) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item.child-option) {
  padding-left: 40px;
}

:deep(.el-select-dropdown__item.is-disabled) {
  color: var(--el-text-color-secondary);
  cursor: not-allowed;
}
</style>
