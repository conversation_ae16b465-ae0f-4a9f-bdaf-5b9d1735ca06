<template>
  <div class="station-selector-container">
    <div v-if="!hideSelector" class="station-display flex items-center">
      <Icon icon="ep:location" class="mr-1 text-16px" />
      <span class="station-label">当前水厂：</span>
      <el-tree-select v-model="selectedStationId" :data="treeData" :props="treeProps" :render-after-expand="false"
        :default-expand-all="true" placeholder="请选择水厂" class="station-tree-select" @change="handleStationChange">
        <template #default="{ data }">
          <span class="flex items-center">
            <Icon :icon="data.children && data.children.length ? 'ep:map-location' : 'ep:location'" class="icon" />
            <span class="station-text" :data-parent="data.children && data.children.length ? '1' : '0'">
              {{ data.name }}
            </span>
          </span>
        </template>
      </el-tree-select>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { SystemFactoryApi } from '@/api/system/factory'
import { useAppStore } from '@/store/modules/app'
import type { Factory } from '@/types/factory'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'

// 创建一个全局缓存，存储水厂列表数据
const CACHE_KEY = 'STATION_SELECTOR_CACHE'
const CACHE_EXPIRE_TIME = 1000 * 60 * 30 // 30分钟过期

// 检查是否有有效的缓存数据
const getStationListFromCache = (): Factory[] | null => {
  const cacheStr = localStorage.getItem(CACHE_KEY)
  if (!cacheStr) return null

  try {
    const cache = JSON.parse(cacheStr)
    // 检查缓存是否过期
    if (cache.expireTime && cache.expireTime > Date.now() && Array.isArray(cache.data)) {
      return cache.data
    }
  } catch (e) {
    console.error('解析水厂缓存数据失败', e)
  }

  // 缓存无效或过期，清除缓存
  localStorage.removeItem(CACHE_KEY)
  return null
}

// 将水厂列表数据存入缓存
const saveStationListToCache = (stationList: Factory[]) => {
  const cache = {
    data: stationList,
    expireTime: Date.now() + CACHE_EXPIRE_TIME
  }
  localStorage.setItem(CACHE_KEY, JSON.stringify(cache))
}

const props = defineProps({
  // 是否只显示活跃状态的水厂
  onlyActive: {
    type: Boolean,
    default: true
  },
  // 是否使用面包屑样式（更紧凑的布局）
  breadcrumbStyle: {
    type: Boolean,
    default: false
  },
  // 是否使用全局水厂状态，如果为false则组件维护自己的状态
  useGlobal: {
    type: Boolean,
    default: true
  },
  // 默认选中的水厂ID，仅在useGlobal=false时使用
  defaultStationId: {
    type: Number,
    default: undefined
  },
  // 是否隐藏选择器（只使用状态）
  hideSelector: {
    type: Boolean,
    default: false
  },
  // 是否强制刷新缓存
  forceRefresh: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['change'])
const appStore = useAppStore()

// 水厂列表
const stationList = ref<Factory[]>([])
// 当前选中的水厂ID
const selectedStationId = ref<number | undefined>(undefined)
// 加载状态
const loading = ref(false)

// TreeSelect 配置
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 处理树形数据，标记不可选的节点
const treeData = computed(() => {
  return processTreeData(stationList.value)
})

// 处理树形数据，为不可选的节点添加标记
const processTreeData = (data: Factory[]): Factory[] => {
  return data.map(item => {
    const processedItem = { ...item }

    // 如果有子节点，递归处理
    if (item.children && item.children.length > 0) {
      processedItem.children = processTreeData(item.children)
    }

    return processedItem
  })
}

// 判断节点是否可选（只有 level=3 的节点可选）
const isSelectable = (data: Factory): boolean => {
  return data.level === 3
}

// 获取水厂列表
const getStationList = async (forceRefresh = props.forceRefresh) => {
  // 如果已经在加载中，则返回
  if (loading.value) return

  // 先尝试从缓存获取数据，除非要求强制刷新
  if (!forceRefresh) {
    // const cachedData = getStationListFromCache()
    const cachedData = false
    if (cachedData) {
      console.log('使用缓存的水厂数据')
      processStationListData(cachedData)
      return
    }
  }

  try {
    loading.value = true
    // 使用treeFactoryByModuleCodeAndRoleCode接口获取水厂列表
    const res = await SystemFactoryApi.treeFactoryByModuleCodeAndRoleCode({"moduleCode": "system", "roleCode": "select"})
    if (res && res.data) {
      // 保存到缓存
      // saveStationListToCache(res.data)
      // 处理数据
      processStationListData(res.data)
    }
  } catch (error) {
    console.error('获取水厂列表失败:', error)
    ElMessage.error('获取水厂列表失败')
  } finally {
    loading.value = false
  }
}

// 处理水厂列表数据
const processStationListData = (data: Factory[]) => {
  stationList.value = data

  // 如果有水厂，根据全局/本地模式选择默认水厂
  if (stationList.value.length > 0) {
    if (!props.useGlobal) {
      // 本地模式：如果没有设置默认水厂ID，则使用第一个 level=3 的水厂
      if (!localStationId.value) {
        const firstLevel3 = findFirstLevel3Station(stationList.value)
        if (firstLevel3) {
          localStationId.value = firstLevel3.id
          selectedStationId.value = firstLevel3.id
          emitChangeEvent(firstLevel3)
        }
      } else {
        // 如果设置了默认水厂ID，查找并触发事件
        const station = findStationById(localStationId.value)
        if (station) {
          selectedStationId.value = station.id
          emitChangeEvent(station)
        }
      }
    } else {
      // 全局模式：检查当前选中的水厂是否在新的水厂列表中
      const globalStation = appStore.getCurrentStation
      if (!globalStation) {
        // 如果没有选中水厂，选择第一个 level=3 的水厂
        const firstLevel3 = findFirstLevel3Station(stationList.value)
        if (firstLevel3) {
          appStore.setCurrentStation(firstLevel3)
          selectedStationId.value = firstLevel3.id
          emitChangeEvent(firstLevel3)
        }
      } else {
        // 检查当前选中的水厂是否在新的水厂列表中
        const currentStationInList = findStationById(globalStation.id)
        if (currentStationInList) {
          // 如果当前水厂在新的列表中，继续使用
          selectedStationId.value = globalStation.id
        } else {
          // 如果当前水厂不在新的列表中，选择第一个 level=3 的水厂
          const firstLevel3 = findFirstLevel3Station(stationList.value)
          if (firstLevel3) {
            appStore.setCurrentStation(firstLevel3)
            selectedStationId.value = firstLevel3.id
            emitChangeEvent(firstLevel3)
          }
        }
      }
    }
  }
}

// 查找第一个 level=3 的水厂
const findFirstLevel3Station = (stations: Factory[]): Factory | null => {
  for (const station of stations) {
    if (station.level === 3) {
      return station
    }
    if (station.children && station.children.length > 0) {
      const found = findFirstLevel3Station(station.children)
      if (found) return found
    }
  }
  return null
}

// 递归查找水厂
const findStationById = (id: number): Factory | undefined => {
  // 遍历所有水厂
  for (const station of stationList.value) {
    // 检查当前水厂
    if (station.id === id) {
      return station
    }
    // 检查子水厂
    if (station.children && station.children.length > 0) {
      for (const child of station.children) {
        if (child.id === id) {
          return child
        }
      }
    }
  }
  return undefined
}

// 处理水厂选择变化
const handleStationChange = (stationId: number) => {
  const selectedStation = findStationById(stationId)
  if (selectedStation && isSelectable(selectedStation)) {
    if (props.useGlobal) {
      // 全局模式：更新全局状态
      appStore.setCurrentStation(selectedStation)
    } else {
      // 本地模式：更新本地状态
      localStationId.value = stationId
    }
    emitChangeEvent(selectedStation)

    // 添加切换成功的提示
    ElMessage.success(`已切换到：${selectedStation.name}`)
  } else if (selectedStation && !isSelectable(selectedStation)) {
    // 如果选择了不可选的节点，恢复之前的选择
    ElMessage.warning('该水厂不可选择，请选择子水厂')
    // 恢复之前的选择
    if (props.useGlobal) {
      const globalStation = appStore.getCurrentStation
      selectedStationId.value = globalStation?.id
    } else {
      selectedStationId.value = localStationId.value
    }
  }
}

// 本地状态管理
const localStationId = ref<number | undefined>(props.defaultStationId)

// 触发change事件
const emitChangeEvent = (station: Factory) => {
  emit('change', station)
}

// 显示的水厂信息（全局模式或本地模式）
const displayStation = computed(() => {
  if (props.useGlobal) {
    // 全局模式
    const globalStation = appStore.getCurrentStation
    if (globalStation) {
      return globalStation
    }
  } else {
    // 本地模式
    if (localStationId.value) {
      const localStation = findStationById(localStationId.value)
      if (localStation) {
        return localStation
      }
    }
  }
  return { id: 0, name: '请选择水厂' }
})

// 全局模式下，监听全局水厂变化
watch(() => appStore.getCurrentStation, (newStation) => {
  if (props.useGlobal && newStation) {
    selectedStationId.value = newStation.id
    emit('change', newStation)
  }
}, { immediate: true })

// 监听forceRefresh属性变化
watch(() => props.forceRefresh, (newValue) => {
  if (newValue) {
    getStationList(true)
  }
})

// 初始化
onMounted(() => {
  getStationList()
})

// 暴露获取水厂列表的方法，以便外部可以刷新
defineExpose({
  refreshStations: () => getStationList(true),
  getStationList: () => stationList.value,
  getCurrentStation: () => displayStation.value
})
</script>

<style lang="scss" scoped>
.station-selector-container {
  .station-display {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 16px;
    color: #2c3e50;
    transition: all 0.3s;
    line-height: 1.6;

    &:hover {
      color: var(--el-color-primary);
    }

    .station-label {
      font-size: 16px;
      color: inherit;
      white-space: nowrap;
      margin-right: 8px;
    }

    .station-tree-select {
      min-width: 200px;
    }
  }
}

:deep(.el-tree-node__content) {
  height: 34px !important;
  line-height: 34px !important;
  font-size: 16px !important;
  padding: 0 20px !important;
  transition: background 0.2s;
  display: flex;
  align-items: center;
}

:deep(.el-tree-node__content .icon) {
  font-size: 18px !important;
  margin-right: 6px;
  vertical-align: middle;
  color: inherit;
}

:deep(.el-tree-node__content .station-text) {
  font-size: 16px !important;
  line-height: 34px !important;
  font-weight: normal;
  color: #606266;
}

:deep(.el-tree-node:has(.el-tree-node__children) > .el-tree-node__content .station-text) {
  font-weight: bold !important;
  color: #222 !important;
}

:deep(.el-tree-node:has(.el-tree-node__children) > .el-tree-node__content) {
  background: #f5f7fa !important;
}

:deep(.el-tree-node:not(:has(.el-tree-node__children)) > .el-tree-node__content) {
  padding-left: 40px !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  color: var(--el-color-primary) !important;
  background: var(--el-color-primary-light-9) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content .station-text) {
  color: var(--el-color-primary) !important;
}

:deep(.el-tree-node__content:hover) {
  background: var(--el-fill-color-light) !important;
}
</style>
