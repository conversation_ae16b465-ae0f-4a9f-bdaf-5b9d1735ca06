<template>
  <div class="briefing-review-container">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="简报标题">
          <el-input v-model="queryParams.title" placeholder="请输入简报标题" clearable />
        </el-form-item>
        <el-form-item label="提交日期">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <el-table
      v-loading="loading"
      :data="briefingList"
      style="width: 100%"
      border
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="简报标题" min-width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="reportDate" label="报告日期" width="120" />
      <el-table-column prop="reporter" label="报告人" width="120" />
      <el-table-column prop="department" label="所属部门" width="120">
        <template #default="scope">
          {{ getDepartmentLabel(scope.row.department) }}
        </template>
      </el-table-column>
      <el-table-column prop="submitTime" label="提交时间" width="180" />
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template #default="scope">
          <el-button link type="primary" @click="viewBriefing(scope.row)">查看</el-button>
          <el-button v-if="scope.row.status === 'pending'" link type="success" @click="approvalBriefing(scope.row)">审核</el-button>
          <el-button v-if="scope.row.status === 'pending'" link type="danger" @click="rejectBriefing(scope.row)">退回</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 查看简报对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="简报详情"
      width="800px"
      destroy-on-close
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="简报标题">{{ currentBriefing.title }}</el-descriptions-item>
        <el-descriptions-item label="报告日期">{{ currentBriefing.reportDate }}</el-descriptions-item>
        <el-descriptions-item label="报告人">{{ currentBriefing.reporter }}</el-descriptions-item>
        <el-descriptions-item label="所属部门">{{ getDepartmentLabel(currentBriefing.department) }}</el-descriptions-item>
        <el-descriptions-item label="安全检查情况">{{ currentBriefing.safetyInspection }}</el-descriptions-item>
        <el-descriptions-item label="隐患整改情况">{{ currentBriefing.hazardRectification }}</el-descriptions-item>
        <el-descriptions-item label="安全培训情况">{{ currentBriefing.safetyTraining }}</el-descriptions-item>
        <el-descriptions-item label="事故分析情况">{{ currentBriefing.accidentAnalysis }}</el-descriptions-item>
        <el-descriptions-item label="下期工作计划">{{ currentBriefing.workPlan }}</el-descriptions-item>
        <el-descriptions-item label="注意事项">{{ currentBriefing.notes }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button v-if="currentBriefing.status === 'pending'" type="success" @click="approvalBriefing(currentBriefing)">通过</el-button>
          <el-button v-if="currentBriefing.status === 'pending'" type="danger" @click="rejectBriefing(currentBriefing)">退回</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="approvalDialogVisible"
      title="审核简报"
      width="500px"
      destroy-on-close
    >
      <el-form :model="approvalForm" label-width="100px">
        <el-form-item label="审核意见">
          <el-input
            v-model="approvalForm.comment"
            type="textarea"
            :rows="3"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="approvalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitApproval">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 退回对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="退回简报"
      width="500px"
      destroy-on-close
    >
      <el-form :model="rejectForm" label-width="100px">
        <el-form-item label="退回原因" required>
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入退回原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReject">确认退回</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 查询参数
const queryParams = reactive({
  title: '',
  dateRange: [],
  status: ''
})

// 状态选项
const statusOptions = [
  { label: '待审核', value: 'pending' },
  { label: '已通过', value: 'approved' },
  { label: '已退回', value: 'rejected' }
]

// 部门选项
const departmentOptions = [
  { label: '安全管理部', value: 'safety' },
  { label: '生产部', value: 'production' },
  { label: '工程部', value: 'engineering' },
  { label: '质量部', value: 'quality' }
]

// 获取部门标签
const getDepartmentLabel = (value: string) => {
  const dept = departmentOptions.find(item => item.value === value)
  return dept ? dept.label : value
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const statusObj = statusOptions.find(item => item.value === status)
  return statusObj ? statusObj.label : status
}

// 获取状态类型（用于el-tag样式）
const getStatusType = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'approved':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return 'info'
  }
}

const loading = ref(false)
const dialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const rejectDialogVisible = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const briefingList = ref([])
const currentBriefing = ref<any>({})

// 审核表单
const approvalForm = reactive({
  id: '',
  comment: ''
})

// 退回表单
const rejectForm = reactive({
  id: '',
  reason: ''
})

// 加载简报列表
const loadBriefingList = () => {
  loading.value = true
  
  // 模拟获取数据
  setTimeout(() => {
    briefingList.value = [
      {
        id: '1001',
        title: '2023年第一季度安全生产工作简报',
        reportDate: '2023-03-30',
        reporter: '张安全',
        department: 'safety',
        submitTime: '2023-03-30 14:25:36',
        status: 'pending',
        safetyInspection: '本季度共开展安全检查12次，发现安全隐患25项，已整改22项，剩余3项正在整改中。',
        hazardRectification: '上季度遗留隐患已全部整改完毕，本季度新增隐患主要集中在设备老化和操作流程不规范方面。',
        safetyTraining: '本季度组织了3次全厂安全知识培训，培训内容包括消防安全和应急预案演练。',
        accidentAnalysis: '本季度发生轻微安全事故1起，已处理完毕并加强了相关区域的安全措施。',
        workPlan: '计划下季度对重点区域进行专项检查，并组织一次全面的安全生产演练活动。',
        notes: '请各部门提高安全意识，做好日常安全检查工作。'
      },
      {
        id: '1002',
        title: '2023年第二季度安全生产工作简报',
        reportDate: '2023-06-29',
        reporter: '李安全',
        department: 'production',
        submitTime: '2023-06-29 16:10:42',
        status: 'approved',
        safetyInspection: '本季度共开展安全检查15次，发现安全隐患18项，已整改18项。',
        hazardRectification: '所有隐患均已按时整改完毕，整改效果良好。',
        safetyTraining: '本季度组织了2次全厂安全知识培训，培训效果显著。',
        accidentAnalysis: '本季度无安全事故发生，安全生产态势良好。',
        workPlan: '计划下季度开展消防安全专项检查和培训。',
        notes: '夏季高温，请注意防暑降温措施。'
      },
      {
        id: '1003',
        title: '2023年第三季度安全生产工作简报',
        reportDate: '2023-09-28',
        reporter: '王安全',
        department: 'engineering',
        submitTime: '2023-09-28 15:40:21',
        status: 'rejected',
        safetyInspection: '本季度共开展安全检查14次，发现安全隐患20项，已整改15项，剩余5项正在整改中。',
        hazardRectification: '上季度遗留隐患已整改完毕，本季度新增隐患主要在电气设备方面。',
        safetyTraining: '本季度组织了1次全厂安全知识培训，参与率95%。',
        accidentAnalysis: '本季度无安全事故发生。',
        workPlan: '计划下季度对重点区域进行专项检查。',
        notes: '注意天气变化，做好季节性安全防范。'
      }
    ]
    
    total.value = 3
    loading.value = false
  }, 500)
}

// 查询
const handleQuery = () => {
  currentPage.value = 1
  loadBriefingList()
}

// 重置查询
const resetQuery = () => {
  queryParams.title = ''
  queryParams.dateRange = []
  queryParams.status = ''
  handleQuery()
}

// 处理页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadBriefingList()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadBriefingList()
}

// 查看简报
const viewBriefing = (row: any) => {
  currentBriefing.value = row
  dialogVisible.value = true
}

// 审核简报
const approvalBriefing = (row: any) => {
  approvalForm.id = row.id
  approvalForm.comment = ''
  approvalDialogVisible.value = true
}

// 提交审核
const submitApproval = () => {
  // 模拟提交请求
  setTimeout(() => {
    ElMessage.success('审核通过成功')
    
    // 更新列表状态
    const index = briefingList.value.findIndex(item => item.id === approvalForm.id)
    if (index !== -1) {
      briefingList.value[index].status = 'approved'
    }
    
    approvalDialogVisible.value = false
    dialogVisible.value = false
  }, 500)
}

// 退回简报
const rejectBriefing = (row: any) => {
  rejectForm.id = row.id
  rejectForm.reason = ''
  rejectDialogVisible.value = true
}

// 提交退回
const submitReject = () => {
  if (!rejectForm.reason) {
    ElMessage.warning('请输入退回原因')
    return
  }
  
  // 模拟提交请求
  setTimeout(() => {
    ElMessage.success('退回成功')
    
    // 更新列表状态
    const index = briefingList.value.findIndex(item => item.id === rejectForm.id)
    if (index !== -1) {
      briefingList.value[index].status = 'rejected'
    }
    
    rejectDialogVisible.value = false
    dialogVisible.value = false
  }, 500)
}

onMounted(() => {
  loadBriefingList()
})
</script>

<style lang="scss" scoped>
.briefing-review-container {
  padding: 20px 0;
  
  .filter-container {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 