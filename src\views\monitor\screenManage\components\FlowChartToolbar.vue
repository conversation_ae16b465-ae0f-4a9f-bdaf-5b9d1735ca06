<template>
  <div class="toolbar">
    <div class="zoom-controls">
      <button @click="zoomOut" class="custom-btn text-btn">
        缩小
      </button>
      <el-dropdown trigger="click" @command="handleZoomLevelSelected" style="margin: 0 5px;">
        <span class="zoom-level el-dropdown-link">{{ Math.round(zoom * 100) }}%
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-for="level in zoomLevels" :key="level" :command="level / 100">
              {{ level }}%
            </el-dropdown-item>
            <el-dropdown-item command="fit">适应画面</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <button @click="zoomIn" class="custom-btn text-btn">
        放大
      </button>
      <button @click="resetZoom" class="custom-btn primary-btn reset-btn">
        适应画面
      </button>
      <button @click="resetPosition" class="custom-btn primary-btn reset-btn">
        重置位置
      </button>
    </div>
    <div class="tools-info">


      <!-- 添加拖动和添加元素提示 -->
      <span class="operation-tip">按住Ctrl键可在SVG范围内拖动监测点，位置始终保持相对于SVG的坐标</span>

      <!-- 监测点数量统计 -->
      <span class="points-count" v-if="points.length > 0">
        <i class="el-icon-location-information"></i> 监测点数量: {{ points.length }}
      </span>

      <!-- 添加位点下拉框 -->
      <div class="add-element-controls">
        <el-dropdown trigger="click" @command="handleAddCommand">
          <el-button type="primary" size="small">
            添加监测点 <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu class="custom-dropdown">
              <div class="dropdown-section-title">监测点图形</div>
              <el-dropdown-item command="circle">
                <div class="dropdown-item-content">
                  <span class="point-icon circle-point"></span>
                  <span>圆形监测点</span>
                </div>
              </el-dropdown-item>
              <el-dropdown-item command="square">
                <div class="dropdown-item-content">
                  <span class="point-icon square-point"></span>
                  <span>方形监测点</span>
                </div>
              </el-dropdown-item>
              <el-dropdown-item command="triangle">
                <div class="dropdown-item-content">
                  <span class="point-icon triangle-point"></span>
                  <span>三角形监测点</span>
                </div>
              </el-dropdown-item>
              <div class="dropdown-section-title">文本框</div>
              <el-dropdown-item command="text">
                <div class="dropdown-item-content">
                  <span class="point-icon text-icon"></span>
                  <span>标签文本</span>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 添加元素提示 -->
      <div v-if="addingElementType" class="adding-element-tip">
        <span>{{ getAddingElementTip() }}</span>
        <el-button size="small" type="danger" @click="cancelAddElement">取消添加</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  zoom: {
    type: Number,
    default: 1
  },
  points: {
    type: Array,
    default: () => []
  },
  addingElementType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits([
  'update:zoom',
  'reset-position',
  'add-element',
  'cancel-add'
])

// 预定义缩放级别
const zoomLevels = [25, 50, 75, 100, 125, 150, 200, 300, 400];

// 处理缩放级别选择
const handleZoomLevelSelected = (level) => {
  // 处理"适应画面"选项
  if (level === 'fit') {
    emit('update:zoom', 1); // 1.0代表完全适应视图
    ElMessage.success(`缩放调整为适应画面`);
    return;
  }

  emit('update:zoom', level);
  ElMessage.success(`缩放比例: ${Math.round(level * 100)}%`);
}

// 缩放控制
const zoomIn = () => {
  const newZoom = Math.min(props.zoom + 0.1, 4)
  emit('update:zoom', newZoom)
  ElMessage.success(`缩放比例: ${Math.round(newZoom * 100)}%`)
}

const zoomOut = () => {
  const newZoom = Math.max(props.zoom - 0.1, 0.2)
  emit('update:zoom', newZoom)
  ElMessage.success(`缩放比例: ${Math.round(newZoom * 100)}%`)
}

const resetZoom = () => {
  emit('update:zoom', 1)
  ElMessage.success('已调整缩放比例为适应画面')
}

// 重置SVG位置
const resetPosition = () => {
  emit('reset-position')
  ElMessage.success('已重置SVG位置')
}

// 处理添加监测点命令
const handleAddCommand = (command) => {
  emit('add-element', command)
}

// 取消添加元素
const cancelAddElement = () => {
  emit('cancel-add')
}

// 获取添加元素的提示文本
const getAddingElementTip = () => {
  if (!props.addingElementType) return '';

  if (props.addingElementType === 'text') {
    return '请在SVG上点击确认标签文本位置';
  }

  const pointTypeName = {
    circle: '圆形',
    square: '方形',
    triangle: '三角形'
  }[props.addingElementType] || '';

  return `请在SVG上点击确认${pointTypeName}监测点位置`;
}
</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  height: 50px;
  box-sizing: border-box;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-level {
  min-width: 50px;
  text-align: center;
  font-weight: 500;
  cursor: pointer;
  padding: 2px 8px;
  border-radius: 4px;
  transition: all 0.3s;
  user-select: none;
}

.zoom-level:hover {
  background-color: #ecf5ff;
  color: #409EFF;
}

.el-dropdown-link {
  display: flex;
  align-items: center;
}

.tools-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.zoom-tip,
.operation-tip,
.points-count {
  font-size: 13px;
  color: #606266;
}

.points-count {
  color: #409EFF;
  font-weight: 500;
}

.custom-btn {
  border: none;
  background: none;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.text-btn {
  color: #606266;
}

.text-btn:hover {
  color: #409EFF;
  background-color: #ecf5ff;
}

.primary-btn {
  color: #409EFF;
  background-color: #ecf5ff;
}

.primary-btn:hover {
  background-color: #d9ecff;
}

.reset-btn {
  margin-left: 8px;
}

.adding-element-tip {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #E6A23C;
  font-weight: 500;
}

.custom-dropdown .dropdown-section-title {
  padding: 8px 16px;
  font-size: 12px;
  color: #909399;
  background-color: #f5f7fa;
}

.dropdown-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.point-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.circle-point {
  background-color: #F56C6C;
  border-radius: 50%;
}

.square-point {
  background-color: #E6A23C;
}

.triangle-point {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 16px solid #409EFF;
}

.text-icon {
  background-color: transparent;
  position: relative;
}

.text-icon::before {
  content: "T";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
  color: #333;
}
</style>