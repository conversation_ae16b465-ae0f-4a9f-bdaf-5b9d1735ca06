<template>
  <div class="code-editor">
    <Codemirror
      :value="modelValue"
      @update:value="handleChange"
      :options="options"
      ref="editor"
      :height="height"
      :width="width"
      :hintTables="tables"
      @ready="onReady"
    />
  </div>
</template>

<script setup lang="ts">
import Codemirror from 'codemirror-editor-vue3'
import 'codemirror/lib/codemirror.css'
import 'codemirror/theme/monokai.css'
import 'codemirror/theme/dracula.css'
import 'codemirror/mode/sql/sql.js'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/addon/hint/show-hint.css'
import 'codemirror/addon/hint/show-hint'
import 'codemirror/addon/hint/sql-hint'
import 'codemirror/addon/hint/javascript-hint'

// SQL 自动补全配置
const sqlHintTables = {
  // 常用SQL关键字
  keywords: [
    'SELECT',
    'FROM',
    'WHERE',
    'AND',
    'OR',
    'IN',
    'NOT',
    'NULL',
    'LIKE',
    'BETWEEN',
    'GROUP BY',
    'ORDER BY',
    'HAVING',
    'JOIN',
    'LEFT JOIN',
    'RIGHT JOIN',
    'INNER JOIN',
    'UNION',
    'UNION ALL',
    'EXISTS',
    'CASE',
    'WHEN',
    'THEN',
    'ELSE',
    'END',
    'AS',
    'DISTINCT',
    'COUNT',
    'SUM',
    'AVG',
    'MAX',
    'MIN',
    'TOP',
    'LIMIT',
    'OFFSET'
  ],
  // 常用函数
  functions: [
    'ABS',
    'AVG',
    'CEILING',
    'COUNT',
    'FLOOR',
    'MAX',
    'MIN',
    'ROUND',
    'SUM',
    'CONCAT',
    'LENGTH',
    'LOWER',
    'UPPER',
    'SUBSTRING',
    'TRIM',
    'REPLACE',
    'CURRENT_DATE',
    'CURRENT_TIME',
    'CURRENT_TIMESTAMP',
    'DATE_FORMAT',
    'YEAR',
    'MONTH',
    'DAY',
    'HOUR',
    'MINUTE',
    'SECOND'
  ],
  // 数据类型
  types: [
    'INT',
    'BIGINT',
    'DECIMAL',
    'FLOAT',
    'DOUBLE',
    'CHAR',
    'VARCHAR',
    'TEXT',
    'DATE',
    'TIME',
    'DATETIME',
    'TIMESTAMP',
    'BOOLEAN'
  ]
}

// JavaScript 自动补全配置
const jsHintTables = {
  // JavaScript 内置对象
  Array: [
    'length',
    'push',
    'pop',
    'shift',
    'unshift',
    'splice',
    'slice',
    'concat',
    'join',
    'reverse',
    'sort',
    'filter',
    'map',
    'forEach',
    'reduce',
    'find',
    'findIndex',
    'includes',
    'indexOf',
    'lastIndexOf'
  ],
  String: [
    'length',
    'charAt',
    'charCodeAt',
    'concat',
    'includes',
    'indexOf',
    'lastIndexOf',
    'match',
    'replace',
    'search',
    'slice',
    'split',
    'substring',
    'toLowerCase',
    'toUpperCase',
    'trim'
  ],
  Object: [
    'keys',
    'values',
    'entries',
    'assign',
    'create',
    'defineProperty',
    'freeze',
    'seal',
    'is'
  ],
  Date: [
    'now',
    'parse',
    'UTC',
    'getFullYear',
    'getMonth',
    'getDate',
    'getDay',
    'getHours',
    'getMinutes',
    'getSeconds',
    'getMilliseconds',
    'getTime',
    'setTime'
  ],
  Math: [
    'PI',
    'E',
    'abs',
    'ceil',
    'floor',
    'round',
    'max',
    'min',
    'pow',
    'sqrt',
    'random',
    'sin',
    'cos',
    'tan',
    'log',
    'exp'
  ],
  // Vue 3 相关
  Vue: [
    'ref',
    'reactive',
    'computed',
    'watch',
    'watchEffect',
    'onMounted',
    'onUnmounted',
    'onUpdated',
    'nextTick',
    'defineComponent'
  ],
  // Element Plus 相关
  ElMessage: ['success', 'warning', 'info', 'error'],
  ElMessageBox: ['confirm', 'alert', 'prompt'],
  // 常用工具函数
  Utils: [
    'formatDate',
    'formatNumber',
    'deepClone',
    'debounce',
    'throttle',
    'isEmpty',
    'isObject',
    'isArray',
    'isFunction',
    'isString',
    'isNumber',
    'isBoolean'
  ]
}

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  language: {
    type: String,
    default: 'sql'
  },
  height: {
    type: [String, Number],
    default: '200px'
  },
  width: {
    type: [String, Number],
    default: '100%'
  },
  theme: {
    type: String,
    default: 'monokai'
  },
  tables: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'ready'])

const editor = ref()

// 刷新编辑器
const refreshEditor = () => {
  if (editor.value) {
    editor.value.refresh()
  }
}

// 编辑器配置
const options = computed(() => ({
  // 语言及语法模式
  mode: props.language === 'sql' ? 'text/x-sql' : props.language,
  // 主题
  theme: props.theme,
  // 显示函数
  line: true,
  // 显示行号
  lineNumbers: true,
  // 软换行
  lineWrapping: true,
  // tab宽度
  tabSize: 4,
  // 代码提示功能
  hintOptions: {
    // 避免由于提示列表只有一个提示信息时，自动填充
    completeSingle: false,
    // 根据语言类型获取对应的提示配置
    tables: props.language === 'sql' ? sqlHintTables : jsHintTables
  }
}))

// 处理内容变化
const handleChange = (value: string) => {
  emit('update:modelValue', value)
}

// 编辑器就绪事件
const onReady = (editor) => {
  editor.on('inputRead', function (cm, location) {
    if (/[a-zA-Z]/.test(location.text[0])) {
      cm.showHint()
    }
  })
  // 在编辑器就绪后刷新
  nextTick(() => {
    refreshEditor()
  })
  emit('ready', editor)
}

// 监听内容变化
watch(
  () => props.modelValue,
  () => {
    nextTick(() => {
      refreshEditor()
    })
  }
)

// 暴露方法给父组件
defineExpose({
  getEditor: () => editor.value,
  refreshEditor
})
</script>

<style lang="scss" scoped>
.code-editor {
  width: 100%;
  // border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}
</style>
