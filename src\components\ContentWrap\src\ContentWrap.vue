<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'

defineOptions({ name: 'ContentWrap' })

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('content-wrap')

defineProps({
  title: propTypes.string.def(''),
  message: propTypes.string.def(''),
  bodyStyle: propTypes.object.def({ padding: '10px' })
})
</script>

<template>
  <ElCard :body-style="bodyStyle" :class="[prefixCls, 'content-wrap-card']" shadow="never">
    <template v-if="title" #header>
      <div class="content-wrap-header">
        <span class="content-wrap-title">{{ title }}</span>
        <ElTooltip v-if="message" effect="dark" placement="right">
          <template #content>
            <div class="content-wrap-tooltip">{{ message }}</div>
          </template>
          <Icon :size="14" class="content-wrap-icon" icon="ep:question-filled" />
        </ElTooltip>
        <div class="content-wrap-header-slot">
          <slot name="header"></slot>
        </div>
      </div>
    </template>
    <slot></slot>
  </ElCard>
</template>

<style scoped>
.content-wrap-card {
  margin-bottom: 15px;
}

.content-wrap-header {
  display: flex;
  align-items: center;
}

.content-wrap-title {
  font-size: 16px;
  font-weight: 700;
}

.content-wrap-icon {
  margin-left: 5px;
}

.content-wrap-header-slot {
  display: flex;
  flex-grow: 1;
  padding-left: 20px;
}

.content-wrap-tooltip {
  max-width: 200px;
}
</style>
