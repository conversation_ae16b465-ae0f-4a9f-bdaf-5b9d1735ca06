<template>
  <div class="violation-standard">
    <div class="operation-bar">
      <el-button type="primary" @click="handleAddStandard">
        新增处罚标准
      </el-button>
    </div>
    
    <el-table :data="standardList" border style="width: 100%">
      <el-table-column prop="code" label="标准编号" width="120" />
      <el-table-column prop="type" label="违章类型" width="150" />
      <el-table-column prop="description" label="违章描述" />
      <el-table-column prop="punishment" label="处罚方式" width="200" />
      <el-table-column prop="score" label="扣分" width="100" />
      <el-table-column prop="fine" label="罚款(元)" width="120" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <add-standard-dialog
      v-model="showAddDialog"
      :edit-data="editData"
      @success="handleSuccess"
    />
  </div>
</template>

<script>
import AddStandardDialog from '../dialogs/AddStandardDialog.vue'

export default {
  name: 'ViolationStandard',
  components: {
    AddStandardDialog
  },
  data() {
    return {
      standardList: [],
      showAddDialog: false,
      editData: null
    }
  },
  created() {
    this.getStandardList()
  },
  methods: {
    getStandardList() {
      // TODO: 从后端获取处罚标准列表
      this.standardList = [
        {
          code: 'VIO001',
          type: '安全操作',
          description: '未按规定佩戴安全帽',
          punishment: '警告+罚款',
          score: 2,
          fine: 100
        }
      ]
    },
    handleAddStandard() {
      this.editData = null
      this.showAddDialog = true
    },
    handleEdit(row) {
      this.editData = { ...row }
      this.showAddDialog = true
    },
    handleDelete(row) {
      this.$confirm('确认删除该处罚标准？', '提示', {
        type: 'warning'
      }).then(() => {
        // TODO: 调用删除接口
        this.$message.success('删除成功')
        this.getStandardList()
      }).catch(() => {})
    },
    handleSuccess() {
      this.showAddDialog = false
      this.getStandardList()
    }
  }
}
</script>

<style lang="scss" scoped>
.violation-standard {
  .operation-bar {
    margin-bottom: 20px;
  }
}
</style> 