<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">入库管理</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleAdd">
              <el-icon>
                <Plus />
              </el-icon>新增入库
            </el-button>
            <el-button @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>导出
            </el-button>
          </div>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-270px)] flex flex-col">
        <div class="w-full h-[50px] mb-2 gap-2 flex items-center">
          <el-form :model="searchForm" inline>
            <el-form-item label="仓库：">
              <el-select v-model="searchForm.warehouse" placeholder="请选择仓库" style="width: 200px">
                <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="入库类型：">
              <el-select v-model="searchForm.type" placeholder="请选择入库类型" style="width: 200px">
                <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="入库物料：">
              <el-input v-model="searchForm.material" placeholder="请输入入库物料" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="tableData" border height="100%">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="stockInNo" label="入库单号" align="center" min-width="100" />
                <el-table-column prop="type" label="入库类型" align="center" width="100" />
                <el-table-column prop="material" label="入库物料" align="center" min-width="100"
                  :show-overflow-tooltip="true" />
                <el-table-column prop="operator" label="经办人" align="center" width="120" />
                <el-table-column prop="operateTime" label="经办时间" align="center" width="160" sortable />
                <el-table-column prop="stockInTime" label="入库时间" align="center" width="160" sortable />
                <el-table-column prop="warehouse" label="入库仓库" align="center" width="140" />
                <el-table-column label="操作" align="center" width="80" fixed="right">
                  <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end mt-2">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Plus, Download } from '@element-plus/icons-vue'

// 搜索表单数据
const searchForm = reactive({
  warehouse: '',
  type: '',
  material: ''
})

// 仓库选项
const warehouseOptions = [
  { value: '1007仓库名称', label: '1007仓库名称' },
  { value: '广仓库', label: '广仓库' },
  { value: '江东水厂1号仓库', label: '江东水厂1号仓库' }
]

// 入库类型选项
const typeOptions = [
  { value: '盘盈入库', label: '盘盈入库' },
  { value: '调整入库', label: '调整入库' },
  { value: '直接入库', label: '直接入库' },
  { value: '领用还回', label: '领用还回' }
]

// 表格数据
const tableData = ref([
  {
    stockInNo: '***********-173025227645',
    type: '盘盈入库',
    material: '三角带',
    operator: '中电建中南院',
    operateTime: '2024-10-30 09:37:56',
    stockInTime: '2024-10-30 09:37:56',
    warehouse: '1007仓库名称'
  },
  {
    stockInNo: 'IN-20240926-172734168203',
    type: '调整入库',
    material: '2024-04-11 14:14:25自动化新增 物料名称-改',
    operator: '中电建中南院',
    operateTime: '2024-09-26 17:08:02',
    stockInTime: '2024-09-26 17:08:02',
    warehouse: '1007仓库名称'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  },
  {
    stockInNo: '***********-172713960624',
    type: '直接入库',
    material: '泵-2水泵次氯酸钠',
    operator: '中电建中南院',
    operateTime: '2024-09-24 08:59:36',
    stockInTime: '2024-09-24 08:59:54',
    warehouse: '广仓库'
  }
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(3)

// 搜索方法
const handleSearch = () => {
  console.log('搜索条件：', searchForm)
}

// 重置方法
const handleReset = () => {
  searchForm.warehouse = ''
  searchForm.type = ''
  searchForm.material = ''
}

// 新增方法
const handleAdd = () => {
  console.log('新增入库')
}

// 导出方法
const handleExport = () => {
  console.log('导出数据')
}

// 详情方法
const handleDetail = (row: any) => {
  console.log('查看详情：', row)
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}
</script>

<style scoped lang="scss">
.stock-in-management {
  padding: 16px;

  .search-bar {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;

    .search-input {
      width: 280px;
    }
  }

  .operation-bar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  :deep(.el-table) {
    margin-top: 20px;

    .el-button {
      padding: 0 5px;
      height: auto;
    }
  }
}
</style>
