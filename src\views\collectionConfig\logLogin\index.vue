<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">登录日志</span>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleExport">
              <Icon icon="ep:download" class="mr-1" />导出记录
            </el-button>
          </div>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex flex-col">
        <!-- 搜索表单 -->
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="用户名" style="width: 200px;">
              <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
            </el-form-item>
            <el-form-item label="登录状态" style="width: 200px;">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="成功" value="success" />
                <el-option label="失败" value="failed" />
              </el-select>
            </el-form-item>
            <el-form-item label="IP地址" style="width: 200px;">
              <el-input v-model="searchForm.ip" placeholder="请输入IP地址" clearable />
            </el-form-item>
            <el-form-item label="时间范围" style="width: 300px;">
              <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 异常登录统计 -->
        <div class="mb-4">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>今日登录次数</span>
                  <Icon icon="ep:user" class="text-[32px] text-blue-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">128</span>
                  <span class="text-green-500">↑ 5.2%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>异常登录次数</span>
                  <Icon icon="ep:warning" class="text-[32px] text-red-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">3</span>
                  <span class="text-red-500">↑ 1.2%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>登录失败次数</span>
                  <Icon icon="ep:close" class="text-[32px] text-orange-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">5</span>
                  <span class="text-green-500">↓ 0.8%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6">
                <div class="flex items-center justify-between text-gray-500">
                  <span>活跃用户数</span>
                  <Icon icon="ep:user-filled" class="text-[32px] text-green-400" />
                </div>
                <div class="flex flex-row items-baseline justify-between">
                  <span class="text-3xl font-bold text-gray-700">45</span>
                  <span class="text-green-500">↑ 2.5%</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 登录记录列表 -->
        <div class="flex-1">
          <el-table :data="tableData" border style="width: 100%" height="100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="time" label="登录时间" width="180" align="center" />
            <el-table-column prop="username" label="用户名" min-width="120" />
            <el-table-column prop="ip" label="IP地址" width="150" align="center" />
            <el-table-column prop="location" label="登录地点" width="150" align="center" />
            <el-table-column prop="device" label="登录设备" width="150" align="center" />
            <el-table-column prop="status" label="登录状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                  {{ row.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="失败原因" min-width="150" show-overflow-tooltip />
            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleDetail(row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>

    <!-- 登录详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="登录详情" width="600px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="用户名">{{ currentDetail.username }}</el-descriptions-item>
        <el-descriptions-item label="登录时间">{{ currentDetail.time }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ currentDetail.ip }}</el-descriptions-item>
        <el-descriptions-item label="登录地点">{{ currentDetail.location }}</el-descriptions-item>
        <el-descriptions-item label="登录设备">{{ currentDetail.device }}</el-descriptions-item>
        <el-descriptions-item label="浏览器">{{ currentDetail.browser }}</el-descriptions-item>
        <el-descriptions-item label="操作系统">{{ currentDetail.os }}</el-descriptions-item>
        <el-descriptions-item label="登录状态">
          <el-tag :type="currentDetail.status === 'success' ? 'success' : 'danger'">
            {{ currentDetail.status === 'success' ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="失败原因" v-if="currentDetail.status === 'failed'">
          {{ currentDetail.reason }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 定义登录记录类型
interface LoginRecord {
  id: string
  time: string
  username: string
  ip: string
  location: string
  device: string
  browser: string
  os: string
  status: 'success' | 'failed'
  reason?: string
}

// 搜索表单
const searchForm = reactive({
  username: '',
  status: '',
  ip: '',
  dateRange: []
})

// 表格数据
const tableData = ref<LoginRecord[]>([
  {
    id: '1',
    time: '2024-04-21 10:00:00',
    username: 'admin',
    ip: '*************',
    location: '北京市',
    device: 'Windows PC',
    browser: 'Chrome 120.0.0.0',
    os: 'Windows 10',
    status: 'success'
  },
  {
    id: '2',
    time: '2024-04-21 09:30:00',
    username: 'user1',
    ip: '*************',
    location: '上海市',
    device: 'MacBook Pro',
    browser: 'Safari 17.0',
    os: 'macOS 14.0',
    status: 'failed',
    reason: '密码错误'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框
const detailDialogVisible = ref(false)
const currentDetail = ref<LoginRecord>({} as LoginRecord)

// 方法
const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', searchForm)
}

const resetSearch = () => {
  searchForm.username = ''
  searchForm.status = ''
  searchForm.ip = ''
  searchForm.dateRange = []
}

const handleDetail = (row: LoginRecord) => {
  currentDetail.value = row
  detailDialogVisible.value = true
}

const handleExport = () => {
  // 实现导出逻辑
  ElMessage.success('导出成功')
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
