<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="700px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      :disabled="type === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="作业类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择作业类型" style="width: 100%">
              <el-option label="高空作业" value="height" />
              <el-option label="动火作业" value="fire" />
              <el-option label="受限空间作业" value="confined" />
              <el-option label="临时用电" value="electricity" />
              <el-option label="吊装作业" value="lifting" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请部门" prop="department">
            <el-input v-model="form.department" placeholder="请输入申请部门" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="申请人" prop="applicant">
            <el-input v-model="form.applicant" placeholder="请输入申请人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              placeholder="请选择开始日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
              v-model="form.endDate"
              type="date"
              placeholder="请选择结束日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="作业地点" prop="location">
        <el-input v-model="form.location" placeholder="请输入作业地点" />
      </el-form-item>

      <el-form-item label="作业内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="3"
          placeholder="请详细描述作业内容"
        />
      </el-form-item>

      <el-form-item label="安全措施" prop="safetyMeasures">
        <el-input
          v-model="form.safetyMeasures"
          type="textarea"
          :rows="3"
          placeholder="请详细描述安全防护措施"
        />
      </el-form-item>

      <el-form-item label="作业人员" prop="workers">
        <el-select
          v-model="form.workers"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入作业人员姓名"
          style="width: 100%"
        >
          <el-option
            v-for="item in workerOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息"
        />
      </el-form-item>

      <template v-if="type === 'view' && form.status !== 'pending'">
        <el-divider>审批信息</el-divider>
        <el-form-item label="审批人">
          <el-input v-model="form.approver" disabled />
        </el-form-item>
        <el-form-item label="审批意见">
          <el-input v-model="form.approvalNotes" type="textarea" :rows="2" disabled />
        </el-form-item>
        <el-form-item label="审批时间">
          <el-input v-model="form.approvalTime" disabled />
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ type === 'view' ? '关闭' : '取消' }}</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="type !== 'view'">
          {{ type === 'add' ? '提交申请' : '确定' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'WorkTicketDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'add'
    },
    formData: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      form: {
        type: '',
        department: '',
        applicant: '',
        phone: '',
        startDate: '',
        endDate: '',
        location: '',
        content: '',
        safetyMeasures: '',
        workers: [],
        notes: '',
        status: 'pending',
        approver: '',
        approvalNotes: '',
        approvalTime: ''
      },
      rules: {
        type: [
          { required: true, message: '请选择作业类型', trigger: 'change' }
        ],
        department: [
          { required: true, message: '请输入申请部门', trigger: 'blur' }
        ],
        applicant: [
          { required: true, message: '请输入申请人姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        location: [
          { required: true, message: '请输入作业地点', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入作业内容', trigger: 'blur' }
        ],
        safetyMeasures: [
          { required: true, message: '请输入安全措施', trigger: 'blur' }
        ],
        workers: [
          { required: true, message: '请选择作业人员', trigger: 'change' },
          { type: 'array', min: 1, message: '至少选择一名作业人员', trigger: 'change' }
        ]
      },
      workerOptions: [
        { value: '张三', label: '张三' },
        { value: '李四', label: '李四' },
        { value: '王五', label: '王五' }
      ]
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    },
    dialogTitle() {
      const titles = {
        add: '申请作业票',
        edit: '编辑作业票',
        view: '查看作业票'
      }
      return titles[this.type] || '作业票信息'
    }
  },
  watch: {
    formData: {
      handler(val) {
        if (val) {
          this.form = { ...val }
        } else {
          this.resetForm()
        }
      },
      immediate: true
    }
  },
  methods: {
    resetForm() {
      this.form = {
        type: '',
        department: '',
        applicant: '',
        phone: '',
        startDate: '',
        endDate: '',
        location: '',
        content: '',
        safetyMeasures: '',
        workers: [],
        notes: '',
        status: 'pending',
        approver: '',
        approvalNotes: '',
        approvalTime: ''
      }
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    },
    handleClose() {
      this.visible = false
      this.resetForm()
    },
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()
        // 这里实现提交逻辑
        console.log('提交数据：', this.form)
        this.$emit('success')
        this.handleClose()
      } catch (error) {
        console.error('表单验证失败：', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dialog {
  :deep(.el-form) {
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 10px;
  }
}
</style> 