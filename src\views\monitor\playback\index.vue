<template>
  <div class="playback-page">
    <el-card class="playback-card">
      <div class="playback-content">
        <!-- 控制区域 -->
        <div class="control-panel">
          <el-form :inline="true" :model="searchForm" class="flex flex-wrap items-center">
            <el-form-item label="工艺段：" class="mb-2">
              <el-select v-model="selectedProcess" placeholder="请选择工艺段" style="width: 220px" filterable clearable
                :loading="processLoading" @change="handleProcessChange" class="process-select">
                <el-option v-for="item in processOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围：" class="mb-2 time-range-item">
              <el-date-picker v-model="searchForm.timeRange" type="datetimerange" range-separator="至"
                start-placeholder="开始时间" end-placeholder="结束时间" :disabledDate="disabledDate" @change="handleTimeChange"
                class="date-picker" />
            </el-form-item>
            <el-form-item label="播放速度" class="mb-2">
              <el-select v-model="searchForm.speed" placeholder="请选择播放速度" style="width: 100px" class="speed-select"
                @change="handleSpeedChange">
                <el-option v-for="item in speedOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="mb-2">
              <el-button-group class="control-buttons">
                <el-button type="primary" :icon="VideoPlay" @click="handlePlay">播放</el-button>
                <el-button type="primary" :icon="VideoPause" @click="handlePause">暂停</el-button>
                <el-button type="primary" :icon="RefreshRight" @click="handleReset">重置</el-button>
              </el-button-group>
            </el-form-item>
          </el-form>
        </div>

        <!-- 播放区域 -->
        <div class="player-container">
          <div v-if="!selectedProcess" class="empty-state">
            <el-empty description="请先选择工艺段" />
          </div>
          <div v-else-if="!svgContent" class="empty-state">
            <el-empty description="当前工艺段没有可用画面" />
          </div>
          <div v-else ref="svgContainer" class="svg-container" @wheel.prevent="handleWheel" @mousedown="handleMouseDown"
            @mouseup="handleMouseUp" @mouseleave="handleMouseUp">
            <div class="zoom-control">
              <button class="zoom-btn" @click="zoomIn" title="放大">
                <span class="zoom-icon">
                  <Plus />
                </span>
              </button>
              <button class="zoom-btn" @click="resetZoom" title="重置">
                <span class="zoom-icon">
                  <RefreshRight />
                </span>
              </button>
              <button class="zoom-btn" @click="zoomOut" title="缩小">
                <span class="zoom-icon">
                  <Minus />
                </span>
              </button>
            </div>
            <!-- 使用v-html插入SVG内容 -->
            <div class="svg-wrapper" ref="svgWrapper" :style="svgTransformStyle" v-html="svgContent"></div>
          </div>
        </div>

        <!-- 进度条区域 -->
        <div class="progress-container">
          <div class="flex items-center">
            <span class="mr-2 progress-label">进度:</span>
            <span class="mr-2 text-sm text-gray-500">{{ playbackProgress }}%</span>
            <el-slider v-model="playbackProgress" :min="0" :max="100" :format-tooltip="formatTooltip"
              @change="handleProgressChange" @input="handleProgressInput" class="flex-1" />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { getIndicatorDataByScreenAndTime, getPointsDataByScreenId, getScreenById, getScreenListByFactoryId } from '@/api/monitor/screenManage'
import { useAppStore } from '@/store/modules/app'
import { Minus, Plus, RefreshRight, VideoPause, VideoPlay } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onBeforeUnmount, onMounted, reactive, ref } from 'vue'
import { SVG_URL } from '@/config/url'

// App store
const appStore = useAppStore()

// 表单数据
const searchForm = reactive({
  timeRange: null as any,
  speed: 1
})

// 播放速度选项
const speedOptions = [
  { value: 0.5, label: '0.5x' },
  { value: 1, label: '1.0x' },
  { value: 2, label: '2.0x' },
  { value: 4, label: '4.0x' }
]

// 回放进度
const playbackProgress = ref(0)

// 工艺段相关状态
const selectedProcess = ref<string | number>('')
const currentScreen = ref<any>(null)
const processOptions = ref<any[]>([])
const processLoading = ref(false)

// SVG相关
const svgContainer = ref<HTMLElement | null>(null)
const svgWrapper = ref<HTMLElement | null>(null)
const svgContent = ref<string>('')
const scale = ref(1)
const translateX = ref(0)
const translateY = ref(0)
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartY = ref(0)
const pointsData = ref<any[]>([])

/**
 * 工艺画面回放功能
 *
 * 播放流程：
 * 1. 用户选择工艺段和时间范围后点击播放
 * 2. 系统调用API获取用户选择的整个时间范围内的历史数据
 * 3. API返回每个监测点在时间段内的完整数据系列
 * 4. 数据加载完成后开始播放，根据当前播放时间点从已加载的数据中找出匹配的值
 * 5. 进度条随时间平滑过渡，监测点值也会随之平滑变化
 * 6. 整个播放过程只需加载一次数据，提高性能和用户体验
 */

// 定义类型
interface DataPoint {
  timestamp: string;
  value: any;
}

interface PointData {
  indicatorCode: string;
  showType?: string;
  data: DataPoint[];
  name?: string;
}

// 监测点的值和数据
const pointValues = ref<Record<string, any>>({})
const timeSeriesData = ref<Record<string, DataPoint[]>>({}) // 存储每个监测点的时间序列数据

// 计算样式
const svgTransformStyle = computed(() => {
  return {
    transform: `scale(${scale.value}) translate(${translateX.value}px, ${translateY.value}px)`,
    transformOrigin: 'center',
    transition: isDragging.value ? 'none' : 'transform 0.2s'
  }
})

// 缩放控制
const zoomIn = () => {
  if (scale.value < 3) {
    scale.value += 0.1
  }
}

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value -= 0.1
  }
}

const resetZoom = () => {
  scale.value = 1
  translateX.value = 0
  translateY.value = 0
}

// 鼠标滚轮缩放
const handleWheel = (e: WheelEvent) => {
  if (e.deltaY < 0) {
    zoomIn()
  } else {
    zoomOut()
  }
}

// 鼠标拖拽
const handleMouseDown = (e: MouseEvent) => {
  isDragging.value = true
  dragStartX.value = e.clientX
  dragStartY.value = e.clientY
}

const handleMouseUp = () => {
  isDragging.value = false
}

const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value) return

  const deltaX = e.clientX - dragStartX.value
  const deltaY = e.clientY - dragStartY.value

  translateX.value += deltaX / scale.value
  translateY.value += deltaY / scale.value

  dragStartX.value = e.clientX
  dragStartY.value = e.clientY
}

// 禁用日期函数
const disabledDate = (time: Date) => {
  // 禁用未来日期
  return time.getTime() > Date.now()
}

// 时间变化处理
const handleTimeChange = () => {
  if (searchForm.timeRange && Array.isArray(searchForm.timeRange) && searchForm.timeRange.length === 2) {
    // 检查时间范围是否有效（不超过1天）
    const startTime = new Date(searchForm.timeRange[0]).getTime()
    const endTime = new Date(searchForm.timeRange[1]).getTime()
    const diffHours = (endTime - startTime) / (1000 * 60 * 60)

    if (diffHours > 24) {
      ElMessage.warning('时间范围不能超过1天，已自动调整')
      // 自动调整为1天
      const newEndTime = new Date(startTime + 24 * 60 * 60 * 1000)
      searchForm.timeRange = [searchForm.timeRange[0], newEndTime]
    }

    console.log('时间范围变化:', searchForm.timeRange)
    dataLoaded.value = false // 时间范围变化时重置
  }
}

// 获取工艺段选项
const fetchProcessOptions = async () => {
  try {
    processLoading.value = true
    const factoryId = appStore.getCurrentStation?.id

    if (!factoryId) {
      console.warn('未选择水厂，无法获取工艺段选项')
      processOptions.value = []
      return
    }

    const res = await getScreenListByFactoryId(factoryId)

    if (Array.isArray(res)) {
      processOptions.value = res.map(item => ({
        id: item.id,
        name: item.name,
      }))
      console.log(`已加载${processOptions.value.length}个工艺段选项`)
    } else {
      processOptions.value = []
      ElMessage.warning('未获取到工艺段数据')
    }
  } catch (error) {
    console.error('获取工艺段选项失败:', error)
    processOptions.value = []
    ElMessage.error('获取工艺段数据失败')
  } finally {
    processLoading.value = false
  }
}

// 处理工艺段变更
const handleProcessChange = async (value: string | number) => {
  if (value) {
    await fetchScreenData(value)
  } else {
    currentScreen.value = null
    svgContent.value = ''
    pointsData.value = []
  }

  // 重置回放进度
  playbackProgress.value = 0
  resetZoom()
  dataLoaded.value = false // 切换工艺段时重置
}

// 获取画面和位点信息
const fetchScreenData = async (screenId: string | number) => {
  try {
    if (!screenId) {
      return
    }

    const loadingInstance = ElMessage({
      message: '正在加载工艺图...',
      type: 'info',
      duration: 0
    })

    const res = await getScreenById(screenId)
    loadingInstance.close()

    if (res != null) {
      // 取第一个画面展示
      const screen = res

      const pointsRes = await getPointsDataByScreenId(screenId);

      // 确保svgUrl使用正确格式
      let svgContent = screen.svg || screen.svgContent || '';
      if (screen.svgUrl && !svgContent) {
        // 如果只有svgUrl，检查是否需要添加前缀
        if (!screen.svgUrl.startsWith('http://') && !screen.svgUrl.startsWith('https://')) {
          svgContent = SVG_URL + screen.svgUrl;
        } else {
          svgContent = screen.svgUrl;
        }
      }

      currentScreen.value = {
        id: screen.id,
        name: screen.name,
        svg: svgContent,
        points: pointsRes || []
      }

      console.log('获取到的画面数据:', currentScreen.value)

      // 处理SVG内容
      await processSvgContent(currentScreen.value)

      // 存储监测点数据
      pointsData.value = currentScreen.value.points || []

      // 初始化监测点值
      resetPointValues()
    } else {
      currentScreen.value = null
      svgContent.value = ''
      pointsData.value = []
      console.warn('未找到对应的画面信息')
      if (res && Array.isArray(res) && res.length === 0) {
        ElMessage.info('当前工艺段没有可用画面')
      }
    }
  } catch (error) {
    console.error('获取画面信息失败:', error)
    ElMessage.error('获取画面信息失败')
    currentScreen.value = null
    svgContent.value = ''
    pointsData.value = []
  }
}

// 移除SVG根元素的style、width、height属性
function cleanSvgRootAttrs(svg: string): string {
  return svg.replace(/<svg([^>]*)>/, (match, attrs) => {
    let newAttrs = attrs
      .replace(/style="[^"]*"/gi, '')
      .replace(/width="[^"]*"/gi, '')
      .replace(/height="[^"]*"/gi, '');
    return `<svg${newAttrs}>`;
  });
}

// 处理SVG内容 - 将URL转为嵌入式SVG
const processSvgContent = async (screen: any) => {
  if (!screen) {
    svgContent.value = ''
    return
  }

  // 如果已有SVG内容，直接使用
  if (screen.svg && !screen.svg.startsWith('http')) {
    // 移除SVG中的<style>标签，防止污染全局样式，并清理根属性
    svgContent.value = cleanSvgRootAttrs(
      screen.svg.replace(/<style[\s\S]*?<\/style>/gi, '')
    )
    return
  }

  // 如果是URL，需要获取SVG内容
  if (screen.svg && (screen.svg.startsWith('http') || !screen.svg.includes('<svg'))) {
    try {
      // 处理URL - 如果不是完整URL，添加SVG_URL前缀
      let url = screen.svg;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = SVG_URL + url;
        console.log(`添加SVG_URL前缀后的URL: ${url}`);
      }

      const response = await fetch(url)
      if (response.ok) {
        let text = await response.text()
        // 移除SVG中的<style>标签，防止污染全局样式，并清理根属性
        text = cleanSvgRootAttrs(
          text.replace(/<style[\s\S]*?<\/style>/gi, '')
        )
        svgContent.value = text
      } else {
        console.error('获取SVG内容失败:', response.statusText)
        svgContent.value = ''
        ElMessage.error('获取SVG内容失败')
      }
    } catch (error) {
      console.error('获取SVG内容异常:', error)
      svgContent.value = ''
      ElMessage.error('获取SVG内容失败')
    }
    return
  }

  // 没有SVG内容
  svgContent.value = ''
}

// 初始化监测点值
const resetPointValues = () => {
  pointValues.value = {}
  if (pointsData.value && pointsData.value.length > 0) {
    pointsData.value.forEach(point => {
      if (point.code) {
        pointValues.value[point.code] = {
          value: null,
          timestamp: null,
          type: point.type || 'default'
        }
      }
    })
  }
}

// 播放控制相关变量
const isPlaying = ref(false)
const playbackTimer = ref<number | null>(null)
const currentPlayTime = ref<Date | null>(null)
const playbackStepMinutes = ref(1) // 每步播放1分钟
const dataUpdateInterval = ref<number | null>(null) // 数据更新定时器
const baseDataUpdateInterval = 60000 // 基础数据更新间隔（毫秒）
const dataProcessing = ref(false) // 添加一个标记来跟踪数据是否正在处理中
const nextTimeoutId = ref<number | null>(null) // 添加一个变量来存储下一次数据获取的timeout ID
const isAutoUpdating = ref(false) // 添加标志区分自动更新和手动拖动

// 进度条平滑过渡相关变量
const lastDataPointTime = ref<Date | null>(null) // 上一个数据点时间
const nextDataPointTime = ref<Date | null>(null) // 下一个数据点时间
const progressUpdateInterval = 200 // 进度条更新间隔(毫秒)

// 数据已加载标记
const dataLoaded = ref(false)

// 处理播放
const handlePlay = () => {
  if (!selectedProcess.value) {
    ElMessage.warning('请先选择工艺段')
    return
  }

  if (!searchForm.timeRange || !Array.isArray(searchForm.timeRange) || searchForm.timeRange.length !== 2) {
    ElMessage.warning('请先选择时间范围')
    return
  }

  // 检查时间范围是否有效（不超过1天）
  const startTime = new Date(searchForm.timeRange[0]).getTime()
  const endTime = new Date(searchForm.timeRange[1]).getTime()
  const diffHours = (endTime - startTime) / (1000 * 60 * 60)

  if (diffHours > 24) {
    ElMessage.warning('时间范围不能超过1天，请重新选择')
    return
  }

  // 如果已经在播放，则不重复开始
  if (isPlaying.value) {
    return
  }

  // 如果数据已加载，直接播放
  if (dataLoaded.value) {
    startPlayback(startTime, endTime)
    return
  }

  // 开始加载数据
  const loadingInstance = ElMessage({
    message: '正在加载历史数据...',
    type: 'info',
    duration: 0
  })

  // 先获取整个时间段的数据
  fetchMonitoringPointsData(new Date(startTime))
    .then(() => {
      loadingInstance.close()
      dataLoaded.value = true // 标记数据已加载
      // 数据加载完成后开始播放
      startPlayback(startTime, endTime)
      ElMessage.success('数据加载完成，开始播放')
    })
    .catch(error => {
      loadingInstance.close()
      console.error('加载历史数据失败:', error)
      ElMessage.error('加载历史数据失败，请重试')
    })
}

// 开始播放流程 - 数据已加载完成
const startPlayback = (startTime: number, endTime: number) => {
  // 开始播放
  isPlaying.value = true
  console.log('开始播放, 速度:', searchForm.speed)

  // 初始化播放时间（如果没有当前播放时间或进度为0，则从开始时间开始）
  if (!currentPlayTime.value || playbackProgress.value === 0) {
    currentPlayTime.value = new Date(startTime)
  }

  // 清除可能存在的旧定时器
  clearTimers()

  // 设置进度条更新定时器，根据播放速度调整推进步长
  playbackTimer.value = window.setInterval(() => {
    if (!isPlaying.value || !currentPlayTime.value) return

    const totalDuration = endTime - startTime

    // 基础数据间隔是3分钟（180000ms）
    const baseDataInterval = 3 * 60 * 1000 // 3分钟
    // 根据播放速度调整推进步长
    const step = baseDataInterval * searchForm.speed // 例如：1x=3分钟, 2x=6分钟, 4x=12分钟, 0.5x=1.5分钟

    // 推进当前播放时间
    let nextTime = currentPlayTime.value.getTime() + step

    // 检查是否到达或超过结束时间
    if (nextTime >= endTime) {
      handlePause() // 暂停播放
      currentPlayTime.value = new Date(startTime) // 重置到开始时间
      playbackProgress.value = 0 // 重置进度
      ElMessage.info('播放完成')
      return
    }

    // 更新当前播放时间
    currentPlayTime.value = new Date(nextTime)

    // 更新监测点的值
    updatePointValuesByTime(currentPlayTime.value)

    // 更新进度条
    const currentPosition = nextTime - startTime
    const newProgress = Math.min(Math.floor((currentPosition / totalDuration) * 100), 100)

    // 设置自动更新标志，避免触发手动拖动事件
    isAutoUpdating.value = true
    playbackProgress.value = newProgress
    // 重置标志
    setTimeout(() => {
      isAutoUpdating.value = false
    }, 0)

  }, progressUpdateInterval) // 保持固定的更新频率，但推进步长与速度关联
}

// 处理播放速度变化
const handleSpeedChange = () => {
  // 如果正在播放，需要重新设置定时器以应用新的速度
  if (isPlaying.value && searchForm.timeRange) {
    // 暂存当前播放时间
    const currentTime = currentPlayTime.value

    // 暂停并重新开始播放以应用新速度
    handlePause()

    // 恢复当前播放时间
    currentPlayTime.value = currentTime

    // 重新开始播放
    const startTime = new Date(searchForm.timeRange[0]).getTime()
    const endTime = new Date(searchForm.timeRange[1]).getTime()
    startPlayback(startTime, endTime)
  }
}

// 处理暂停
const handlePause = () => {
  // 处理暂停
  isPlaying.value = false
  clearTimers()
  console.log('暂停播放')
}

// 清除所有定时器
const clearTimers = () => {
  if (playbackTimer.value !== null) {
    window.clearInterval(playbackTimer.value)
    playbackTimer.value = null
  }

  if (dataUpdateInterval.value !== null) {
    window.clearInterval(dataUpdateInterval.value)
    dataUpdateInterval.value = null
  }

  if (nextTimeoutId.value !== null) {
    window.clearTimeout(nextTimeoutId.value)
    nextTimeoutId.value = null
  }
}

// 根据具体时间点获取数据
const fetchPointDataByTime = async (time: Date) => {
  console.log('获取时间点数据:', time.toLocaleString())

  // 调用接口获取监测点数据
  return fetchMonitoringPointsData(time)
}

// 调用接口获取监测点数据
const fetchMonitoringPointsData = async (time: Date) => {
  if (!selectedProcess.value || !pointsData.value.length || !searchForm.timeRange) return

  try {
    console.log('调用接口获取监测点数据:', time.toLocaleString())

    // 获取当前水厂编码
    const factoryCode = appStore.getCurrentStation?.code || ''
    if (!factoryCode) {
      console.warn('无法获取当前水厂编码')
      return
    }

    // 直接使用用户选择的时间范围
    if (!Array.isArray(searchForm.timeRange) || searchForm.timeRange.length !== 2) {
      console.warn('无效的时间范围')
      return
    }

    // 格式化时间为 yyyy-MM-dd HH:mm:ss
    const formatDate = (date: Date): string => {
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + ' ' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0') + ':' +
        String(date.getSeconds()).padStart(2, '0');
    };

    const formattedStartTime = formatDate(new Date(searchForm.timeRange[0]));
    const formattedEndTime = formatDate(new Date(searchForm.timeRange[1]));

    console.log(`请求数据时间范围: ${formattedStartTime} 至 ${formattedEndTime}`);

    // 调用新的API接口获取数据
    const res = await getIndicatorDataByScreenAndTime({
      screenId: selectedProcess.value,
      factoryCode,
      startTime: formattedStartTime,
      endTime: formattedEndTime
    });

    // 清空之前的时间序列数据
    timeSeriesData.value = {};

    // 处理后端返回的监测点数组
    if (res && Array.isArray(res)) {
      // 遍历每个监测点
      res.forEach(pointData => {
        const indicatorCode = pointData.indicatorCode;
        if (!indicatorCode) return;

        // 确保监测点在pointValues中初始化
        if (!pointValues.value[indicatorCode]) {
          pointValues.value[indicatorCode] = {
            value: null,
            timestamp: null,
            type: pointData.showType || 'default',
            pointDataType: pointData.pointType
          };
        }

        // 处理时序数据
        if (Array.isArray(pointData.indicatorValueList)) {
          // 按时间戳排序
          const sortedData = [...pointData.indicatorValueList].sort((a, b) => {
            const timeA = new Date(a.time).getTime();
            const timeB = new Date(b.time).getTime();
            return timeA - timeB;
          });

          // 转换数据格式
          const formattedData = sortedData.map(item => ({
            timestamp: item.time,
            value: item.value,
            pointDataType: pointData.pointType
          }));

          timeSeriesData.value[indicatorCode] = formattedData;

          // 初始设置为第一个值
          if (formattedData.length > 0) {
            pointValues.value[indicatorCode].value = formattedData[0].value;
            pointValues.value[indicatorCode].timestamp = formattedData[0].timestamp;
          }
        }
      });

      console.log('获取到监测点时间序列数据:', Object.keys(timeSeriesData.value).length);

      // 更新SVG中的监测点显示
      updateSvgPoints();
    } else {
      console.warn('获取到的历史数据格式不正确或为空');
    }
  } catch (error) {
    console.error('获取监测点数据失败:', error);
    ElMessage.error('获取历史数据失败，请检查网络连接');
  }
}

// 根据当前播放时间更新监测点值
const updatePointValuesByTime = (currentTime: Date) => {
  if (!timeSeriesData.value) return;

  const currentTimestamp = currentTime.getTime();


  // 遍历所有监测点
  Object.keys(timeSeriesData.value).forEach(indicatorCode => {
    const series = timeSeriesData.value[indicatorCode];
    if (!Array.isArray(series) || series.length === 0) return;

    // 寻找最接近currentTime的数据点
    let bestIndex = 0;
    let bestTimeDiff = Infinity;

    for (let i = 0; i < series.length; i++) {
      const dataPoint = series[i];
      const timestamp = new Date(dataPoint.timestamp).getTime();
      const diff = Math.abs(timestamp - currentTimestamp);

      if (diff < bestTimeDiff) {
        bestTimeDiff = diff;
        bestIndex = i;
      }
    }

    // 更新当前值
    if (pointValues.value[indicatorCode]) {
      const currentDataPoint = series[bestIndex];
      pointValues.value[indicatorCode].value = currentDataPoint.value;
      pointValues.value[indicatorCode].timestamp = currentDataPoint.timestamp;
    }
  });

  // 更新SVG中的监测点显示
  updateSvgPoints();
}

// 更新SVG中的监测点显示
const updateSvgPoints = () => {
  if (!svgWrapper.value) return;

  const svgDoc = svgWrapper.value;

  console.log(svgDoc)

  // 遍历所有有数据的监测点
  Object.keys(pointValues.value).forEach(indicatorCode => {
    if (!indicatorCode) return;

    // 查找对应的元素 - 基于data-indicator-code属性
    const elements = svgDoc.querySelectorAll(`[data-indicator-code="${indicatorCode}"]`);

    elements.forEach((element: Element) => {
      const pointValue = pointValues.value[indicatorCode];
      if (!pointValue) return;
      console.log("eeeeeeeeeeeeeeeeee", pointValue

      );

      const value = pointValue.value;
      const showType = pointValue.type;

      // 根据监测点的showType属性判断如何显示
      if (showType === 'text' || element.tagName === 'text') {
        // 更新文本显示，保留3位小数
        // 非文本元素但需要显示文本：查找子文本元素或创建一个
        let textSubElement = element.querySelector('text');

        if (textSubElement) {
          // 如果找到子文本元素，更新它
          textSubElement.textContent = value !== null ? Number(value).toFixed(3) : '--';;
          console.log(`通过子文本元素更新点位[${showType}]内容:`);
        }

      } else if (showType === 'color' || ['rect', 'circle', 'path'].includes(element.tagName)) {
        // 根据值更新颜色、填充等样式
        updateElementStyle(element, value, pointValue);
      }
    });
  });
}

// 根据值更新元素样式
const updateElementStyle = (element: Element, value: any, pointData: any) => {

  if (value === null) {
    // 数据缺失，使用灰色
    element.setAttribute('fill', '#cccccc')
    return
  }


  // 根据点位类型和值应用不同的样式逻辑
  const pointType = pointData.pointType || 'default';
  // pointType为7：开关状态，true/1为绿色，false/0为红色
  console.log(pointType)
  // console.log(pointData)
  if (pointType === 'alarm') {
    // 报警类型点位
    if (value > 0) {
      // 有报警，红色
      element.setAttribute('fill', 'red')
    } else {
      // 无报警，绿色
      element.setAttribute('fill', '#04FD04')
    }
  } else if (pointType === 'status') {
    // 状态类型点位
    if (value === 1 || value === 'on' || value === 'true' || value === true) {
      // 运行中
      element.setAttribute('fill', '#04FD04')
    } else {
      // 停止
      element.setAttribute('fill', 'gray')
    }
  } else if (pointType === 'value') {
    // 数值类型，根据阈值设置颜色
    // 这里假设point包含了阈值配置
    const min = pointData.min !== undefined ? pointData.min : 0
    const max = pointData.max !== undefined ? pointData.max : 100
    const percent = (value - min) / (max - min)

    if (percent < 0.3) {
      element.setAttribute('fill', 'blue')
    } else if (percent < 0.7) {
      element.setAttribute('fill', '#04FD04')
    } else {
      element.setAttribute('fill', 'red')
    }
  } else {
    // 默认样式
    element.setAttribute('fill', 'blue')
    const pointDataType = pointData.pointDataType || '0';
    if (pointDataType === '7') {
      // 状态类型点位
      if (value == '1' || value === 'on' || value === 'true' || value === true) {
        // 运行中
        element.setAttribute('fill', '#04FD04')
      } else {
        // 停止
        element.setAttribute('fill', 'red')
      }
    }

  }
}

// 格式化滑块提示
const formatTooltip = (val: number) => {
  if (!searchForm.timeRange || !Array.isArray(searchForm.timeRange) || searchForm.timeRange.length !== 2) {
    return `${val}%`
  }

  const startTime = new Date(searchForm.timeRange[0]).getTime()
  const endTime = new Date(searchForm.timeRange[1]).getTime()
  const currentTime = startTime + (endTime - startTime) * (val / 100)

  return new Date(currentTime).toLocaleTimeString()
}

// 重置
const handleReset = () => {
  // 如果正在播放，先暂停
  if (isPlaying.value) {
    handlePause()
  }

  selectedProcess.value = ''
  currentScreen.value = null
  svgContent.value = ''
  searchForm.timeRange = null
  searchForm.speed = 1
  playbackProgress.value = 0
  currentPlayTime.value = null
  lastDataPointTime.value = null
  nextDataPointTime.value = null
  dataProcessing.value = false
  dataLoaded.value = false // 重置时重置

  // 清空时间序列数据
  timeSeriesData.value = {}

  resetZoom()
}

// 进度变化处理
const handleProgressChange = (val: number) => {
  // 如果是自动更新，不执行手动拖动逻辑
  if (isAutoUpdating.value) return

  // 拖动结束也自动暂停
  if (isPlaying.value) handlePause();
  if (!searchForm.timeRange || !Array.isArray(searchForm.timeRange) || searchForm.timeRange.length !== 2) return

  const rangeStartTime = new Date(searchForm.timeRange[0]).getTime()
  const rangeEndTime = new Date(searchForm.timeRange[1]).getTime()

  // 根据进度计算当前时间点
  const currentTime = rangeStartTime + (rangeEndTime - rangeStartTime) * (val / 100)
  const timePoint = new Date(currentTime)

  // 更新当前播放时间
  currentPlayTime.value = timePoint

  // 更新上一个数据点时间
  lastDataPointTime.value = timePoint

  // 计算下一个数据点时间（3分钟后）
  const nextTime = new Date(timePoint.getTime() + 3 * 60 * 1000)
  // 确保不超过结束时间
  if (nextTime.getTime() > rangeEndTime) {
    nextDataPointTime.value = new Date(rangeEndTime)
  } else {
    nextDataPointTime.value = nextTime
  }

  // 使用现有数据更新显示
  updatePointValuesByTime(timePoint)
}

// 拖动进度条时实时刷新点位
const handleProgressInput = (val: number) => {
  // 如果是自动更新，不执行手动拖动逻辑
  if (isAutoUpdating.value) return

  // 拖动时自动暂停
  if (isPlaying.value) handlePause();
  if (!searchForm.timeRange || !Array.isArray(searchForm.timeRange) || searchForm.timeRange.length !== 2) return
  const rangeStartTime = new Date(searchForm.timeRange[0]).getTime()
  const rangeEndTime = new Date(searchForm.timeRange[1]).getTime()
  const currentTime = rangeStartTime + (rangeEndTime - rangeStartTime) * (val / 100)
  updatePointValuesByTime(new Date(currentTime))
}

// 监听水厂ID变化
const handleStationChange = () => {
  // 当水厂变化时，清空已选择的工艺段并重新获取工艺段列表
  selectedProcess.value = ''
  currentScreen.value = null
  svgContent.value = ''
  processOptions.value = []
  resetZoom()
  fetchProcessOptions()
}

// 添加鼠标移动事件监听
onMounted(() => {
  fetchProcessOptions()

  // 添加鼠标移动事件
  window.addEventListener('mousemove', handleMouseMove)

  // 监听水厂变化
  appStore.$subscribe((mutation, state) => {
    if (mutation.type === 'direct' && state.currentStation) {
      handleStationChange()
    }
  })
})

// 移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('mousemove', handleMouseMove)

  // 清除所有定时器
  clearTimers()
})
</script>

<style scoped lang="scss">
.playback-page {
  width: 100%;
  height: 100%;
  /* 使用100%代替100vh */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.playback-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin: 0;
  padding: 0;
  height: 100%;

  :deep(.el-card__body) {
    height: 100%;
    padding: 15px;
    padding-bottom: 5px;
    /* 减小底部内边距 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

.playback-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.control-panel {
  padding: 5px 0;
  /* 减小控制面板内边距 */
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
  margin-bottom: 5px;
  /* 减小底部边距 */
}

.player-container {
  flex: 1;
  background-color: #091637;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  min-height: 200px;
  /* 减小最小高度 */
  margin-bottom: 5px;
  /* 减小底部边距 */
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  color: #909399;
}

.svg-container {
  width: 100%;
  height: 100%;
  background-color: #091637;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  .zoom-control {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 10;

    .zoom-btn {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.7);
      }

      .zoom-icon {
        font-size: 18px;
      }
    }
  }
}

.svg-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;

  svg {
    max-width: 100%;
    max-height: 100%;
    width: auto !important;
    height: auto !important;
  }
}

.progress-container {
  padding: 5px 0;
  /* 减小内边距 */
  margin-top: 0;
  width: 100%;
  flex-shrink: 0;
  background: #fff;
  z-index: 2;

  .progress-label {
    font-weight: 500;
    color: #606266;
  }

  .el-slider {
    margin-top: 0;
    margin-bottom: 0;
  }
}

.playback-controls {
  display: flex;
  align-items: center;
  margin-top: 5px;
  margin-bottom: 0;
}

.play-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #409eff;
  border: none;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-right: 10px;
  transition: all 0.3s;

  &:hover {
    background-color: #66b1ff;
  }

  .play-icon {
    font-size: 20px;
  }
}

.time-display {
  margin-left: 10px;
  color: #606266;
  font-size: 14px;
}

.process-select,
.date-picker,
.speed-select {
  .el-input__wrapper {
    box-shadow: 0 0 0 1px #dcdfe6 inset;

    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }
}

.time-range-item {
  @media (max-width: 768px) {
    width: 100%;

    .el-date-editor {
      width: 100%;
    }
  }
}
</style>
