<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="证照类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择证照类型" style="width: 100%">
          <el-option label="企业资质证书" value="enterprise" />
          <el-option label="特种作业证" value="special" />
          <el-option label="安全生产许可证" value="safety" />
          <el-option label="职业健康证" value="health" />
        </el-select>
      </el-form-item>
      <el-form-item label="证照编号" prop="certificateNo">
        <el-input v-model="form.certificateNo" placeholder="请输入证照编号" />
      </el-form-item>
      <el-form-item label="持证人" prop="holderName">
        <el-input v-model="form.holderName" placeholder="请输入持证人姓名" />
      </el-form-item>
      <el-form-item label="发证日期" prop="issueDate">
        <el-date-picker
          v-model="form.issueDate"
          type="date"
          placeholder="请选择发证日期"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="有效期至" prop="expiryDate">
        <el-date-picker
          v-model="form.expiryDate"
          type="date"
          placeholder="请选择有效期"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'CertificateDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'add'
    },
    formData: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'success'],
  data() {
    return {
      form: {
        type: '',
        certificateNo: '',
        holderName: '',
        issueDate: '',
        expiryDate: '',
        notes: ''
      },
      rules: {
        type: [
          { required: true, message: '请选择证照类型', trigger: 'change' }
        ],
        certificateNo: [
          { required: true, message: '请输入证照编号', trigger: 'blur' },
          { max: 50, message: '证照编号不能超过50个字符', trigger: 'blur' }
        ],
        holderName: [
          { required: true, message: '请输入持证人姓名', trigger: 'blur' },
          { max: 100, message: '持证人姓名不能超过100个字符', trigger: 'blur' }
        ],
        issueDate: [
          { required: true, message: '请选择发证日期', trigger: 'change' }
        ],
        expiryDate: [
          { required: true, message: '请选择有效期', trigger: 'change' }
        ],
        notes: [
          { max: 500, message: '备注不能超过500个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    },
    dialogTitle() {
      const titles = {
        add: '新增证照',
        edit: '编辑证照',
        renew: '更新证照'
      }
      return titles[this.type] || '证照信息'
    }
  },
  watch: {
    formData: {
      handler(val) {
        if (val) {
          this.form = { ...val }
        } else {
          this.resetForm()
        }
      },
      immediate: true
    }
  },
  methods: {
    resetForm() {
      this.form = {
        type: '',
        certificateNo: '',
        holderName: '',
        issueDate: '',
        expiryDate: '',
        notes: ''
      }
    },
    handleClose() {
      this.visible = false
      this.resetForm()
    },
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()
        // 这里实现提交逻辑
        console.log('提交数据：', this.form)
        this.$emit('success')
        this.handleClose()
      } catch (error) {
        console.error('表单验证失败：', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dialog {
  :deep(.el-upload) {
    width: 100%;
  }
}
</style> 