<template>
  <div class="risk-assessment">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-card class="assessment-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="作业地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入作业地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="作业类型" prop="workType">
              <el-select v-model="form.workType" placeholder="请选择作业类型" style="width: 100%">
                <el-option
                  v-for="item in workTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="作业内容描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请详细描述作业内容"
          />
        </el-form-item>
      </el-card>

      <el-card class="assessment-card">
        <template #header>
          <div class="card-header">
            <span>危险性评估</span>
          </div>
        </template>
        <el-form-item label="事故类型" prop="accidentType">
          <el-select v-model="form.accidentType" placeholder="请选择可能发生的事故类型" style="width: 100%">
            <el-option
              v-for="item in accidentTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="发生频率(L)" prop="frequency">
              <el-select v-model="form.frequency" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in frequencyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    分值：{{ item.value }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="暴露程度(E)" prop="exposure">
              <el-select v-model="form.exposure" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in exposureOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    分值：{{ item.value }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="后果严重性(C)" prop="consequence">
              <el-select v-model="form.consequence" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in consequenceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    分值：{{ item.value }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="assessment-card" v-if="showResult">
        <template #header>
          <div class="card-header">
            <span>评估结果</span>
          </div>
        </template>
        <div class="result-info">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="result-item">
                <span class="label">风险值(D)：</span>
                <span class="value">{{ riskValue }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="result-item">
                <span class="label">风险等级：</span>
                <el-tag :type="riskLevelType">{{ riskLevel }}</el-tag>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <el-card class="assessment-card">
        <template #header>
          <div class="card-header">
            <span>管控措施</span>
          </div>
        </template>
        <el-form-item label="管控措施" prop="controls">
          <el-input
            v-model="form.controls"
            type="textarea"
            :rows="4"
            placeholder="请输入针对性的管控措施"
          />
        </el-form-item>
      </el-card>

      <div class="form-buttons">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="calculateRisk">评估风险</el-button>
        <el-button type="success" @click="saveAssessment" :disabled="!showResult">保存评估</el-button>
      </div>
    </el-form>

    <el-dialog
      v-model="showTips"
      title="管控建议"
      width="600px"
    >
      <div class="tips-content">
        <h4>风险等级：{{ riskLevel }}</h4>
        <div class="tips-text">{{ controlTips }}</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="showTips = false">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 评估记录对话框 -->
    <assessment-dialog
      v-model="showAssessmentDialog"
      :assessment-data="assessmentData"
      @success="handleAssessmentSuccess"
    />
  </div>
</template>

<script>
import AssessmentDialog from '../dialogs/AssessmentDialog.vue'

export default {
  name: 'RiskAssessment',
  components: {
    AssessmentDialog
  },
  data() {
    return {
      form: {
        location: '',
        workType: '',
        description: '',
        accidentType: '',
        frequency: '',
        exposure: '',
        consequence: '',
        controls: ''
      },
      workTypeOptions: [
        { value: 'height', label: '高空作业' },
        { value: 'confined', label: '受限空间作业' },
        { value: 'hot', label: '动火作业' },
        { value: 'lifting', label: '吊装作业' },
        { value: 'electrical', label: '电气作业' }
      ],
      accidentTypeOptions: [
        { value: 'fall', label: '高处坠落' },
        { value: 'collapse', label: '坍塌' },
        { value: 'electric', label: '触电' },
        { value: 'fire', label: '火灾' },
        { value: 'mechanical', label: '机械伤害' }
      ],
      frequencyOptions: [
        { value: 10, label: '经常发生' },
        { value: 6, label: '偶尔发生' },
        { value: 3, label: '极少发生' },
        { value: 1, label: '几乎不可能' }
      ],
      exposureOptions: [
        { value: 10, label: '连续暴露' },
        { value: 6, label: '每天工作时暴露' },
        { value: 3, label: '每周一次' },
        { value: 1, label: '极少暴露' }
      ],
      consequenceOptions: [
        { value: 10, label: '死亡' },
        { value: 6, label: '严重伤害' },
        { value: 3, label: '轻微伤害' },
        { value: 1, label: '轻微影响' }
      ],
      rules: {
        location: [
          { required: true, message: '请输入作业地点', trigger: 'blur' }
        ],
        workType: [
          { required: true, message: '请选择作业类型', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入作业内容描述', trigger: 'blur' }
        ],
        accidentType: [
          { required: true, message: '请选择事故类型', trigger: 'change' }
        ],
        frequency: [
          { required: true, message: '请选择发生频率', trigger: 'change' }
        ],
        exposure: [
          { required: true, message: '请选择暴露程度', trigger: 'change' }
        ],
        consequence: [
          { required: true, message: '请选择后果严重性', trigger: 'change' }
        ],
        controls: [
          { required: true, message: '请输入管控措施', trigger: 'blur' }
        ]
      },
      showResult: false,
      showTips: false,
      riskValue: 0,
      riskLevel: '',
      riskLevelType: '',
      showAssessmentDialog: false,
      assessmentData: null
    }
  },
  computed: {
    controlTips() {
      const tips = {
        '极高风险': '必须立即采取措施，在采取管控措施前严禁作业。建议：\n1. 重新设计作业方案\n2. 增加工程控制措施\n3. 完善应急预案\n4. 加强人员培训',
        '高风险': '需要优先采取管控措施，制定专项方案。建议：\n1. 实施工程控制\n2. 加强现场监督\n3. 规范操作流程\n4. 配备专职安全员',
        '中度风险': '需要采取管控措施，并定期检查。建议：\n1. 制定作业指导书\n2. 开展安全教育\n3. 配备防护设备\n4. 定期检查维护',
        '低风险': '采取常规管控措施即可。建议：\n1. 执行标准操作规程\n2. 做好个人防护\n3. 保持工作场所整洁\n4. 定期开展检查'
      }
      return tips[this.riskLevel] || ''
    }
  },
  methods: {
    calculateRisk() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          const { frequency, exposure, consequence } = this.form
          this.riskValue = frequency * exposure * consequence

          if (this.riskValue >= 400) {
            this.riskLevel = '极高风险'
            this.riskLevelType = 'danger'
          } else if (this.riskValue >= 200) {
            this.riskLevel = '高风险'
            this.riskLevelType = 'warning'
          } else if (this.riskValue >= 70) {
            this.riskLevel = '中度风险'
            this.riskLevelType = 'warning'
          } else {
            this.riskLevel = '低风险'
            this.riskLevelType = 'success'
          }

          this.showResult = true
          this.showTips = true
        }
      })
    },
    saveAssessment() {
      if (!this.showResult) {
        this.$message.warning('请先进行风险评估')
        return
      }
      
      // 准备评估数据
      this.assessmentData = {
        ...this.form,
        riskValue: this.riskValue,
        riskLevel: this.riskLevel,
        method: 'LEC',
        assessmentDate: new Date().toISOString().split('T')[0]
      }
      this.showAssessmentDialog = true
    },
    handleAssessmentSuccess(data) {
      this.$message.success('评估记录保存成功')
      this.resetForm()
    },
    resetForm() {
      this.$refs.formRef.resetFields()
      this.showResult = false
      this.riskValue = 0
      this.riskLevel = ''
      this.riskLevelType = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.risk-assessment {
  .assessment-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .result-info {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .result-item {
      display: flex;
      align-items: center;
      gap: 12px;

      .label {
        font-weight: bold;
      }

      .value {
        font-size: 24px;
        color: #409eff;
      }
    }
  }

  .form-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 20px;
  }
}

.tips-content {
  h4 {
    margin-top: 0;
    margin-bottom: 16px;
  }

  .tips-text {
    white-space: pre-line;
    line-height: 1.6;
  }
}
</style> 