<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="主题名称" prop="themeName">
        <el-input
          v-model="queryParams.themeName"
          class="!w-240px"
          clearable
          placeholder="请输入主题名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button
          plain
          type="primary"
          @click="openForm('create')"
        >
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="主题名称" prop="themeName" show-overflow-tooltip />
      <el-table-column align="center" label="排序" prop="sort" />
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            @click="goToIndicators(scope.row.id)"
          >
            主题指标
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.currentPage"
      :total="total"
      @pagination="getPage"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <el-dialog
    v-model="dialogVisible"
    :title="formType === 'create' ? '新增主题' : '编辑主题'"
    width="400px"
    @close="resetDialog"
  >
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="80px">
      <el-form-item label="主题名称" prop="themeName">
        <el-input v-model="formData.themeName" placeholder="请输入主题名称" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" :max="9999" style="width: 100%" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import * as ThemeApi from '@/api/monitor/themedata'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { computed, watch } from 'vue'

defineOptions({ name: 'MonitorThemeManage' })

const appStore = useAppStore()
const currentFactory = computed(() => appStore.getCurrentStation)

const message = useMessage() // 消息弹窗
const router = useRouter()

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 主题表格数据
const queryParams = reactive({
  currentPage: 1,
  pageSize: 10,
  themeName: '',
  factoryId: ''
})
const queryFormRef = ref() // 搜索的表单

const dialogVisible = ref(false)
const formType = ref<'create' | 'update'>('create')
const formRef = ref()
const formData = reactive({
  id: undefined,
  themeName: '',
  factoryId: '',
  sort: 0
})
const formRules = {
  themeName: [{ required: true, message: '请输入主题名称', trigger: 'blur' }],
  factoryId: [{ required: true, message: '请选择厂站', trigger: 'blur' }]
}

/** 查询主题列表 */
const getPage = async () => {
  loading.value = true
  try {
    queryParams.factoryId = currentFactory.value?.id || ''
    const  data  = await ThemeApi.getThemePage(queryParams)
    console.log(data)
    list.value = data.records || data.list || []
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.currentPage = 1
  getPage()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const openForm = async (type: 'create' | 'update', id?: number) => {
  formType.value = type
  if (type === 'update' && id) {
    const data = await ThemeApi.getTheme(id)
    if (!data) {
      message.error('获取主题详情失败')
      return
    }
    formData.id = data.id
    formData.themeName = data.themeName
    formData.factoryId = data.factoryId
    formData.sort = data.sort
  } else {
    formData.id = undefined
    formData.themeName = ''
    formData.factoryId = currentFactory.value?.id || ''
    formData.sort = 0
  }
  dialogVisible.value = true
}

const submitForm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    try {
      formData.factoryId = currentFactory.value?.id || ''
      if (formType.value === 'create') {
        await ThemeApi.addTheme(formData)
        message.success('新增成功')
      } else {
        await ThemeApi.updateTheme(formData)
        message.success('修改成功')
      }
      dialogVisible.value = false
      getPage()
    } catch {}
  })
}

const resetDialog = () => {
  if (formRef.value) formRef.value.resetFields()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await ThemeApi.deleteTheme(id)
    message.success('删除成功')
    await getPage()
  } catch {}
}

/** 跳转到主题指标页面 */
const goToIndicators = (themeId: number) => {
  router.push(`/monitor/themedata/${themeId}/indicators`)
}

/** 监听水厂切换，自动刷新数据 **/
watch(
  () => currentFactory.value?.id,
  (newId, oldId) => {
    if (newId && newId !== oldId) {
      queryParams.factoryId = newId
      getPage()
    }
  },
  { immediate: true }
)

/** 初始化 **/
onMounted(() => {
  queryParams.factoryId = currentFactory.value?.id || ''
  getPage()
})
</script>
