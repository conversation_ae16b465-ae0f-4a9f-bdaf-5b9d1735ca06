<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">数据报送</span>
          <el-button type="primary" @click="handleAdd">添加报送</el-button>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex flex-col">
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="评估方案" style="width: 200px;">
              <el-select v-model="searchForm.scheme" placeholder="请选择评估方案" clearable>
                <el-option v-for="item in schemeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="评估对象" style="width: 200px;">
              <el-select v-model="searchForm.target" placeholder="请选择评估对象" clearable>
                <el-option v-for="item in targetOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="报送状态" style="width: 200px;">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="待报送" value="pending" />
                <el-option label="已报送" value="submitted" />
                <el-option label="已审核" value="approved" />
                <el-option label="已驳回" value="rejected" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="relative w-full h-full">
          <div class="absolute w-full h-full">
            <el-table :data="tableData" border style="width: 100%">
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="scheme" label="评估方案" min-width="150" />
              <el-table-column prop="target" label="评估对象" min-width="120" />
              <el-table-column prop="assessTime" label="评估时间" width="160" align="center" />
              <el-table-column prop="submitTime" label="报送时间" width="160" align="center" />
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getStatusTag(row.status)">{{ getStatusText(row.status) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="submitter" label="报送人" width="100" align="center" />
              <el-table-column prop="reviewer" label="审核人" width="100" align="center" />
              <el-table-column label="操作" width="250" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                  <el-button type="primary" link @click="handleSubmit(row)">报送</el-button>
                  <el-button type="primary" link @click="handleDetail(row)">详情</el-button>
                  <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>

    <!-- 数据报送表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '添加报送' : '编辑报送'" width="800px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="评估方案" prop="scheme">
          <el-select v-model="form.scheme" placeholder="请选择评估方案">
            <el-option v-for="item in schemeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="评估对象" prop="target">
          <el-select v-model="form.target" placeholder="请选择评估对象">
            <el-option v-for="item in targetOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="评估时间" prop="assessTime">
          <el-date-picker v-model="form.assessTime" type="datetime" placeholder="请选择评估时间" />
        </el-form-item>
        <el-form-item label="评估数据" prop="data">
          <el-table :data="form.data" border style="width: 100%">
            <el-table-column prop="index" label="评估指标" min-width="150">
              <template #default="{ row }">
                <el-select v-model="row.index" placeholder="请选择评估指标">
                  <el-option v-for="item in indexOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="指标值" width="150">
              <template #default="{ row }">
                <el-input v-model="row.value" placeholder="请输入指标值" />
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" width="100">
              <template #default="{ row }">
                <el-input v-model="row.unit" placeholder="单位" disabled />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ $index }">
                <el-button type="danger" link @click="removeData($index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button type="primary" link @click="addData">添加数据</el-button>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 数据报送详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="报送详情" width="800px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="评估方案">{{ selectedItem?.scheme }}</el-descriptions-item>
        <el-descriptions-item label="评估对象">{{ selectedItem?.target }}</el-descriptions-item>
        <el-descriptions-item label="评估时间">{{ selectedItem?.assessTime }}</el-descriptions-item>
        <el-descriptions-item label="报送时间">{{ selectedItem?.submitTime }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTag(selectedItem?.status)">
            {{ getStatusText(selectedItem?.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="报送人">{{ selectedItem?.submitter }}</el-descriptions-item>
        <el-descriptions-item label="审核人">{{ selectedItem?.reviewer }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ selectedItem?.remark }}</el-descriptions-item>
        <el-descriptions-item label="评估数据" :span="1">
          <el-table :data="selectedItem?.data" border style="width: 100%">
            <el-table-column prop="index" label="评估指标" min-width="150" />
            <el-table-column prop="value" label="指标值" width="150" />
            <el-table-column prop="unit" label="单位" width="100" />
          </el-table>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  scheme: '',
  target: '',
  status: ''
})

// 评估方案选项
const schemeOptions = [
  { label: '电力系统运行评估方案', value: 'power' },
  { label: '水处理系统评估方案', value: 'water' },
  { label: '环境监测评估方案', value: 'environment' }
]

// 评估对象选项
const targetOptions = [
  { label: '主变压器', value: 'transformer' },
  { label: '水泵', value: 'pump' },
  { label: '风机', value: 'fan' },
  { label: '污水处理设备', value: 'sewage' }
]

// 评估指标选项
const indexOptions = [
  { label: '功率因数', value: 'powerFactor', unit: '%' },
  { label: '负载率', value: 'loadRate', unit: '%' },
  { label: '温度', value: 'temperature', unit: '℃' },
  { label: '振动', value: 'vibration', unit: 'mm/s' },
  { label: '噪声', value: 'noise', unit: 'dB' }
]

// 表格数据
const tableData = ref([
  {
    id: '1',
    scheme: '电力系统运行评估方案',
    target: '主变压器',
    assessTime: '2024-04-21 10:00:00',
    submitTime: '2024-04-21 11:00:00',
    status: 'submitted',
    submitter: '张三',
    reviewer: '李四',
    remark: '正常报送',
    data: [
      { index: '功率因数', value: '0.95', unit: '%' },
      { index: '负载率', value: '75', unit: '%' },
      { index: '温度', value: '65', unit: '℃' }
    ]
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref<FormInstance>()
const selectedItem = ref<any>(null)

// 表单数据
const form = reactive({
  scheme: '',
  target: '',
  assessTime: '',
  data: [
    {
      index: '',
      value: '',
      unit: ''
    }
  ],
  remark: ''
})

// 表单验证规则
const rules = {
  scheme: [{ required: true, message: '请选择评估方案', trigger: 'change' }],
  target: [{ required: true, message: '请选择评估对象', trigger: 'change' }],
  assessTime: [{ required: true, message: '请选择评估时间', trigger: 'change' }]
}

// 方法
const getStatusTag = (status: string) => {
  switch (status) {
    case 'pending':
      return 'info'
    case 'submitted':
      return 'warning'
    case 'approved':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '待报送'
    case 'submitted':
      return '已报送'
    case 'approved':
      return '已审核'
    case 'rejected':
      return '已驳回'
    default:
      return '未知'
  }
}

const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', searchForm)
}

const resetSearch = () => {
  searchForm.scheme = ''
  searchForm.target = ''
  searchForm.status = ''
}

const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
  Object.assign(form, {
    scheme: '',
    target: '',
    assessTime: '',
    data: [
      {
        index: '',
        value: '',
        unit: ''
      }
    ],
    remark: ''
  })
}

const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  Object.assign(form, row)
}

const handleDetail = (row: any) => {
  selectedItem.value = row
  detailDialogVisible.value = true
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该报送记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实现删除逻辑
    ElMessage.success('删除成功')
  })
}

const handleSubmit = (row: any) => {
  ElMessageBox.confirm('确定要报送该数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实现报送逻辑
    ElMessage.success('报送成功')
  })
}

const handleSave = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      // 实现保存逻辑
      console.log('表单数据:', form)
      dialogVisible.value = false
      ElMessage.success(dialogType.value === 'add' ? '添加成功' : '编辑成功')
    }
  })
}

const addData = () => {
  form.data.push({
    index: '',
    value: '',
    unit: ''
  })
}

const removeData = (index: number) => {
  form.data.splice(index, 1)
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
