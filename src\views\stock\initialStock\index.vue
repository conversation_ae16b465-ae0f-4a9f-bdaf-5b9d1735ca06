<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">期初库存</span>
          <div class="flex gap-2">
            <el-upload class="upload-demo" action="" :auto-upload="false" :show-file-list="true"
              :on-change="handleFileChange">
              <el-button type="primary">批量导入库存</el-button>
            </el-upload>
          </div>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-280px)] flex flex-col">
        <div class="w-full h-[50px] mb-2 gap-2 flex items-center">
          <el-form :model="searchForm" inline>
            <el-form-item label="商品名称">
              <el-input v-model="searchForm.productName" placeholder="请输入商品名称" />
            </el-form-item>
            <el-form-item label="商品编号">
              <el-input v-model="searchForm.productCode" placeholder="请输入商品编号" />
            </el-form-item>
            <el-form-item label="仓库">
              <el-select v-model="searchForm.warehouse" placeholder="请选择仓库" style="width: 100%">
                <el-option v-for="item in warehouses" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 w-full flex flex-col">
          <div class="relative w-full h-full">
            <div class="absolute w-full h-full">
              <el-table :data="inventoryList" border height="100%">
                <el-table-column prop="productName" label="商品名称" align="center" />
                <el-table-column prop="productCode" label="商品编号" align="center" />
                <el-table-column prop="warehouseName" label="仓库" align="center" />
                <el-table-column prop="quantity" label="库存数量" align="center" />
                <el-table-column prop="amount" label="库存金额" align="center" />
              </el-table>
            </div>
          </div>
          <div class="w-full flex-1 flex items-center justify-end mt-2">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
              layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const searchForm = reactive({
  productName: '',
  productCode: '',
  warehouse: ''
})

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(3)

const warehouses = ref([
  { id: '1', name: '仓库 A' },
  { id: '2', name: '仓库 B' }
])

const inventoryList = ref([
  {
    productName: '示例商品1',
    productCode: 'P001',
    warehouseName: '仓库 A',
    quantity: 100,
    amount: 5000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  },
  {
    productName: '示例商品2',
    productCode: 'P002',
    warehouseName: '仓库 B',
    quantity: 200,
    amount: 10000
  }
])

const handleSearch = () => {
  console.log('查询', searchForm)
}

const handleReset = () => {
  console.log('重置', searchForm)
}

const handleFileChange = (file) => {
  console.log('文件上传变化', file)
  // 这里可以添加文件解析逻辑，模拟批量导入数据
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleSearch()
}
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 20px !important;
}
</style>
