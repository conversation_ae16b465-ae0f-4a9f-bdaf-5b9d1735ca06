import * as echarts from "echarts";
// import "echarts-liquidfill";
import { onMounted, onBeforeUnmount, Ref } from "vue";

export enum RenderType {
  SVGRenderer = "svg",
  CanvasRenderer = "canvas",
}
export interface EChartsReturnType {
  getInstance: () => echarts.ECharts | null;
  setOption: (option: any) => void;
  onResize: () => void;
}

export function useECharts(
  container: Ref<HTMLDivElement | null>,
  renderer: RenderType = RenderType.CanvasRenderer
) {
  let echartsInstance: echarts.ECharts | null = null;

  function getInstance() {
    if (!echartsInstance) initEchart();
    return echartsInstance;
  }
  function initEchart() {
    if (!container.value) {
      console.warn('ECharts container is not ready yet')
      return
    }
    try {
      echartsInstance = echarts.init(container.value, null, { renderer })
    } catch (e) {
      console.error('Failed to initialize ECharts:', e)
      echartsInstance = null
    }
  }

  function setOption(option) {
    if (!container.value) {
      console.warn('ECharts container is not ready yet')
      return
    }
    
    try {
      if (!echartsInstance) initEchart()
      if (echartsInstance) {
        echartsInstance.setOption(option)
      }
    } catch (e) {
      console.error('Failed to set ECharts option:', e)
    }
  }

  function onResize() {
    echartsInstance?.resize();
  }

  function dispose() {
    echartsInstance?.dispose();
    echartsInstance = null;
  }

  onMounted(() => {
    window.addEventListener("resize", onResize);
  });
  onBeforeUnmount(() => {
    window.removeEventListener("resize", onResize);
    dispose();
  });

  return { getInstance, setOption, onResize, dispose };
}
