<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">报告模板</span>
          <el-button type="primary" @click="handleAdd">添加模板</el-button>
        </div>
      </template>

      <div class="h-[calc(100vh-270px)] w-full flex flex-col">
        <div class="mb-4">
          <el-form :inline="true" class="search-form">
            <el-form-item label="模板名称" style="width: 200px;">
              <el-input v-model="searchForm.name" placeholder="请输入模板名称" clearable />
            </el-form-item>
            <el-form-item label="适用方案" style="width: 200px;">
              <el-select v-model="searchForm.scheme" placeholder="请选择评估方案" clearable>
                <el-option v-for="item in schemeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" style="width: 200px;">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="启用" value="active" />
                <el-option label="禁用" value="inactive" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="relative w-full h-full">
          <div class="absolute w-full h-full">
            <el-table :data="tableData" border style="width: 100%">
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="name" label="模板名称" min-width="150" />
              <el-table-column prop="scheme" label="适用方案" min-width="150" />
              <el-table-column prop="description" label="模板描述" min-width="200" show-overflow-tooltip />
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-switch v-model="row.status" :active-value="'active'" :inactive-value="'inactive'"
                    @change="handleStatusChange(row)" />
                </template>
              </el-table-column>
              <el-table-column prop="creator" label="创建人" width="100" align="center" />
              <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
              <el-table-column label="操作" width="300" align="center" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                  <el-button type="primary" link @click="handlePreview(row)">预览</el-button>
                  <el-button type="primary" link @click="handleCopy(row)">复制</el-button>
                  <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div class="w-full flex items-center justify-end mt-4">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>

    <!-- 模板编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '添加模板' : '编辑模板'" width="1000px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="适用方案" prop="scheme">
          <el-select v-model="form.scheme" placeholder="请选择评估方案">
            <el-option v-for="item in schemeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </el-form-item>
        <el-form-item label="报告内容" prop="content">
          <div class="border rounded p-4">
            <div class="mb-4">
              <el-button type="primary" link @click="addSection('intro')">添加对象简介</el-button>
              <el-button type="primary" link @click="addSection('result')">添加评估结果</el-button>
              <el-button type="primary" link @click="addSection('indexList')">添加指标清单</el-button>
              <el-button type="primary" link @click="addSection('analysis')">添加指标分析</el-button>
              <el-button type="primary" link @click="addSection('dataTable')">添加数据表</el-button>
            </div>
            <div class="space-y-4">
              <div v-for="(section, index) in form.content" :key="index" class="border rounded p-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="font-bold">{{ getSectionTitle(section.type) }}</span>
                  <div>
                    <el-button type="primary" link @click="moveUp(index)" :disabled="index === 0">上移</el-button>
                    <el-button type="primary" link @click="moveDown(index)"
                      :disabled="index === form.content.length - 1">下移</el-button>
                    <el-button type="danger" link @click="removeSection(index)">删除</el-button>
                  </div>
                </div>
                <div v-if="section.type === 'intro'">
                  <el-input v-model="section.content" type="textarea" :rows="4" placeholder="请输入评估对象简介" />
                </div>
                <div v-if="section.type === 'result'">
                  <el-input v-model="section.content" type="textarea" :rows="4" placeholder="请输入综合评估结果" />
                </div>
                <div v-if="section.type === 'indexList'">
                  <el-table :data="section.content" border style="width: 100%">
                    <el-table-column prop="name" label="指标名称" min-width="150" />
                    <el-table-column prop="value" label="指标值" width="120" />
                    <el-table-column prop="unit" label="单位" width="80" />
                    <el-table-column prop="standard" label="标准值" width="120" />
                    <el-table-column prop="result" label="评估结果" width="100" />
                  </el-table>
                </div>
                <div v-if="section.type === 'analysis'">
                  <el-input v-model="section.content" type="textarea" :rows="4" placeholder="请输入指标分析内容" />
                </div>
                <div v-if="section.type === 'dataTable'">
                  <el-table :data="section.content" border style="width: 100%">
                    <el-table-column prop="time" label="时间" width="160" />
                    <el-table-column prop="index" label="指标" min-width="120" />
                    <el-table-column prop="value" label="数值" width="120" />
                    <el-table-column prop="unit" label="单位" width="80" />
                  </el-table>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="模板预览" width="1000px">
      <div class="preview-content">
        <div v-for="(section, index) in previewContent" :key="index" class="mb-6">
          <h3 class="text-lg font-bold mb-2">{{ getSectionTitle(section.type) }}</h3>
          <div v-if="section.type === 'intro' || section.type === 'result' || section.type === 'analysis'">
            <p class="whitespace-pre-wrap">{{ section.content }}</p>
          </div>
          <div v-if="section.type === 'indexList' || section.type === 'dataTable'">
            <el-table :data="section.content" border style="width: 100%">
              <el-table-column v-for="col in getTableColumns(section.type)" :key="col.prop" v-bind="col" />
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'

// 搜索表单
const searchForm = reactive({
  name: '',
  scheme: '',
  status: ''
})

// 评估方案选项
const schemeOptions = [
  { label: '电力系统运行评估方案', value: 'power' },
  { label: '水处理系统评估方案', value: 'water' },
  { label: '环境监测评估方案', value: 'environment' }
]

// 表格数据
const tableData = ref([
  {
    id: '1',
    name: '电力系统评估报告模板',
    scheme: '电力系统运行评估方案',
    description: '用于电力系统运行评估的标准报告模板',
    status: 'active',
    creator: '张三',
    createTime: '2024-04-21 10:00:00',
    content: []
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 对话框
const dialogVisible = ref(false)
const previewDialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref<FormInstance>()
const previewContent = ref<any[]>([])

// 表单数据
const form = reactive({
  name: '',
  scheme: '',
  description: '',
  content: [] as any[]
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  scheme: [{ required: true, message: '请选择适用方案', trigger: 'change' }],
  description: [{ required: true, message: '请输入模板描述', trigger: 'blur' }]
}

// 方法
const getSectionTitle = (type: string) => {
  switch (type) {
    case 'intro':
      return '评估对象简介'
    case 'result':
      return '综合评估结果'
    case 'indexList':
      return '评估指标清单'
    case 'analysis':
      return '评估指标分析'
    case 'dataTable':
      return '评估数据表'
    default:
      return '未知'
  }
}

const getTableColumns = (type: string) => {
  if (type === 'indexList') {
    return [
      { prop: 'name', label: '指标名称', minWidth: 150 },
      { prop: 'value', label: '指标值', width: 120 },
      { prop: 'unit', label: '单位', width: 80 },
      { prop: 'standard', label: '标准值', width: 120 },
      { prop: 'result', label: '评估结果', width: 100 }
    ]
  } else if (type === 'dataTable') {
    return [
      { prop: 'time', label: '时间', width: 160 },
      { prop: 'index', label: '指标', minWidth: 120 },
      { prop: 'value', label: '数值', width: 120 },
      { prop: 'unit', label: '单位', width: 80 }
    ]
  }
  return []
}

const addSection = (type: string) => {
  const defaultContent = {
    intro: '',
    result: '',
    indexList: [],
    analysis: '',
    dataTable: []
  }
  form.content.push({
    type,
    content: defaultContent[type as keyof typeof defaultContent]
  })
}

const removeSection = (index: number) => {
  form.content.splice(index, 1)
}

const moveUp = (index: number) => {
  if (index > 0) {
    const temp = form.content[index]
    form.content[index] = form.content[index - 1]
    form.content[index - 1] = temp
  }
}

const moveDown = (index: number) => {
  if (index < form.content.length - 1) {
    const temp = form.content[index]
    form.content[index] = form.content[index + 1]
    form.content[index + 1] = temp
  }
}

const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', searchForm)
}

const resetSearch = () => {
  searchForm.name = ''
  searchForm.scheme = ''
  searchForm.status = ''
}

const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
  Object.assign(form, {
    name: '',
    scheme: '',
    description: '',
    content: []
  })
}

const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  Object.assign(form, row)
}

const handlePreview = (row: any) => {
  previewContent.value = row.content
  previewDialogVisible.value = true
}

const handleCopy = (row: any) => {
  dialogType.value = 'add'
  dialogVisible.value = true
  Object.assign(form, {
    ...row,
    name: `${row.name} - 副本`,
    id: undefined
  })
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该模板吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实现删除逻辑
    ElMessage.success('删除成功')
  })
}

const handleStatusChange = (row: any) => {
  // 实现状态变更逻辑
  ElMessage.success(`模板已${row.status === 'active' ? '启用' : '禁用'}`)
}

const handleSave = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      // 实现保存逻辑
      console.log('表单数据:', form)
      dialogVisible.value = false
      ElMessage.success(dialogType.value === 'add' ? '添加成功' : '编辑成功')
    }
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

.preview-content {
  :deep(h3) {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--el-border-color);
  }
}
</style>
