<template>
  <div ref="chartRef" class="w-full h-full"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import type { EChartsType } from 'echarts'

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  value: {
    type: Number,
    default: 0
  },
  name: {
    type: String,
    default: ''
  },
  unit: {
    type: String,
    default: ''
  },
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: 3
  }
})

const chartRef = ref<HTMLElement | null>(null)
let chart: EChartsType | null = null

function initChart() {
  if (!chartRef.value) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  updateChart()
}

function updateChart() {
  if (!chart) return
  
  const option = {
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        center: ['50%', '75%'],
        radius: '100%',
        min: props.min,
        max: props.max,
        splitNumber: 5,
        axisLine: {
          lineStyle: {
            width: 6,
            color: [
              [0.3, '#67e0e3'],
              [0.7, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        pointer: {
          icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
          length: '12%',
          width: 20,
          offsetCenter: [0, '-60%'],
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        splitLine: {
          length: 20,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        axisLabel: {
          color: '#464646',
          fontSize: 14,
          distance: -60,
          formatter: function(value) {
            if (value === props.min || value === props.max) {
              return value + (props.unit ? ` ${props.unit}` : '');
            }
            return '';
          }
        },
        title: {
          offsetCenter: [0, '-10%'],
          fontSize: 14
        },
        detail: {
          offsetCenter: [0, '30%'],
          valueAnimation: true,
          formatter: function(value) {
            return value.toFixed(2) + (props.unit ? ` ${props.unit}` : '');
          },
          color: 'auto',
          fontSize: 20,
          fontWeight: 'bold'
        },
        data: [
          {
            value: props.value,
            name: props.name
          }
        ]
      }
    ]
  };
  
  chart.setOption(option)
}

onMounted(() => {
  initChart()
})

watch([() => props.value, () => props.name, () => props.unit], () => {
  updateChart()
}, { deep: true })
</script>

<style scoped>
</style> 