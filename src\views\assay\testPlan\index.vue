<template>
  <ContentWrap title="采样计划管理">
    <div class="operation-bar">
      <el-button type="primary" @click="createRegularPlan">新建常规采样计划</el-button>
      <el-button type="primary" @click="createTempPlan">新建临时采样计划</el-button>
      <el-input v-model="searchKeyword" placeholder="搜索计划名称" style="width: 15rem; margin-left: 1rem" clearable @clear="refreshData" />
      <el-button type="primary" @click="refreshData">
        <el-icon><Refresh /></el-icon>
      </el-button>
    </div>
    
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 常规采样计划 -->
      <el-tab-pane label="常规采样计划" name="regular">
        <el-table
          v-loading="loading"
          :data="filteredRegularPlans"
          border
          style="width: 100%;"
          class="plan-table responsive-table"
        >
          <el-table-column prop="id" label="ID" min-width="60" width="80" align="center" />
          <el-table-column prop="name" label="采样计划名称" min-width="160" show-overflow-tooltip />
          <el-table-column prop="planCode" label="计划编号" min-width="120" align="center" />
          <el-table-column label="执行人员" width="180">
            <template #default="{ row }">
              <div class="executor-roles">
                <div class="role-item">
                  <el-tag size="small" type="success">采样</el-tag>
                  <span>{{ row.samplerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="warning">检测</el-tag>
                  <span>{{ row.testerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="info">审核</el-tag>
                  <span>{{ row.reviewerName || '-' }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="检测项目" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.testItemName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="采样点" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.samplingPointName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="frequency" label="采样频率" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ getFrequencyText(row.frequency) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startDate" label="开始日期" min-width="100" width="120" align="center" />
          <el-table-column prop="endDate" label="结束日期" min-width="100" width="120" align="center" />
          <el-table-column prop="priority" label="优先级" min-width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="expectedSampleQuantity" label="样品数量" min-width="100" align="center">
            <template #default="{ row }">
              {{ row.expectedSampleQuantity || 100 }}mL
            </template>
          </el-table-column>
          <el-table-column prop="expectedSampleNature" label="样品性质" min-width="80" align="center">
            <template #default="{ row }">
              {{ getSampleNatureText(row.expectedSampleNature) }}
            </template>
          </el-table-column>
          <el-table-column prop="expectedSupernatant" label="预期上清液" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="expected-info">{{ row.expectedSupernatant || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="isEnabled" label="启用状态" min-width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.isEnabled ? 'success' : 'info'" size="small">
                {{ row.isEnabled ? '启用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="200" width="240" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button link type="primary" size="small" @click="handleEdit('regular', row)">编辑</el-button>
                <el-button link type="success" size="small" @click="handleViewTasks(row)">查看任务</el-button>
                <el-dropdown trigger="click">
                  <el-button link type="primary" size="small">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleChangePlanStatus(row)">
                        {{ row.status === 'pending' ? '启用计划' : (row.status === 'active' ? '暂停计划' : '启用计划') }}
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleCheckConflicts(row)">检查冲突</el-dropdown-item>
                      <el-dropdown-item @click="handleDelete('regular', row)" class="danger-item">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="regularPagination.currentPage"
            v-model:page-size="regularPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="regularPlans.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-tab-pane>
      
      <!-- 临时采样计划 -->
      <el-tab-pane label="临时采样计划" name="temporary">
        <el-table
          v-loading="loading"
          :data="filteredTempPlans"
          border
          style="width: 100%;"
          class="plan-table"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="planCode" label="计划编号" min-width="120" align="center" />
          <el-table-column prop="name" label="采样计划名称" min-width="180" show-overflow-tooltip />
          <el-table-column prop="planDatetime" label="执行时间" width="150" align="center">
            <template #default="{ row }">
              {{ formatDateTime(row.planDatetime) }}
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" effect="plain" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="执行人员" width="180">
            <template #default="{ row }">
              <div class="executor-roles">
                <div class="role-item">
                  <el-tag size="small" type="success">采样</el-tag>
                  <span>{{ row.samplerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="warning">检测</el-tag>
                  <span>{{ row.testerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="info">审核</el-tag>
                  <span>{{ row.reviewerName || '-' }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="检测项目" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.testItemName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="采样点" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.samplingPointName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="创建原因" min-width="150" show-overflow-tooltip />
          <el-table-column prop="expectedSampleQuantity" label="样品数量" min-width="100" align="center">
            <template #default="{ row }">
              {{ row.expectedSampleQuantity || 100 }}mL
            </template>
          </el-table-column>
          <el-table-column prop="expectedSampleNature" label="样品性质" min-width="80" align="center">
            <template #default="{ row }">
              {{ getSampleNatureText(row.expectedSampleNature) }}
            </template>
          </el-table-column>
          <el-table-column prop="expectedSupernatant" label="预期上清液" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="expected-info">{{ row.expectedSupernatant || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="isEnabled" label="启用状态" min-width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.isEnabled ? 'success' : 'info'" size="small">
                {{ row.isEnabled ? '启用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="220" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button link type="primary" size="small" @click="handleEdit('temporary', row)">编辑</el-button>
                <el-button link type="success" size="small" @click="handleViewTasks(row)">查看任务</el-button>
                <el-dropdown trigger="click">
                  <el-button link type="primary" size="small">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleChangePlanStatus(row)">
                        {{ row.status === 'pending' ? '启用计划' : (row.status === 'active' ? '暂停计划' : '启用计划') }}
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleCheckConflicts(row)">检查冲突</el-dropdown-item>
                      <el-dropdown-item @click="handleDelete('temporary', row)" class="danger-item">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="tempPagination.currentPage"
            v-model:page-size="tempPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="tempPlans.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-tab-pane>
      
      <!-- 任务执行 -->
      <el-tab-pane label="任务执行" name="tasks">
        <!-- 操作栏 -->
        <div class="mb-4">

          <el-button type="success" @click="handleBatchAssign" :disabled="selectedTasks.length === 0">
            <el-icon><User /></el-icon>批量分配 ({{ selectedTasks.length }})
          </el-button>
          <el-button @click="handleRefreshTasks">
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </div>

        <el-table
          v-loading="loading"
          :data="filteredTasks"
          border
          style="width: 100%;"
          class="plan-table"
          @selection-change="handleTaskSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="taskCode" label="任务编号" min-width="140" align="center" />
          <el-table-column prop="planName" label="关联采样计划" min-width="180" show-overflow-tooltip />
          <el-table-column prop="taskDatetime" label="执行时间" width="150" align="center">
            <template #default="{ row }">
              {{ formatDateTime(row.taskDatetime) }}
            </template>
          </el-table-column>
          <el-table-column prop="samplingPointName" label="采样点" min-width="120" align="center" />
          <el-table-column prop="testItemName" label="检测项目" min-width="140" show-overflow-tooltip />
          <el-table-column label="执行人员" width="180">
            <template #default="{ row }">
              <div class="executor-roles">
                <div class="role-item">
                  <el-tag size="small" type="success">采样</el-tag>
                  <span>{{ row.samplerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="warning">检测</el-tag>
                  <span>{{ row.testerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="info">审核</el-tag>
                  <span>{{ row.reviewerName || '-' }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" min-width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getTaskStatusType(row.status)" size="small">
                {{ getTaskStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="260" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleAssignTask(row)"
                  :disabled="row.status !== 'pending'"
                >
                  分配任务
                </el-button>
                <el-button
                  link
                  type="info"
                  size="small"
                  @click="handleViewTaskDetail(row)"
                >
                  查看详情
                </el-button>
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="handleCancelTask(row)"
                  v-if="row.status !== 'completed' && row.status !== 'cancelled'"
                >
                  取消任务
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="taskPagination.currentPage"
            v-model:page-size="taskPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="tasks.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 弹窗组件 -->
    <RegularPlanDialog ref="regularPlanDialogRef" @success="handleDialogSuccess" />
    <TempPlanDialog ref="tempPlanDialogRef" @success="handleDialogSuccess" />
    

    
    <!-- 冲突检查结果弹窗 -->
    <el-dialog v-model="conflictDialogVisible" title="计划冲突检查结果" width="50%">
      <div v-if="conflicts.length > 0">
        <el-alert type="warning" title="检测到以下冲突，请调整计划:" :closable="false" show-icon />
        <el-table :data="conflicts" border style="width: 100%; margin-top: 1rem;" class="conflict-table">
          <el-table-column prop="type" label="冲突类型" min-width="100" width="120" align="center" />
          <el-table-column prop="description" label="冲突描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="suggestion" label="建议操作" min-width="150" show-overflow-tooltip />
        </el-table>
      </div>
      <div v-else>
        <el-alert type="success" title="未检测到任何冲突，可以安排执行" :closable="false" show-icon />
      </div>
      <template #footer>
        <el-button @click="conflictDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleAdjustPlan" v-if="conflicts.length > 0">调整计划</el-button>
        <el-button type="success" @click="handleConfirmPlan" v-if="conflicts.length === 0">确认计划</el-button>
      </template>
    </el-dialog>

    <!-- 任务详情弹窗 -->
    <el-dialog v-model="taskDetailDialogVisible" title="任务详情" width="60%" class="task-detail-dialog">
      <div v-if="currentTask" class="task-detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">基本信息</span>
              <el-tag :type="getTaskStatusType(currentTask.status)" size="small">
                {{ getTaskStatusText(currentTask.status) }}
              </el-tag>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>任务ID：</label>
                <span>{{ currentTask.id }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>关联计划：</label>
                <span>{{ currentTask.planName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>执行日期：</label>
                <span>{{ currentTask.taskDate }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 16px;">
            <el-col :span="8">
              <div class="detail-item">
                <label>采样点：</label>
                <span>{{ currentTask.samplingPoint }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>采样人员：</label>
                <span>{{ currentTask.sampler || '未分配' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>检测人员：</label>
                <span>{{ currentTask.tester || '未分配' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 16px;">
            <el-col :span="8">
              <div class="detail-item">
                <label>审核人员：</label>
                <span>{{ currentTask.reviewer || '未分配' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>创建时间：</label>
                <span>{{ currentTask.createTime || '2024-01-15 10:30:00' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>备注：</label>
                <span>{{ currentTask.remark || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 检测项目信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
          <template #header>
            <span class="card-title">检测项目</span>
          </template>
          <el-table :data="currentTaskItems" border class="task-detail-table">
            <el-table-column prop="name" label="项目名称" min-width="120" />
            <el-table-column prop="category" label="类别" min-width="80" width="100" align="center" />
            <el-table-column prop="method" label="检测方法" min-width="150" show-overflow-tooltip />
            <el-table-column prop="standardValue" label="标准值" min-width="100" width="120" align="center">
              <template #default="{ row }">
                {{ row.standardValue }} {{ row.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" min-width="80" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.priority === 'high' ? 'danger' : row.priority === 'medium' ? 'warning' : 'info'" size="small">
                  {{ row.priority === 'high' ? '高' : row.priority === 'medium' ? '中' : '低' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 执行记录 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
          <template #header>
            <span class="card-title">执行记录</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="record in taskExecutionRecords"
              :key="record.id"
              :timestamp="record.time"
              :type="record.type"
            >
              <div class="timeline-content">
                <div class="timeline-title">{{ record.action }}</div>
                <div class="timeline-desc" v-if="record.description">{{ record.description }}</div>
                <div class="timeline-operator" v-if="record.operator">操作人：{{ record.operator }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>

        <!-- 备注信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 16px;" v-if="currentTask.remark">
          <template #header>
            <span class="card-title">备注信息</span>
          </template>
          <p class="remark-content">{{ currentTask.remark || '暂无备注' }}</p>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="taskDetailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleEditTask" v-if="currentTask?.status === 'pending'">编辑任务</el-button>
        </div>
      </template>
    </el-dialog>

  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Refresh } from '@element-plus/icons-vue'
import RegularPlanDialog from './components/RegularPlanDialog.vue'
import TempPlanDialog from './components/TempPlanDialog.vue'

// 数据类型定义 - 根据API文档v5.0更新
// 统一的采样计划接口（常规计划）
interface RegularPlan {
  id: number;
  factoryId: number;
  planCode: string;
  name: string;
  type: 'regular';
  description?: string;
  frequency: string;  // 采样频率
  testItem: number;   // 单个检测项目ID
  testItemName?: string; // 检测项目名称（显示用）
  samplingPoint: number; // 单个采样点ID
  samplingPointName?: string; // 采样点名称（显示用）
  startDate: string;
  endDate: string;

  // 执行人员信息
  samplerId: number;
  testerId: number;
  reviewerId: number;
  samplerName?: string;  // 显示用
  testerName?: string;   // 显示用
  reviewerName?: string; // 显示用

  // 样品预期信息
  expectedSampleQuantity?: number; // 默认100
  expectedSampleNature?: string;   // 默认liquid
  expectedSampleAppearance?: string;
  expectedSupernatant?: string;    // 预期上清液情况
  samplingInstructions?: string;

  priority?: string;     // 优先级
  isEnabled: boolean;    // 启用状态
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

// 统一的采样计划接口（临时计划）
interface TempPlan {
  id: number;
  factoryId: number;
  planCode: string;
  name: string;
  type: 'temporary';
  description?: string;
  reason: string;       // 临时计划必填
  testItem: number;     // 单个检测项目ID
  testItemName?: string; // 检测项目名称（显示用）
  samplingPoint: number; // 单个采样点ID
  samplingPointName?: string; // 采样点名称（显示用）
  planDatetime: string; // 临时计划执行时间

  // 执行人员信息
  samplerId: number;
  testerId: number;
  reviewerId: number;
  samplerName?: string;  // 显示用
  testerName?: string;   // 显示用
  reviewerName?: string; // 显示用

  // 样品预期信息
  expectedSampleQuantity?: number; // 默认100
  expectedSampleNature?: string;   // 默认liquid
  expectedSampleAppearance?: string;
  expectedSupernatant?: string;    // 预期上清液情况
  samplingInstructions?: string;

  priority: string;      // 优先级
  isEnabled: boolean;    // 启用状态
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

interface Task {
  id: number;
  taskCode: string;
  planId: number;
  planName: string;
  taskDatetime: string;
  testItem: number;
  testItemName?: string;
  samplingPoint: number;
  samplingPointName?: string;

  // 执行人员信息
  samplerId: number;
  testerId: number;
  reviewerId: number;
  samplerName?: string;
  testerName?: string;
  reviewerName?: string;

  priority?: string;
  status: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

interface Conflict {
  type: string;
  description: string;
  suggestion: string;
}

interface TestItem {
  id: number;
  name: string;
  category?: string;
  method?: string;
  standardValue?: string;
  unit?: string;
  priority?: 'high' | 'medium' | 'low';
}

interface SamplingPoint {
  id: number;
  name: string;
}

interface Staff {
  id: number;
  name: string;
  role: string;
}

// 页面状态
const activeTab = ref('regular')
const loading = ref(false)
const searchKeyword = ref('')

// 弹窗引用
const regularPlanDialogRef = ref()
const tempPlanDialogRef = ref()



// 冲突检查弹窗
const conflictDialogVisible = ref(false)
const conflicts = ref<Conflict[]>([])
const currentPlan = ref<RegularPlan | TempPlan | null>(null)

// 任务详情弹窗
const taskDetailDialogVisible = ref(false)
const currentTask = ref<Task | null>(null)

// 当前任务的检测项目
const currentTaskItems = ref<TestItem[]>([])

// 任务执行记录
const taskExecutionRecords = ref<ExecutionRecord[]>([])

// 选择的任务
const selectedTasks = ref<Task[]>([])

// 定义执行记录类型
interface ExecutionRecord {
  id: number
  action: string
  description?: string
  operator?: string
  time: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
}

// 分页配置
const regularPagination = ref({
  currentPage: 1,
  pageSize: 10
})

const tempPagination = ref({
  currentPage: 1,
  pageSize: 10
})

const taskPagination = ref({
  currentPage: 1,
  pageSize: 10
})

// 检测项目数据 (与基础信息模块保持一致)
const testItems: TestItem[] = [
  { id: 111, name: 'COD', category: '有机物', method: '重铬酸钾法', standardValue: '≤50', unit: 'mg/L', priority: 'high' },
  { id: 112, name: 'BOD5', category: '有机物', method: '稀释接种法', standardValue: '≤10', unit: 'mg/L', priority: 'high' },
  { id: 113, name: 'TOC', category: '有机物', method: '燃烧氧化法', standardValue: '≤30', unit: 'mg/L', priority: 'medium' },
  { id: 121, name: '氨氮', category: '营养盐', method: '纳氏试剂比色法', standardValue: '≤5', unit: 'mg/L', priority: 'high' },
  { id: 122, name: '总磷', category: '营养盐', method: '钼酸铵分光光度法', standardValue: '≤0.5', unit: 'mg/L', priority: 'high' },
  { id: 123, name: '总氮', category: '营养盐', method: '碱性过硫酸钾消解法', standardValue: '≤15', unit: 'mg/L', priority: 'high' },
  { id: 124, name: '硝酸盐氮', category: '营养盐', method: '紫外分光光度法', standardValue: '≤10', unit: 'mg/L', priority: 'medium' },
  { id: 131, name: 'pH值', category: '物理指标', method: '玻璃电极法', standardValue: '6-9', unit: '无量纲', priority: 'high' },
  { id: 132, name: '悬浮物', category: '物理指标', method: '重量法', standardValue: '≤10', unit: 'mg/L', priority: 'high' },
  { id: 133, name: '浊度', category: '物理指标', method: '散射法', standardValue: '≤5', unit: 'NTU', priority: 'medium' },
  { id: 211, name: '含水率', category: '污泥性质', method: '重量法', standardValue: '≤80', unit: '%', priority: 'medium' },
  { id: 212, name: '污泥浓度', category: '污泥性质', method: '重量法', standardValue: '2000-4000', unit: 'mg/L', priority: 'high' },
  { id: 213, name: '污泥沉降比', category: '污泥性质', method: '静置沉降法', standardValue: '15-30', unit: '%', priority: 'medium' },
  { id: 311, name: '铜', category: '重金属', method: '原子吸收分光光度法', standardValue: '≤0.5', unit: 'mg/L', priority: 'low' },
  { id: 312, name: '锌', category: '重金属', method: '原子吸收分光光度法', standardValue: '≤1.0', unit: 'mg/L', priority: 'low' }
]

// 采样点数据 (与基础信息模块保持一致)
const samplingPoints: SamplingPoint[] = [
  { id: 1, name: '进水总口' },
  { id: 2, name: '生化池进口' },
  { id: 3, name: '生化池出口' },
  { id: 4, name: '二沉池出水' },
  { id: 5, name: '出水总口' },
  { id: 6, name: '污泥浓缩池' },
  { id: 7, name: '污泥脱水间' },
  { id: 8, name: '回流污泥' },
  { id: 9, name: '中间水池' },
  { id: 10, name: '应急排放口' }
]

// 人员数据 (扩展更多人员)
const staffList: Staff[] = [
  { id: 1, name: '张三', role: '采样员' },
  { id: 2, name: '李四', role: '检测员' },
  { id: 3, name: '王五', role: '质控员' },
  { id: 4, name: '赵六', role: '审核员' },
  { id: 5, name: '钱七', role: '采样员' },
  { id: 6, name: '孙八', role: '检测员' },
  { id: 7, name: '周九', role: '质控员' },
  { id: 8, name: '吴十', role: '技术员' }
]

// 建立ID-对象映射，方便快速查找
const testItemMap = testItems.reduce((acc, item) => {
  acc[item.id] = item
  return acc
}, {} as Record<number, TestItem>)

const samplingPointMap = samplingPoints.reduce((acc, point) => {
  acc[point.id] = point
  return acc
}, {} as Record<number, SamplingPoint>)

// 模拟数据 - 常规计划 (优化后的完整数据)
const regularPlans = ref<RegularPlan[]>([
  {
    id: 1,
    factoryId: 1,
    name: '进水水质日常检测计划',
    samplingFrequency: 'daily',
    description: '监测进水水质指标变化，确保进水符合处理要求',
    testItems: [111, 112, 121, 131, 132],  // COD, BOD5, 氨氮, pH值, 悬浮物
    samplingPoints: [1],  // 进水总口
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: 'processing',
    samplerId: 1001,
    testerId: 1002,
    reviewerId: 1003,
    samplerName: '张三',
    testerName: '李四',
    reviewerName: '赵六',
    expectedSampleQuantity: 2,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '无色透明',
    expectedSupernatant: '清澈',
    samplingInstructions: '每日上午9点采样，注意水样代表性',
    needTest: true
  },
  {
    id: 2,
    factoryId: 1,
    name: '出水水质监测计划',
    samplingFrequency: 'daily',
    description: '监测出水水质达标情况，确保排放合规',
    testItems: [111, 112, 121, 122, 123, 131, 132, 133],  // 全套水质指标
    samplingPoints: [5],  // 出水总口
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: 'processing',
    samplerId: 1004,
    testerId: 1005,
    reviewerId: 1003,
    samplerName: '钱七',
    testerName: '孙八',
    reviewerName: '赵六',
    expectedSampleQuantity: 1,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '微黄透明',
    expectedSupernatant: '清澈',
    samplingInstructions: '每日8:00、14:00、20:00三次采样',
    needTest: true
  },
  {
    id: 3,
    name: '污泥浓度检测计划',
    frequency: '每日两次',
    description: '监测污泥浓度变化，控制生化处理效果',
    testItems: [212, 213],  // 污泥浓度, 污泥沉降比
    samplingPoints: [6, 8],  // 污泥浓缩池, 回流污泥
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: 'processing',
    sampler: '钱七',
    tester: '周九',
    reviewer: '王五',
    expectedSampleQuantity: 1,
    expectedSampleNature: '半固体',
    expectedSampleAppearance: '棕色粘稠',
    expectedSupernatant: '浑浊',
    samplingInstructions: '每日上午10点和下午4点采样',
    needTest: true
  },
  {
    id: 4,
    name: '生化池工艺监测计划',
    frequency: '每日一次',
    description: '监测生化处理工艺运行状态',
    testItems: [121, 122, 131, 132],  // 氨氮, 总磷, pH值, 悬浮物
    samplingPoints: [2, 3],  // 生化池进口, 生化池出口
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: 'processing',
    sampler: '张三',
    tester: '李四',
    reviewer: '王五',
    expectedSampleQuantity: 2,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '微黄浑浊',
    expectedSupernatant: '浑浊',
    samplingInstructions: '每日上午11点同时采集进出口样品',
    needTest: true
  },
  {
    id: 5,
    name: '中间处理工艺监测计划',
    frequency: '每周三次',
    description: '监测中间处理单元运行效果',
    testItems: [111, 112, 121, 133],  // COD, BOD5, 氨氮, 浊度
    samplingPoints: [4, 9],  // 二沉池出水, 中间水池
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: 'pending',
    sampler: '钱七',
    tester: '孙八',
    reviewer: '周九',
    expectedSampleQuantity: 1,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '微黄透明',
    expectedSupernatant: '清澈',
    samplingInstructions: '每周一、三、五上午采样',
    needTest: true
  },
  {
    id: 6,
    name: '污泥脱水效果监测计划',
    frequency: '每周两次',
    description: '监测污泥脱水处理效果',
    testItems: [211, 212],  // 含水率, 污泥浓度
    samplingPoints: [7],  // 污泥脱水间
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: 'pending',
    sampler: '钱七',
    tester: '周九',
    reviewer: '王五',
    expectedSampleQuantity: 1,
    expectedSampleNature: '半固体',
    expectedSampleAppearance: '深棕色固体',
    expectedSupernatant: '-',
    samplingInstructions: '每周二、五脱水作业时采样',
    needTest: true
  },
  {
    id: 7,
    name: '营养盐全面监测计划',
    frequency: '每周一次',
    description: '全面监测各处理单元营养盐去除效果',
    testItems: [121, 122, 123, 124],  // 氨氮, 总磷, 总氮, 硝酸盐氮
    samplingPoints: [1, 3, 5],  // 进水总口, 生化池出口, 出水总口
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: 'processing',
    sampler: '张三',
    tester: '李四',
    reviewer: '赵六',
    expectedSampleQuantity: 1,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '透明至微黄',
    expectedSupernatant: '清澈',
    samplingInstructions: '每周三上午同时采集三个点位样品',
    needTest: true
  },
  {
    id: 8,
    name: '有机物去除效果监测计划',
    frequency: '每周两次',
    description: '监测有机污染物去除效果',
    testItems: [111, 112, 113],  // COD, BOD5, TOC
    samplingPoints: [1, 5],  // 进水总口, 出水总口
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: 'processing',
    sampler: '钱七',
    tester: '孙八',
    reviewer: '周九',
    expectedSampleQuantity: 1,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '无色至微黄',
    expectedSupernatant: '清澈',
    samplingInstructions: '每周二、五上午采样对比',
    needTest: true
  }
])

// 模拟数据 - 临时计划 (优化后的完整数据)
const tempPlans = ref<TempPlan[]>([
  {
    id: 1,
    name: '进水异常检测',
    planDate: '2024-01-15',
    reason: '进水pH值异常波动，疑似工业废水混入',
    description: '紧急检测进水异常原因，确定污染源',
    testItems: [131, 121, 311, 312],  // pH值, 氨氮, 铜, 锌
    samplingPoints: [1],  // 进水总口
    responsible: '张三',
    priority: 'high',
    status: 'completed',
    sampler: '张三',
    tester: '李四',
    reviewer: '赵六',
    expectedSampleQuantity: 3,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '异常颜色',
    expectedSupernatant: '待观察',
    samplingInstructions: '每2小时采样一次，连续采样12小时',
    needTest: true
  },
  {
    id: 2,
    name: '重金属污染源排查',
    planDate: '2024-01-20',
    reason: '发现出水中重金属含量超标，需排查污染源',
    description: '全流程排查重金属污染来源',
    testItems: [311, 312],  // 铜, 锌
    samplingPoints: [1, 2, 3, 4, 5],  // 全流程采样
    responsible: '李四',
    priority: 'high',
    status: 'processing',
    sampler: '钱七',
    tester: '孙八',
    reviewer: '周九',
    expectedSampleQuantity: 2,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '正常透明',
    expectedSupernatant: '清澈',
    samplingInstructions: '同时采集各处理单元样品，确保时间同步',
    needTest: true
  },
  {
    id: 3,
    name: '新型絮凝剂效果验证',
    planDate: '2024-01-25',
    reason: '验证新采购絮凝剂的处理效果',
    description: '对比测试新型絮凝剂与原有药剂的处理效果',
    testItems: [132, 133, 122],  // 悬浮物, 浊度, 总磷
    samplingPoints: [2, 3, 4],  // 生化池进出口, 二沉池出水
    responsible: '王五',
    priority: 'medium',
    status: 'pending',
    sampler: '张三',
    tester: '李四',
    reviewer: '王五',
    expectedSampleQuantity: 1,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '微黄透明',
    expectedSupernatant: '清澈',
    samplingInstructions: '分别在投加前后采样对比',
    needTest: true
  },
  {
    id: 4,
    name: '冬季低温运行效果评估',
    planDate: '2024-01-30',
    reason: '冬季低温条件下生化处理效果评估',
    description: '评估低温对生化处理系统的影响',
    testItems: [111, 112, 121, 122],  // COD, BOD5, 氨氮, 总磷
    samplingPoints: [1, 3, 5],  // 进水、生化池出口、出水
    responsible: '周九',
    priority: 'medium',
    status: 'pending',
    sampler: '钱七',
    tester: '孙八',
    reviewer: '赵六',
    expectedSampleQuantity: 1,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '透明至微黄',
    expectedSupernatant: '清澈',
    samplingInstructions: '连续7天每日采样，记录水温',
    needTest: true
  },
  {
    id: 5,
    name: '污泥膨胀问题诊断',
    planDate: '2024-02-05',
    reason: '生化池出现污泥膨胀现象',
    description: '诊断污泥膨胀原因并制定解决方案',
    testItems: [212, 213, 121, 131],  // 污泥浓度, 污泥沉降比, 氨氮, pH值
    samplingPoints: [6, 8],  // 污泥浓缩池, 回流污泥
    responsible: '钱七',
    priority: 'high',
    status: 'pending',
    sampler: '钱七',
    tester: '周九',
    reviewer: '王五',
    expectedSampleQuantity: 2,
    expectedSampleNature: '半固体',
    expectedSampleAppearance: '棕色膨胀状',
    expectedSupernatant: '浑浊',
    samplingInstructions: '每4小时采样一次，观察沉降性能',
    needTest: true
  },
  {
    id: 6,
    name: '应急排放水质检测',
    planDate: '2024-02-10',
    reason: '设备故障需要启用应急排放',
    description: '应急排放前水质安全检测',
    testItems: [111, 112, 121, 122, 131, 132],  // 全套基础指标
    samplingPoints: [10],  // 应急排放口
    responsible: '赵六',
    priority: 'high',
    status: 'pending',
    sampler: '张三',
    tester: '李四',
    reviewer: '赵六',
    expectedSampleQuantity: 3,
    expectedSampleNature: '液体',
    expectedSampleAppearance: '待检测',
    expectedSupernatant: '待检测',
    samplingInstructions: '排放前30分钟、排放时、排放后30分钟各采样一次',
    needTest: true
  }
])

// 模拟数据 - 任务 (优化后的完整数据)
const tasks = ref<Task[]>([
  {
    id: 1,
    planId: 1,
    planName: '进水水质日常检测计划',
    planType: 'regular',
    taskDate: '2024-01-15',
    samplingPoint: '进水总口',
    testItems: 'COD, BOD5, 氨氮, pH值, 悬浮物',
    assignee: '张三',
    sampler: '张三',
    tester: '李四',
    reviewer: '赵六',
    status: 'completed',
    remark: '已完成当日检测任务'
  },
  {
    id: 2,
    planId: 2,
    planName: '出水水质监测计划',
    planType: 'regular',
    taskDate: '2024-01-15',
    samplingPoint: '出水总口',
    testItems: 'COD, BOD5, 氨氮, 总磷, 总氮, pH值, 悬浮物, 浊度',
    assignee: '钱七',
    sampler: '钱七',
    tester: '孙八',
    reviewer: '赵六',
    status: 'sampling',
    remark: '今日第二次采样任务'
  },
  {
    id: 3,
    planId: 3,
    planName: '污泥浓度检测计划',
    planType: 'regular',
    taskDate: '2024-01-16',
    samplingPoint: '污泥浓缩池',
    testItems: '污泥浓度, 污泥沉降比',
    sampler: '钱七',
    tester: '周九',
    reviewer: '王五',
    status: 'pending',
    remark: '明日上午采样任务'
  },
  {
    id: 4,
    planId: 2,
    planName: '重金属污染源排查',
    planType: 'temporary',
    taskDate: '2024-01-20',
    samplingPoint: '进水总口',
    testItems: '铜, 锌',
    assignee: '钱七',
    sampler: '钱七',
    tester: '孙八',
    reviewer: '周九',
    status: 'testing',
    remark: '紧急排查任务进行中'
  },
  {
    id: 5,
    planId: 4,
    planName: '生化池工艺监测计划',
    planType: 'regular',
    taskDate: '2024-01-16',
    samplingPoint: '生化池进口',
    testItems: '氨氮, 总磷, pH值, 悬浮物',
    assignee: '张三',
    sampler: '张三',
    tester: '李四',
    reviewer: '王五',
    status: 'sampling',
    remark: '生化池进口监测'
  },
  {
    id: 6,
    planId: 4,
    planName: '生化池工艺监测计划',
    planType: 'regular',
    taskDate: '2024-01-16',
    samplingPoint: '生化池出口',
    testItems: '氨氮, 总磷, pH值, 悬浮物',
    assignee: '张三',
    sampler: '张三',
    tester: '李四',
    reviewer: '王五',
    status: 'sampling',
    remark: '生化池出口监测'
  },
  {
    id: 7,
    planId: 1,
    planName: '进水异常检测',
    planType: 'temporary',
    taskDate: '2024-01-15',
    samplingPoint: '进水总口',
    testItems: 'pH值, 氨氮, 铜, 锌',
    assignee: '张三',
    sampler: '张三',
    tester: '李四',
    reviewer: '赵六',
    status: 'completed',
    remark: '异常检测已完成，结果正常'
  },
  {
    id: 8,
    planId: 7,
    planName: '营养盐全面监测计划',
    planType: 'regular',
    taskDate: '2024-01-17',
    samplingPoint: '进水总口',
    testItems: '氨氮, 总磷, 总氮, 硝酸盐氮',
    assignee: '张三',
    sampler: '张三',
    tester: '李四',
    reviewer: '赵六',
    status: 'pending',
    remark: '每周三营养盐监测'
  },
  {
    id: 9,
    planId: 8,
    planName: '有机物去除效果监测计划',
    planType: 'regular',
    taskDate: '2024-01-16',
    samplingPoint: '进水总口',
    testItems: 'COD, BOD5, TOC',
    assignee: '钱七',
    sampler: '钱七',
    tester: '孙八',
    reviewer: '周九',
    status: 'completed',
    remark: '有机物去除效果对比监测'
  },
  {
    id: 10,
    planId: 8,
    planName: '有机物去除效果监测计划',
    planType: 'regular',
    taskDate: '2024-01-16',
    samplingPoint: '出水总口',
    testItems: 'COD, BOD5, TOC',
    assignee: '钱七',
    sampler: '钱七',
    tester: '孙八',
    reviewer: '周九',
    status: 'completed',
    remark: '有机物去除效果对比监测'
  }
])

// 计算过滤后的列表数据
const filteredRegularPlans = computed(() => {
  const start = (regularPagination.value.currentPage - 1) * regularPagination.value.pageSize
  const end = start + regularPagination.value.pageSize
  
  if (!searchKeyword.value) {
    return regularPlans.value.slice(start, end)
  }
  
  const filtered = regularPlans.value.filter(plan => 
    plan.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
  
  return filtered.slice(start, end)
})

const filteredTempPlans = computed(() => {
  const start = (tempPagination.value.currentPage - 1) * tempPagination.value.pageSize
  const end = start + tempPagination.value.pageSize
  
  if (!searchKeyword.value) {
    return tempPlans.value.slice(start, end)
  }
  
  const filtered = tempPlans.value.filter(plan => 
    plan.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
  
  return filtered.slice(start, end)
})

const filteredTasks = computed(() => {
  const start = (taskPagination.value.currentPage - 1) * taskPagination.value.pageSize
  const end = start + taskPagination.value.pageSize
  
  if (!searchKeyword.value) {
    return tasks.value.slice(start, end)
  }
  
  const filtered = tasks.value.filter(task => 
    task.planName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    task.testItems.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
  
  return filtered.slice(start, end)
})

// 初始化
onMounted(() => {
  refreshData()
})

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    // 实际项目中这里应该是实际的API调用
  } catch (error) {
    console.error('数据加载失败:', error)
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 分页处理
const handleSizeChange = () => {
  refreshData()
}

const handleCurrentChange = () => {
  refreshData()
}

// 标签页切换处理
const handleTabChange = () => {
  searchKeyword.value = ''
  refreshData()
}

// 获取状态类型和文本
const getStatusType = (status: string) => {
  switch (status) {
    case 'pending': return 'info'
    case 'processing': return 'primary'
    case 'completed': return 'success'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '未开始'
    case 'processing': return '进行中'
    case 'completed': return '已完成'
    default: return '未知'
  }
}

// 获取优先级类型和文本
const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'normal': return 'info'
    case 'high': return 'warning'
    case 'urgent': return 'danger'
    default: return 'info'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'normal': return '普通'
    case 'high': return '高'
    case 'urgent': return '紧急'
    default: return '普通'
  }
}

// 获取采样频率文本
const getFrequencyText = (frequency: string) => {
  switch (frequency) {
    case 'daily': return '每日'
    case 'weekly': return '每周'
    case 'monthly': return '每月'
    case 'quarterly': return '季度'
    default: return frequency || '-'
  }
}

// 获取样品性质文本
const getSampleNatureText = (nature: string) => {
  switch (nature) {
    case 'liquid': return '液体'
    case 'solid': return '固体'
    case 'semi-solid': return '半固体'
    case 'gas': return '气体'
    default: return nature || '-'
  }
}

// 格式化日期时间
const formatDateTime = (datetime: string) => {
  if (!datetime) return '-'
  return datetime.replace('T', ' ').substring(0, 19)
}

// 获取任务状态类型和文本
const getTaskStatusType = (status: string) => {
  switch (status) {
    case 'unassigned': return 'info'
    case 'assigned': return 'warning'
    case 'processing': return 'primary'
    case 'completed': return 'success'
    default: return 'info'
  }
}

const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'unassigned': return '未分配'
    case 'assigned': return '已分配'
    case 'processing': return '执行中'
    case 'completed': return '已完成'
    default: return '未知'
  }
}

// 创建常规计划
const createRegularPlan = () => {
  regularPlanDialogRef.value.open('create')
}

// 创建临时计划
const createTempPlan = () => {
  tempPlanDialogRef.value.open('create')
}

// 编辑计划
const handleEdit = (type: string, row: RegularPlan | TempPlan) => {
  if (type === 'regular') {
    regularPlanDialogRef.value.open('update', row)
  } else {
    tempPlanDialogRef.value.open('update', row)
  }
}

// 删除计划
const handleDelete = (type: string, row: RegularPlan | TempPlan) => {
  ElMessageBox.confirm(`确定要删除「${row.name}」吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟删除操作
    if (type === 'regular') {
      const index = regularPlans.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        regularPlans.value.splice(index, 1)
      }
    } else {
      const index = tempPlans.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        tempPlans.value.splice(index, 1)
      }
    }
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  refreshData()
}

// 查看任务
const handleViewTasks = (row: RegularPlan | TempPlan) => {
  activeTab.value = 'tasks'
  searchKeyword.value = row.name
  refreshData()
}

// 检查计划冲突
const handleCheckConflicts = (row: RegularPlan | TempPlan) => {
  loading.value = true
  currentPlan.value = row
  
  // 模拟API调用
  setTimeout(() => {
    // 随机生成冲突，实际项目中应该是通过API获取
    const hasConflict = Math.random() > 0.5
    
    if (hasConflict) {
      conflicts.value = [
        {
          type: '人员冲突',
          description: '执行日期与其他任务重叠，人员安排冲突',
          suggestion: '调整执行时间或更换执行人员'
        },
        {
          type: '设备冲突',
          description: '检测设备已被其他计划预约',
          suggestion: '调整检测时间或协调设备使用'
        }
      ]
    } else {
      conflicts.value = []
    }
    
    conflictDialogVisible.value = true
    loading.value = false
  }, 1000)
}

// 调整计划
const handleAdjustPlan = () => {
  conflictDialogVisible.value = false
  
  // 根据计划类型打开编辑弹窗
  if (currentPlan.value) {
    if ('frequency' in currentPlan.value) {
      regularPlanDialogRef.value.open('update', currentPlan.value)
    } else {
      tempPlanDialogRef.value.open('update', currentPlan.value)
    }
  }
}

// 确认计划
const handleConfirmPlan = () => {
  conflictDialogVisible.value = false
  
  // 模拟API调用，更新计划状态为进行中
  if (currentPlan.value) {
    currentPlan.value.status = 'processing'
    
    // 生成对应的任务
    const newTask: Task = {
      id: tasks.value.length + 1,
      planId: currentPlan.value.id,
      planName: currentPlan.value.name,
      planType: 'frequency' in currentPlan.value ? 'regular' : 'temporary',
      taskDate: new Date().toISOString().split('T')[0],
      samplingPoint: currentPlan.value.samplingPoints && currentPlan.value.samplingPoints.length > 0
        ? samplingPointMap[currentPlan.value.samplingPoints[0]].name
        : '未指定',
      testItems: currentPlan.value.testItems && currentPlan.value.testItems.length > 0
        ? currentPlan.value.testItems.map((id: number) => testItemMap[id]?.name || `ID:${id}`).join(', ')
        : '未指定',
      status: 'pending'
    }
    
    tasks.value.push(newTask)
    
    ElMessage.success('计划已确认，任务已生成')
    refreshData()
  }
}

// 更改计划状态
const handleChangePlanStatus = (row: RegularPlan | TempPlan) => {
  let nextStatus: string
  let message: string
  
  if (row.status === 'pending') {
    nextStatus = 'processing'
    message = '计划已开始执行'
  } else if (row.status === 'processing') {
    nextStatus = 'completed'
    message = '计划已标记为完成'
  } else {
    nextStatus = 'processing'
    message = '计划已重新执行'
  }
  
  // 更新状态
  row.status = nextStatus
  
  ElMessage.success(message)
}

// 任务分配 - 带二次确认弹窗
const handleAssignTask = (task: Task) => {
  // 构建确认信息
  const confirmContent = `
    <div style="line-height: 1.6;">
      <p><strong>任务信息：</strong></p>
      <p>计划名称：${task.planName}</p>
      <p>采样点位：${task.samplingPoint}</p>
      <p>检测项目：${task.testItems}</p>
      <p>计划日期：${task.taskDate}</p>
      <br/>
      <p><strong>分配信息：</strong></p>
      <p>采样人员：${task.sampler || '未指定'}</p>
      <p>检测人员：${task.tester || '未指定'}</p>
      <p>审核人员：${task.reviewer || '未指定'}</p>
      <br/>
      <p style="color: #E6A23C;"><strong>确认分配此任务？</strong></p>
    </div>
  `

  ElMessageBox.confirm(confirmContent, '分配任务确认', {
    confirmButtonText: '确认分配',
    cancelButtonText: '取消',
    type: 'warning',
    dangerouslyUseHTMLString: true,
    customClass: 'assign-task-confirm'
  }).then(() => {
    // 更新任务状态为采样中
    const taskIndex = tasks.value.findIndex(t => t.id === task.id)
    if (taskIndex !== -1) {
      tasks.value[taskIndex].status = 'sampling'
      tasks.value[taskIndex].assignTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
      ElMessage.success(`任务已分配，状态更新为采样中`)
    }
  }).catch(() => {
    ElMessage.info('已取消分配')
  })
}





// 查看任务详情
const handleViewTaskDetail = (task: Task) => {
  currentTask.value = task

  // 模拟获取任务相关的检测项目
  currentTaskItems.value = [
    {
      id: 1,
      name: 'COD',
      category: '水质',
      method: '重铬酸钾法',
      standardValue: '≤50',
      unit: 'mg/L',
      priority: 'high'
    },
    {
      id: 2,
      name: 'BOD5',
      category: '水质',
      method: '稀释与接种法',
      standardValue: '≤10',
      unit: 'mg/L',
      priority: 'medium'
    },
    {
      id: 3,
      name: 'SS',
      category: '水质',
      method: '重量法',
      standardValue: '≤10',
      unit: 'mg/L',
      priority: 'low'
    }
  ]

  // 模拟获取任务执行记录
  taskExecutionRecords.value = [
    {
      id: 1,
      action: '任务创建',
      description: '系统自动创建任务',
      operator: '系统',
      time: '2024-01-15 09:00:00',
      type: 'info'
    },
    {
      id: 2,
      action: '任务分配',
      description: `任务已分配给 ${task.assignee || '张三'}`,
      operator: '李主管',
      time: '2024-01-15 09:30:00',
      type: 'primary'
    }
  ]

  // 根据任务状态添加更多记录
  if (task.status === 'processing') {
    taskExecutionRecords.value.push({
      id: 3,
      action: '开始执行',
      description: '任务执行人员开始采样工作',
      operator: task.assignee || '张三',
      time: '2024-01-15 10:00:00',
      type: 'success'
    })
  } else if (task.status === 'completed') {
    taskExecutionRecords.value.push(
      {
        id: 3,
        action: '开始执行',
        description: '任务执行人员开始采样工作',
        operator: task.assignee || '张三',
        time: '2024-01-15 10:00:00',
        type: 'success'
      },
      {
        id: 4,
        action: '任务完成',
        description: '采样工作已完成，样品已送检',
        operator: task.assignee || '张三',
        time: '2024-01-15 11:30:00',
        type: 'success'
      }
    )
  }

  taskDetailDialogVisible.value = true
}

// 从详情弹窗编辑任务
const handleEditTask = () => {
  if (currentTask.value) {
    taskDetailDialogVisible.value = false
    // 这里可以打开编辑弹窗或跳转到编辑页面
    ElMessage.info('跳转到任务编辑页面')
  }
}



// 取消任务
const handleCancelTask = (task: Task) => {
  ElMessageBox.confirm(`确定要取消任务「${task.id}」吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟API调用
    const index = tasks.value.findIndex(t => t.id === task.id)
    if (index !== -1) {
      tasks.value.splice(index, 1)
      ElMessage.success('任务已取消')
    }
  }).catch(() => {})
}

// ==================== 新增执行功能 ====================

// 任务选择变化
const handleTaskSelectionChange = (selection: Task[]) => {
  selectedTasks.value = selection
}



// 批量分配任务
const handleBatchAssign = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要分配的任务')
    return
  }

  ElMessage.info('批量分配功能开发中...')
}



// 刷新任务
const handleRefreshTasks = () => {
  ElMessage.success('任务列表已刷新')
  // 这里可以重新获取数据
}





</script>

<script lang="ts">
export default {
  name: 'AssayTestPlan'
}
</script>

<style scoped>
.operation-bar {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.pagination-container {
  margin-top: 1.5rem;
  text-align: right;
  padding: 0.5rem 0;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
}

:deep(.el-table) {
  border-collapse: collapse;
  width: 100%;
  border-radius: 0.25rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  padding: 0.85rem 0.75rem;
  border: 1px solid var(--el-table-border-color);
}

:deep(.el-table td) {
  padding: 0.85rem 0.75rem;
  border: 1px solid var(--el-table-border-color);
}

:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid var(--el-table-border-color);
}

:deep(.el-table-column--selection .cell) {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

:deep(.el-dropdown-link) {
  cursor: pointer;
  display: flex;
  align-items: center;
}

:deep(.el-tabs__item) {
  font-size: 1rem;
  font-weight: 500;
  padding: 0 1.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__active-bar) {
  height: 2px;
}

:deep(.el-button) {
  font-weight: 500;
}

:deep(.el-tag) {
  font-weight: 500;
  padding: 0 0.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
}

:deep(.el-dialog__body) {
  padding: 1.5rem;
}

:deep(.el-alert) {
  margin-bottom: 1rem;
}

:deep(.el-form-item) {
  margin-bottom: 1.25rem;
}

:deep(.el-input), 
:deep(.el-select),
:deep(.el-date-picker) {
  width: 100%;
}

:deep(.el-dialog__footer) {
  padding: 1rem 1.5rem 1.5rem;
  text-align: right;
}

/* 表格优化样式 */
.plan-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.plan-table :deep(.el-table__header) {
  background-color: #f8fafc;
}

.plan-table :deep(.el-table__header th) {
  background-color: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

.plan-table :deep(.el-table__body tr:hover) {
  background-color: #f9fafb;
}

.plan-table :deep(.el-table__body td) {
  padding: 12px 8px;
  border-bottom: 1px solid #f3f4f6;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
}

:deep(.danger-item) {
  color: var(--el-color-danger) !important;
}

:deep(.danger-item:hover) {
  background-color: var(--el-color-danger-light-9) !important;
  color: var(--el-color-danger) !important;
}

/* 响应式表格优化 */
.responsive-table {
  /* 基础响应式设置 */
  table-layout: auto;
}

/* 大屏适配 (≥1920px) */
@media (min-width: 1920px) {
  .responsive-table :deep(.el-table__cell) {
    padding: 16px 12px;
    font-size: 14px;
  }

  .responsive-table :deep(.el-table__header th) {
    font-size: 14px;
    font-weight: 600;
  }

  .responsive-table :deep(.el-tag) {
    font-size: 12px;
    padding: 4px 8px;
  }

  .responsive-table :deep(.el-button) {
    font-size: 13px;
    padding: 6px 12px;
  }
}

/* 超大屏适配 (≥2560px) */
@media (min-width: 2560px) {
  .responsive-table :deep(.el-table__cell) {
    padding: 20px 16px;
    font-size: 16px;
  }

  .responsive-table :deep(.el-table__header th) {
    font-size: 16px;
    font-weight: 600;
  }

  .responsive-table :deep(.el-tag) {
    font-size: 14px;
    padding: 6px 12px;
  }

  .responsive-table :deep(.el-button) {
    font-size: 14px;
    padding: 8px 16px;
  }
}

/* 中等屏幕适配 (1366px-1919px) */
@media (max-width: 1919px) and (min-width: 1366px) {
  .responsive-table :deep(.el-table__cell) {
    padding: 10px 8px;
    font-size: 13px;
  }

  .responsive-table :deep(.el-table__header th) {
    font-size: 13px;
  }

  .responsive-table :deep(.el-tag) {
    font-size: 11px;
    padding: 2px 6px;
  }

  .responsive-table :deep(.el-button) {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 小屏幕适配 (≤1365px) */
@media (max-width: 1365px) {
  .responsive-table :deep(.el-table__cell) {
    padding: 8px 6px;
    font-size: 12px;
  }

  .responsive-table :deep(.el-table__header th) {
    font-size: 12px;
  }

  .responsive-table :deep(.el-tag) {
    font-size: 10px;
    padding: 2px 4px;
  }

  .responsive-table :deep(.el-button) {
    font-size: 11px;
    padding: 3px 6px;
  }
}

/* 冲突检查表格样式 */
.conflict-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.conflict-table :deep(.el-table__header) {
  background-color: #fef7e0;
}

.conflict-table :deep(.el-table__header th) {
  background-color: #fef7e0 !important;
  color: #b45309;
  font-weight: 600;
  border-bottom: 2px solid #f59e0b;
}

.conflict-table :deep(.el-table__body tr:hover) {
  background-color: #fffbeb;
}

.conflict-table :deep(.el-table__body td) {
  padding: 12px 8px;
  border-bottom: 1px solid #fed7aa;
}

/* 任务详情弹窗样式 */
.task-detail-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.task-detail-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  margin: 0;
}

.task-detail-dialog :deep(.el-dialog__title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.task-detail-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.task-detail-dialog :deep(.el-dialog__body) {
  padding: 24px;
  background-color: #f8fafc;
}

.task-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.detail-card :deep(.el-card__header) {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.detail-item {
  margin-bottom: 12px;
}

.detail-item label {
  font-weight: 600;
  color: #6b7280;
  margin-right: 8px;
  min-width: 80px;
  display: inline-block;
}

.detail-item span {
  color: #374151;
}

.task-detail-table {
  border-radius: 6px;
  overflow: hidden;
}

.task-detail-table :deep(.el-table__header) {
  background-color: #f3f4f6;
}

.task-detail-table :deep(.el-table__header th) {
  background-color: #f3f4f6 !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #d1d5db;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 4px;
}

.timeline-operator {
  color: #9ca3af;
  font-size: 12px;
}

.remark-content {
  color: #374151;
  line-height: 1.6;
  margin: 0;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 执行人员角色样式 */
.executor-roles {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;

  .el-tag {
    min-width: 32px;
    text-align: center;
  }

  span {
    color: #606266;
    font-weight: 500;
  }
}

/* 分配任务确认弹窗样式 */
:deep(.assign-task-confirm) {
  .el-message-box__content {
    padding: 20px 24px;
  }

  .el-message-box__message {
    line-height: 1.6;

    p {
      margin: 8px 0;
    }

    strong {
      color: #303133;
      font-weight: 600;
    }
  }
}
</style>