<template>
  <el-dialog v-model="visible" title="采样任务详情" width="70%" class="sampling-task-detail-dialog">
    <div v-if="currentTask" class="task-detail-content">
      <!-- 基本信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">基本信息</span>
            <el-tag :type="getStatusType(currentTask.status)" size="small">
              {{ getStatusText(currentTask.status) }}
            </el-tag>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>任务名称：</label>
              <span>{{ currentTask.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>计划名称：</label>
              <span>{{ currentTask.planName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>采样点：</label>
              <span>{{ currentTask.samplingPoint }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 16px;">
          <el-col :span="8">
            <div class="detail-item">
              <label>计划日期：</label>
              <span>{{ currentTask.planDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>执行人员：</label>
              <span>{{ currentTask.executor || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>样品ID：</label>
              <span>{{ currentTask.sampleId || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 执行人员信息 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
        <template #header>
          <span class="card-title">执行人员</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>采样人员：</label>
              <span>{{ currentTask.sampler || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>检测人员：</label>
              <span>{{ currentTask.tester || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>审核人员：</label>
              <span>{{ currentTask.reviewer || '未分配' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 样品预期信息 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
        <template #header>
          <span class="card-title">样品预期信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>样品性质：</label>
              <span>{{ currentTask.sampleNature || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>预期数量：</label>
              <span>{{ currentTask.sampleQuantity || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>是否需要检测：</label>
              <el-tag :type="currentTask.needTest ? 'success' : 'info'" size="small">
                {{ currentTask.needTest ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 16px;">
          <el-col :span="12">
            <div class="detail-item">
              <label>预期外观：</label>
              <span>{{ currentTask.expectedAppearance || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>预期上清液：</label>
              <span>{{ currentTask.expectedSupernatant || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 16px;" v-if="currentTask.samplingInstructions">
          <el-col :span="24">
            <div class="detail-item">
              <label>采样说明：</label>
              <p class="instruction-content">{{ currentTask.samplingInstructions }}</p>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 现场执行信息 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;" v-if="showExecutionInfo">
        <template #header>
          <span class="card-title">现场执行信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>采样地点：</label>
              <span>{{ currentTask.samplingLocation || '待填写' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>实际采样时间：</label>
              <span>{{ currentTask.actualSamplingTime || '待填写' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 16px;" v-if="currentTask.samplingCondition">
          <el-col :span="24">
            <div class="detail-item">
              <label>现场采样情况：</label>
              <p class="condition-content">{{ currentTask.samplingCondition }}</p>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 实际样品信息 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;" v-if="showActualInfo">
        <template #header>
          <span class="card-title">实际样品信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>实际数量：</label>
              <span>{{ currentTask.actualSampleQuantity || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>样品状态：</label>
              <el-tag :type="getSampleStatusType(currentTask.sampleStatus)" size="small">
                {{ currentTask.sampleStatus || '待确定' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>完成时间：</label>
              <span>{{ currentTask.completedTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 16px;">
          <el-col :span="12">
            <div class="detail-item">
              <label>实际外观：</label>
              <div class="comparison-display">
                <div class="expected">预期: {{ currentTask.expectedAppearance || '-' }}</div>
                <div class="actual" :class="{ 'different': isAppearanceDifferent }">
                  实际: {{ currentTask.sampleAppearance || '待填写' }}
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>实际上清液：</label>
              <div class="comparison-display">
                <div class="expected">预期: {{ currentTask.expectedSupernatant || '-' }}</div>
                <div class="actual" :class="{ 'different': isSupernatantDifferent }">
                  实际: {{ currentTask.supernatant || '待填写' }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 备注信息 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;" v-if="currentTask.remark">
        <template #header>
          <span class="card-title">备注信息</span>
        </template>
        <p class="remark-content">{{ currentTask.remark }}</p>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出详情</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'SamplingTaskDetailDialog' })

// 弹窗显示状态
const visible = ref(false)

// 当前任务
const currentTask = ref<any>(null)

// 是否显示执行信息（采样中和已完成状态）
const showExecutionInfo = computed(() => {
  return currentTask.value && ['processing', 'completed', 'abnormal'].includes(currentTask.value.status)
})

// 是否显示实际信息（已完成状态）
const showActualInfo = computed(() => {
  return currentTask.value && ['completed', 'abnormal'].includes(currentTask.value.status)
})

// 外观是否不同
const isAppearanceDifferent = computed(() => {
  return currentTask.value?.sampleAppearance && 
         currentTask.value?.expectedAppearance && 
         currentTask.value.sampleAppearance !== currentTask.value.expectedAppearance
})

// 上清液是否不同
const isSupernatantDifferent = computed(() => {
  return currentTask.value?.supernatant && 
         currentTask.value?.expectedSupernatant && 
         currentTask.value.supernatant !== currentTask.value.expectedSupernatant
})

// 打开弹窗
const open = (task: any) => {
  currentTask.value = { ...task }
  visible.value = true
}

// 状态相关方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'assigned': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'abnormal': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待采样',
    'assigned': '已分配',
    'processing': '采样中',
    'completed': '已完成',
    'abnormal': '异常'
  }
  return textMap[status] || status
}

const getSampleStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '正常': 'success',
    '异常': 'danger',
    '需复检': 'warning'
  }
  return typeMap[status] || 'info'
}

// 导出详情
const handleExport = () => {
  ElMessage.success('正在导出采样任务详情...')
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.sampling-task-detail-dialog {
  .task-detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .detail-card {
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-weight: 600;
      color: #303133;
    }
  }

  .detail-item {
    margin-bottom: 12px;

    label {
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
    }

    span {
      color: #303133;
    }
  }

  .instruction-content,
  .condition-content,
  .remark-content {
    color: #606266;
    line-height: 1.6;
    margin: 8px 0 0 0;
    padding: 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .comparison-display {
    .expected {
      color: #909399;
      font-size: 12px;
      margin-bottom: 4px;
    }

    .actual {
      color: #303133;
      
      &.different {
        color: #e6a23c;
        font-weight: 500;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
