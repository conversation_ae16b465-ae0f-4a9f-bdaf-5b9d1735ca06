<template>
  <div class="factory-page">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="水厂名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入水厂名称" clearable />
        </el-form-item>
        <!--        <el-form-item label="水厂类型" prop="type">-->
        <!--          <el-select v-model="queryParams.type" placeholder="请选择水厂类型" clearable style="width: 200px">-->
        <!--            <el-option-->
        <!--              v-for="item in typeOptions"-->
        <!--              :key="item.value"-->
        <!--              :label="item.label"-->
        <!--              :value="item.value"-->
        <!--            />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="所在区域" prop="region">-->
        <!--          <el-select v-model="queryParams.region" placeholder="请选择所在区域" clearable style="width: 200px">-->
        <!--            <el-option-->
        <!--              v-for="item in regionOptions"-->
        <!--              :key="item.value"-->
        <!--              :label="item.label"-->
        <!--              :value="item.value"-->
        <!--            />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="状态" prop="isActive">
          <el-select v-model="queryParams.isActive" placeholder="请选择状态" clearable style="width: 200px">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="primary" @click="handleAdd">新增水厂</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮和表格 -->
    <el-card class="table-card">
      <!--      <template #header>-->
      <!--        <div class="card-header">-->
      <!--          <span>水厂列表</span>-->
      <!--          <div>-->
      <!--&lt;!&ndash;            <el-button @click="toggleExpandAll">&ndash;&gt;-->
      <!--&lt;!&ndash;              {{ isExpandAll ? '折叠' : '展开' }}&ndash;&gt;-->
      <!--&lt;!&ndash;            </el-button>&ndash;&gt;-->
      <!--            <el-button type="primary" @click="handleAdd">新增水厂</el-button>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </template>-->

      <el-table v-loading="loading" :data="factoryList" row-key="id" border ref="tableRef"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
        <!--        <el-table-column label="序号" type="index" width="60" align="center" />-->
        <el-table-column prop="name" label="水厂名称" min-width="250" />
        <el-table-column prop="type" label="水厂类型" width="120" />
        <!--        <el-table-column prop="region" label="所在区域" width="150" />-->
        <el-table-column prop="orderNum" label="排序" width="80" align="center" />
        <el-table-column prop="level" label="层级" width="80" align="center" />
        <el-table-column prop="isActive" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isActive ? 'success' : 'danger'">
              {{ row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link @click="handleToggleStatus(row)">
              {{ row.isActive ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="650px" :close-on-click-modal="false">
      <el-form ref="factoryFormRef" :model="factoryForm" :rules="rules" label-width="100px">
        <el-form-item label="水厂名称" prop="name">
          <el-input v-model="factoryForm.name" placeholder="请输入水厂名称" />
        </el-form-item>
        <el-form-item label="父级厂站" prop="parentId">
          <el-select v-model="factoryForm.parentId" placeholder="请选择父级厂站" clearable style="width: 100%">
            <el-option v-for="item in availableParentFactories" :key="item.id!" :label="item.name" :value="item.id!" />
          </el-select>
        </el-form-item>
        <el-form-item label="水厂类型" prop="type">
          <el-select v-model="factoryForm.type" placeholder="请选择水厂类型" style="width: 100%">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="所在区域" prop="region">-->
        <!--          <el-select v-model="factoryForm.region" placeholder="请选择所在区域" style="width: 100%">-->
        <!--            <el-option-->
        <!--              v-for="item in regionOptions"-->
        <!--              :key="item.value"-->
        <!--              :label="item.label"-->
        <!--              :value="item.value"-->
        <!--            />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="factoryForm.orderNum" :min="0" :max="9999" controls-position="right"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="层级" prop="level">
          <el-input-number v-model="factoryForm.level" :min="1" :max="10" controls-position="right"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="填报人员" prop="userIdList">
          <div class="flex">
            <div class="flex-1 reporter-list">
              <div v-if="selectedReporters.length > 0" class="flex flex-wrap gap-2">
                <div v-for="user in selectedReporters" :key="user.id" class="reporter-tag">
                  <el-avatar :size="24" v-if="user.avatar" :src="user.avatar" />
                  <el-avatar :size="24" v-else>{{ user.nickname.charAt(0) }}</el-avatar>
                  <span class="ml-1">{{ user.nickname }}</span>
                </div>
              </div>
              <div v-else class="text-gray-400 mt-1">未选择填报人员</div>
            </div>
            <el-button type="primary" link @click="openReporterSelect">选择人员</el-button>
          </div>
        </el-form-item>
        <el-form-item label="审核人" prop="auditorId">
          <div class="flex">
            <div class="flex-1 reporter-list">
              <div v-if="selectedAuditor" class="flex flex-wrap gap-2">
                <div class="reporter-tag">
                  <el-avatar :size="24" v-if="selectedAuditor.avatar" :src="selectedAuditor.avatar" />
                  <el-avatar :size="24" v-else>{{ selectedAuditor.nickname.charAt(0) }}</el-avatar>
                  <span class="ml-1">{{ selectedAuditor.nickname }}</span>
                </div>
              </div>
              <div v-else class="text-gray-400 mt-1">未选择审核人</div>
            </div>
            <el-button type="primary" link @click="openAuditorSelect">选择审核人</el-button>
          </div>
        </el-form-item>
        <el-form-item v-if="currentEditId" label="级联更新" prop="cascadeUpdateAudit">
          <el-switch v-model="factoryForm.cascadeUpdateAudit" active-text="级联更新子级审核人配置" />
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-switch v-model="factoryForm.isActive" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 用户选择弹窗 -->
    <UserSelectForm ref="userSelectFormRef" @confirm="handleUserSelectConfirm" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { FactoryApi } from '@/api/report/factory'
import type { Factory, FactoryQueryParams } from '@/types/factory'
import * as UserApi from '@/api/system/user'
import UserSelectForm from '@/components/UserSelectForm/index.vue'

defineOptions({ name: 'Factory' })

// 水厂类型选项
const typeOptions = [
  { label: '排涝泵站', value: '排涝泵站' },
  { label: '污水厂', value: '地下水' },
  { label: '净水厂', value: '净水厂' }
]

// 区域选项
const regionOptions = [
  { label: '华东', value: '华东' },
  { label: '华南', value: '华南' },
  { label: '华北', value: '华北' },
  { label: '华中', value: '华中' },
  { label: '西南', value: '西南' },
  { label: '西北', value: '西北' },
  { label: '东北', value: '东北' }
]

// 查询参数
const queryParams = reactive({
  name: '',
  type: '',
  region: '',
  isActive: undefined as boolean | undefined
})

// 表格数据
const loading = ref(false)
const factoryList = ref<Factory[]>([])
const tableRef = ref()
const isExpandAll = ref(false)

// 父级厂站选项
const factoryOptions = ref<Factory[]>([])

// 当前编辑的工厂ID
const currentEditId = ref<number | undefined>(undefined)

// 扁平化处理树形数据，用于父级厂站选择
const flattenFactoryTree = (tree: Factory[]) => {
  const result: Factory[] = [];

  const flatten = (nodes: Factory[]) => {
    if (!nodes) return;

    nodes.forEach(node => {
      const { children, ...rest } = node;
      result.push(rest as Factory);

      if (children && children.length > 0) {
        flatten(children);
      }
    });
  };

  flatten(tree);
  return result;
};

// 获取父级水厂选项
const getParentFactoryOptions = async () => {
  try {
    const res = await FactoryApi.listParentFactory();
    if (res && (res as any).code === 0) {
      factoryOptions.value = (res as any).data || [];
    } else {
      ElMessage.error('获取父级水厂失败');
    }
  } catch (error) {
    console.error('获取父级水厂失败:', error);
    ElMessage.error('获取父级水厂失败');
  }
};

// 过滤出可选的父级工厂（排除自己及其子工厂）
const availableParentFactories = computed(() => {
  if (!currentEditId.value) {
    // 新增模式，所有工厂都可以选择
    return factoryOptions.value.filter(factory => !!factory.id);
  } else {
    // 编辑模式，排除自己和可能的子工厂
    return factoryOptions.value.filter(factory => {
      // 排除自身
      if (factory.id === currentEditId.value) return false;

      // 排除所有可能的子工厂 (根据parentId进行简单判断)
      if (factory.parentId === currentEditId.value) return false;

      return !!factory.id; // 确保id不为undefined
    });
  }
});

// 用户选择
const selectedReporters = ref<UserApi.UserVO[]>([])
const selectedAuditor = ref<UserApi.UserVO | null>(null)

// 表单数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const factoryForm = reactive({
  id: undefined as number | undefined,
  name: '',
  parentId: undefined as number | undefined,
  type: '',
  region: '',
  orderNum: 0,
  level: 1,
  userIdList: [] as number[],
  auditorId: undefined as number | undefined,
  isActive: true,
  cascadeUpdateAudit: false
})

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入水厂名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择水厂类型', trigger: 'change' }
  ],
  orderNum: [
    { required: true, message: '请输入排序号', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请输入层级', trigger: 'blur' }
  ],
  auditorId: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (factoryForm.userIdList.length > 0 && !value) {
          callback(new Error('已选择填报人员，请选择审核人'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 表单ref
const factoryFormRef = ref()
const userSelectFormRef = ref()

// 获取水厂列表
const getFactoryList = async (params = queryParams) => {
  loading.value = true
  try {
    console.log('查询参数:', params)
    const res = await FactoryApi.getFactoryTree(params)
    console.log('获取到的数据:', res)
    factoryList.value = res.data || []
  } catch (error) {
    console.error('获取水厂列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 打开选择填报人员弹窗
const openReporterSelect = () => {
  userSelectFormRef.value?.open(0, selectedReporters.value)
}

// 打开选择审核人弹窗
const openAuditorSelect = () => {
  userSelectFormRef.value?.open(1, selectedAuditor.value ? [selectedAuditor.value] : [])
}

// 查询
const handleQuery = () => {
  const params = {
    ...queryParams,
    name: queryParams.name.trim(),
    type: queryParams.type.trim(),
    region: queryParams.region.trim()
  }
  getFactoryList(params)
}

// 重置查询
const resetQuery = () => {
  queryParams.name = ''
  queryParams.type = ''
  queryParams.region = ''
  queryParams.isActive = undefined
  handleQuery()
}

// 新增水厂
const handleAdd = () => {
  dialogTitle.value = '新增水厂'
  currentEditId.value = undefined
  factoryForm.id = undefined
  factoryForm.name = ''
  factoryForm.parentId = undefined
  factoryForm.type = ''
  factoryForm.orderNum = 0
  factoryForm.level = 3
  factoryForm.userIdList = []
  factoryForm.auditorId = undefined
  factoryForm.isActive = true
  factoryForm.cascadeUpdateAudit = false
  selectedReporters.value = []
  selectedAuditor.value = null
  getParentFactoryOptions()
  dialogVisible.value = true
}

// 编辑水厂
const handleEdit = async (row: Factory) => {
  try {
    currentEditId.value = row.id
    const res = await FactoryApi.getFactory(row.id!)
    Object.assign(factoryForm, {
      id: res.id,
      name: res.name,
      parentId: res.parentId,
      type: res.type,
      orderNum: res.orderNum || 0,
      level: res.level || 3,
      userIdList: res.userIdList || [],
      auditorId: res.auditorId,
      isActive: res.isActive,
      cascadeUpdateAudit: false
    })

    // 获取已选择的填报人员信息
    if (res.userIdList && res.userIdList.length > 0) {
      try {
        const userList = await UserApi.getSimpleUserList()
        selectedReporters.value = userList.filter(user =>
          res.userIdList?.includes(user.id)
        )
      } catch (error) {
        console.error('获取填报人员信息失败:', error)
        selectedReporters.value = []
      }
    } else {
      selectedReporters.value = []
    }

    // 获取审核人信息
    if (res.auditorId) {
      try {
        const userList = await UserApi.getSimpleUserList()
        selectedAuditor.value = userList.find(user => user.id === res.auditorId) || null
      } catch (error) {
        console.error('获取审核人信息失败:', error)
        selectedAuditor.value = null
      }
    } else {
      selectedAuditor.value = null
    }

    getParentFactoryOptions()
    dialogTitle.value = '编辑水厂'
    dialogVisible.value = true
  } catch (error) {
    console.error('获取水厂信息失败:', error)
    ElMessage.error('获取水厂信息失败')
  }
}

// 处理用户选择确认
const handleUserSelectConfirm = (type: number, userList: UserApi.UserVO[]) => {
  if (type === 0) {
    selectedReporters.value = userList
    factoryForm.userIdList = userList.map(user => user.id)
  } else {
    selectedAuditor.value = userList[0] || null
    factoryForm.auditorId = userList[0]?.id
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!factoryFormRef.value) return

  try {
    await factoryFormRef.value.validate()
    const submitData = {
      ...factoryForm,
      name: factoryForm.name.trim(),
      type: factoryForm.type.trim(),
      region: factoryForm.region.trim()
    }
    const api = submitData.id ? FactoryApi.updateFactory : FactoryApi.addFactory
    await api(submitData)

    ElMessage.success(submitData.id ? '修改成功' : '新增成功')
    dialogVisible.value = false
    getFactoryList()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

// 删除水厂
const handleDelete = (row: Factory) => {
  ElMessageBox.confirm('确认删除该水厂吗？', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await FactoryApi.deleteFactory(row.id!)
      ElMessage.success('删除成功')
      getFactoryList()
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

// 切换状态
const handleToggleStatus = async (row: Factory) => {
  try {
    const updateData = {
      id: row.id,
      name: row.name,
      type: row.type,
      region: row.region,
      isActive: !row.isActive
    }
    await FactoryApi.updateFactory(updateData)
    ElMessage.success(`${row.isActive ? '禁用' : '启用'}成功`)
    getFactoryList()
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新状态失败')
  }
}

// 展开/折叠所有节点
const toggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  if (tableRef.value) {
    if (isExpandAll.value) {
      // 展开所有
      expandAllNodes();
    } else {
      // 折叠所有
      collapseAllNodes();
    }
  }
}

// 展开所有节点
const expandAllNodes = () => {
  const expandedKeys = getAllKeys(factoryList.value);
  tableRef.value.store.states.expandedKeys.value = expandedKeys;
}

// 折叠所有节点
const collapseAllNodes = () => {
  tableRef.value.store.states.expandedKeys.value = [];
}

// 获取所有节点的ID
const getAllKeys = (data: Factory[]): number[] => {
  const keys: number[] = [];

  const getKeys = (items: Factory[]) => {
    if (!items) return;

    items.forEach(item => {
      if (item.id) {
        keys.push(item.id);
      }
      if (item.children && item.children.length > 0) {
        getKeys(item.children);
      }
    });
  };

  getKeys(data);
  return keys;
}

onMounted(() => {
  console.log('开始获取水厂列表');
  getFactoryList().then(() => {
    // 默认状态下不展开
    isExpandAll.value = false;
  });
})
</script>

<style scoped lang="scss">
.factory-page {
  padding: 20px;

  .search-card {
    margin-bottom: 20px;
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .reporter-list {
    min-height: 32px;
    padding: 2px 0;
  }

  .reporter-tag {
    display: flex;
    align-items: center;
    background-color: #f0f2f5;
    border-radius: 16px;
    padding: 4px 12px 4px 4px;
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .flex {
    display: flex;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .gap-2 {
    gap: 8px;
  }

  .ml-1 {
    margin-left: 4px;
  }

  .mt-1 {
    margin-top: 4px;
  }

  .text-gray-400 {
    color: #a0aec0;
  }
}
</style>
