<template>
  <div class="w-full h-[calc(100vh-170px)]">
    <el-card class="h-full">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">入库申请单</span>
          <el-button type="primary" @click="submitStockInRequest">提交申请</el-button>
        </div>
      </template>
      <div class="w-full h-[calc(100vh-270px)] flex flex-col">
        <div class="w-full h-[200px] mb-2">
          <div class="w-full h-full flex flex-col">
            <div class="w-full h-[30px] gap-2 flex items-center">
              <span>基本信息：</span>
            </div>
            <div class="flex-1 w-full">
              <el-form :model="formData" label-width="100px" ref="formRef">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="申请单号" prop="requestNo">
                      <el-input v-model="formData.requestNo" placeholder="系统自动生成" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="申请日期" prop="requestDate">
                      <el-date-picker v-model="formData.requestDate" type="date" placeholder="选择日期"
                        value-format="YYYY-MM-DD" style="width: 100%" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="申请人" prop="applicant">
                      <el-input v-model="formData.applicant" placeholder="申请人姓名" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="仓库" prop="warehouse">
                      <el-select v-model="formData.warehouse" placeholder="请选择仓库" style="width: 100%">
                        <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label"
                          :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="供应商" prop="supplier">
                      <el-select v-model="formData.supplier" placeholder="请选择供应商" style="width: 100%">
                        <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                          :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="入库类型" prop="inboundType">
                      <el-select v-model="formData.inboundType" placeholder="请选择入库类型" style="width: 100%">
                        <el-option label="采购入库" value="purchase" />
                        <el-option label="退货入库" value="return" />
                        <el-option label="调拨入库" value="transfer" />
                        <el-option label="其他入库" value="other" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="备注" prop="remark">
                  <el-input v-model="formData.remark" type="textarea" :rows="2" placeholder="请输入备注信息" />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
        <div class="w-full flex-1 flex flex-col">
          <div class="w-full h-1/7 flex flex-col">
            <div class="w-full h-[30px] gap-2 flex items-center">
              <span>验收附件：</span>
            </div>
            <div class="w-full flex flex-1 justify-center items-center border-b-2">
              <div class="w-full h-full flex justify-between items-center pb-1">
                <span class="text-sm text-gray-500">可上传到货验收单、质检报告等相关文件，支持jpg、png、pdf格式，单个文件不超过10MB</span>
                <el-upload class="upload-demo" action="#" :auto-upload="false" :file-list="formData.attachments"
                  :on-change="handleFileChange" :on-remove="handleFileRemove" multiple>
                  <el-button type="primary">选择文件</el-button>
                </el-upload>
              </div>
            </div>
          </div>
          <div class="w-full flex-1 flex flex-col pt-1">
            <div class="w-full h-[40px] gap-2 flex items-center justify-between">
              <span>商品信息：</span>
              <div class="flex items-center gap-2">
                <el-button type="primary" @click="showProductSelectDialog" size="small" :icon="Plus">添加商品</el-button>
                <el-button type="success" @click="importProductsFromExcel" size="small" :icon="Upload">批量导入</el-button>
                <el-button type="danger" @click="removeSelectedProducts" :disabled="selectedProductRows.length === 0"
                  size="small" :icon="Delete">
                  批量删除
                </el-button>
              </div>
            </div>
            <div class="flex-1 w-full flex flex-col">
              <div class="relative w-full h-full">
                <div class="absolute w-full h-full">
                  <el-table :data="productTableRef" stripe border style="width: 100%; height: 100%"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" type="index" width="50" align="center" />
                    <el-table-column prop="code" label="商品编码" width="120" align="center" />
                    <el-table-column prop="name" label="商品名称" min-width="150" align="center" />
                    <el-table-column prop="spec" label="规格型号" width="120" align="center" />
                    <el-table-column prop="unit" label="单位" width="80" align="center" />
                    <el-table-column prop="quantity" label="数量" width="120" align="center">
                      <template #default="scope">
                        <el-input-number v-model="scope.row.quantity" :min="1" :precision="0" controls-position="right"
                          style="width: 100%" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="price" label="单价(元)" width="120" align="center" />
                    <el-table-column prop="amount" label="金额(元)" width="120" align="center">
                      <template #default="scope">
                        {{ (scope.row.price * scope.row.quantity).toFixed(2) }}
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" fixed="right">
                      <template #default="scope">
                        <el-button type="danger" link @click="removeProduct(scope.$index)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              <div class="w-full flex-1 flex items-center justify-end mt-2">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
                  layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
                  @current-change="handleCurrentChange" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    <!-- 商品选择对话框 -->
    <el-dialog v-model="productSelectDialogVisible" title="选择商品" width="900px" destroy-on-close>
      <div class="mb-4 flex justify-between">
        <el-input v-model="productSearch" placeholder="输入商品名称或编码搜索" clearable style="width: 300px"
          @keyup.enter="searchProducts">
          <template #append>
            <el-button @click="searchProducts" :icon="Search" />
          </template>
        </el-input>

        <el-select v-model="productCategory" placeholder="商品分类" clearable style="width: 200px">
          <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>

      <el-table :data="productList" stripe border style="width: 100%" height="400px" @row-click="handleProductSelect">
        <el-table-column label="商品编码" prop="code" width="120" />
        <el-table-column label="商品名称" prop="name" min-width="150" />/
        <el-table-column label="规格型号" prop="spec" width="120" />/
        <el-table-column label="单位" prop="unit" width="80" />/
        <el-table-column label="单价(元)" prop="price" width="100" />/
        <el-table-column label="库存" prop="stock" width="80" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click.stop="addProduct(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="productSelectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmProductSelection">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Excel导入对话框 -->
    <el-dialog v-model="importDialogVisible" title="批量导入商品" width="500px" destroy-on-close>
      <el-upload class="upload-demo" drag action="#" :auto-upload="false" :on-change="handleExcelChange" :limit="1"
        accept=".xlsx, .xls">
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text"> 将Excel文件拖到此处，或<em>点击上传</em> </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传xlsx或xls格式的Excel文件，<el-link type="primary" @click="downloadTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { reactive } from 'vue'
import { Plus, Upload, Delete, Search } from '@element-plus/icons-vue'

const productSelectDialogVisible = ref(false)
const productSearch = ref('')
const productCategory = ref('')
const productList = ref([
  {
    id: 1,
    code: 'SP001',
    name: '钢管',
    spec: 'Φ20mm',
    unit: '米',
    price: 15.5,
    quantity: 0,
    stock: 500
  },
  {
    id: 2,
    code: 'SP002',
    name: '铜管',
    spec: 'Φ15mm',
    unit: '米',
    price: 25.8,
    quantity: 0,
    stock: 300
  },
  {
    id: 3,
    code: 'SP003',
    name: '阀门',
    spec: 'DN50',
    unit: '个',
    price: 45.0,
    quantity: 0,
    stock: 100
  },
  {
    id: 4,
    code: 'SP004',
    name: '水泵',
    spec: '3KW',
    unit: '台',
    price: 1200.0,
    quantity: 0,
    stock: 20
  },
  {
    id: 5,
    code: 'SP005',
    name: '电缆',
    spec: '4×2.5mm²',
    unit: '米',
    price: 8.5,
    quantity: 0,
    stock: 800
  }
])

const importDialogVisible = ref(false)

interface Product {
  id: number
  code: string
  name: string
  spec: string
  unit: string
  price: number
  quantity: number
  stock?: number
}

const formData = reactive({
  requestNo: 'RK' + new Date().getTime(),
  requestDate: new Date().toISOString().slice(0, 10),
  applicant: '张三',
  warehouse: '',
  supplier: '',
  inboundType: 'purchase',
  remark: '',
  products: [] as Product[],
  attachments: [] as any[]
})

const warehouseOptions = [
  { label: '主仓库', value: '1' },
  { label: '备用仓库', value: '2' },
  { label: '临时仓库', value: '3' }
]
const supplierOptions = [
  { label: '供应商A', value: '1' },
  { label: '供应商B', value: '2' },
  { label: '供应商C', value: '3' }
]

// 商品分类选项
const categoryOptions = [
  { label: '原材料', value: '1' },
  { label: '半成品', value: '2' },
  { label: '成品', value: '3' },
  { label: '工具', value: '4' }
]

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(3)

const productTableRef = ref([
  {
    index: 1,
    code: 'SP001',
    name: '笔记本电脑',
    spec: 'i7-12700H/16GB/512GB SSD',
    unit: '台',
    quantity: 2,
    price: 6500.0,
    amount: 13000.0
  },
  {
    index: 2,
    code: 'SP002',
    name: '机械键盘',
    spec: 'RGB背光/108键',
    unit: '个',
    quantity: 5,
    price: 350.0,
    amount: 1750.0
  },
  {
    index: 3,
    code: 'SP003',
    name: '显示器',
    spec: '27寸/4K/60Hz',
    unit: '台',
    quantity: 1,
    price: 2200.0,
    amount: 2200.0
  },
  {
    index: 4,
    code: 'SP004',
    name: '鼠标',
    spec: '无线/12000DPI',
    unit: '个',
    quantity: 10,
    price: 120.0,
    amount: 1200.0
  },
  {
    index: 5,
    code: 'SP005',
    name: '耳机',
    spec: '蓝牙/降噪',
    unit: '副',
    quantity: 3,
    price: 800.0,
    amount: 2400.0
  }
])

const selectedProductRows = ref([])

// 搜索商品
const searchProducts = () => {
  ElMessage.success('搜索功能待实现')
}

// 选择商品行
const handleProductSelect = () => { }

// 添加单个商品
const addProduct = (product) => {
  ElMessage.success(`已添加商品：${product.name}`)
}

// 处理选中行变化
const handleSelectionChange = (selection) => {
  selectedProductRows.value = selection
}

// 移除单个商品
const removeProduct = (index) => {
  formData.products.splice(index, 1)
}

// 确认商品选择
const confirmProductSelection = () => {
  productSelectDialogVisible.value = false
  ElMessage.success('已添加选中商品')
}

const submitStockInRequest = () => {
  ElMessage.success('入库申请提交成功')
}

// 打开商品选择对话框
const showProductSelectDialog = () => {
  productSelectDialogVisible.value = true
}

// 打开导入对话框
const importProductsFromExcel = () => {
  importDialogVisible.value = true
}

// 下载导入模板
const downloadTemplate = () => {
  ElMessage.success('模板下载功能待实现')
}

// 处理Excel文件变化
const handleExcelChange = () => { }

// 批量移除商品
const removeSelectedProducts = () => {
  // 简化的删除逻辑
  ElMessage.success('删除成功')
}

// 确认导入
const confirmImport = () => {
  importDialogVisible.value = false
  ElMessage.success('导入成功')
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 附件上传相关
const handleFileChange = () => {
  return true
}

const handleFileRemove = () => { }
</script>
<style scoped lang="scss"></style>
