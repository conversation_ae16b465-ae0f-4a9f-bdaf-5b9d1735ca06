// 导出后台服务地址
export const SERVER_URL = "http://127.0.0.1:9000";

// 导出协同服务地址
export const WS_SERVER_URL = "ws://127.0.0.1:48084";

// 协同服务初始化失败时，提供的默认初始化数据
export const defaultSheetData = [
  {
    name: "Sheet1",
    celldata: [
      {
        r: 0,
        c: 0,
        v: {
          v: "协同服务不可用，当前为普通模式",
          bg: "#ff0000",
          fc: "#ffffff",
        },
      },
    ],
  },
];


// 处理协同图片上传
export const uploadImage = async (file: File) => {
	// 此处拿到的是上传的 file 对象，进行文件上传 ，配合 node 接口实现
	const formData = new FormData();
	formData.append("image", file);

	// const { data } = ;

	// // *** 关键步骤：需要返回一个地址给 luckysheet ，用于显示图片
	// if (data.code === 200) return Promise.resolve(data.url);
	// else return Promise.resolve("image upload error");
};

// 处理上传图片的地址
export const imageUrlHandle = (url: string) => {
	// 已经是 // http data 开头则不处理
	if (/^(?:\/\/|(?:http|https|data):)/i.test(url)) return url;
	// 不然拼接服务器路径
	return SERVER_URL + url;
};

// 获取随机值
export const getRandom = () => {
	return Math.random().toString(16).slice(2, 8);
};

// 获取当前环境是否为 开发环境
export const isDev = () => {
	// eslint-disable-next-line @typescript-eslint/ban-ts-comment
	// @ts-ignore
	return import.meta.env.MODE === "development";
};

// 获取 loadUrl 地址
export const getLoadUrl = (gridKey: string) => {
	return `http://127.0.0.1:5000/api/loadSheetData?gridkey=gridkey_demo`;
};

