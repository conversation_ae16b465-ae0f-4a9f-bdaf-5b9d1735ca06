<template>
  <div class="violation-query">
    <el-form :model="queryForm" inline>
      <el-form-item label="违章时间">
        <el-date-picker
          v-model="queryForm.timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="违章人员">
        <el-input
          v-model="queryForm.violatorName"
          placeholder="请输入违章人员姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="所属部门">
        <el-select
          v-model="queryForm.department"
          placeholder="请选择部门"
          clearable
        >
          <el-option
            v-for="item in departmentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="违章类型">
        <el-select
          v-model="queryForm.violationType"
          placeholder="请选择违章类型"
          clearable
        >
          <el-option
            v-for="item in violationTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态">
        <el-select
          v-model="queryForm.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="待处理" value="pending" />
          <el-option label="已处理" value="processed" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="queryResults" border style="width: 100%">
      <el-table-column prop="recordId" label="记录编号" width="120" />
      <el-table-column prop="violatorName" label="违章人员" width="120" />
      <el-table-column prop="department" label="所属部门" width="150" />
      <el-table-column prop="violationType" label="违章类型" width="120" />
      <el-table-column prop="location" label="违章地点" width="150" />
      <el-table-column prop="violationTime" label="违章时间" width="180" />
      <el-table-column prop="description" label="违章描述" />
      <el-table-column prop="punishment" label="处罚结果" width="200" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === '已处理' ? 'success' : 'warning'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: 'ViolationQuery',
  data() {
    return {
      queryForm: {
        timeRange: [],
        violatorName: '',
        department: '',
        violationType: '',
        status: ''
      },
      departmentOptions: [
        { value: 'dept1', label: '施工一部' },
        { value: 'dept2', label: '施工二部' },
        { value: 'dept3', label: '安全部' }
      ],
      violationTypeOptions: [
        { value: 'safety', label: '安全操作' },
        { value: 'equipment', label: '设备使用' },
        { value: 'environment', label: '环境保护' }
      ],
      queryResults: [],
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  methods: {
    handleQuery() {
      // TODO: 调用后端查询接口
      this.queryResults = [
        {
          recordId: 'R202401001',
          violatorName: '张三',
          department: '施工一部',
          violationType: '安全操作',
          location: 'A区施工现场',
          violationTime: '2024-01-15 09:30:00',
          description: '未按规定佩戴安全帽',
          punishment: '警告+罚款100元',
          status: '待处理'
        }
      ]
      this.total = this.queryResults.length
    },
    handleReset() {
      this.queryForm = {
        timeRange: [],
        violatorName: '',
        department: '',
        violationType: '',
        status: ''
      }
      this.handleQuery()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.handleQuery()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.violation-query {
  .el-form {
    margin-bottom: 20px;
  }

  .el-pagination {
    margin-top: 20px;
    justify-content: flex-end;
  }
}
</style> 